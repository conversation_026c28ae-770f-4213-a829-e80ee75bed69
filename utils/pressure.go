package utils

import (
	"git.zuoyebang.cc/infra/pkg/navigator"
	"github.com/gin-gonic/gin"
	"strconv"
)

const PressShiftTimes = 1 // 压测偏移量

const HTTP_X_BD_CALLER_INFO = "HTTP_X_BD_CALLER_INFO"

func GetPressShiftTimes(ctx *gin.Context) uint64 {
	shiftTimes := uint64(PressShiftTimes)
	if ctx.Request == nil || ctx.Request.Header == nil {
		return shiftTimes
	}

	callerInfo := ctx.GetHeader(HTTP_X_BD_CALLER_INFO)
	if v, e := strconv.ParseInt(callerInfo, 10, 64); e == nil {
		shiftTimes = uint64(v)
	}
	return shiftTimes
}

func RegressUidForArray(data []int64) []int64 {
	ret := make([]int64, 0, len(data))
	for _, receiverId := range data {
		receiverId = int64(navigator.RegressUid(uint64(receiverId)))
		ret = append(ret, receiverId)
	}
	return ret
}

func ShiftUidForArray(data []int64, shiftTimes uint64) []int64 {
	ret := make([]int64, 0, len(data))
	for _, receiverId := range data {
		receiverId = int64(navigator.ShiftUid(uint64(receiverId), shiftTimes))
		ret = append(ret, receiverId)
	}
	return ret
}
