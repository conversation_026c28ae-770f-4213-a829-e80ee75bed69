package utils

import (
	"encoding/hex"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
)

func AESDecrypt(encryptData string, secret string) (decryptData string) {
	defer func() {
		if r := recover(); r != nil {
			return
		}
	}()

	key := []byte(secret)
	encrypted, _ := hex.DecodeString(encryptData)
	data := utils.AESDecrypt(encrypted, key)
	decryptData = string(data)
	return
}

func AESEncrypt(decryptData string, secret string) (encryptData string) {
	defer func() {
		if r := recover(); r != nil {
			return
		}
	}()

	key := []byte(secret)
	decrypted := []byte(decryptData)
	data := utils.AESEncrypt(decrypted, key)
	encryptData = hex.EncodeToString(data)
	return
}
