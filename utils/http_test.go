package utils

import (
	"fmt"
	"testing"
	"time"
)

func TestHttpBuildQuery(t *testing.T) {
	query := map[string]string{
		"taskId":     "1",
		"studentUid": "qqq",
	}
	println(HttpBuildQuery(query))
}

func TestTime(t *testing.T) {
	day := "2024-04-15"
	currentDay, _ := time.Parse("2006-01-02", day)
	fmt.Println(currentDay.Weekday(), int(currentDay.Weekday()))
	startDay := GetMonday(currentDay).Format("2006-01-02")
	endDay := GetSunday(currentDay).Format("2006-01-02")
	fmt.Println(startDay, endDay)
}

func TestGetFirstLetter(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"t1", args{"wo shi shui"}, ""},
		{"t2", args{"wo直播课yi"}, ""},
		{"t3", args{"wo直播"}, ""},
		{"t4", args{"作业帮dyi"}, ""},
		{"t5", args{"行书"}, ""},
		{"t6", args{"dd"}, ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetPinyin(tt.args.str)
			t.Logf("GetPinyin() = %v, want %v", got, tt.want)
		})
	}
}
