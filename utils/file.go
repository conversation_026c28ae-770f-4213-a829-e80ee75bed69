package utils

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"bufio"
	"encoding/csv"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"io"
	"os"
	"strconv"
	"strings"
	"time"
)

func WriteToCsv(file string, data [][]string, utfWithBOM bool) error {
	File, err := os.OpenFile(file, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return err
	}
	defer File.Close()
	var fErr error
	if utfWithBOM {
		_, fErr = File.WriteString("\xEF\xBB\xBF")
	}
	if fErr != nil {
		return fErr
	}
	WriterCsv := csv.NewWriter(File)
	for _, _d := range data {
		err1 := WriterCsv.Write(_d)
		if err1 != nil {
			return err1
		}
	}

	WriterCsv.Flush()
	return nil
}

func UploadFile2Bos(ctx *gin.Context, localFile, fileName, fileType string, category string) (pathFile, dwUrl string, err error) {
	remoteFile := fmt.Sprintf("course/%s/%s", category, fileName)
	zlog.Infof(ctx, "UploadFile2Bos req:%+v", []interface{}{ctx, localFile, remoteFile, fileType})
	u, err := helpers.BaiduBucket2.UploadLocalFile(ctx, localFile, remoteFile, fileType, true)
	zlog.Infof(ctx, "UploadFile2Bos res:%+v,err:%+v", u, err)
	if err != nil {
		zlog.Warnf(ctx, "upload file fail err:%+v", err)
		return "", u, err
	}
	return remoteFile + "." + fileType, u, nil
}

func GetSilkName(ctx *gin.Context, oriFileName string, fileType int64) (string, error) {
	fileName := oriFileName + strconv.FormatInt(time.Now().UnixNano(), 10)
	localOriginalFileName := "/tmp/" + fileName + defines.VoiceType2TextMap[fileType]

	err := helpers.BaiduBucket2.Download2Local(ctx, oriFileName, localOriginalFileName)
	if err != nil {
		zlog.Warnf(ctx, "GetJsonContentByMessageType MessageTypeVoice Download2Local err， err：%+v", err)
		return "", err
	}
	defer func() {
		_ = os.Remove(localOriginalFileName)
		if _err := recover(); _err != nil {
			zlog.Errorf(ctx, "panic err : %+v", _err)
		}
	}()

	err = AudioConverterRef.ConvertToSilk(ctx, localOriginalFileName, fileName)
	if err != nil {
		zlog.Warnf(ctx, "转换为silk文件失败, err:%+v", err)
		return "", nil
	}
	return fileName + ".silk", nil
}

func GetFileContent(ctx *gin.Context, fileName string) (bool, [][]string, error) {
	dstFileName := "./" + strconv.FormatInt(time.Now().UnixNano(), 10) + ".csv"
	err := helpers.BaiduBucket2.Download2Local(ctx, fileName, dstFileName)
	if err != nil {
		return false, nil, err
	}
	defer func() {
		_e := os.Remove(dstFileName)
		if _e != nil {

		}
		if err := recover(); err != nil {
			zlog.Errorf(ctx, "panic err : %+v", err)
		}
	}()
	newDstFileName := dstFileName
	newDstFileName = strings.Replace(newDstFileName, ".csv", "temp.csv", 1)
	defer func() {
		os.Remove(newDstFileName)
	}()
	utfWithBOM, err := removeBOMAndCreateNewFile(dstFileName, newDstFileName)
	file, err := os.Open(newDstFileName)
	if err != nil {
		return false, nil, err
	}
	defer file.Close()

	ret := make([][]string, 0)
	reader := csv.NewReader(file)
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		} else if err != nil {
			fmt.Println("Error:", err)
			return false, nil, err
		}
		ret = append(ret, record)
	}
	return utfWithBOM, ret, nil
}

// checkBOM 检查并去除UTF-8 BOM
func checkBOM(reader *bufio.Reader) (bool, error) {
	// 读取前三个字节
	buffer, err := reader.Peek(3)
	if err != nil {
		return false, err
	}

	// 检查是否为BOM
	if buffer[0] == 0xEF && buffer[1] == 0xBB && buffer[2] == 0xBF {
		// 如果是BOM，则丢弃这三个字节
		_, err = reader.Discard(3)
		if err != nil {
			return true, err
		}
		return true, nil
	}

	return false, nil
}

func removeBOMAndCreateNewFile(inputPath, outputPath string) (utfWithBOM bool, err error) {
	// 打开原始CSV文件
	file, err := os.Open(inputPath)
	if err != nil {
		return
	}
	defer file.Close()

	bufReader := bufio.NewReader(file)

	// 检查并去除BOM
	utfWithBOM, err = checkBOM(bufReader)
	if err != nil {
		return
	}

	// 创建新的CSV文件
	newFile, err := os.Create(outputPath)
	if err != nil {
		return
	}
	defer newFile.Close()

	// 将去除BOM后的内容写入新文件
	_, err = io.Copy(newFile, bufReader)
	if err != nil {
		return
	}

	return
}
