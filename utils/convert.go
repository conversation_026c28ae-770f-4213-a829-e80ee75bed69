package utils

import (
	"fmt"
	"reflect"
	"strings"
)

// JoinSlice 将任意类型的数组用分隔符拼接成字符串，支持通过 defineMap 进行值映射
func JoinSlice(slice interface{}, sep string, defineMap map[interface{}]string) string {
	if slice == nil {
		return ""
	}

	val := reflect.ValueOf(slice)
	if val.Kind() != reflect.Slice {
		return fmt.Sprint(slice)
	}

	length := val.Len()
	if length == 0 {
		return ""
	}

	var builder strings.Builder
	for i := 0; i < length; i++ {
		if i > 0 {
			builder.WriteString(sep)
		}

		// 获取当前元素值
		itemValue := val.Index(i).Interface()

		// 如果提供了映射map且能找到对应值，则使用映射值
		if defineMap != nil {
			if mappedValue, ok := defineMap[itemValue]; ok {
				builder.WriteString(mappedValue)
				continue
			}
		}

		// 找不到映射值时使用原值
		builder.WriteString(fmt.Sprint(itemValue))
	}

	return builder.String()
}
