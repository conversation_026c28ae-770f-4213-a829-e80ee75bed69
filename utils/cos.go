package utils

import (
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/url"
	"strings"
)

// GetUrlPath 获取url路径部分
func GetUrlPath(ctx *gin.Context, uri string) (string, error) {
	u, err := url.ParseRequestURI(uri)
	if err != nil {
		zlog.Warnf(ctx, "url.ParseRequestURI %s err:%v", uri, err)
		return "", err
	}

	if strings.HasPrefix(u.Path, "/") {
		return u.Path[1:], nil
	}

	return u.Path, nil
}
