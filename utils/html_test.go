package utils

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"sync"
	"testing"
)

func TestGetHtmlTitle(t *testing.T) {
	env.SetRootPath("../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()
	helpers.InitCos()
	ctx := gin.CreateNewContext(engine)

	url := "http://zuoyebang.com/"
	title, description, icon, err := GetHtmlBaseInfo(url)
	fmt.Println(title, description, icon, err)

	if len(icon) > 0 {
		imageUrl, err := DownloadImgToBos(ctx, icon)
		fmt.Println(imageUrl, err)
	}

}

func TestUploadFile2Bos(t *testing.T) {
	env.SetRootPath("../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()
	helpers.InitCos()
	ctx := gin.CreateNewContext(engine)

	//data, err := helpers.BosBucket.DownloadContent(ctx, "qa_d050f4cda11335e5e4b97b1dc03e6709.png")
	//fmt.Println(string(data), err)
	url := "https://testimg.zuoyebang.cc/fudao_2087623419498671604.jpg"
	wg := sync.WaitGroup{}
	for i := 0; i < 3; i++ {
		v := i
		wg.Add(1)
		go func() {
			defer wg.Done()
			url = fmt.Sprintf("%s?t=%d", url, v)
			imageUrl, err := DownloadImgToBos(ctx, url)
			fmt.Println(imageUrl, err)
		}()
	}
	wg.Wait()
}
