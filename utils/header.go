package utils

import (
	"github.com/gin-gonic/gin"
)

// GetZYBUSS 获取Cookie中的ZYBUSS
func GetZYBUSS(ctx *gin.Context) (zybuss string, err error) {
	_ = ctx.Request.ParseForm()
	zybuss, err = ctx.Cookie("zybuss")
	if err != nil || zybuss == "" {
		zybuss, err = ctx.Cookie("ZYBUSS")
		if err != nil || zybuss == "" {
			zybuss = ctx.Request.Form.Get("ZYBUSS")
			if zybuss == "" {
				return
			}
		}
	}
	return
}
