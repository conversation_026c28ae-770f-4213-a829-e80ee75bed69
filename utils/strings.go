package utils

import (
	"fmt"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/mozillazg/go-pinyin"
)

func StringToIntSlice(ctx *gin.Context, str string) ([]int, error) {
	if len(str) == 0 {
		return nil, nil
	}
	var strSlice []string
	if strings.Contains(str, "，") {
		strSlice = strings.Split(str, "，")
	} else {
		strSlice = strings.Split(str, ",")
	}
	ret := make([]int, 0)
	for _, _strItem := range strSlice {
		_trimAfter := strings.Trim(_strItem, " ")
		_intItem, _err := strconv.Atoi(_trimAfter)
		if _err != nil {
			return nil, _err
		}
		ret = append(ret, _intItem)
	}
	return ret, nil
}

func StringToInt64Slice(ctx *gin.Context, str string) ([]int64, error) {
	if len(str) == 0 {
		return nil, nil
	}
	var strSlice []string
	if strings.Contains(str, "，") {
		strSlice = strings.Split(str, "，")
	} else {
		strSlice = strings.Split(str, ",")
	}
	ret := make([]int64, 0)
	for _, _strItem := range strSlice {
		_trimAfter := strings.Trim(_strItem, " ")
		_int64Item, _err := strconv.ParseInt(_trimAfter, 10, 64)
		if _err != nil {
			return nil, _err
		}
		ret = append(ret, _int64Item)
	}
	return ret, nil
}

func StringToIntMap(ctx *gin.Context, str string) (map[int]bool, error) {
	if len(str) == 0 {
		return nil, nil
	}
	var strSlice []string
	if strings.Contains(str, "，") {
		strSlice = strings.Split(str, "，")
	} else {
		strSlice = strings.Split(str, ",")
	}
	ret := map[int]bool{}
	for _, _strItem := range strSlice {
		_trimAfter := strings.Trim(_strItem, " ")
		_intItem, _err := strconv.Atoi(_trimAfter)
		if _err != nil {
			return nil, _err
		}
		ret[_intItem] = true
	}
	return ret, nil
}

func IntStrContains(big int64, small int64) bool {
	return strings.Contains(strconv.Itoa(int(big)), strconv.Itoa(int(small)))
}

func Int64ArrayToString(arr []int64) string {
	// 使用 strconv 库将 int64 转换为字符串
	strArr := make([]string, len(arr))
	for i, v := range arr {
		strArr[i] = fmt.Sprintf("%d", v)
	}

	// 使用 strings.Join 将字符串数组连接起来，用逗号分隔
	result := strings.Join(strArr, ",")

	return result
}

// GetPhoneLast4 获取手机号后四位
func GetPhoneLast4(phone string) string {
	return phone[len(phone)-4:]
}

func MarshalIgnoreError(v interface{}) string {
	tmpStr, _ := jsoniter.MarshalToString(v)
	return tmpStr
}

func GetPinyin(chineseStr string) []Pinyin {
	result := []Pinyin{}
	if chineseStr == "" {
		return result
	}
	args := pinyin.NewArgs()
	args.Style = pinyin.Tone

	var nonChineseBuffer []rune

	// 遍历字符串中的每个字符
	for i, w := 0, 0; i < len(chineseStr); i += w {
		runeValue, width := utf8.DecodeRuneInString(chineseStr[i:])
		w = width

		if isChineseRune(runeValue) {
			if len(nonChineseBuffer) > 0 {
				nonChineseStr := string(nonChineseBuffer)
				result = append(result, Pinyin{
					Chinese: nonChineseStr,
					Pinyin:  "",
				})
				nonChineseBuffer = nil
			}

			pinyinList := pinyin.Pinyin(string(runeValue), args)
			if len(pinyinList) > 0 && len(pinyinList[0]) > 0 {
				result = append(result, Pinyin{
					Chinese: string(runeValue),
					Pinyin:  pinyinList[0][0],
				})
			}
		} else {
			// 如果不是汉字，存储在缓冲区中
			nonChineseBuffer = append(nonChineseBuffer, runeValue)
		}
	}

	// 处理最后的非汉字字符
	if len(nonChineseBuffer) > 0 {
		nonChineseStr := string(nonChineseBuffer)
		result = append(result, Pinyin{
			Chinese: nonChineseStr,
			Pinyin:  "",
		})
	}

	return result
}

type Pinyin struct {
	Chinese string `json:"chinese"`
	Pinyin  string `json:"pinyin"`
}

func isChineseRune(r rune) bool {
	return r >= 0x4E00 && r <= 0x9FFF
}
