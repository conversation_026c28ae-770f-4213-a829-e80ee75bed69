package utils

import (
	"strconv"
	"strings"
)

func Str2intArray(str string, split string) ([]int, error) {
	if str == "" {
		return []int{}, nil
	}
	intArray := []int{}
	strArray := strings.Split(str, split)
	for _, subStr := range strArray {
		number, err := strconv.Atoi(subStr)
		if err != nil {
			return nil, err
		}
		intArray = append(intArray, number)
	}
	return intArray, nil
}

func StrArray2intArray(str string, split1 string, split2 string) ([]int, error) {
	if str == "" {
		return []int{}, nil
	}
	intArray := []int{}
	strArray := strings.Split(str, split1)
	for _, strVal := range strArray {
		strArray2 := strings.Split(strVal, split2)
		for _, subStr := range strArray2 {
			number, err := strconv.Atoi(subStr)
			if err != nil {
				return nil, err
			}
			intArray = append(intArray, number)
		}
	}
	return intArray, nil
}

func SplitInt64Array(arr []int64, num int) [][]int64 {
	max := len(arr)
	var segmens = make([][]int64, 0)
	if max <= num {
		return append(segmens, arr)
	}
	quantity := max / num

	for i := 0; i <= quantity; i++ {
		minCur := i * num
		if minCur >= max {
			return segmens
		}
		maxCur := (i + 1) * num
		if maxCur > max {
			maxCur = max
		}
		segmens = append(segmens, arr[minCur:maxCur])
	}
	return segmens
}

func SplitInterfaceArray(arr []interface{}, num int) [][]interface{} {
	max := len(arr)
	var segmens = make([][]interface{}, 0)
	if max <= num {
		return append(segmens, arr)
	}
	quantity := max / num

	for i := 0; i <= quantity; i++ {
		minCur := i * num
		if minCur >= max {
			return segmens
		}
		maxCur := (i + 1) * num
		if maxCur > max {
			maxCur = max
		}
		segmens = append(segmens, arr[minCur:maxCur])
	}
	return segmens
}

func SplitStrArray(arr []string, num int) [][]string {
	max := len(arr)
	var segmens = make([][]string, 0)
	if max <= num {
		return append(segmens, arr)
	}
	quantity := max / num

	for i := 0; i <= quantity; i++ {
		minCur := i * num
		if minCur >= max {
			return segmens
		}
		maxCur := (i + 1) * num
		if maxCur > max {
			maxCur = max
		}
		segmens = append(segmens, arr[minCur:maxCur])
	}
	return segmens
}

func SecurityPhone(phone string) string {
	if phone != "" && len(phone) == 11 {
		return phone[:3] + "****" + phone[7:]
	}
	return ""
}

func FilterDuplicatesInt64(nums []int64) []int64 {
	result := make([]int64, 0, len(nums))
	seen := make(map[int64]bool)

	for _, num := range nums {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}

	return result
}

func FilterStrDuplicates(nums []string) []string {
	result := make([]string, 0, len(nums))
	seen := make(map[string]bool)

	for _, num := range nums {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}

	return result
}

func FilterInt64Duplicates(nums []int64) []int64 {
	result := make([]int64, 0, len(nums))
	seen := make(map[int64]bool)

	for _, num := range nums {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}

	return result
}

func FilterStrDuplicatesAndEmpty(nums []string) []string {
	result := make([]string, 0, len(nums))
	seen := make(map[string]bool)

	for _, num := range nums {
		if num == "" {
			continue
		}
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}

	return result
}
