package utils

import (
	"fmt"
	"time"
)

func ChangeStampToYmdHm(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	return t.Format("2006-01-02 15:04")
}

func ChangeStampToYmdHms(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	return t.Format("2006-01-02 15:04:01")
}

// FormatDuration 将秒数转换为"xx小时xx分钟xx秒"格式
func FormatDuration(seconds int64) string {
	if seconds <= 0 {
		return "0秒"
	}

	hours := seconds / 3600
	minutes := (seconds % 3600) / 60
	secs := seconds % 60

	var result string
	if hours > 0 {
		result += fmt.Sprintf("%d小时", hours)
	}
	if minutes > 0 {
		result += fmt.Sprintf("%d分钟", minutes)
	}
	if secs > 0 || result == "" {
		result += fmt.Sprintf("%d秒", secs)
	}

	return result
}

// GetMonday 获取参数所在周的周一
func GetMonday(t time.Time) time.Time {
	days := (t.Weekday() + 6) % 7
	monday := t.AddDate(0, 0, -int(days))
	return time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, time.Local)
}

// GetSunday 获取参数所在周的周天
func GetSunday(t time.Time) time.Time {
	days := (t.Weekday() + 6) % 7
	sunday := t.AddDate(0, 0, +6-int(days))
	return time.Date(sunday.Year(), sunday.Month(), sunday.Day(), 23, 59, 59, 999999999, time.Local)
}

func TimeStampFormat(timeStamp int64) string {
	timeFormat := time.Unix(timeStamp, 0)
	return timeFormat.Format("2006-01-02 15:04")
}
