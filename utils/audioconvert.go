package utils

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"os"
	"os/exec"
)

var (
	AudioConverterRef = &AudioConverter{}
)

// AudioConverter 是一个用于转换音频格式的结构体
type AudioConverter struct {
}

func (ac *AudioConverter) ConvertToSilk(ctx *gin.Context, inputFile string, outputFile string) (err error) {
	outputFilePcm := "/tmp/" + outputFile + ".pcm"
	defer func() {
		os.Remove(outputFilePcm)
		if err := recover(); err != nil {
			zlog.Errorf(ctx, "panic err : %+v", err)
		}
	}()
	zlog.Infof(ctx, "ConvertToSilk, inputFile:%+v, outputFile:%+v", inputFile, outputFile)
	err = ac.ConvertToPcm(inputFile, outputFilePcm)
	if err != nil {
		zlog.Warnf(ctx, "ConvertToPcm error, err:%+v", err)
		return
	}

	outputFileSilk := "/tmp/" + outputFile + ".silk"
	defer func() {
		os.Remove(outputFilePcm)
		if err := recover(); err != nil {
			zlog.Errorf(ctx, "panic err : %+v", err)
		}
	}()
	zlog.Infof(ctx, "ConvertToSilk, inputFile:%+v, outputFile:%+v", inputFile, outputFile)
	err = ac.PcmToSILK(outputFilePcm, outputFileSilk)
	if err != nil {
		zlog.Warnf(ctx, "PcmToSILK error, err:%+v", err)
		return
	}

	_, err = helpers.BaiduBucket2.UploadLocalFile(ctx, outputFileSilk, outputFile, "silk", true)
	if err != nil {
		zlog.Warnf(ctx, "bos上传silk文件失败, err:%+v", err)
	}
	return
}

func (ac *AudioConverter) ConvertToPcm(inputFile string, outputFile string) error {
	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()
	cmd := exec.Command("ffmpeg",
		"-i", inputFile,
		"-f", "s16le",
		"-ar", "24000",
		"-ac", "1",
		"-acodec", "pcm_s16le",
		"-y",
		outputFile,
	)

	err := cmd.Run()
	if err != nil {
		return err
	}

	return nil
}

func (ac *AudioConverter) PcmToSILK(inputFile string, outputFile string) error {
	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()
	// 执行命令将PCM文件转换为SILK文件
	cmd := exec.Command("encoder", inputFile, outputFile, "-rate", "15000", "-tencent")

	err := cmd.Run()
	if err != nil {
		return err
	}
	return nil
}
