package utils

import (
	"context"
	"fmt"
	"math"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// 默认参数
const (
	DefaultTimeout    = 30 * time.Second // 默认超时时间：30秒
	DefaultMaxRetries = 0                // 默认重试次数：0（不重试）
	ChannelBufferSize = 10               // 通道缓冲区大小
)

// TaskFunc 定义任务函数类型
type TaskFunc[T any] func(ctx *gin.Context, data T) error

// Metrics 性能监控指标
type Metrics struct {
	TotalTasks    int64   // 总任务数
	SuccessTasks  int64   // 成功任务数
	FailedTasks   int64   // 失败任务数
	TotalDuration int64   // 总执行时间（毫秒）
	MinDuration   int64   // 最小执行时间（毫秒）
	MaxDuration   int64   // 最大执行时间（毫秒）
	AvgDuration   float64 // 平均执行时间（毫秒）
}

// String 返回格式化的性能指标字符串
func (m *Metrics) String() string {
	return fmt.Sprintf(
		"Concurrent Executor Metrics {Total: %d, Success: %d, Failed: %d, Min: %dms, Max: %dms, Avg: %.2fms}",
		m.TotalTasks,
		m.SuccessTasks,
		m.FailedTasks,
		m.MinDuration,
		m.MaxDuration,
		m.AvgDuration,
	)
}

// WorkerMetrics 定义每个 worker 的本地性能指标
type WorkerMetrics struct {
	TotalDuration int64 // 总执行时间（毫秒）
	MinDuration   int64 // 最小执行时间（毫秒）
	MaxDuration   int64 // 最大执行时间（毫秒）
	SuccessTasks  int64 // 成功任务数
	FailedTasks   int64 // 失败任务数
}

// ConcurrentExecutor 并发执行器
type ConcurrentExecutor[T any] struct {
	ctx           *gin.Context
	workerNum     int
	taskChan      chan T
	errChan       chan error
	wg            sync.WaitGroup
	taskFn        TaskFunc[T]
	timeout       time.Duration   // 任务超时时间
	maxRetries    int             // 最大重试次数
	globalMetrics *Metrics        // 全局性能监控指标
	workerMetrics []WorkerMetrics // 每个 worker 的本地性能指标
	mu            sync.Mutex      // 用于汇总时的锁
}

// NewConcurrentExecutor 创建并发执行器（使用默认超时和重试参数）
func NewConcurrentExecutor[T any](ctx *gin.Context, workerNum int, taskFn TaskFunc[T]) *ConcurrentExecutor[T] {
	return &ConcurrentExecutor[T]{
		ctx:           ctx,
		workerNum:     workerNum,
		taskFn:        taskFn,
		timeout:       DefaultTimeout,
		maxRetries:    DefaultMaxRetries,
		taskChan:      make(chan T, ChannelBufferSize),
		errChan:       make(chan error, ChannelBufferSize),
		globalMetrics: &Metrics{},
		workerMetrics: make([]WorkerMetrics, workerNum), // 每个 worker 一个本地计数器
	}
}

// NewConcurrentExecutorWithOptions 创建并发执行器（使用自定义超时和重试参数）
func NewConcurrentExecutorWithOptions[T any](ctx *gin.Context, workerNum int, taskFn TaskFunc[T], timeout time.Duration, maxRetries int) *ConcurrentExecutor[T] {
	return &ConcurrentExecutor[T]{
		ctx:           ctx,
		workerNum:     workerNum,
		taskFn:        taskFn,
		timeout:       timeout,
		maxRetries:    maxRetries,
		taskChan:      make(chan T, ChannelBufferSize),
		errChan:       make(chan error, ChannelBufferSize),
		globalMetrics: &Metrics{},
		workerMetrics: make([]WorkerMetrics, workerNum), // 每个 worker 一个本地计数器
	}
}

// Execute 执行任务列表
func (e *ConcurrentExecutor[T]) Execute(items []T) error {
	if len(items) == 0 {
		return nil
	}
	e.workerNum = int(math.Min(float64(e.workerNum), float64(len(items))))

	var errs []error
	atomic.StoreInt64(&e.globalMetrics.TotalTasks, int64(len(items)))

	// 启动工作线程
	for i := 0; i < e.workerNum; i++ {
		e.wg.Add(1)
		go func(workerID int) {
			defer e.wg.Done()
			for data := range e.taskChan {
				e.processTask(data, workerID)
			}
		}(i)
	}

	// 生产任务
	go func() {
		defer close(e.taskChan)
		for _, item := range items {
			select {
			case e.taskChan <- item:
			case <-e.ctx.Done():
				return
			}
		}
	}()

	// 等待所有任务完成
	e.wg.Wait()
	close(e.errChan)

	// 汇总性能指标
	e.mergeMetrics()

	// 收集错误
	for err := range e.errChan {
		if err != nil {
			errs = append(errs, err)
		}
	}

	// 如果有错误，返回聚合错误
	if len(errs) > 0 {
		return MultiError(errs)
	}
	return nil
}

// processTask 处理单个任务
func (e *ConcurrentExecutor[T]) processTask(data T, workerID int) {
	taskCtx, cancel := context.WithTimeout(e.ctx, e.timeout)
	defer cancel()

	startTime := time.Now()
	err := e.executeWithRetry(taskCtx, data)
	duration := time.Since(startTime).Milliseconds()

	// 更新本地性能指标
	wm := &e.workerMetrics[workerID]
	if wm.MinDuration == 0 || duration < wm.MinDuration {
		wm.MinDuration = duration
	}
	if duration > wm.MaxDuration {
		wm.MaxDuration = duration
	}
	wm.TotalDuration += duration
	if err != nil {
		wm.FailedTasks++
	} else {
		wm.SuccessTasks++
	}
}

// executeWithRetry 执行任务并支持重试
func (e *ConcurrentExecutor[T]) executeWithRetry(ctx context.Context, data T) error {
	var lastErr error
	for attempt := 0; attempt <= e.maxRetries; attempt++ {
		select {
		case <-ctx.Done():
			return ctx.Err() // 超时或取消
		default:
			err := e.taskFn(e.ctx, data)
			if err == nil {
				return nil // 成功则退出
			}
			lastErr = err
			if attempt < e.maxRetries {
				zlog.Warnf(e.ctx, "Task failed (attempt %d/%d): %v", attempt+1, e.maxRetries, err)
				time.Sleep(time.Millisecond * 100 * time.Duration(attempt+1)) // 指数退避
			}
		}
	}
	if lastErr != nil {
		zlog.Errorf(e.ctx, "Task failed after %d retries: %v", e.maxRetries, lastErr)
	}
	return lastErr
}

// mergeMetrics 汇总本地计数器到全局计数器
func (e *ConcurrentExecutor[T]) mergeMetrics() {
	e.mu.Lock()
	defer e.mu.Unlock()

	for _, wm := range e.workerMetrics {
		if wm.MinDuration > 0 && (e.globalMetrics.MinDuration == 0 || wm.MinDuration < e.globalMetrics.MinDuration) {
			e.globalMetrics.MinDuration = wm.MinDuration
		}
		if wm.MaxDuration > e.globalMetrics.MaxDuration {
			e.globalMetrics.MaxDuration = wm.MaxDuration
		}
		e.globalMetrics.TotalDuration += wm.TotalDuration
		e.globalMetrics.SuccessTasks += wm.SuccessTasks
		e.globalMetrics.FailedTasks += wm.FailedTasks
	}

	// 计算平均执行时间
	totalTasks := e.globalMetrics.SuccessTasks + e.globalMetrics.FailedTasks
	if totalTasks > 0 {
		e.globalMetrics.AvgDuration = float64(e.globalMetrics.TotalDuration) / float64(totalTasks)
	}
}

// MultiError 自定义错误类型，用于合并多个错误
type MultiError []error

func (m MultiError) Error() string {
	var sb strings.Builder
	for i, err := range m {
		if i > 0 {
			sb.WriteString("; ")
		}
		sb.WriteString(err.Error())
	}
	return sb.String()
}

// GetMetrics 返回性能指标
func (e *ConcurrentExecutor[T]) GetMetrics() *Metrics {
	return e.globalMetrics
}

// GenerateSequence 生成从0到n-1的序列
func GenerateSequence(n int) []int {
	result := make([]int, n)
	for i := 0; i < n; i++ {
		result[i] = i
	}
	return result
}
