package utils

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
)

const (
	PicPrefix = "jpg"
	PicSuffix = ".jpg"
)

func DownloadImgToBos(ctx *gin.Context, url string) (imgUrl string, err error) {
	// 更新状态
	var resp *http.Response
	resp, err = http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var imgByte []byte
	imgByte, err = io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	imgPidByte := append(imgByte, []byte(url)...)
	pid := GetImagePid(imgPidByte)
	_, err = helpers.BosBucket.UploadFileContent(ctx, string(imgByte), pid, PicPrefix)
	if err != nil {
		return "", err
	}

	host := defines.BosDomainOnline
	if env.GetRunEnv() == env.RunEnvTest {
		host = defines.BosDomainOffline
	}
	imgUrl = "https://" + host + "/" + pid + PicSuffix
	return
}

func GetImagePid(imgByte []byte) string {
	prefix := "zyb"
	if env.GetRunEnv() == env.RunEnvTest {
		prefix = "qa"
	}

	h := md5.New()
	h.Write(imgByte)
	cipherStr := h.Sum(nil)
	return fmt.Sprintf("%s_%s", prefix, hex.EncodeToString(cipherStr))
}
