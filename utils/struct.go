package utils

import "reflect"

func StructToMap(obj interface{}, tag string) map[string]interface{} {
	result := make(map[string]interface{})
	value := reflect.ValueOf(obj).Elem() // 获取指针的值
	typ := value.Type()                  // 获取类型信息

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)                   // 获取字段信息
		tagValue := field.Tag.Get(tag)          // 获取标签名称
		if tagValue != "" && !field.Anonymous { // 如果有标签且不是匿名字段
			result[tagValue] = value.FieldByIndex([]int{i}).Interface() // 设置到Map中
		}
	}

	return result
}

func GroupBy[T any](list []T, field string) map[any][]T {
	groups := make(map[any][]T)
	for _, item := range list {
		value := reflect.ValueOf(item)
		fieldValue := value.FieldByName(field).Interface()
		groups[fieldValue] = append(groups[fieldValue], item)
	}
	return groups
}

func ToMap[T any](list []T, field string) map[any]T {
	groups := make(map[any]T)
	for _, item := range list {
		value := reflect.ValueOf(item)
		fieldValue := value.FieldByName(field).Interface()
		groups[fieldValue] = item
	}
	return groups
}

func Int64Collect[T any](list []T, field string) []int64 {
	groups := []int64{}
	for _, item := range list {
		value := reflect.ValueOf(item)
		fieldValue := value.FieldByName(field).Interface()
		val, ok := fieldValue.(int64)
		if ok {
			groups = append(groups, val)
		}
	}
	return groups
}
