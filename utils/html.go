package utils

import (
	"assistantdeskgo/components"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"net/http"
	"net/url"
)

func GetHtmlBaseInfo(urlStr string) (title, description, icon string, err error) {
	// 解析URL
	var urlInfo *url.URL
	urlInfo, err = url.Parse(urlStr)
	if err != nil {
		return "", "", "", err
	}

	var res *http.Response
	res, err = http.Get(urlStr)
	if err != nil {
		return "", "", "", components.ErrorUrlInvalid
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		return "", "", "", components.ErrorUrlInvalid
	}

	// 使用goquery解析HTML文档
	var doc *goquery.Document
	doc, err = goquery.NewDocumentFromReader(res.Body)
	if err != nil {
		fmt.Println(doc)
	}

	// 查找并获取<title>标签的文本内容
	doc.Find("title").Each(func(i int, s *goquery.Selection) {
		title = s.Text()
	})

	// 获取页面描述
	doc.Find("meta").Each(func(i int, s *goquery.Selection) {
		if name, _ := s.Attr("name"); name == "description" {
			description, _ = s.Attr("content")
			return
		}
	})

	// 获取图标
	doc.Find("link").Each(func(i int, s *goquery.Selection) {
		if name, _ := s.Attr("rel"); name == "icon" {
			icon, _ = s.Attr("href")
			return
		}
	})

	if len(icon) == 0 {
		return
	}

	var iconInfo *url.URL
	if iconInfo, err = url.Parse(icon); err != nil {
		return "", "", "", err
	}

	if len(iconInfo.Scheme) == 0 {
		iconInfo.Scheme = urlInfo.Scheme
	}
	icon = iconInfo.String()

	return
}
