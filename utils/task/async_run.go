package task

import (
	"runtime"
	"sync"
)

func AsyncRun(fs ...func()) []error {
	var (
		wg   sync.WaitGroup
		errs []error
		mtx  sync.Mutex
	)

	if len(fs) == 0 {
		return nil
	}

	addError := func(err error) {
		mtx.Lock()
		defer mtx.Unlock()

		errs = append(errs, err)
	}

	addFunc := func(f func()) {
		if f == nil {
			return
		}

		wg.Add(1)
		go func() {
			defer func() {
				if err := recover(); err != nil {
					buf := make([]byte, 2048)
					runtime.Stack(buf, false)
					addError(newPanicError(err, buf))
				}
				wg.Done()
			}()

			f()
		}()
	}

	for i := range fs {
		addFunc(fs[i])
	}

	wg.Wait()
	return errs
}
