package task

import (
	"encoding/csv"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/saintfish/chardet"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io"
	"os"
	"strings"
	"testing"
)

func TestGetFile(t *testing.T) {
	newDstFileName := "/Users/<USER>/Downloads/sailorscoretemplateadddatatype.csv"
	file, err := os.Open(newDstFileName)
	if err != nil {
		t.Log(err)
		return
	}
	defer file.Close()

	//csvReader := csv.NewReader(file)
	// 使用GBK解码器转换数据流
	reader := transform.NewReader(file, simplifiedchinese.GBK.NewDecoder())

	// 创建csv读取器
	csvReader := csv.NewReader(reader)
	csvReader.Comma = ','             // 设置分隔符，根据你的CSV文件实际情况设置
	csvReader.LazyQuotes = true       // 处理引号内的逗号等特殊情况
	csvReader.TrimLeadingSpace = true // 去除字段前的空白字符

	ret := make([][]string, 0)
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		} else if err != nil {
			fmt.Println("Error:", err)
			t.Log(err)
		}

		ret = append(ret, record)
	}
	t.Log(ret)
}

func isCorrectEncoding(s string, encName string) bool {
	var decoder transform.Transformer
	switch encName {
	case "UTF-8":
		decoder = transform.Chain(transform.Nop, transform.Nop) // 对于UTF-8，直接返回true，因为没有解码器需要应用。
	case "GBK":
		decoder = simplifiedchinese.GBK.NewDecoder() // 使用GBK解码器
	case "Big5":
		decoder = simplifiedchinese.HZGB2312.NewDecoder() // 使用Big5解码器
	default:
		return false // 不支持的编码格式返回false
	}
	reader := transform.NewReader(strings.NewReader(s), decoder)
	_, err := io.ReadAll(reader) // 尝试读取并忽略结果，只检查错误是否为nil。如果为nil，则认为是正确的编码。
	return err == nil            // 如果无错误，返回true，表示可能正确解码。注意实际应用中可能需要更复杂的逻辑来判断。
}

func TestFile(t *testing.T) {
	data, err := os.ReadFile("/Users/<USER>/Downloads/excelsailorscore2.csv")
	if err != nil {
		t.Log(err)
		return
	}
	// 创建检测器
	detector := chardet.NewTextDetector()

	// 检测编码
	result, err := detector.DetectBest(data)
	if err != nil {
		t.Fatal(err)
	}

	// 输出结果
	fmt.Printf("Detected charset: %s\n", result.Charset)
	fmt.Printf("Confidence: %d\n", result.Confidence)
}

func TestStr(t *testing.T) {
	str := "q4学科竞赛"
	t.Log(len(str))
	t.Log(fwyyutils.Substr(str, 0, 5))
}
