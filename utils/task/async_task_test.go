package task

import (
	"fmt"
	"testing"
	"time"
)

func TestAsyncTaskAdd(_ *testing.T) {
	t := NewAsyncTask()
	fmt.Println(time.Now())
	for i := 0; i < 100; i++ {
		t.Add(func() { time.Sleep(time.Duration(10*i) * time.Millisecond) })
	}

	fmt.Println(t.Wait())
	fmt.Println(time.Now())
}

func TestAsyncTaskAddN(_ *testing.T) {
	t := NewAsyncTask()
	fmt.Println(time.Now())
	fs := []func(){
		func() { fmt.Println(1); panic(1) },
		func() { fmt.Println(2); panic(2) },
		func() { fmt.Println(3); panic(3) },
		func() { fmt.Println(4); panic("4") },
	}

	t.Add(fs...)
	_ = t.Wait()
	//	fmt.Println(t.Errors())
	fmt.Println(time.Now())
}
