package task

import (
	"runtime"
	"sync"
)

type AsyncTask interface {
	Add(...func())
	Wait() []error
}

func NewAsyncTask() AsyncTask {
	return &asyncTask{
		wg: &sync.WaitGroup{},
	}
}

func NewAsyncTaskWithWaitGroup(wg *sync.WaitGroup) AsyncTask {
	return &asyncTask{
		wg: wg,
	}
}

type asyncTask struct {
	wg   *sync.WaitGroup
	errs []error
	mtx  sync.Mutex
}

func (at *asyncTask) Add(f ...func()) {
	for i := range f {
		at.add(f[i])
	}
}

func (at *asyncTask) add(f func()) {
	if f == nil {
		return
	}

	at.wg.Add(1)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				buf := make([]byte, 2048)
				runtime.Stack(buf, false)
				at.addError(newPanicError(err, buf))
			}
			at.wg.Done()
		}()

		f()
	}()
}

func (at *asyncTask) Wait() []error {
	at.wg.Wait()
	return at.errs
}

func (at *asyncTask) addError(err error) {
	at.mtx.Lock()
	defer at.mtx.Unlock()

	at.errs = append(at.errs, err)
}
