package task

import (
	"fmt"
	"testing"
	"time"
)

func TestAsyncRun(t *testing.T) {
	type args struct {
		fs []func()
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "no error",
			args: args{
				fs: []func(){
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Millisecond) },
				},
			},
		},
		{
			name: "with error",
			args: args{
				fs: []func(){
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Second) },
					func() { time.Sleep(time.Millisecond) },
					func() { panic(1) },
					func() { panic(2) },
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			begin := time.Now()
			if got := AsyncRun(tt.args.fs...); (got != nil) != tt.wantErr {
				t.Errorf("AsyncRun() = %v, want %v", got, tt.wantErr)
			}

			fmt.Println(time.Now().Sub(begin))
		})
	}
}
