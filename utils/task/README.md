# 异步任务执行工具，支持执行闭包`func()`函数。
1. 封装了`sync.WaitGroup`
2. 对于每一个协程都添加`recover`，防止`panic`扩散
3. 返回的`[]error`包含产生了`panic`的协程的堆栈信息

# `API`
支持两种风格`api`

## 1. 函数风格

```go
    var(
        f1 func()
        f2 func()
    )

    errs := task.AsyncRun(f1,f2)
    if errs ....{
        //...
    }

```

## 2.对象风格
```go
    var(
        f1 func()
        f2 func()
        f3 func()
    )

    at := task.NewAsyncTask()
    at.Add(f1,f2)

    f3()

    errs := at.Wait()

    if errs ....{
        //...
    }
```
