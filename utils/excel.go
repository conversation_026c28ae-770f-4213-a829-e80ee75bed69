package utils

import (
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

// ExportExcel 导出Excel通用方法
func ExportExcel(ctx *gin.Context, fileName string, headers []string, records [][]string) error {
	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))

	// 创建 Excel 文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			zlog.Errorf(ctx, "close excel file failed, err: %v", err)
		}
	}()

	// 计算每列的最大宽度
	colWidths := make([]float64, len(headers))
	// 先计算表头的宽度
	for j, header := range headers {
		width := 0.0
		for _, char := range header {
			if char > 0x7F { // 判断是否为非 ASCII 字符（如中文）
				width += 2
			} else {
				width += 1
			}
		}
		colWidths[j] = width
	}
	// 再计算数据的宽度
	for _, row := range records {
		for j, cell := range row {
			width := 0.0
			for _, char := range cell {
				if char > 0x7F { // 判断是否为非 ASCII 字符（如中文）
					width += 2
				} else {
					width += 1
				}
			}
			if width > colWidths[j] {
				colWidths[j] = width
			}
		}
	}

	// 设置列宽
	for i, width := range colWidths {
		col := string(rune('A' + i))
		adjustedWidth := width * 1.2
		if adjustedWidth < 8 {
			adjustedWidth = 8
		}
		if adjustedWidth > 40 {
			adjustedWidth = 40
		}
		f.SetColWidth("Sheet1", col, col, adjustedWidth)
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 11,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#CCCCCC"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		return err
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		if err := f.SetCellValue("Sheet1", cell, header); err != nil {
			return err
		}
	}
	// 应用表头样式
	f.SetRowStyle("Sheet1", 1, 1, headerStyle)

	// 写入数据
	for i, record := range records {
		row := i + 2
		for j, value := range record {
			cell := fmt.Sprintf("%s%d", string(rune('A'+j)), row)
			if err := f.SetCellValue("Sheet1", cell, value); err != nil {
				return err
			}
		}
	}

	// 冻结首行
	if err := f.SetPanes("Sheet1", &excelize.Panes{
		Freeze:      true,
		Split:       false,
		XSplit:      0,
		YSplit:      1,
		TopLeftCell: "A2",
		ActivePane:  "bottomLeft",
	}); err != nil {
		return err
	}

	// 写入响应
	return f.Write(ctx.Writer)
}

// ExcelSheet Excel工作表数据结构
type ExcelSheet struct {
	SheetName string
	Headers   []string
	Records   [][]string
}

// ExportMultiSheetExcel 导出多Sheet页的Excel通用方法
func ExportMultiSheetExcel(ctx *gin.Context, fileName string, sheets []ExcelSheet) error {
	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))

	// 创建 Excel 文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			zlog.Errorf(ctx, "close excel file failed, err: %v", err)
		}
	}()

	// 处理每个sheet
	for i, sheet := range sheets {
		sheetName := sheet.SheetName
		if i == 0 {
			// 重命名默认的Sheet1
			f.SetSheetName("Sheet1", sheetName)
		} else {
			// 创建新的Sheet
			f.NewSheet(sheetName)
		}

		// 计算每列的最大宽度
		colWidths := make([]float64, len(sheet.Headers))
		// 先计算表头的宽度
		for j, header := range sheet.Headers {
			width := 0.0
			for _, char := range header {
				if char > 0x7F {
					width += 2
				} else {
					width += 1
				}
			}
			colWidths[j] = width
		}
		// 再计算数据的宽度
		for _, row := range sheet.Records {
			for j, cell := range row {
				width := 0.0
				for _, char := range cell {
					if char > 0x7F {
						width += 2
					} else {
						width += 1
					}
				}
				if width > colWidths[j] {
					colWidths[j] = width
				}
			}
		}

		// 设置列宽
		for i, width := range colWidths {
			col := string(rune('A' + i))
			adjustedWidth := width * 1.2
			if adjustedWidth < 8 {
				adjustedWidth = 8
			}
			if adjustedWidth > 40 {
				adjustedWidth = 40
			}
			f.SetColWidth(sheetName, col, col, adjustedWidth)
		}

		// 设置表头样式
		headerStyle, err := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold: true,
				Size: 11,
			},
			Fill: excelize.Fill{
				Type:    "pattern",
				Color:   []string{"#CCCCCC"},
				Pattern: 1,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
		})
		if err != nil {
			return err
		}

		// 写入表头
		for i, header := range sheet.Headers {
			cell := fmt.Sprintf("%s1", string(rune('A'+i)))
			if err := f.SetCellValue(sheetName, cell, header); err != nil {
				return err
			}
		}
		// 应用表头样式
		f.SetRowStyle(sheetName, 1, 1, headerStyle)

		// 写入数据
		for i, record := range sheet.Records {
			row := i + 2
			for j, value := range record {
				cell := fmt.Sprintf("%s%d", string(rune('A'+j)), row)
				if err := f.SetCellValue(sheetName, cell, value); err != nil {
					return err
				}
			}
		}

		// 冻结首行
		if err := f.SetPanes(sheetName, &excelize.Panes{
			Freeze:      true,
			Split:       false,
			XSplit:      0,
			YSplit:      1,
			TopLeftCell: "A2",
			ActivePane:  "bottomLeft",
		}); err != nil {
			return err
		}
	}

	return f.Write(ctx.Writer)
}
