package utils

import (
	"fmt"
	"strconv"
	"strings"
)

func SplitBusinessKey(clueId string) (studentUid, courseId int64, err error) {
	parts := strings.Split(clueId, "_")
	if len(parts) != 2 {
		return 0, 0, fmt.<PERSON><PERSON>rf("invalid businessKey format, businessKey: %s", clueId)
	}
	studentUid, err1 := strconv.ParseInt(parts[0], 10, 64)
	courseId, err2 := strconv.ParseInt(parts[1], 10, 64)

	if err1 != nil || err2 != nil {
		return courseId, studentUid, fmt.Errorf("failed to parse studentUid from clueId, clueId: %s, err1: %v, err2: %v", clueId, err1, err2)
	}
	return studentUid, courseId, nil
}

func GetCUAKey(clueId string, deviceUid int64) string {
	if deviceUid > 0 {
		return fmt.Sprintf("%s_%d", clueId, deviceUid)
	} else {
		return clueId
	}

}

func GetClueId(courseId, studentUid int64) string {
	return fmt.Sprintf("%d_%d", studentUid, courseId)
}
