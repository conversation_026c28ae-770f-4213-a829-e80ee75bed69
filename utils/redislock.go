package utils

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

var releaseLockScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
    `

func ReleaseLockByValue(ctx *gin.Context, lockKey, lockValue string) (bool, error) {
	// 使用Lua脚本来安全释放锁
	result, err := helpers.RedisClient.Lua(ctx, releaseLockScript, 1, lockKey, lockValue)
	if err != nil {
		return false, err
	}
	return result == 1, nil
}

func LockRetry(ctx *gin.Context, key string, value interface{}, expire uint64, retry int) (result bool, err error) {
	if retry > 10 {
		zlog.Infof(ctx, "重试次数不能超过10次，入参为%+v。", retry)
		retry = 10
	}
	count := 0
	for count < retry {
		result, err = helpers.RedisClient.SetNxByEX(ctx, key, value, expire)
		if err != nil {
			zlog.Warnf(ctx, "获取分布式锁异常，key：%+v，value：%+v", key, value)
			count += 1
			time.Sleep(time.Millisecond * 300)
			continue
		}
		if result {
			return
		}
		zlog.Infof(ctx, "获取分布式锁失败，key：%+v，value：%+v", key, value)
		count += 1
		time.Sleep(time.Millisecond * 300)
	}
	return
}
