package utils

import (
	"regexp"
	"strconv"
	"strings"
)

func MaskPhone11(phone string) string {
	// 使用正则表达式检查手机号格式
	match, _ := regexp.MatchString(`^\d{11}$`, phone)
	if !match {
		return phone
	}

	// 替换中间4位为****
	masked := phone[:3] + "****" + phone[7:]
	return masked
}

func MaskStudentUid(studentUid int64) string {
	str := strconv.Itoa(int(studentUid))
	// 使用正则表达式检查手机号格式
	match, _ := regexp.MatchString(`^\d{10}$`, str)
	if !match {
		return str
	}

	// 替换中间4位为****
	masked := str[:2] + "****" + str[6:]
	return masked
}

func GetMailPrefix(mail string) string {
	if len(mail) == 0 {
		return ""
	}

	splits := strings.Split(mail, "@")
	if len(splits) > 1 {
		return splits[0]
	}
	return mail
}
