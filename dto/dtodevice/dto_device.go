package dtodevice

type GetUserDeviceListByCourseReq struct {
	CourseId int64 `json:"courseId" form:"courseId" required:"true"`
	PeronUid int64 `json:"personUid" form:"personUid" required:"true"`
}

type GetUserDeviceListByCourseRsp struct {
	DeviceList []GetUserDeviceListByCourseDevice `json:"deviceList"`
}

type GetUserDeviceListByCourseDevice struct {
	Id       string `json:"id"`
	WxName   string `json:"wxName"`
	WxAvatar string `json:"wxAvatar"`
	WeixinId string `json:"weixinId"`
	KpUid    string `json:"kpUid"`
	WxPhone  string `json:"wxPhone"`
}
