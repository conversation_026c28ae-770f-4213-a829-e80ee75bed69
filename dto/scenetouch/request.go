package scenetouch

type ContextOptionsReq struct {
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	CourseId     int64  `json:"courseId" form:"courseId"`
	LessonId     int64  `json:"lessonId" form:"lessonId"`
	SceneType    int64  `json:"sceneType" form:"sceneType"`
	Key          string `json:"key" form:"key"`
}

type GroupFilterGroupBindReq struct {
	AssistantUid         int64   `json:"assistantUid" form:"assistantUid"`
	PersonUid            int64   `json:"personUid" form:"personUid"`
	CourseId             int64   `json:"courseId" form:"courseId"`
	LessonId             int64   `json:"lessonId" form:"lessonId"`
	SendType             int64   `json:"sendType" form:"sendType"`
	TaskId               int64   `json:"taskId" form:"taskId"`
	SceneContext         string  `json:"sceneContext" form:"sceneContext"`
	ArrBindWxStudentUids []int64 `json:"arrBindWxStudentUids" form:"arrBindWxStudentUids"`
}

type UseJobReq struct {
	CourseId     int64                 `json:"courseId" form:"courseId"`
	LessonId     int64                 `json:"lessonId" form:"lessonId"`
	SceneType    int64                 `json:"sceneType" form:"sceneType"`
	GroupList    []PrepareJobGroupItem `json:"groupList" form:"groupList"`
	SceneContext string                `json:"sceneContext" form:"sceneContext"`
}

type PrepareJobGroupItem struct {
	GroupName     string   `json:"groupName" form:"groupName"`
	StudentUids   []int64  `json:"studentUids" form:"studentUids"`
	NeedVariables []string `json:"needVariables"`
}

type JobStatusReq struct {
	JobId int64 `json:"jobId" form:"jobId"`
}

type JobResultReq struct {
	JobId    int64 `json:"jobId" form:"jobId"`
	OnlyFail bool  `json:"onlyFail" form:"onlyFail"`
}
