package scenetouch

type ContextOptionsRsp struct {
	Total int64               `json:"total"`
	List  []ContextOptionItem `json:"list"`
}

type ContextOptionItem struct {
	Label         string                 `json:"label"`
	Value         interface{}            `json:"value"`
	CanSelect     bool                   `json:"canSelect"`
	DefaultSelect bool                   `json:"defaultSelect"`
	Extra         map[string]interface{} `json:"extra"`
}

type GroupFilterGroupBindRsp struct {
	ResultList []GroupFilterGroupBindItem `json:"resultList"`
}

type GroupFilterGroupBindItem struct {
	Title          string   `json:"title"`
	LabelList      []string `json:"labelList"`
	DefaultTpl     []string `json:"defaultTpl"`
	StudentUidList []int64  `json:"studentUidList"`
	VariableNames  []string `json:"variableNames"`
}

type UseJobRsp struct {
	JobId    int64  `json:"jobId"`
	ParamKey string `json:"paramKey"`
}

type JobStatusRsp struct {
	JobId         int64  `json:"jobId"`
	CreateTime    int64  `json:"createTime"`
	StartTime     int64  `json:"startTime"`
	EndTime       int64  `json:"endTime"`
	SuccessNum    int    `json:"successNum"`
	FailNum       int    `json:"failNum"`
	TotalNum      int    `json:"totalNum"`
	RunStatusDesc string `json:"runStatusDesc"`
	Status        int    `json:"status"`
	UpdateTime    int64  `json:"updateTime"`
	ExpireTime    int64  `json:"expireTime"`
}

type JobResultRsp struct {
	JobId        int64             `json:"jobId"`
	CourseId     int64             `json:"courseId"`
	LessonId     int64             `json:"lessonId"`
	AssistantUid int64             `json:"assistantUid"`
	SceneType    int               `json:"sceneType"`
	TaskResults  []TaskResultsItem `json:"taskResults"`
	SceneContext string            `json:"sceneContext"`
}

type TaskResultsItem struct {
	GroupName   string                 `json:"groupName"`
	StudentName string                 `json:"studentName"`
	StudentUid  int64                  `json:"studentUid"`
	Variables   map[string]interface{} `json:"variables"`
	Status      int                    `json:"status"`
	FailReasons []string               `json:"failReasons"`
}
