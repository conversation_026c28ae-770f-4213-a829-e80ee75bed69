package dtochatword

type CreateTalkReq struct {
	CategoryId  int64                  `json:"categoryId"`
	Content     string                 `json:"content"`
	Title       string                 `json:"title"`
	Keyword     []string               `json:"keyword"`
	GroupId     int64                  `json:"groupId"`
	GroupIdList []int64                `json:"groupIdList"`
	ComposeList []CreateTalkComposeReq `json:"composeList"`
}

type CreateTalkComposeReq struct {
	//ComposeId   int64                  `json:"composeId"`
	MessageList []CreateTalkMessageReq `json:"messageList"`
}

type CreateTalkMessageReq struct {
	Type         int64       `json:"type"`
	Content      interface{} `json:"content"`
	Order        int64       `json:"order"`
	IntervalTime int64       `json:"intervalTime"`
}

type HideTalkReq struct {
	TalkIdList []int64 `json:"talkIdList" form:"talkIdList"`
	Type       int64   `json:"type" form:"type"`
}

type DeleteTalkReq struct {
	TalkIdList []int64 `json:"talkIdList" form:"talkIdList"`
}

type TalkDetailReq struct {
	TalkId int64 `json:"talkId" form:"talkId"`
}
type TalkDetailListReq struct {
	TalkIdList []int64 `json:"talkIdList" form:"talkIdList"`
}

type TalkListRsp struct {
	Total int64           `json:"total"`
	List  []TalkDetailRsp `json:"list"`
}

type TalkDetailRsp struct {
	CategoryId           int64            `json:"categoryId"`
	CategoryName         string           `json:"categoryName"`
	TalkId               int64            `json:"talkId"`
	Hide                 int64            `json:"hide"`
	Content              string           `json:"content"`
	Title                string           `json:"title"`
	Keyword              []string         `json:"keyword"`
	GroupId              int64            `json:"groupId"`
	GroupName            string           `json:"groupName"`
	GroupIdList          []int64          `json:"groupIdList"`
	GroupNameList        []string         `json:"groupNameList"`
	LikeCount            int64            `json:"likeCount"`
	DislikeCount         int64            `json:"dislikeCount"`
	CollectCount         int64            `json:"collectCount"`
	SendCount            int64            `json:"sendCount"`
	EditSendCount        int64            `json:"editSendCount"`
	TransferGroupCount   int64            `json:"transferGroupCount"`
	Collect              int64            `json:"collect"`
	CollectPersonUidList []int64          `json:"collectPersonUidList"`
	Like                 int64            `json:"like"`
	PersonUid            int64            `json:"personUid"`
	PersonName           string           `json:"personName"`
	UpdateUid            int64            `json:"updateUid"`
	UpdateName           string           `json:"updateName"`
	CreateTime           int64            `json:"createTime"`
	UpdateTime           int64            `json:"updateTime"`
	ComposeList          []TalkComposeRsp `json:"composeList"`
}

type TalkDetailHighLightRsp struct {
	TalkDetailRsp
	HighLightContent    []string `json:"highLightContent"`
	HighLightTitle      []string `json:"HighLightTitle"`
	HighLightKeyword    []string `json:"HighLightKeyword"`
	HighLightMesContent []string `json:"HighLightMesContent"`
}

type TalkSearchRsp struct {
	Total int64                    `json:"total"`
	List  []TalkDetailHighLightRsp `json:"list"`
}

type AssistantListTalkReq struct {
	CategoryId  int64   `json:"categoryId" form:"categoryId"`
	Keyword     string  `json:"keyword" form:"keyword"`
	MsgContent  string  `json:"msgContent" form:"msgContent"`
	GroupId     int64   `json:"groupId"  form:"groupId"`
	GroupIdList []int64 `json:"groupIdList"  form:"groupIdList"`
	CreateName  string  `json:"createName" form:"createName"`
	Page        int     `json:"page"  form:"page"`
	PageSize    int     `json:"pageSize" form:"pageSize"`
}

type WeworkListTalkReq struct {
	SearchContent string `json:"searchContent" form:"searchContent"`
	Collect       int64  `json:"collect" form:"collect"`
	CategoryId    int64  `json:"categoryId" form:"categoryId"`
	Page          int    `json:"page" form:"page"`
	PageSize      int    `json:"pageSize" form:"pageSize"`
}

type ListTalkRsp struct {
	CategoryId   int64            `json:"categoryId"`
	CategoryName string           `json:"categoryName"`
	TalkId       int64            `json:"talkId"`
	Content      string           `json:"content"`
	Title        string           `json:"title"`
	Keyword      []string         `json:"keyword"`
	GroupId      int64            `json:"groupId"`
	GroupName    string           `json:"groupName"`
	PersonUid    int64            `json:"personUid"`
	PersonName   string           `json:"personName"`
	CreateTime   int64            `json:"createTime"`
	UpdateTime   int64            `json:"updateTime"`
	ComposeList  []TalkComposeRsp `json:"composeList"`
}

type TalkComposeRsp struct {
	ComposeId   int64                   `json:"composeId"`
	MessageList []TalkComposeMessageRsp `json:"messageList"`
}

type TalkComposeMessageRsp struct {
	Type         int64       `json:"type"`
	MessageId    int64       `json:"messageId"`
	Content      interface{} `json:"content"` // 可变对象，map结构（https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=500859683）
	Order        int64       `json:"order"`
	IntervalTime int64       `json:"intervalTime"`
}
