package dtochatword

type CreateCategoryReq struct {
	CategoryName string `json:"categoryName" form:"categoryName"`
}

type DeleteCategoryReq struct {
	CategoryId int64 `json:"categoryId" form:"categoryId"`
}

type CategoryInfoRsp struct {
	CategoryId   int64  `json:"categoryId"`
	CategoryName string `json:"categoryName"`
	CreateTime   int64  `json:"createTime"`
}

type CategoryWithKeywordRsp struct {
	CategoryInfoRsp
	Keyword []string `json:"keyword"`
}
