package dtopreclass

import "git.zuoyebang.cc/fwyybase/fwyylibs/defines"

// TagInfoConf key 配置平台结构
type TagInfoConf struct {
	Label        string  `json:"label"`
	Seasons      []int64 `json:"seasons"`
	Grades       []int64 `json:"grades"`
	ServiceTypes []int   `json:"serviceTypes"`
	Enable       bool    `json:"enable"`
}

type AIAutoTag struct {
	MsgID                  string                  `json:"-" mapstructure:"-"`
	SceneType              int                     `json:"sceneType" mapstructure:"sceneType"`
	CourseID               int64                   `json:"courseId" mapstructure:"courseId"`
	AssistantUID           int64                   `json:"assistantUid" mapstructure:"assistantUid"`
	StudentUID             int64                   `json:"studentUid" mapstructure:"studentUid"`
	CreateTime             int64                   `json:"createTime" mapstructure:"createTime"`
	LessonPreClassTagInfos []LessonPreClassTagInfo `json:"lessonPreClassTagInfos" mapstructure:"lessonPreClassTagInfos"`
}

type LessonPreClassTagInfo struct {
	LessonID int64    `json:"lessonId" mapstructure:"lessonId"`
	TagType  int      `json:"tagType" mapstructure:"tagType"` // 标签类型，对应失联、请假等
	ExtInfo  *ExtInfo `json:"extInfo" mapstructure:"extInfo"`
}

type ExtInfo struct {
	BanxueTag        string `json:"banXueTag" mapstructure:"banXueTag"`
	ContentTime      int64  `json:"contentTime" mapstructure:"contentTime"`
	FirstLeaveReason string `json:"firstLeaveReason" mapstructure:"firstLeaveReason"`
	LeaveSeason      string `json:"leaveSeason" mapstructure:"leaveSeason"`
}

type UpdatePreClassTagDto struct {
	StudentUID   int64
	LessonResult []*LessonStateResult
	UpdatedKeys  []defines.PreClassConfigAchillesKey
	Err          error
}

type LessonPlayTypeDto struct {
	Name string `json:"name"`
}
