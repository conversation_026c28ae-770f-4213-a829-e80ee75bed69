package dtopreclass

import "git.zuoyebang.cc/fwyybase/fwyylibs/defines"

type UpdateMultiPreClassTagReq struct {
	AssistantUid    int64                   `json:"assistantUid"`
	PersonUid       int64                   `json:"personUid"`
	CourseId        int64                   `json:"courseId"`
	StudentInfos    []StudentInfo           `json:"studentInfos"`
	PreClassTagInfo defines.PreClassTagInfo `json:"preClassTagInfo"`
	UpdateTime      int64                   `json:"-"`
	DataSource      int                     `json:"-"`
}

type UpdatePreClassTagReq struct {
	AssistantUid    int64                   `json:"assistantUid"`
	PersonUid       int64                   `json:"personUid"`
	CourseId        int64                   `json:"courseId"`
	LeadsId         int64                   `json:"leadsId"`
	StudentUid      int64                   `json:"studentUid"`
	PreClassTagInfo defines.PreClassTagInfo `json:"preClassTagInfo"`
	UpdateTime      int64                   `json:"-"`
	DataSource      int                     `json:"-"`
}

type StudentInfo struct {
	LeadsId    int64 `json:"leadsId"`
	StudentUid int64 `json:"studentUid"`
}

type GetPreClassDetailReq struct {
	CourseId    int64   `json:"courseId"`
	LessonId    int64   `json:"lessonId"`
	StudentUids []int64 `json:"studentUids"`
}

type GetAllLessonStatesReq struct {
	CourseId   int64 `json:"courseId"`
	StudentUid int64 `json:"studentUid"`
}
