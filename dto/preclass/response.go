package dtopreclass

import (
	lessonconsts "git.zuoyebang.cc/fwyybase/fwyylibs/consts/lesson"
	"git.zuoyebang.cc/fwyybase/fwyylibs/defines"
)

const (
	EditFailed  = 0
	EditSuccess = 1
)

type UpdateMultiPreClassTagRsp struct {
	FailedStudentIds    []int64               `json:"failedStudentIds"`
	PreClassResultInfos []*PreClassResultInfo `json:"preClassResultInfos"`
}

type PreClassResultInfo struct {
	StudentUid         int64                `json:"studentUid"`
	LessonStateResults []*LessonStateResult `json:"lessonStateResults"`
}

type LessonStateResult struct {
	LessonId      int64                      `json:"lessonId"`
	PreClassState lessonconsts.PreClassState `json:"preClassState"`
	EditSuccess   int                        `json:"editSuccess"`
}

type UpdatePreClassTagRsp struct {
	LessonStateResults []*LessonStateResult
	UpdatedKeys        []defines.PreClassConfigAchillesKey
}

type GetPreClassDetailRsp struct {
	PreClassDetails []*PreClassDetail `json:"preClassDetails"`
	FailedStudents  []int64           `json:"failedStudents"`
}

type PreClassDetail struct {
	Id               int64  `json:"id"`
	StudentUid       int64  `json:"studentUid"`
	CourseId         int64  `json:"courseId"`
	LessonId         int64  `json:"lessonId"`
	AssistantUid     int64  `json:"assistantUid"`
	PreAttend        int    `json:"preAttend"`
	CreateTime       int64  `json:"createTime"`
	UpdateTime       int64  `json:"updateTime"`
	PreAttendTime    int64  `json:"preAttendTime"`
	LeaveSeason      string `json:"leaveSeason"`
	FirstLeaveReason string `json:"firstLeaveReason"`
	ContentTime      string `json:"contentTime"`
	RemindTime       string `json:"remindTime"`
	AccompanyTag     string `json:"accompanyTag"`
	IsSyncRemind     int    `json:"isSyncRemind"`
	RemindId         int64  `json:"remindId"`
}

type GetAllLessonStatesRsp struct {
	CourseName       string             `json:"courseName"`
	TeacherName      string             `json:"teacherName"`
	LessonStateInfos []*LessonStateInfo `json:"lessonStateInfos"`
}

type LessonStateInfo struct {
	LessonId         int    `json:"lessonId"`
	LessonName       string `json:"lessonName"`
	PlayTypeText     string `json:"playTypeText"`
	LessonTime       string `json:"lessonTime"`
	LessonStartTime  int    `json:"lessonStartTime"`
	LessonStopTime   int    `json:"lessonStopTime"`
	PreAttend        int    `json:"preAttend"`
	LeaveSeason      string `json:"leaveSeason"`
	FirstLeaveReason string `json:"firstLeaveReason"`
	ContentTime      string `json:"contentTime"`
	IsSyncRemind     int    `json:"isSyncRemind"`
	RemindTime       string `json:"remindTime"`
	AccompanyTag     string `json:"accompanyTag"`
	PreAttendTime    int64  `json:"preAttendTime"`
	RemindId         int64  `json:"remindId"`
}
