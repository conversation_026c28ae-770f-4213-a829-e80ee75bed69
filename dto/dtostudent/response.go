package dtostudent

import "assistantdeskgo/utils"

type StudentDetailRes struct {
	StudentUid          int            `json:"studentUid"`
	EncryptedStudentUid string         `json:"encryptedStudentUid"`
	StudentName         string         `json:"studentName"`
	NamePinyin          []utils.Pinyin `json:"namePinyin"`
	Nickname            string         `json:"nickname"`
	Sex                 int            `json:"sex"`
	Grade               string         `json:"grade"`
	School              string         `json:"school"`
	Phone               string         `json:"phone"`
	Area                string         `json:"area"`
	AssistantUid        int64          `json:"assistantUid"`
	AssistantPhone      string         `json:"assistantPhone"`
	BelongObj           BelongerObj    `json:"belongObj"`
	DeviceInfo          string         `json:"deviceInfo"`
}
type BelongerObj struct {
	Belongval   int    `json:"belongVal"` //belonger
	BelongerStr string `json:"belongStr"` //belongerStr
}

type WXBelongerRes struct {
	Result bool `json:"result"`
}
type WXBelongerMapRes struct {
	Label string `json:"label"`
	Value int    `json:"value"`
}
type WXBelongerMapListRes struct {
	List []WXBelongerMapRes `json:"list"`
}

type StudentUidByWxIdRes struct {
	StudentUid int `json:"studentUid"`
}

type GetStudentByPhoneRsp struct {
	StudentInfos []GetStudentInfo `json:"studentInfos"`
}

type GetStudentByUidRsp struct {
	StudentInfos []GetStudentInfo `json:"studentInfos"`
}

type GetStudentInfo struct {
	StudentUid  int64  `json:"studentUid"`
	Phone       string `json:"phone"`
	StudentName string `json:"studentName"`
	Avatar      string `json:"avatar"`
}
