package dtoassistant

type SendGroupMessageReq struct {
	AssistantUid   int64         `json:"assistantUid" form:"assistantUid"`
	StudentUid     int64         `json:"studentUid" form:"studentUid"`
	MessageGroupId int64         `json:"messageGroupId" form:"messageGroupId"`
	RemoteId       string        `json:"RemoteId" form:"RemoteId"`
	MessageList    []messageInfo `json:"messageList" form:"messageList"`
	ChatType       int           `json:"chatType" form:"chatType"`
}

type messageInfo struct {
	MessageId      int64    `json:"messageId" form:"messageId"`
	MessageContent []string `json:"messageContent" form:"messageContent"`
}

type MessageCheckReq struct {
	AssistantUid  int64   `json:"assistantUid" form:"assistantUid"`
	StudentUid    []int64 `json:"studentUid" form:"studentUid"`
	ChatType      int     `json:"chatType" form:"chatType"`
	GroupRemoteId string  `json:"groupRemoteId" form:"groupRemoteId"`
}

type AddAssistantNoticeReq struct {
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	PersonUid    int64  `json:"personUid" form:"personUid"`
	Type         int64  `json:"type" form:"type"`
	DetailType   int64  `json:"detailType" form:"detailType"`
	Content      string `json:"content" form:"content"`
	SourceId     int64  `json:"sourceId" form:"sourceId"`
	ExtData      string `json:"extData" form:"extData"`
}
