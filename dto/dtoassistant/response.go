package dtoassistant

type SendGroupMessageRsp struct {
	TaskId int64 `json:"taskId" form:"taskId"`
}

type MessageCheckRes struct {
	Status      int64  `json:"status" form:"status"`
	Reason      string `json:"reason" form:"reason"`
	UsedLimit   int64  `json:"usedLimit" form:"usedLimit"`
	NoticeLimit int64  `json:"noticeLimit" form:"noticeLimit"`
}

type AddAssistantNoticeResp struct {
	Id int64 `json:"id" form:"id"`
}

type TouchSendWrapperResp struct {
	TaskId              int64   `json:"taskId"`
	NoReportStudentList []int64 `json:"noReportStudentList"`
	Reason              string  `json:"reason"`
	Status              int     `json:"status"`
}
