package dtocsv

type UploadSailorScoreRsp struct {
	Success int64  `json:"success"` // 是否提交成功：0-成功、1-失败
	Prompt  string `json:"prompt"`  // 失败提示
}

type CsvTaskListRet struct {
	Total int64            `json:"total"`
	List  []*ExcelTaskItem `json:"list"`
}

type ExcelTaskItem struct {
	Id            int    `json:"id"`
	OperatorUname string `json:"operatorUname"`
	OperatorTime  string `json:"operatorTime"`
	Status        int    `json:"status"`
	StatusStr     string `json:"statusStr"`
	FileName      string `json:"fileName"`
	RetFile       string `json:"retFile"`
	SuccessCount  int64  `json:"successCount"`
	FailCount     int64  `json:"failCount"`
}

type CanUploadSailorScoreRsp struct {
	CanUpload int64 `json:"canUpload"` // 是否可以导入：0-可以、1-不可以
}
