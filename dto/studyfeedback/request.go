package studyfeedback

type GetPointByTidReq struct {
	TidList []int64 `json:"tidList" form:"tidList"`
}

type AddPointTidsReq struct {
	Point   string  `json:"point" form:"point"`
	TidList []int64 `json:"tidList" form:"tidList"`
}

type GetExplainReq struct {
	PointIdList []int64 `json:"pointIdList" form:"pointIdList"`
}

type AddPointExplainsReq struct {
	Point    string `json:"point" form:"point"`
	Explains []struct {
		ExplainIndex int    `json:"explainIndex"`
		Explain      string `json:"explain"`
	} `json:"explains"`
}

type DeletePointReq struct {
	Point string `json:"point" form:"point"`
}

type RemovePointReq struct {
	PointId int64 `json:"pointId" form:"pointId"`
}
