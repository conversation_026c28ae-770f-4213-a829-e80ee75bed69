package studyfeedback

type PageInfo struct {
	Pn    int   `json:"pn"`
	Rn    int   `json:"rn"`
	Total int64 `json:"total"`
}

type PointInfoWithTid struct {
	Id         int64  `json:"id"`
	Point      string `json:"point"`
	Tid        int64  `json:"tid"`
	Creator    int64  `json:"creator"`
	Updater    int64  `json:"updater"`
	CreateTime int64  `json:"createTime"`
	UpdateTime int64  `json:"updateTime"`
	Deleted    int    `json:"deleted,omitempty"`
}

type GetPointByTidRsp struct {
	PointList []PointInfoWithTid `json:"list"`
}

type AddPointTidsRsp struct {
}

type PointExplain struct {
	Id           int64  `json:"id"`
	PointId      int64  `json:"pointId"`
	ExplainIndex int    `json:"explainIndex"`
	Explain      string `json:"explain"`
	Creator      int64  `json:"creator"`
	Updater      int64  `json:"updater"`
	CreateTime   int64  `json:"createTime"`
	UpdateTime   int64  `json:"updateTime"`
	Deleted      int    `json:"deleted,omitempty"`
}

type GetExplainRsp struct {
	PointExplainList []PointExplain `json:"list"`
}

type AddPointExplainsRsp struct {
}

type RemovePointRsp struct {
}
