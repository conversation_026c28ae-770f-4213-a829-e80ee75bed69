package dtooplog

type OperateLogAddReq struct {
	Refer        string `json:"refer" form:"refer"`
	Module       string `json:"module" form:"module"`
	Service      string `json:"service" form:"service"`
	Content      string `json:"content" form:"content"`
	Before       string `json:"before" form:"before"`
	Remark       string `json:"remark" form:"remark"`
	RelationId   string `json:"relationId" form:"relationId"`
	RelationType string `json:"relationType" form:"relationType"`
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	PersonUid    int64  `json:"personUid" form:"personUid"`
	LogId        string `json:"logId" form:"logId"`
	RequestId    string `json:"requestId" form:"requestId"`
	OperateTime  int64  `json:"operateTime" form:"operateTime"`
}

type OperateLogQueryReq struct {
	RelationId   string `json:"relationId"`
	RelationType string `json:"relationType"`
	PersonUid    int64  `json:"personUid"`
	Page         int    `json:"page"`
	PageSize     int    `json:"pageSize"`
}

type OperateLogQueryRsp struct {
	Refer        string `json:"refer"`
	Module       string `json:"module"`
	Service      string `json:"service"`
	Content      string `json:"content"`
	Before       string `json:"before"`
	Remark       string `json:"remark"`
	RelationId   string `json:"relationId"`
	RelationType string `json:"relationType"`
	AssistantUid int64  `json:"assistantUid"`
	LogId        string `json:"logId"`
	RequestId    string `json:"requestId"`
	PersonUid    int64  `json:"operatorUid"`
	OperateTime  int64  `json:"operateTime"`
}
