package dtocreatepictask

type CreateCaptureTask struct {
	AssistantUid int          `json:"assistantUid"`
	UseType      int          `json:"useType"`
	PicUrlItems  []PicUrlItem `json:"picUrlItems"`
	ExtData      string       `json:"extData"`
	UnionId      string       `json:"unionId"`
}

type PicUrlItem struct {
	PicUrl     []string `json:"picUrl"`
	StudentUid int      `json:"studentUid"`
	GroupId    string   `json:"groupId"`
	TaskId     int      `json:"taskId"`
	Width      int      `json:"width"`
	Height     int      `json:"height"`
}

type ChildTaskItem struct {
	PicUrl       string `json:"picUrl"`
	SendId       string `json:"sendId"`
	ParentTaskId int    `json:"parentTaskId"`
	UseType      int    `json:"useType"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	ExtData      string `json:"extData"`
	Cookies      string `json:"cookieStr"`
}

type RetCreateCaptureTask struct {
	TaskId int `json:"taskId"`
}

type QueryOfflineCallback struct {
	ErrCode      int    `form:"errCode"`
	ErrMsg       string `form:"errMsg"`
	SendId       int    `form:"sendId"`
	ParentTaskId int    `form:"parentTaskId"`
}

type BodyOfflineCallback struct {
	PdfDwnURL  string   `form:"pdfDwnURL"`
	ImgDwnURLs []string `form:"imgDwnURLs"`
	TxtDwnURLs []string `form:"txtDwnURLs"`
	TotalPage  int      `form:"totalPage"`
	TaskId     string   `form:"taskID"`
}
