package dtosop

type StudentCourseRemarkReq struct {
	StudentUid int64 `form:"studentUid" json:"studentUid" query:"studentUid" binding:"required"`
	CourseId   int64 `form:"courseId" json:"courseId" query:"courseId" binding:"required"`
	PageNo     int   `form:"pageNo" json:"pageNo" query:"pageNo" binding:"required"`
	PageSize   int   `form:"pageSize" json:"pageSize" query:"pageSize" binding:"required"`
}
