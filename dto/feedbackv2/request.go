package feedbackv2

type FeedbackDataReq struct {
	CourseID     int64   `json:"courseId"`
	StudentUid   int64   `json:"studentUid"`
	LessonIDList []int64 `json:"lessonIdList"`
}

type LessonInfoReq struct {
	CourseID int64 `form:"courseId" json:"courseId"`
}

type PointExplainListReq struct {
	CpuID   int64 `form:"cpuId" json:"cpuId"`
	PointID int64 `form:"pointId" json:"pointId"`
	Pn      int   `form:"pn" json:"pn"`
	Rn      int   `form:"rn" json:"rn"`
}

type ExplainConfItem struct {
	Tag  string `json:"tag"`
	Text string `json:"text"`
}

type PointExplainUpsertReq struct {
	ID          int64             `json:"id"`
	CpuID       int64             `json:"cpuId"`
	PointID     int64             `json:"pointId"`
	MasterLevel int               `json:"masterLevel"`
	LowerLimit  string            `json:"lowerLimit"`
	LowerIsOpen int               `json:"lowerIsOpen"`
	UpperLimit  string            `json:"upperLimit"`
	UpperIsOpen int               `json:"upperIsOpen"`
	ExplainConf []ExplainConfItem `json:"explainConf"`
}

type PointExplainDeleteReq struct {
	IdList []int64 `json:"idList"`
}
