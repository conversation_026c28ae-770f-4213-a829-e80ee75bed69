package feedbackv2

type StudyViewStruct struct {
	TotalLessonNum             int64  `json:"totalLessonNum"`
	InclassAttendLessonNum     int64  `json:"inclassAttendLessonNum"`
	InclassFinishLessonNum     int64  `json:"inclassFinishLessonNum"`
	WatchAttendLessonNum       int64  `json:"watchAttendLessonNum"`
	WatchFinishLessonNum       int64  `json:"watchFinishLessonNum"`
	InteractionTotalNum        int64  `json:"interactionTotalNum"`
	InteractionParticipateCnt  int64  `json:"interactionParticipateCnt"`
	InteractionParticipateRate string `json:"interactionParticipateRate"`
	InteractionRightCnt        int64  `json:"interactionRightCnt"`
	InteractionRightRate       string `json:"interactionRightRate"`
	HomeworkSubmitNum          int64  `json:"homeworkSubmitNum"`
	HomeworkAssignNum          int64  `json:"homeworkAssignNum"`
	HomeworkFirstRightCnt      int64  `json:"homeworkFirstRightCnt"`
	HomeworkFirstCorrectCnt    int64  `json:"homeworkFirstCorrectCnt"`
	HomeworkFirstCorrectRate   string `json:"homeworkFirstCorrectRate"`
}

type PointDetailItem struct {
	PointID                    int64             `json:"pointId"`
	PointName                  string            `json:"pointName"`
	LessonList                 []LessonListItem  `json:"lessonList"`
	MasterLevel                string            `json:"masterLevel"`
	MasterStatus               string            `json:"masterStatus"`
	InteractionParticipateRate string            `json:"interactionParticipateRate"`
	InteractionRightRate       string            `json:"interactionRightRate"`
	InteractionRightFrac       float64           `json:"interactionRightFrac"`
	ExplainList                []ExplainListItem `json:"explainList"`
}

type ExplainListItem struct {
	Tag  string `json:"tag"`
	Text string `json:"text"`
}

type LessonListItem struct {
	LessonID     int64  `json:"lessonId"`
	LessonName   string `json:"lessonName"`
	LessonStatus int64  `json:"lessonStatus"`
}

type PointDetailStruct struct {
	List  []PointDetailItem `json:"list"`
	Total int               `json:"total"`
}

type FeedbackDataRsp struct {
	StudyView   StudyViewStruct   `json:"studyView"`
	PointDetail PointDetailStruct `json:"pointDetail"`
}

type LessonInfoItem struct {
	CourseID      int64  `json:"courseId"`
	CourseName    string `json:"courseName"`
	CpuID         int64  `json:"cpuId"`
	MainGradeID   int64  `json:"mainGradeId"`
	LessonID      int64  `json:"lessonId"`
	LessonName    string `json:"lessonName"`
	LessonType    int64  `json:"lessonType"`
	Mode          int64  `json:"mode"`
	CanSelect     bool   `json:"canSelect"`
	DefaultSelect bool   `json:"defaultSelect"`
	StartTime     int64  `json:"startTime"`
	StopTime      int64  `json:"stopTime"`
}

type LessonInfoRsp struct {
	List  []LessonInfoItem `json:"list"`
	Total int              `json:"total"`
}

type PointExplainListRsp struct {
	Total int64                  `json:"total"`
	List  []PointExplainListItem `json:"list"`
}

type PointExplainListItem struct {
	Id           int64  `json:"id"`
	CpuId        int64  `json:"cpuId"`
	PointId      int64  `json:"pointId"`
	MasterLevel  int    `json:"masterLevel"`
	LowerLimit   string `json:"lowerLimit"`
	LowerIsOpen  int    `json:"lowerIsOpen"`
	UpperLimit   string `json:"upperLimit"`
	UppperIsOpen int    `json:"uppperIsOpen"`
	ExplainConf  string `json:"explainConf"`
	Creator      int64  `json:"creator"`
	Updater      int64  `json:"updater"`
	CreateTime   int64  `json:"createTime"`
	UpdateTime   int64  `json:"updateTime"`
}

type PointExplainUpsertRsp struct {
}
type PointExplainDeleteRsp struct {
}
