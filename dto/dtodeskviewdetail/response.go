package dtodeskviewdetail

type GetTeacherQRCodeResp struct {
	WxQRCode string `json:"wxQRCode" form:"wxQRCode"`
}

type AddInterViewItem struct {
	CourseId int64 `json:"courseId"`
	RecordId int64 `json:"recordId"`
}

type GetInterViewItem struct {
	Id         int64  `json:"id"`
	CourseId   int64  `json:"courseId"`
	StudentUid int64  `json:"studentUid"`
	Type       int64  `json:"type"`
	Uid        int64  `json:"uid"`
	Content    string `json:"content"`
	CreateTime int64  `json:"createTime"`
	UpdateTime int64  `json:"updateTime"`
}
