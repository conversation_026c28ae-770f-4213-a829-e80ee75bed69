package dtolaxintagfixtool

type GetLaxinTagFileTaskResponse struct {
	List  []*LaxinTagTaskItem `json:"list"`
	Total int64               `json:"total"` // 总数
}

type LaxinTagTaskItem struct {
	ID             int64  `json:"id"`             // 任务 id
	OriginFileName string `json:"originFileName"` // 原始文件名称
	Comment        string `json:"comment"`        // 备忘
	Status         int    `json:"status"`         // 上传状态
	OperatorType   int    `json:"operatorType"`   // 操作类型：创建/删除
	OperatorName   string `json:"operatorName"`   // 操作人姓名 userInfo 获取
	UpdateTime     int64  `json:"updateTime"`     // 上传完成时间
	RetFileURL     string `json:"retFileUrl"`     // 结果文件地址：下载数据
}

type GetLaxinTagCaseResponse struct {
	List  []*LaxinTagCaseItem `json:"list"`
	Total int64               `json:"total"` // 总数
}

type LaxinTagCaseItem struct {
	Name           string `json:"name"`           // 姓名
	CourseID       int    `json:"courseId"`       // 课程 ID
	AssistantPhone string `json:"assistantPhone"` // 资产手机号
	PersonEmail    string `json:"personEmail"`    // 真人 email
	FirstLineTeam  string `json:"firstLineTeam"`  // 一级团队
	SecondLineTeam string `json:"secondLineTeam"` // 二级团队
	Operator       string `json:"operator"`       // 操作人
	IsDeleted      bool   `json:"isDeleted"`      // 有效/已删除
	UpdateTime     int64  `json:"updateTime"`     // 上传完成时间
}

type GetCaseChangeLogResponse struct {
	List []*GetCaseChangeLogItem `json:"list"`
}

type GetCaseChangeLogItem struct {
	RecordID          int64  `json:"recordId"`     // 数据表 id
	OperatorType      int    `json:"operatorType"` // 操作类型：创建/删除
	CourseID          int    `json:"courseId"`
	AssistantPhone    string `json:"assistantPhone"`    // 资产手机号
	AssistantUID      int64  `json:"assistantUid"`      // 资产 id
	PersonEmailPrefix string `json:"personEmailPrefix"` // 真人 email
	Operator          string `json:"operator"`          // 操作人
	CreateTime        string `json:"createTime"`        // 创建时间
}
