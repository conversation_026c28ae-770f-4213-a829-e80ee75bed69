package dtolaxintagfixtool

type UploadLaxinTagFileRequest struct {
	Type           int    `json:"type" form:"type"`                     // 业务类型(1,创建，2，删除)
	Comment        string `json:"comment" form:"comment"`               // 备忘
	OriginFileName string `json:"originFileName" form:"originFileName"` // 原始文件名
	RemoteFileName string `json:"remoteFileName" form:"remoteFileName"` // bos 文件名
}

type GetLaxinTagFileTaskRequest struct {
	OperatorName string `json:"operatorName" form:"operatorName"`
	Status       int    `json:"status" form:"status"`     // 0,1,2,3 = 待启动，执行中，执行成功，部分失败
	FileName     string `json:"fileName" form:"fileName"` // 搜索
	Page         int    `json:"page" form:"page"`         // 页数
	Size         int    `json:"size" form:"size"`         // 每页的数字
}

type GetLaxinTagCaseRequest struct {
	CourseID          string `json:"courseId" form:"courseId"`
	AssistantPhone    string `json:"assistantPhone" form:"assistantPhone"`       // 资产手机号
	PersonEmailPrefix string `json:"personEmailPrefix" form:"personEmailPrefix"` // 真人 email
	Page              int    `json:"page" form:"page"`                           // 页数
	Size              int    `json:"size" form:"size"`                           // 每页的数字
}

type GetCaseChangeLogRequest struct {
	CourseID          string `json:"courseId" form:"courseId"`
	AssistantPhone    string `json:"phone" form:"phone"` // 资产手机号
	PersonEmailPrefix string `json:"email" form:"email"` // 真人 email
}

type TestConsume struct {
	TaskId int `json:"taskId" form:"taskId"` // 页数
}
