package dtopublicsea

type ClueListResp struct {
	AssistantPhone string      `json:"assistant<PERSON>hone"`
	AssistantUid   int64       `json:"assistantUid"`
	Fields         []FieldInfo `json:"fields"`
	List           []ClueItem  `json:"list"`
	Total          int64       `json:"total"`
	SelectedCnt    int64       `json:"selectedCnt"`
}

type FieldInfo struct {
	FilterMap []Option `json:"filterMap"`
	FeConfig  FeConfig `json:"feConfig"`
}

type Option struct {
	Value interface{} `json:"value"`
	Text  string      `json:"text"`
}

type FeConfig struct {
	Label       string `json:"label"`
	Prop        string `json:"prop"`
	Slot        bool   `json:"slot"`
	CName       string `json:"cname"`
	Width       int64  `json:"width"`
	MinWidth    int64  `json:"minWidth"`
	Numerator   string `json:"numerator"`   // 分子
	Denominator string `json:"denominator"` // 分母
}

type ClueItem struct {
	ClueId               string `json:"clueId"`
	Batch                string `json:"batch"`
	StudentUid           int64  `json:"studentUid"`
	CourseId             int64  `json:"courseId"`
	StudentPhone         string `json:"studentPhone"`
	StudentName          string `json:"studentName"`
	SourceIsCall         bool   `json:"sourceIsCall"`
	SourceGradeId        int64  `json:"sourceGradeId"`
	SourceGradeName      string `json:"sourceGradeName"`
	SourceSemesterName   string `json:"sourceSemesterName"`
	ProvinceName         string `json:"provinceName"`
	CityName             string `json:"cityName"`
	CityLevelName        string `json:"cityLevelName"`
	UserTypeName         string `json:"userTypeName"`
	LeftAccessCnt        int64  `json:"leftAccessCnt"`
	LeftAccessSeconds    int64  `json:"leftAccessSeconds"`
	AccessStatus         int64  `json:"accessStatus"`
	LatestAccessTime     string `json:"latestAccessTime"`
	LatestOrderTime      string `json:"latestOrderTime"`
	NoCourseLastFrom     string `json:"noCourseLastFrom"`
	NoCourseLastFromName string `json:"noCourseLastFromName"`
	ExpiredTime          int64  `json:"expiredTime"`
	ManualIntention      int64  `json:"manualIntention"`
	ManualCallStatus     int64  `json:"manualCallStatus"`
	ManualRemark         string `json:"manualRemark"`
	CallOwnerNum         int64  `json:"callOwnerNum"`
	CallOwnerAccessNum   int64  `json:"callOwnerAccessNum"`
	CallAllNum           int64  `json:"callAllNum"`
	CallAllAccessNum     int64  `json:"callAllAccessNum"`
	EduProbeStatus       int64  `json:"eduProbeStatus"`    // 0=未生成，1=已生成
	EduProbeUrl          string `json:"eduProbeReportUrl"` // 测评或报告
	EduProbeWriteUrl     string `json:"eduProbeTestUrl"`   // 报告地址
	MaskStudentUid       string `json:"maskStudentUid"`    // mask后的学生uid
}

type GetFilterMapResp struct {
	FilterList []FilterItem `json:"filterList"`
}

type FilterItem struct {
	ClassId   int64         `json:"classId"`
	ClassName string        `json:"className"`
	Children  []FieldDetail `json:"children"`
}

type FieldDetail struct {
	Key         string   `json:"key"`
	Label       string   `json:"label"`
	Type        string   `json:"type"`
	PlaceHolder string   `json:"placeHolder"`
	IsMulti     bool     `json:"isMulti"`
	Options     []Option `json:"options"`
}

type RetrieveResp struct {
	RetrieveStatus bool `json:"retrieveStatus"`
}

type ClueStatusResp struct {
	LeftAccessSeconds int64 `json:"leftAccessSeconds"`
	LeftAccessCnt     int64 `json:"leftAccessCnt"`
	AccessStatus      int64 `json:"accessStatus"`
}
