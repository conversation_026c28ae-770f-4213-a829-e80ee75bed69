package dtoserviceend

type GetFilterMapResp struct {
	FilterList []FilterItem `json:"filterList"`
}

type FilterItem struct {
	ClassId   int64         `json:"classId"`
	ClassName string        `json:"className"`
	Children  []FieldDetail `json:"children"`
}

type FieldDetail struct {
	Key         string   `json:"key"`
	Label       string   `json:"label"`
	Type        string   `json:"type"`
	PlaceHolder string   `json:"placeHolder"`
	IsMulti     bool     `json:"isMulti"`
	Options     []Option `json:"options"`
}

type Option struct {
	Value interface{} `json:"value"`
	Text  string      `json:"text"`
}

type RecordListResp struct {
	AssistantPhone string       `json:"assistantPhone"`
	AssistantUid   int64        `json:"assistantUid"`
	Fields         []FieldInfo  `json:"fields"`
	List           []RecordItem `json:"list"`
	Total          int64        `json:"total"`
	SelectedCnt    int64        `json:"selectedCnt"`
}

type FieldInfo struct {
	FilterMap []Option `json:"filterMap"`
	FeConfig  FeConfig `json:"feConfig"`
}

type FeConfig struct {
	Label       string `json:"label"`
	Prop        string `json:"prop"`
	Slot        bool   `json:"slot"`
	CName       string `json:"cname"`
	Width       int64  `json:"width"`
	MinWidth    int64  `json:"minWidth"`
	Numerator   string `json:"numerator"`   // 分子
	Denominator string `json:"denominator"` // 分母
}

type RecordItem struct {
	StudentUid           int64    `json:"studentUid"`
	StudentPhone         string   `json:"studentPhone"`
	IsTrans              int64    `json:"isTrans"`
	AddWxStatus          int64    `json:"noCourseAddWxStatus"`
	StudentName          string   `json:"studentName"`
	SourceGradeId        int64    `json:"sourceGradeId"`
	SourceGradeName      string   `json:"sourceGradeName"`
	UserTypeName         string   `json:"userTypeName"`
	RetrieveType         string   `json:"retrieveType"`
	ClueId               string   `json:"clueId"`
	Batch                string   `json:"batch"`
	SourceSemesterName   string   `json:"sourceSemesterName"`
	NoCourseLastFrom     string   `json:"noCourseLastFrom"`
	NoCourseLastFromName string   `json:"noCourseLastFromName"`
	CallOwnerNum         int64    `json:"callOwnerNum"`
	CallOwnerAccessNum   int64    `json:"callOwnerAccessNum"`
	ProvinceName         string   `json:"provinceName"`
	CityName             string   `json:"cityName"`
	CityLevelName        string   `json:"cityLevelName"`
	AllocTime            string   `json:"allocTime"`
	TransSubject         []string `json:"transSubject"`
	CourseId             int64    `json:"courseId"`
	LeadsId              int64    `json:"leadsId"`
}
