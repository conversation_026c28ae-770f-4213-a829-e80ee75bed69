package dtoclue

type GetRemarkListResp struct {
	Remark    string `json:"remark"`
	Time      int64  `json:"time"`
	Intention string `json:"intention"`
	IsCall    int64  `json:"isCall"`
	LeadsId   int64  `json:"leadsId"`
}

type PublicSeaOpLogRes struct {
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	OpLogId    int64 `json:"opLogId"`
}
type ManualIntentionRes struct {
	PublicSeaOpLogReq
	ManualIntention int64 `json:"manualIntention"`
}

type ApiOpLogResp struct {
	OpLogId          int64  `json:"opLogId"`
	ManualIntention  int64  `json:"manualIntention"`  //手动意向
	ManualCallStatus int64  `json:"manualCallStatus"` //手动外呼
	ManualRemark     string `json:"manualRemark"`     //手动备注
	CallLastTime     string `json:"callLastTime"`     //当前老师下线索最新接通时间
}

type ResetTimeOpLogByClueDeviceIdResp struct {
	Result bool `json:"result"`
}
