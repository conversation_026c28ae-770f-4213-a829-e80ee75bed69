package dtoclue

type PublicSeaOpLogReq struct {
	ClueId string `form:"clueId" json:"clueId" query:"clueId" binding:"required"`
}
type ManualIntentionReq struct {
	PublicSeaOpLogReq
	ManualIntention int64 `json:"manualIntention" form:"manualIntention" query:"manualIntention" `
}

type ManualCallStatusReq struct {
	PublicSeaOpLogReq
	ManualCallStatus int64 `json:"manualCallStatus" form:"manualCallStatus" query:"manualCallStatus" `
}

type ManualRemarkReq struct {
	PublicSeaOpLogReq
	ManualRemark string `json:"manualRemark" form:"manualRemark" query:"manualRemark" `
}

type ApiOpLogReq struct {
	FromUid int64    `json:"fromUid" form:"fromUid" binding:"required"`
	ClueIds []string `json:"clueIds" form:"clueIds" binding:"required"`
}

type ResetTimeOpLogByClueDeviceIdReq struct {
	DeviceUid int64  `json:"deviceUid" form:"deviceUid" binding:"required"`
	ClueId    string `json:"clueId" form:"clueId" binding:"required"`
}
