package dtotouch

import "assistantdeskgo/api/touchmisgo"

type SendAddWxMsgRes struct {
	touchmisgo.CardSendRes
}

type StudentSmsFilterRes struct {
	touchmisgo.WeChatCheckRes
}

type CallCheckResp struct {
	CanCall          bool   `json:"canCall"`
	CanNotCallReason string `json:"canNotCallReason"`
}

type CallResp struct {
	CallId int64 `json:"callId"`
}

type GetSmsSendConfigResp struct {
	SendLimit int64        `json:"sendLimit"`
	SendUsed  int64        `json:"sendUsed"`
	TplList   []SmsTplInfo `json:"tplList"`
}

type SmsTplInfo struct {
	TplId      int64  `json:"tplId"`
	TplContent string `json:"tplContent"`
}

type SendSmsSingleResp struct {
	Result bool   `json:"result"`
	Type   int64  `json:"type"`
	Msg    string `json:"msg"`
}

type CallCountInfo struct {
	SuccessCallCount int64
	TotalcallCount   int64
}
