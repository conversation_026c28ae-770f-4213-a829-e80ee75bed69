package dtotouch

import "assistantdeskgo/api/touchmisgo"

type StudentSmsFilterReq struct {
	StudentUids []int64 `json:"studentUids" form:"studentUids" query:"studentUids" binding:"required"`
}

type SendAddWXMsgReq struct {
	StudentList []touchmisgo.CanSendInfoReq `json:"studentList" form:"studentList" query:"studentList" `
}

type CallCheckReq struct {
	FromPhone string `json:"fromPhone" form:"fromPhone" binding:"required"`
	ToPhone   string `json:"toPhone" form:"toPhone" binding:"required"`
	ClueId    string `json:"clueId" form:"clueId" binding:"required"`
	TouchType string `json:"touchType" form:"touchType" binding:"required"`
}

type CallReq struct {
	FromUid   int64  `json:"fromUid" form:"fromUid" binding:"required"`
	FromPhone string `json:"fromPhone" form:"fromPhone" binding:"required"`
	ToPhone   string `json:"toPhone" form:"toPhone" binding:"required"`
	ClueId    string `json:"clueId" form:"clueId" binding:"required"`
	TouchType string `json:"touchType" form:"touchType" binding:"required"`
	CallMode  int64  `json:"callMode" form:"callMode"`
}

type GetSmsSendConfigReq struct {
	ClueId string `json:"clueId" form:"clueId" binding:"required"`
}

type SendSmsSingleReq struct {
	ClueId  string `json:"clueId" form:"clueId" binding:"required"`
	TplId   int64  `json:"tplId" form:"tplId" binding:"required"`
	ToPhone string `json:"toPhone" form:"toPhone" binding:"required"`
}

type ApiTouchCallReq struct {
	FromUid int64    `json:"fromUid" form:"fromUid"  query:"fromUid"  binding:"required"`
	ClueIds []string `json:"clueIds" form:"clueIds"  query:"clueIds"   binding:"required"`
}

type ApiTouchCallAllReq struct {
	ClueIds []string `json:"clueIds" form:"clueIds" binding:"required"`
}
