package dtosource

type GetStudentCallRecordInfoResp struct {
	CallList       []CallInfo       `json:"callList"`
	CallRecordList []CallRecordInfo `json:"callRecordList"`
	CallCountInfo  CallCountInfo    `json:"callCountInfo"`
}

type CallInfo struct {
	Name      string `json:"name"`
	Phone     string `json:"phone"`
	Md5Phone  string `json:"md5Phone"`
	City      string `json:"city"`
	CityLevel string `json:"cityLevel"`
}

type CallRecordInfo struct {
	Name           string `json:"name"`
	StartTime      string `json:"startTime"`
	Duration       int64  `json:"duration"`
	CallResult     string `json:"callResult"`
	CallId         int64  `json:"callId"`
	CallMode       int64  `json:"callMode"`
	FromPhone      string `json:"fromPhone"`
	SourceTypeName string `json:"sourceTypeName"`
	SourceType     int64  `json:"sourceType"`
}

type CallCountInfo struct {
	TotalNum    int64   `json:"totalNum"`
	SuccessNum  int64   `json:"successNum"`
	SuccessRate float64 `json:"successRate"`
}

type GetLessonListResp struct {
	List  []LessonInfo `json:"list"`
	Count CountInfo    `json:"count"`
}

type LessonInfo struct {
	StartTime        int64  `json:"startTime"`
	StopTime         int64  `json:"stopTime"`
	LastPlaybackTime int64  `json:"lastPlaybackTime"`
	PlaybackTime     string `json:"playbackTime"`
	Attend           int64  `json:"attend"`
	Finished         int64  `json:"finished"`
	Playback         int64  `json:"playback"`
	PlaybackFinished int64  `json:"playbackFinished"`
	Preview          int64  `json:"preview"`
	PlayType         string `json:"playType"`
}

type CountInfo struct {
	Total         int64 `json:"total"`
	Attend        int64 `json:"attend"`
	TotalFinished int64 `json:"totalFinished"`
	Finished      int64 `json:"finished"`
	Playback      int64 `json:"playback"`
}

type GetRemarkListResp struct {
	Remark    string `json:"remark"`
	Time      int64  `json:"time"`
	Intention string `json:"intention"`
	IsCall    int64  `json:"isCall"`
	LeadsId   int64  `json:"leadsId"`
}
