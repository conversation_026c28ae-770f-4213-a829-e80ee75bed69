package dtosource

type GetStudentCallRecordInfoReq struct {
	ClueId string `json:"clueId" form:"clueId" binding:"required"`
}

type GetLessonListReq struct {
	ClueId string `json:"clueId" form:"clueId" binding:"required"`
	Type   string `json:"type" form:"type"`
}

type GetRemarkListReq struct {
	ClueId string `json:"clueId" form:"clueId" binding:"required"`
}

type GetLearnReportByClueIdsReq struct {
	ClueIdGradeIdMap map[string]int64 `json:"clueIdGradeIdMap"`
}
