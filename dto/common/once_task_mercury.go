package dtocommon

/*
	{
	    "sopTaskConfig": {
	        "sopTaskUploadLimit": 100,
	        "aiConfigUploadLimit": 100
	    },
	    "sceneList": [
	        {
	            "sceneType": 93,
	            "filterRules": [
	                "beforeLessonStartTime"
	            ]
	        }
	    ],
	    "ruleList": [
	        {
	            "rule4": {
	                "detail": "完课状态=未完课 且（预到课标签=能到 或 伴学且伴学类型=周日伴学、或请假 或 未标记预到课标签）",
	                "ruleInfo": [
	                    {
	                        "key": "lessonUnFinish",
	                        "value": ""
	                    },
	                    {
	                        "key": "preTag",
	                        "value": {
	                            "preAttend": [
	                                5,
	                                1,
	                                0
	                            ],
	                            "banXueTag": [
	                                2
	                            ]
	                        }
	                    }
	                ]
	            }
	        }
	    ]
	}
*/
type SopTaskMercuryConfig struct {
	SopTaskUploadLimit  int `json:"sopTaskUploadLimit"`
	AiConfigUploadLimit int `json:"aiConfigUploadLimit"`
}

type SceneFilterMercuryConfig struct {
	SceneType   int      `json:"sceneType"`
	FilterRules []string `json:"filterRules"`
}

type FilterRuleMercuryConfig struct {
	Detail   string                         `json:"detail"`
	RuleInfo []*FilterRuleItemMercuryConfig `json:"ruleInfo"`
}

type FilterRuleItemMercuryConfig struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

type OnceTaskMercuryConfig struct {
	SopTaskConfig *SopTaskMercuryConfig               `json:"sopTaskConfig"`
	SceneList     []*SceneFilterMercuryConfig         `json:"sceneList"`
	RuleList      map[string]*FilterRuleMercuryConfig `json:"ruleList"`
}
