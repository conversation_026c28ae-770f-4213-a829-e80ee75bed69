package dtocoursetimetable

type GetCourseTimeTableRsp struct {
	CourseList []CourseInfo `json:"courseList"`
	CourseDays []string     `json:"courseDays"`
	CurrentDay string       `json:"currentDay"`
}

type CourseInfo struct {
	CourseName  string `json:"courseName"`
	CourseId    string `json:"courseId"`
	LessonName  string `json:"lessonName"`
	StartTime   int64  `json:"startTime"`
	EndTime     int64  `json:"endTime"`
	SubjectName string `json:"subjectName"`
	SubjectId   int64  `json:"subjectId"`
}

type GetTeacherInfoRsp struct {
	CourseName  string `json:"courseName"`
	TeacherName string `json:"teacherName"`
	AvatarUrl   string `json:"avatarUrl"`
}
