package dtosailor

type ListReq struct {
	UserStatus  string            `json:"userStatus" form:"userStatus"`   // 人员状态
	PersonType  string            `json:"personType" form:"personType"`   // 人员类型
	HrPost      string            `json:"hrPost" form:"hrPost"`           // 人力岗位
	ProductLine string            `json:"productLine" form:"productLine"` // 人力归属
	UserName    string            `json:"userName" form:"userName"`       // 姓名
	UserPhone   string            `json:"userPhone" form:"userPhone"`     // 真人手机号
	EmailPrefix string            `json:"emailPrefix" form:"emailPrefix"` // 邮箱前缀
	StaffUid    string            `json:"staffUid" form:"staffUid"`       // 真人uid
	Grade       string            `json:"grade" form:"grade"`             // 学部
	GradeLevel  string            `json:"gradeLevel" form:"gradeLevel"`   // 年级
	Subject     string            `json:"subject" form:"subject"`         // 学科
	Pn          int               `json:"pn" form:"pn"`
	Rn          int               `json:"rn" form:"rn"`
	Cookies     map[string]string `json:"cookies"`
}

type DetailReq struct {
	StaffUid int64 `json:"staffUid" form:"staffUid"`
	InfoType int64 `json:"infoType" form:"infoType"`
}

type TaskInfo struct {
	TaskId int64 `json:"taskId" form:"taskId"`
}

type UpdateExtraInfo struct {
	TaskId    int64  `json:"taskId" form:"taskId"`
	ExtraInfo string `json:"extraInfo" form:"extraInfo"`
}
