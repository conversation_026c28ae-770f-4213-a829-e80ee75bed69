package dtosailor

type UploadSailorScoreRsp struct {
	Success int64  `json:"success"` // 是否提交成功：0-成功、1-失败
	Prompt  string `json:"prompt"`  // 失败提示
}

type ListInitRsp struct {
	FieldKey string         `json:"fieldKey"`
	FieldMap []ListInitItem `json:"fieldMap"`
}

type ListInitItem struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type ListRsp struct {
	List []SailorScoreItem `json:"list"`
	PageInfo
}

type SailorScoreItem struct {
	UserName      string  `json:"userName"`
	UserPhone     string  `json:"userPhone"`
	StaffUID      int64   `json:"staffUid"`
	GradeStr      string  `json:"gradeStr"`
	SubjectStr    string  `json:"subjectStr"`
	GradeLevelStr string  `json:"gradeLevelStr"`
	ProductLine   string  `json:"productLine"`
	HrPost        string  `json:"hrPost"`
	SailorScore   float64 `json:"sailorScore"`
	SailorLevel   string  `json:"sailorLevel"`
	ScoreDetail
}

type ScoreDetailRsp struct {
	Grade     int64         `json:"grade"`
	ScoreList []ScoreDetail `json:"scoreList"`
}

type ScoreDetail struct {
	TeachingQualification float64 `json:"teachingQualification"`
	WorkingAge            float64 `json:"workingAge"`
	Qualification         float64 `json:"qualification"`
	UserPraise            float64 `json:"userPraise"`
	Praise                float64 `json:"praise"`
	Scale                 float64 `json:"scale"`
	Other                 float64 `json:"other"`
	CostLevel             string  `json:"costLevel"`
	Satisfaction          float64 `json:"satisfaction"`
	Punishment            float64 `json:"punishment"`
	Performance           float64 `json:"performance"`
	Exam                  float64 `json:"exam"`
	DataType              string  `json:"dataType"`
	OperateTime           string  `json:"operateTime"`
	OperateName           string  `json:"operateName"`
}

type PageInfo struct {
	Pn    int   `json:"pn"`
	Rn    int   `json:"rn"`
	Total int64 `json:"total"`
}

type InfoRsp struct {
	IsShow    int64     `json:"isShow"`
	StaffUid  int64     `json:"staffUid"`
	ScoreInfo ScoreInfo `json:"scoreInfo"`
}

type ScoreInfo struct {
	Total         float64 `json:"total"`
	TotalLevel    string  `json:"totalLevel"`
	Qualification float64 `json:"qualification"`
	Contribute    float64 `json:"contribute"`
	Level         float64 `json:"level"`
	Other         float64 `json:"other"`
	CostLevel     string  `json:"costLevel"`
	Quality       float64 `json:"quality"`
	Performance   float64 `json:"performance"`
}
