package dtoai

type StudentCallRecordResponse struct {
	Page           int          `json:"page"`
	PageSize       int          `json:"pageSize"`
	Total          int64        `json:"total"`
	CallDetailList []CallDetail `json:"callDetailList"`
}

type CallDetail struct {
	CallId      string         `json:"callId"`
	StartTime   int64          `json:"startTime"`
	StopTime    int64          `json:"stopTime"`
	Status      int64          `json:"status"`
	Type        int64          `json:"type"`
	RecordFile  string         `json:"recordFile"`
	SourceType  string         `json:"sourceType"`
	CourseName  string         `json:"courseName"`
	CourseId    int64          `json:"courseId"`
	CallMode    int64          `json:"callMode"`
	CallModeStr string         `json:"callModeStr"`
	Duration    int64          `json:"duration"`
	Content     []SentenceList `json:"content"`
	Abstract    string         `json:"abstract"`
	Tags        []Tags         `json:"tags"`
	Accurate    int64          `json:"accurate"`
	Inaccurate  int64          `json:"inaccurate"`
	CreateTime  int64          `json:"createTime"`
	CurrentTime int64          `json:"currentTime"`
	Version     int64          `json:"version"`
}

type Tags struct {
	Label       string `json:"label"`
	Value       string `json:"value"`
	Credibility string `json:"credibility"`
	Remark      string `json:"remark"`
}

type SentenceList struct {
	SentenceId int     `json:"sentence_id"`
	StartTime  float64 `json:"start_time"`
	EndTime    float64 `json:"end_time"`
	Role       int     `json:"role"`
	Content    string  `json:"content"`
}

type GetFeedBackResponse struct {
	FeedBackData []FeedBackData `json:"feedBackData" form:"feedBackData"`
}

type FeedBackData struct {
	CallId          string `json:"callId" form:"callId"`
	AccurateCount   int    `json:"accurateCount" form:"accurateCount"`
	InaccurateCount int    `json:"inaccurateCount" form:"inaccurateCount"`
}
