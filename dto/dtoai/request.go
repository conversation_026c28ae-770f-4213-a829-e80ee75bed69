package dtoai

type StudentCallRecordRequest struct {
	CourseId   int64 `json:"courseId" form:"courseId"`
	StudentUid int64 `json:"studentUid" form:"studentUid"`
	PageTab    int64 `json:"pageTab" form:"pageTab"`
	Page       int   `json:"page" form:"page"`
	PageSize   int   `json:"pageSize" form:"pageSize"`
}

type AddFeedBackRequest struct {
	CallId   string `json:"callId" form:"callId"`
	Accurate int    `json:"accurate" form:"accurate"`
}

type GetFeedBackRequest struct {
	CallId string `json:"callId" form:"callId"`
}
