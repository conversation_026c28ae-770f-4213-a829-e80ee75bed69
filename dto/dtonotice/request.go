package dtonotice

type SystemNoticeSaveParam struct {
	Id           int64  `form:"id"`
	Title        string `form:"title"`        // 通知标题
	PublisherUID int64  `form:"publisherUid"` // 发布人uid
	ClassID      int8   `form:"classId"`      // 所属栏目
	Status       int8   `form:"status"`       // 状态：1-编辑中；2-已发布；3-已结束
	ReceiverScopeParam
	ProfilePhoto          string `form:"profilePhoto"`          // 通知头像
	Content               string `form:"content"`               // 通知内容
	VideoAddr             string `form:"videoAddr"`             // 视频cos地址
	Abstract              string `form:"abstract"`              // 通知摘要
	CommentFlag           bool   `form:"commentFlag"`           // 评论设置：0-不允许评论；1-允许评论
	CreateTime            uint32 `form:"createTime"`            // 创建时间
	PublishTime           uint32 `form:"publishTime"`           // 发布时间
	UpdateTime            uint32 `form:"updateTime"`            // 更新时间
	MockReadNum           int64  `form:"mockReadNum"`           // 模拟阅读数
	MockContentLikeNum    int64  `form:"mockContentLikeNum"`    // 模拟图文点赞数
	MockVideoLikeNum      int64  `form:"mockVideoLikeNum"`      // 模拟视频点赞数
	MockContentDisLikeNum int64  `form:"mockContentDisLikeNum"` // 模拟图文点踩数
	MockVideoDisLikeNum   int64  `form:"mockVideoDisLikeNum"`   // 模拟视频点踩数
	MockCommentNum        int64  `form:"mockCommentNum"`        // 模拟评论数
}

type SystemNoticeListParam struct {
	StartTime int  `form:"startTime"`
	EndTime   int  `form:"endTime"`
	ClassID   int8 `form:"classId"`
	Status    int8 `form:"status"`
	Page      int  `form:"page"`
	Size      int  `form:"size"`
}

type SystemNoticeGetParam struct {
	Id int64 `form:"id"`
}

type SystemNoticeDeleteParam struct {
	Id     int64 `form:"id"`
	Status int8  `form:"status"`
}

type ReceiverScopeParam struct {
	Scope      int8   `form:"scope"`      // 发送范围：1-筛选员工；2-全体员工
	ScopeGroup string `form:"scopeGroup"` // 目标组织
	ScopeUID   string `form:"scopeUid"`   // 目标uid
}

type ReceiverFilterParam struct {
	NoticeId     int64 `form:"noticeId"`     // 通知id
	Type         int8  `form:"type"`         // 1-图文；2-视频
	FeedbackType int8  `form:"feedbackType"` // 1-点赞；2-点踩
	ReceiverScopeParam
	Uid         string  `form:"uid"`         // 用户uid
	StaffName   string  `form:"staffName"`   // 用户名
	ProductLine []int64 `form:"productLine"` // 业务线
	GroupId     string  `form:"groupId"`     // 组织id
	Region      []int64 `form:"region"`      // 城市
	Grade       []int64 `form:"grade"`       // 年级
	Subject     []int64 `form:"subject"`     // 学科
	GradeLevel  []int64 `form:"gradeLevel"`  // 学部
	StartTime   int64   `form:"startTime"`   // 发送时间
	EndTime     int64   `form:"endTime"`     // 发送时间
	Page        int64   `form:"page"`        // 页码
	Size        int64   `form:"size"`        // 每页大小
}

type UserNoticeDeatilParam struct {
	NoticeId int64 `form:"noticeId"`
}

type UserNoticeFeedbackParam struct {
	Id              int64  `form:"id"`
	NoticeId        int64  `form:"noticeId"`
	Type            int8   `form:"type"`            // 点踩类型：1-图文点踩，2-视频点踩
	FeedbackType    int8   `form:"feedbackType"`    // 1-点赞；2-点踩
	FeedbackContent string `form:"feedbackContent"` // 反馈内容
}

type UserNoticeCommentParam struct {
	NoticeId int64  `form:"noticeId"`
	ParentId int64  `form:"parentId"`
	Comment  string `form:"comment"`
}

type UserNoticeCommentLikeParam struct {
	NoticeId  int64 `form:"noticeId"`
	CommentId int64 `form:"commentId"`
}

type UserNoticeListParam struct {
	ClassId  string `form:"classId"`  // 栏目
	ReadType int8   `form:"readType"` // 0-全部；1-未读；2-已读
}

type UserNoticeReadParam struct {
	NoticeId int64 `form:"noticeId"`
	Type     int8  `form:"type"`
	ReadTime int64 `form:"readTime"`
}

type CommentListParam struct {
	NoticeId   int64 `form:"noticeId"`   // 通知ID
	FilterType int8  `form:"filterType"` // 筛选类型：0-全部评论，1-精选评论
	TimeRange  int8  `form:"timeRange"`  // 时间范围：0-不限，1-最近7天，2-今天，3-昨天，4-前天
	SortType   int8  `form:"sortType"`   // 排序类型：0-按时间倒序，1-按点赞数倒序
	Page       int   `form:"page"`       // 页码
	Size       int   `form:"size"`       // 每页大小
}

type SystemNoticeCommentParam struct {
	Id         int64 `form:"id"`
	NoticeId   int64 `form:"noticeId"`
	IsSelected bool  `form:"isSelected"`
}

// GetPresignedURLParam 获取预签名URL的请求参数
type GetPresignedURLParam struct {
	ObjectKey string `form:"objectKey"` // 对象键
	Method    string `form:"method"`    // HTTP方法：GET/PUT
}
