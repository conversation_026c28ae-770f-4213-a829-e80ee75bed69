package dtonotice

import "git.zuoyebang.cc/pkg/golib/v2/cos"

type CommonRsp struct {
}

type IdRsp struct {
	Id int64 `json:"id"`
}

type NoticeListRsp struct {
	List  []SystemNotice `json:"list"`
	Total int64          `json:"total"`
}

type SystemNotice struct {
	Id             int64                    `json:"id"`
	Title          string                   `json:"title"`
	PublisherUid   int64                    `json:"publisherUid"`
	PublisherName  string                   `json:"publisherName"`
	PublisherGroup string                   `json:"publisherGroup"`
	ClassId        int8                     `json:"classId"`
	Status         int8                     `json:"status"`
	PublishTime    int64                    `json:"publishTime"`
	UpdateTime     int64                    `json:"updateTime"`
	Scope          int8                     `json:"scope"`
	Statistics     *ManagerNoticeStatistics `json:"statistics"`
}

type ManagerNoticeDetailRsp struct {
	Id            int64                    `json:"id"`            // 通知ID
	ClassId       int8                     `json:"classId"`       // 所属栏目
	Title         string                   `json:"title"`         // 标题
	PublisherName string                   `json:"publisherName"` // 发布人
	PublishTime   int64                    `json:"publishTime"`   // 发布时间
	FinishTime    int64                    `json:"finishTime"`    // 结束时间
	Content       string                   `json:"content"`       // 内容
	VideoAddr     string                   `json:"videoAddr"`     // 视频cos地址
	CommentDetail []NoticeComment          `json:"commentDetail"` // 评论详情
	Statistics    *ManagerNoticeStatistics `json:"statistics"`
}

type ManagerNoticeStatistics struct {
	TotalReceiver        int64  `json:"totalReceiver"`
	TotalContentViewer   int64  `json:"totalContentViewer"`
	TotalContentLiker    int64  `json:"totalContentLiker"`
	TotalContentDisLiker int64  `json:"totalContentDisLiker"`
	TotalVideoViewer     int64  `json:"totalVideoViewer"`
	TotalVideoLiker      int64  `json:"totalVideoLiker"`
	TotalVideoDisLiker   int64  `json:"totalVideoDisLiker"`
	ContentViewRate      string `json:"contentViewRate"`
	ContentLikeRate      string `json:"contentLikeRate"`
	ContentDisLikeRate   string `json:"contentDisLikeRate"`
	VideoViewRate        string `json:"videoViewRate"`
	VideoLikeRate        string `json:"videoLikeRate"`
	VideoDisLikeRate     string `json:"videoDisLikeRate"`
}

type NoticeUserInfoListRsp struct {
	List  []NoticeUserInfo `json:"list"`
	Total int64            `json:"total"`
}

type NoticeUserInfo struct {
	Uid                  int64  `json:"uid"`
	Name                 string `json:"name"`                 // 姓名
	ProductLineName      string `json:"productLineName"`      // 业务线
	Region               string `json:"region"`               // 业务地区
	Grade                string `json:"grade"`                // 年级
	GradeLevel           string `json:"gradeLevel"`           // 学部
	Subject              string `json:"subject"`              // 学科
	GroupId              string `json:"groupId"`              // 部门ID
	GroupName            string `json:"groupName"`            // 部门名称
	GroupPath            string `json:"groupPath"`            // 部门路径
	SendTime             int64  `json:"sendTime"`             // 通知送达时间
	ReadTime             int64  `json:"readTime"`             // 浏览时间
	BrowseDuration       int64  `json:"browseDuration"`       // 浏览时长（秒）
	BrowseDurationFormat string `json:"browseDurationFormat"` // 浏览时长（时分秒）
	FeedbackTime         int64  `json:"feedbackTime"`         // 反馈时间
	FeedbackContent      string `json:"feedbackContent"`      // 反馈内容
}

type CountRsp struct {
	Count int `json:"count"`
}

type UserNoticeRsp struct {
	Id            int64                `json:"id"`           // id
	Title         string               `json:"title"`        // 通知标题
	ClassID       int8                 `json:"classId"`      // 所属栏目
	ProfilePhoto  string               `json:"profilePhoto"` // 通知头像
	Abstract      string               `json:"abstract"`     // 通知摘要
	PublishTime   int64                `json:"publishTime"`  // 发布时间
	LastCommentAt int64                `json:"-"`            // 最后评论时间
	Statistics    UserNoticeStatistics `json:"statistics"`   // 通知相关的指标
	IsLike        bool                 `json:"isLike"`       // 是否点赞
}

type UserNoticeListRsp struct {
	List   []UserNoticeRsp `json:"list"`
	Total  int64           `json:"total"`
	Read   int64           `json:"read"`
	UnRead int64           `json:"unRead"`
}

type UserNoticeStatistics struct {
	CommentNum int64    `json:"commentNum"`
	LikeNum    int64    `json:"likeNum"`
	ReadNum    int64    `json:"readNum"`
	ReadUsers  []string `json:"readUsers"`
}

type NoticeDetailRsp struct {
	Id                   int64                `json:"id"`                   // 通知ID
	Title                string               `json:"title"`                // 标题
	ClassID              int8                 `json:"classId"`              // 所属栏目
	PublisherName        string               `json:"publisherName"`        // 发布人
	PublishTime          int64                `json:"publishTime"`          // 发布时间
	Content              string               `json:"content"`              // 内容
	VideoAddr            string               `json:"videoAddr"`            // 视频cos地址
	ContentLikeDetail    *UserNoticeFeedback  `json:"contentLikeDetail"`    // 内容点赞详情
	VideoLikeDetail      *UserNoticeFeedback  `json:"videoLikeDetail"`      // 视频点赞详情
	ContentFeedbackStats *NoticeFeedbackStats `json:"contentFeedbackStats"` // 内容反馈统计
	VideoFeedbackStats   *NoticeFeedbackStats `json:"videoFeedbackStats"`   // 视频反馈统计
	CommentDetail        []NoticeComment      `json:"commentDetail"`        // 评论详情
	CommentCount         int64                `json:"commentCount"`         // 评论数
}

type NoticeComment struct {
	Id              int64            `json:"id"`              // 评论ID
	Comment         string           `json:"comment"`         // 评论内容
	CommentTime     int64            `json:"commentTime"`     // 评论时间
	CommentUser     string           `json:"commentUser"`     // 评论人
	CommentLikeNum  int64            `json:"commentLikeNum"`  // 评论点赞数
	LikeFlag        bool             `json:"likeFlag"`        // 是否点赞
	IsSelected      bool             `json:"isSelected"`      // 是否精选
	ChildrenComment []*NoticeComment `json:"childrenComment"` // 子评论ID
}

type NoticeCommentListRsp struct {
	List          []NoticeComment `json:"list"`
	TotalCount    int             `json:"totalCount"`    // 总评论数
	SelectedCount int             `json:"selectedCount"` // 精选评论数
}
type NoticeFeedbackStats struct {
	FeedbackLikeNum    int64 `json:"feedbackLikeNum"`    // 反馈点赞数
	FeedbackDisLikeNum int64 `json:"feedbackDisLikeNum"` // 反馈点踩数
}

type UserNoticeFeedback struct {
	Id              int64  `json:"id"`              // 评论ID
	FeedbackType    int8   `json:"feedbackType"`    // 反馈类型：1-点赞，2-点踩
	FeedbackContent string `json:"feedbackContent"` // 反馈内容
	FeedbackTime    int64  `json:"feedbackTime"`    // 反馈时间
}

type DictRsp struct {
	Dict map[int8]string `json:"dict"`
}

// BosSignRsp 获取预签名URL的响应
type BosSignRsp struct {
	cos.Credentials
	Endpoint string `json:"endpoint"`
	Domain   string `json:"domain"`
	Bucket   string `json:"bucket"`
	Pid      string `json:"pid"` // uuid，当前token的唯一标识
}
