package dtofilter

type GetRemoteSelectReq struct {
	CourseId int64    `json:"courseId" form:"courseId"`
	LessonId int64    `json:"lessonId" form:"lessonId"`
	Keys     []string `json:"keys" form:"keys"`
}

type GetRemoteSelectResp struct {
	Key     string                  `json:"key"`
	Options []GetRemoteSelectOption `json:"options"`
}

type GetRemoteSelectAttr struct {
	Options []GetRemoteSelectOption `json:"options"`
}

type GetRemoteSelectOption struct {
	Value    string `json:"value"`
	Text     string `json:"text"`
	LessonId int64  `json:"lessonId"`
}

type ListCourseInfoReq struct {
	CourseIdList []int64 `json:"courseIdList" form:"courseIdList"`
}

type GetPronunciationUrlReq struct {
	CourseId   int64 `json:"courseId" form:"courseId"`
	LessonId   int64 `json:"lessonId" form:"lessonId"`
	StudentUid int64 `json:"studentUid" form:"studentUid"`
}

type GetVoiceUrlReq struct {
	CourseId   int64 `json:"courseId" form:"courseId"`
	LessonId   int64 `json:"lessonId" form:"lessonId"`
	StudentUid int64 `json:"studentUid" form:"studentUid"`
}

type GetPronunciationUrlRsp struct {
	Url string `json:"url" form:"url"`
}
type GetVoiceUrlRsp struct {
	Url string `json:"url" form:"url"`
}
