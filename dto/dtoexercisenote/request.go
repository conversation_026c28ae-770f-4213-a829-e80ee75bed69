package dtoexercisenote

type CourseTaskUrlReq struct {
	CourseId int64  `json:"courseId" form:"courseId" binding:"required"`
	TaskId   int64  `json:"taskId" form:"taskId" binding:"required"`
	AppId    string `json:"appId" form:"appId"`
}

type ExportMistakeDetailReq struct {
	CourseId int64 `json:"courseId" form:"courseId" binding:"required"`
	TaskId   int64 `json:"taskId" form:"taskId" binding:"required"`
}
