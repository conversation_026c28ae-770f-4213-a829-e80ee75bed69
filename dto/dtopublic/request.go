package dtopublic

type SopGrayReq struct {
	CourseId int64 `json:"courseId" form:"courseId" binding:"required"`
	LessonId int64 `json:"lessonId" form:"lessonId" binding:"required"`
}

type SceneSopGrayReq struct {
	CourseId int64 `json:"courseId" form:"courseId" binding:"required"`
	LessonId int64 `json:"lessonId" form:"lessonId" binding:"required"`
	Scene    int   `json:"scene" form:"scene" binding:"required"`
}

type DuXueCallGrayReq struct {
	CourseId int64 `json:"courseId" form:"courseId" binding:"required"`
	StaffUid int64 `json:"staffUid" form:"staffUid"`
}
