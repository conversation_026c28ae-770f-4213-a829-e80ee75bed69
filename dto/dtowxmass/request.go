package dtowxmass

type MessageHistoryReq struct {
	AssistantUid int64   `json:"assistantUid" form:"assistantUid"`
	BeginTime    int64   `json:"sendBeginTime" form:"sendBeginTime"`
	EndTime      int64   `json:"sendEndTime" form:"sendEndTime"`
	SceneTypes   []int64 `json:"sceneTypes" form:"sceneTypes"`
	SceneId      int64   `json:"sceneId" form:"sceneId"`
	TaskType     int64   `json:"taskType" form:"taskType"`
	PageNum      int64   `json:"pageNum" form:"pageNum"`
	PageSize     int64   `json:"pageSize" form:"pageSize"`
}

type RetryMainTaskReq struct {
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	MainTaskId   int64 `json:"mainTaskId" form:"mainTaskId"`
}

type RetryGroupTaskReq struct {
	MainTaskId  int64 `json:"mainTaskId" form:"mainTaskId"`
	GroupTaskId int64 `json:"groupTaskId" form:"groupTaskId"`
}

type GetGroupReceiversReq struct {
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	MainTaskId   int64 `json:"mainTaskId" form:"mainTaskId"`
	GroupTaskId  int64 `json:"groupTaskId" form:"groupTaskId"`
	SendType     int64 `json:"sendType" form:"sendType"`
}

type GetSubTaskDetailReq struct {
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	GroupTaskId  int64  `json:"groupTaskId" form:"groupTaskId"`
	SubTaskId    int64  `json:"subTaskId" form:"subTaskId"`
	ParentTaskId string `json:"parentTaskId" form:"parentTaskId"`
	SendType     int64  `json:"sendType" form:"sendType"`
}

type GetCourseAppIdReq struct {
	CourseIds []int64 `json:"courseIds" form:"courseIds"`
}
