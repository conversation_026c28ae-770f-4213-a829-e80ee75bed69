package dtowxmass

import "assistantdeskgo/api/lpcmsg"

type MessageHistoryResp struct {
	Total int64               `json:"total"`
	List  []MainTaskPagedItem `json:"list"`
}

type MainTaskPagedItem struct {
	MainTaskId        int64                `json:"mainTaskId"`
	StatusCode        int64                `json:"statusCode"`
	StatusText        string               `json:"statusText"`
	SendBeginTimeDesc string               `json:"sendBeginTimeDesc"`
	SendEndTimeDesc   string               `json:"sendEndTimeDesc"`
	TaskType          int64                `json:"taskType"`
	TaskTypeDesc      interface{}          `json:"taskTypeDesc"`
	SceneType         int64                `json:"sceneType"`
	SceneTypeDesc     interface{}          `json:"sceneTypeDesc"`
	CreateTime        int64                `json:"createTime"`
	GroupTasks        []GroupTaskPagedItem `json:"groupTasks"`
}

type GroupTaskPagedItem struct {
	GroupTaskId       int64              `json:"groupTaskId"`
	StatusCode        int64              `json:"statusCode"`
	StatusText        string             `json:"statusText"`
	SendBeginTimeDesc string             `json:"sendBeginTimeDesc"`
	SendEndTimeDesc   string             `json:"sendEndTimeDesc"`
	SubTasks          []SubTaskPagedItem `json:"subTasks"`
}

type KpSubTaskItem struct {
	ParentTaskId string              `json:"parentTaskId"`
	StatusCode   int64               `json:"statusCode"`
	StatusText   string              `json:"statusText"`
	SubType      int64               `json:"subType"`
	Content      SubMessagePagedItem `json:"content"`
	ReceiverIds  []int64             `json:"receiverIds"`
}

type SubTaskPagedItem struct {
	SubTaskId     int64               `json:"subTaskId"`
	StatusCode    int64               `json:"statusCode"`
	ParentTaskId  string              `json:"parentTaskId"`
	StatusText    string              `json:"statusText"`
	SubType       int64               `json:"subType"`
	AtMembers     []int64             `json:"atMembers"`
	Content       SubMessagePagedItem `json:"content"`
	KpSubTaskItem *KpSubTaskItem      `json:"kpSubTaskItem,omitempty"`
}

type SubMessagePagedItem struct {
	TplType        int64                  `json:"tplType"`
	MsgType        int64                  `json:"msgType"`
	MsgContent     interface{}            `json:"msgContent"`
	MsgContents    []lpcmsg.MsgContentDto `json:"msgContents"`
	DefaultContent string                 `json:"defaultContent"`
	AtMembers      []string               `json:"atMembers"`
}

type GetPersonReceiversResp struct {
	StudentUid  int64  `json:"studentUid"`
	StudentName string `json:"studentName"`
	Phone       string `json:"phone"`
}

type GetGroupReceiversResp struct {
	GroupName string `json:"groupName"`
}

type GetPersonSubTaskDetailResp struct {
	TotalUids int                          `json:"totalUids"`
	ExecUids  int                          `json:"execUids"`
	List      []GetPersonSubTaskDetailData `json:"list"`
}

type GetPersonSubTaskDetailData struct {
	StatusCode  int64                        `json:"statusCode"`
	StatusText  string                       `json:"statusText"`
	StudentList []GetPersonSubTaskDetailItem `json:"studentList"`
}

type GetPersonSubTaskDetailItem struct {
	StudentUid  int64  `json:"studentUid" form:"studentUid"`   // 接收者
	StudentName string `json:"studentName" form:"studentName"` // 接受者名称，非必填
	WxName      string `json:"wxName" form:"wxName"`           // 接受者其他名称，非必填
	Solution    string `json:"solution" form:"solution"`       // 异常解决方案
	ErrMsg      string `json:"errMsg" form:"errMsg"`           // 异常信息
}

type GetGroupSubTaskDetailResp struct {
	TotalUids int                         `json:"totalUids"`
	ExecUids  int                         `json:"execUids"`
	List      []GetGroupSubTaskDetailData `json:"list"`
}

type GetGroupSubTaskDetailData struct {
	StatusCode int64                       `json:"statusCode"`
	StatusText string                      `json:"statusText"`
	GroupList  []GetGroupSubTaskDetailItem `json:"groupList"`
}

type GetGroupSubTaskDetailItem struct {
	GroupId   int64  `json:"groupId" form:"groupId"`     // 接收者
	GroupName string `json:"groupName" form:"groupName"` // 接受者名称，非必填
	Solution  string `json:"solution" form:"solution"`   // 异常解决方案
	ErrMsg    string `json:"errMsg" form:"errMsg"`       // 异常信息
}

type GetCourseAppIdResp struct {
	AppList []AppObject `json:"appList" form:"appList"`
}

type AppObject struct {
	CourseId int64     `json:"courseId" form:"courseId"`
	AppInfo  []AppInfo `json:"appInfo" form:"appInfo"`
}

type AppInfo struct {
	AppId    string `json:"appId" form:"appId"`
	AppName  string `json:"appName" form:"appName"`
	IconUrl  string `json:"iconUrl" form:"iconUrl"`
	IconType string `json:"iconType" form:"iconType"`
}
