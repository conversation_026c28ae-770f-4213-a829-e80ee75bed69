package dtomessage

type CommonRsp struct {
	Success int64  `json:"success"` // 是否提交成功：0-失败、1-成功
	Message string `json:"message"` // 失败提示
}

type SaveFolderRsp struct {
	CommonRsp
	FolderId int64 `json:"folderId"`
}

type SaveGroupRsp struct {
	CommonRsp
	GroupId int64 `json:"groupId"`
}

type PageInfo struct {
	Pn    int   `json:"pn"`
	Rn    int   `json:"rn"`
	Total int64 `json:"total"`
}

type SideListRsp struct {
	SideListFolders []SideListFolder `json:"list"`
	PageInfo
}

type SideListFolder struct {
	FolderId      int64                  `json:"folderId"`
	FolderName    string                 `json:"folderName"`
	MessageGroups []SideListMessageGroup `json:"messageGroups"`
	Order         int64                  `json:"order"`
}

type SideListMessageGroup struct {
	GroupId   int64  `json:"groupId"`
	GroupName string `json:"groupName"`
	Authority int64  `json:"authority"`
	Desc      string `json:"desc"`
}

type ListRsp struct {
	ListFolderMessageGroups []ListFolderMessageGroup `json:"list"`
	PageInfo
}

type ListFolderMessageGroup struct {
	FolderId         int64  `json:"folderId"`
	FolderName       string `json:"folderName"`
	GroupId          int64  `json:"groupId"`
	GroupName        string `json:"groupName"`
	AuthorityDesc    string `json:"authorityDesc"`
	OrganizationDesc string `json:"organizationDesc"`
	Desc             string `json:"desc"`
	CanDelete        int    `json:"canDelete"`
	CreateName       string `json:"createName"`
	CreateUid        int64  `json:"createUid"`
	CreateTime       int64  `json:"createTime"`
	CreateTimeStr    string `json:"createTimeStr"`
}

type FolderListRsp struct {
	ListFolderMessageGroups []FolderInfo `json:"folderInfoList"`
}

type FolderInfo struct {
	Id    int64  `json:"id"`
	Name  string `json:"name"`
	Order int64  `json:"order,omitempty"`
}

type MessageGroupDetailRsp struct {
	FolderId          int64           `json:"folderId"`
	FolderName        string          `json:"folderName"`
	GroupId           int64           `json:"groupId"`
	GroupName         string          `json:"groupName"`
	GroupDesc         string          `json:"groupDesc"`
	GroupAuthority    int64           `json:"groupAuthority"`    // 消息组权限设置：1-私人、2-团队
	PermissionGroupId int64           `json:"permissionGroupId"` // 团队范围，组ID
	MessageList       []MessageDetail `json:"messageList"`
}

type MessageGroupDetailsRsp struct {
	GroupDetails []*MessageGroupDto `json:"groupDetails"`
}

type MessageGroupDto struct {
	GroupID     int64            `json:"groupID"`
	MessageList []*MessageDetail `json:"messageList"`
}

type MessageDetail struct {
	Type         int64       `json:"type"`         // 消息类型：0-文字、1-图片、2-语音、8-文件、20-视频号、4-卡片链接
	IntervalTime int64       `json:"intervalTime"` // 间隔时间
	Content      interface{} `json:"content"`      // 可变对象，map结构（https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=500859683）
	MessageId    int64       `json:"messageId"`    // 新增时为0，更新时存在
	Order        int64       `json:"order"`        // 顺序
}

type CheckMessageGroupPermissionRsp struct {
	NoPermissionMessageGroupIds []int64 `json:"noPermissionMessageGroupIds"`
}

type GroupPermissionNode struct {
	ID             int64                  `json:"id"`
	Name           string                 `json:"name"`
	WithPermission bool                   `json:"withPermission"`
	Children       []*GroupPermissionNode `json:"children"`
}

type UpdateTaskStatusRsp struct {
	MainTaskId int64 `json:"mainTaskId"`
	IsSuccess  int   `json:"isSuccess"`
}
