package dtomessage

import (
	"assistantdeskgo/api/muse/message"
)

type SaveFolderReq struct {
	Type       int64  `json:"type"` // 0-新增文件夹、1-更新文件夹
	FolderId   int64  `json:"folderId"`
	FolderName string `json:"folderName"`
}

type DeleteFolderReq struct {
	FolderId int64 `json:"folderId"`
}

type FolderOrderReq struct {
	FolderIds []int64 `json:"folderIds"`
}

type SaveMessageGroupReq struct {
	Type              int64     `json:"type"` // 0-新增消息组、1-更新消息组
	FolderId          int64     `json:"folderId"`
	GroupId           int64     `json:"groupId"`
	GroupName         string    `json:"groupName"`
	GroupDesc         string    `json:"groupDesc"`
	GroupAuthority    int64     `json:"groupAuthority"`    // 消息组权限设置：1-私人、2-团队
	PermissionGroupId int64     `json:"permissionGroupId"` // 团队范围，组ID
	MessageList       []Message `json:"messageList"`
}

type DeleteMessageGroupReq struct {
	GroupId int64 `json:"groupId"`
}

type Message struct {
	Type         int64       `json:"type"`         // 消息类型：0-文字、1-图片、2-语音、8-文件、20-视频号、4-卡片链接
	IntervalTime int64       `json:"intervalTime"` // 间隔时间
	Content      interface{} `json:"content"`      // 可变对象，map结构（https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=500859683）
	MessageId    int64       `json:"messageId"`    // 新增时为0，更新时存在
}

type SideListReq struct {
	Pn             int    `json:"pn" form:"pn"`
	Rn             int    `json:"rn" form:"rn"`
	Type           int    `json:"type" form:"type"`                     //个人创建：1/团队共享：2
	Name           string `json:"name" form:"name"`                     //名称
	GroupId        int64  `json:"groupId" form:"groupId"`               //名称
	AvailableRange string `json:"availableRange" form:"availableRange"` //使用范围
}

type ListReq struct {
	Pn                int    `json:"pn" form:"pn"`
	Rn                int    `json:"rn" form:"rn"`
	GroupId           int64  `json:"groupId" form:"groupId"`                     //消息组id
	GroupName         string `json:"groupName" form:"groupName"`                 //消息组名称
	Authority         int    `json:"authority" form:"authority"`                 //个人创建：1/团队共享：2
	PermissionGroupId int64  `json:"permissionGroupId" form:"permissionGroupId"` //团队范围、归属组织
	CreateName        string `json:"createName" form:"createName"`               //创建者姓名
	FolderId          int64  `json:"folderId" form:"folderId"`                   //文件夹id
	StartTime         int64  `json:"startTime" form:"startTime"`                 //开始时间
	EndTime           int64  `json:"endTime" form:"endTime"`                     //结束时间
}

type MessageGroupDetailReq struct {
	GroupId int64 `json:"groupId" form:"groupId"`
}

type MessageGroupDetailsReq struct {
	GroupIds []int64 `json:"groupIds" form:"groupIds"`
}

type CheckMessageGroupPermissionReq struct {
	GroupIds          []int64 `json:"groupIds" form:"groupIds"`                   //消息组id
	Authorities       []int   `json:"authorities" form:"authorities"`             //个人创建：1/团队共享：2
	PermissionGroupId int64   `json:"permissionGroupId" form:"permissionGroupId"` //团队范围、归属组织
	CreatorId         int64   `json:"creatorId" form:"creatorId"`                 //创建者
}

type FolderListReq struct {
	ListType int64 `json:"listType" form:"listType"`
}

type HtmlBaseInfoReq struct {
	Url string `json:"url" form:"url"` //网页地址
}

type HtmlBaseInfoRsp struct {
	Title       string `json:"title"`       //标题
	Description string `json:"description"` //描述
	Icon        string `json:"icon"`        //缩略图
}

type MaterialListReq struct {
	Pn int64 `json:"pn" form:"pn"`
	Rn int64 `json:"rn" form:"rn"`
}

type MaterialListRsp struct {
	List  []MaterialInfo `json:"list"`
	Total int64          `json:"total"`
	Pn    int64          `json:"pn"`
	Rn    int64          `json:"rn"`
}

type MaterialInfo struct {
	Avatar       string `json:"avatar"`
	CoverURL     string `json:"coverUrl"`
	Desc         string `json:"desc"`
	Extras       string `json:"extras"`
	FeedType     int64  `json:"feedType"`
	Nickname     string `json:"nickname"`
	ThumbURL     string `json:"thumbUrl"`
	URL          string `json:"url"`
	Eid          string `json:"eid"`
	ExpireTime   string `json:"expireTime"`
	ThumbPid     string `json:"thumbPid"`
	ShowThumbURL string `json:"showThumbUrl"`
}

type SameTplCourseReq struct {
	Year        int64 `json:"year" form:"year" binding:"required,gte=1"`
	ServiceType int64 `json:"serviceType" form:"serviceType" binding:"required"`
}

type SameTplCourseRsp struct {
	SameTplCourse map[int64][]SameTplCourseInfo `json:"sameTplCourse"`
}

type SameTplCourseInfo struct {
	CardInfo        string            `json:"cardInfo"`
	CourseId        int64             `json:"courseId"`
	CourseName      string            `json:"courseName"`
	CourseTime      string            `json:"courseTime"`
	SameLessonIds   map[int64][]int64 `json:"sameLessonIds"`
	TeacherNameList []string          `json:"teacherNameList"`
}

type SendGroupMessageReq struct {
	TaskType int64     `json:"taskType" form:"taskType"`
	SendTime int64     `json:"sendTime" form:"sendTime"`
	SubType  int64     `json:"subType" form:"subType" binding:"required"`
	Contents []Content `json:"contents" form:"contents" binding:"required"`
	SendType int64     `json:"sendType" form:"sendType"`
}

type Content struct {
	CourseId         int64                 `json:"courseId" form:"courseId" binding:"required"`
	LessonId         int64                 `json:"lessonId" form:"lessonId"`
	LabelStudentList map[string][]int      `json:"labelStudentList"`
	Ability          int                   `json:"ability" form:"ability"`
	Strategy         int                   `json:"strategy" form:"strategy"`
	ReceiverList     []Receiver            `json:"receiverList" form:"receiverList" binding:"required"`
	MessageList      []message.MessageInfo `json:"messageList" form:"messageList" binding:"required"`
}

type Receiver struct {
	StudentUid    int64    `json:"studentUid" form:"studentUid"`
	ChatId        string   `json:"chatId" form:"chatId"`
	AtStudentUids []int64  `json:"atStudentUids" form:"atStudentUids"`
	RemoteIds     []string `json:"remoteIds" form:"remoteIds"`
}

type SendGroupMessageRsp struct {
	TaskId         int64 `json:"taskId"`
	SendingTaskCnt int64 `json:"sendingTaskCnt"`
	HasCronTask    int   `json:"hasCronTask"`
	CronTimeRange  int   `json:"cronTimeRange"`
}

type WxMessageFolderListReq struct {
	Pn      int    `json:"pn" form:"pn"`
	Rn      int    `json:"rn" form:"rn"`
	Name    string `json:"name" form:"name"`       // 名称
	Ability int64  `json:"ability" form:"ability"` // 发送方式 1: 1v1  2: 群发助手
}

type MessageCheckReq struct {
	MessageCount     int64              `json:"messageCount" form:"messageCount"`
	WxIdCount        int64              `json:"wxIdCount" form:"wxIdCount"`
	GroupCount       int64              `json:"groupCount" form:"groupCount"`
	SubType          int64              `json:"subType" form:"subType"`
	IsSkipTaskCnt    int64              `json:"isSkipTaskCnt" form:"isSkipTaskCnt"`
	TaskType         int64              `json:"taskType" form:"taskType"`
	CourseId         int64              `json:"courseId" form:"courseId"`
	StudentUid2WxIds map[int64][]string `json:"studentUid2WxIds" form:"studentUid2WxIds"` // 目前
	Contents         []Content          `json:"contents" form:"contents"`
}

type MessageGroupUpdateAvailableRangeReq struct {
	StudentLevelSource string `json:"studentLevelSource" form:"studentLevelSource"`
	GeneralLevelSource string `json:"generalLevelSource" form:"generalLevelSource"`
}

type UpdateTaskStatusReq struct {
	MainTaskId int64 `json:"mainTaskId" form:"mainTaskId"`
	Operate    int   `json:"operate" form:"operate"`
}

type TouchSendWrapperReq struct {
	SubType      int64       `json:"subType" form:"subType"`           // 发送类型
	SceneType    int64       `json:"sceneType" form:"sceneType"`       // 场景ID
	TaskType     int         `json:"taskType" form:"taskType"`         // 任务类型
	IsDelay      int64       `json:"isDelay" form:"isDelay"`           // 延迟是延迟任务
	PrepareJobId int64       `json:"prepareJobId" form:"prepareJobId"` // 场景化群发数据准备作业ID，发送前需要先生成数据
	Contents     []ContentEx `json:"contents" form:"contents"`         // 发送内容
}

type ContentEx struct {
	SourceInfo       []Source           `json:"sourceInfo" form:"sourceInfo"`
	LabelStudentList map[string][]int64 `json:"labelStudentList" form:"labelStudentList"`
	ReceiverList     []Receiver         `json:"receiverList" form:"receiverList" binding:"required"`
	MessageList      []MessageInfo      `json:"messageList" form:"messageList" binding:"required"`
	Ability          int64              `json:"ability" form:"ability"`
	Strategy         int                `json:"strategy" form:"strategy"`
	Ext              *ExtDetail         `json:"ext" form:"ext"`
	GroupName        string             `json:"groupName" form:"groupName"` // 分组场景化群发，组名
}

type Source struct {
	Type string      `json:"type" form:"type"` // course, lesson
	Key  interface{} `json:"key" form:"key"`
}

type MessageInfo struct {
	DelayTime  int64       `json:"delayTime"`
	AtMembers  AtMembers   `json:"atMembers"`
	MsgType    int64       `json:"msgType"`
	MsgContent interface{} `json:"msgContent"`
}

type AtMembers struct {
	StudentUids []int64  `json:"studentUids"`
	RemoteIds   []string `json:"remoteIds"`
}

type ExtDetail struct {
	ExtType string      `json:"extType"`
	ExtInfo interface{} `json:"extInfo"`
}
