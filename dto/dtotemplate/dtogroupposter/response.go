package dtogroupposter

type AddGroupPosterResp struct {
	Id int64 `json:"id"`
}

type GetByIdResp struct {
	Id          int64   `json:"id"`
	TplName     string  `json:"tplName"`
	TplType     int64   `json:"tplType"`
	TplStyle    int64   `json:"tplStyle"`
	TplGroupIds []int64 `json:"tplGroupIds"`
	TplDesc     string  `json:"tplDesc"`
	TplContent  string  `json:"tplContent"`
}

type FindByPagedResp struct {
	List  []FindByPagedItem `json:"list"`
	Total int64             `json:"total"`
}

type FindByPagedItem struct {
	Id                int64  `json:"id"`
	TplName           string `json:"tplName"`
	TplStyleDesc      string `json:"tplStyleDesc"`
	TplGroupIdsDesc   string `json:"tplGroupIdsDesc"`
	TplDesc           string `json:"tplDesc"`
	TplContent        string `json:"tplContent"`
	TplOperatorName   string `json:"tplOperatorName"`
	TplUpdateTimeDesc string `json:"tplUpdateTimeDesc"`
}
