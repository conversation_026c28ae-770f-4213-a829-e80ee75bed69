package dtogroupposter

import (
	"assistantdeskgo/models/grouppostertemplate"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"unicode/utf8"
)

type GroupPosterReq struct {
	TplName     string  `json:"tplName" form:"tplName"`
	TplStyle    int64   `json:"tplStyle" form:"tplStyle"`
	TplGroupIds []int64 `json:"tplGroupIds" form:"tplGroupIds"`
	TplDesc     string  `json:"tplDesc" form:"tplDesc"`
	TplContent  string  `json:"tplContent" form:"tplContent"`
}

func (groupPoster *GroupPosterReq) validateBase() (err error) {
	if len(groupPoster.TplName) == 0 {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.Validate error, 模板名称 不能为空"))
		return
	}
	if utf8.RuneCountInString(groupPoster.TplName) > 50 {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.Validate error, 模板名称[%s] 长度大于50字符", groupPoster.TplName))
		return
	}

	if _, ok := grouppostertemplate.GetTplStyleOptions()[groupPoster.TplStyle]; !ok {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.Validate error, 模版样式[%d] 不能为空", groupPoster.TplStyle))
		return
	}

	if len(groupPoster.TplGroupIds) == 0 {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.Validate error, 模板归属二级团队 不能为空"))
		return
	}

	_, err = jsoniter.MarshalToString(groupPoster.TplGroupIds)
	if err != nil {
		return
	}

	if utf8.RuneCountInString(groupPoster.TplDesc) > 200 {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.Validate error, 模板描述[%s] 长度不能大于200字符", groupPoster.TplDesc))
		return
	}

	if len(groupPoster.TplContent) == 0 {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.Validate error, 模板内容 不能为空"))
		return
	}

	return
}

type AddGroupPosterReq struct {
	GroupPosterReq
	TplType int64 `json:"tplType" form:"tplType"`
}

func (addGroupPoster *AddGroupPosterReq) ValidateParams() (err error) {
	if _, ok := grouppostertemplate.GetTplTypeOptions()[addGroupPoster.TplType]; !ok {
		err = errors.New(fmt.Sprintf("AddGroupPosterReq.ValidateParams error, tplType[%d] invalid", addGroupPoster.TplType))
		return
	}

	err = addGroupPoster.validateBase()
	return
}

type UpdateGroupPosterReq struct {
	GroupPosterReq
	Id int64 `json:"id" form:"id"`
}

func (updateGroupPoster *UpdateGroupPosterReq) ValidateParams() (err error) {
	err = updateGroupPoster.validateBase()
	return
}

type DeleteGroupPosterReq struct {
	Id int64 `json:"id" form:"id"`
}

type GetByIdReq struct {
	Id int64 `json:"id" form:"id"`
}

type FindByPagedReq struct {
	Id          int64   `json:"id" form:"id"`
	TplType     int64   `json:"tplType" form:"tplType"`
	TplStyle    int64   `json:"tplStyle" form:"tplStyle"`
	TplGroupIds []int64 `json:"tplGroupIds" form:"tplGroupIds"`
	TplName     string  `json:"tplName" form:"tplName"`
	Pn          int64   `json:"pn" form:"pn" binding:"min=0"`
	Rn          int64   `json:"rn" form:"rn" binding:"min=1"`
}
