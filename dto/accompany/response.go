package dtoaccompany

type GetTagInfoRsp struct {
	Tags []*TagInfoDto `json:"tags"`
}

type TagInfoDto struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type GetAccompanyDetailRsp struct {
	Details []*AccompanyDetailInfo `json:"details"`
}

type AccompanyDetailInfo struct {
	CourseId  int64 `json:"courseId"`
	LessonId  int64 `json:"lessonId"`
	StartTime int64 `json:"startTime"`
	EndTime   int64 `json:"endTime"`
	BanxueId  int64 `json:"banxueId"`
}
