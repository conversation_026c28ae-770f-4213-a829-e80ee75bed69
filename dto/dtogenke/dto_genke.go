package dtogenke

type WxCallRecordRsp struct {
	CallId         int64  `json:"callId"`
	StudentUid     int64  `json:"studentUid"`
	StudentName    string `json:"studentName"`
	Remark         string `json:"remark"`
	WeixinName     string `json:"weixinName"`
	Wei<PERSON>NickName string `json:"weixinNickName"`
	StartTime      string `json:"startTime"`
	SortTime       int64  `json:"sortTime"`
	Duration       int64  `json:"duration"`
	CallResult     string `json:"callResult"`
	CallResultType int64  `json:"callResultType"`
	DeviceUid      int64  `json:"deviceUid"`
	MsgType        int64  `json:"msgType"`
}
