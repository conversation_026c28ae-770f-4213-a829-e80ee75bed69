package defines

const CALL_TYPE = 1 //默认展示呼出
const TYPE_IN = 2   //呼入

const SOURCE_TYPE_OTHER = 99
const SOURCE_TYPE_HOME_VISIT = 1
const SOURCE_TYPE_CLASS_ATTENDANCE = 2
const SOURCE_TYPE_HOMEWORK = 3
const SOURCE_TYPE_AGAIN_VISIT = 4
const SOURCE_TYPE_CONTINUE_COURSE = 5
const SOURCE_TYPE_CLASSING = 6
const SOURCE_TYPE_COURSE_ONE = 7
const SOURCE_TYPE_COURSE_TWO = 8
const SOURCE_TYPE_WECHAT = 9
const SOURCE_TYPE_10 = 10  //每日一练
const SOURCE_TYPE_11 = 11  //日常维护
const SOURCE_TYPE_12 = 12  //退课学员
const SOURCE_TYPE_TMK = 13 //TMK类型
const SOURCE_TYPE_14 = 14  //摸底测试
const SOURCE_TYPE_15 = 15  //浣熊催测试
const SOURCE_TYPE_16 = 16  //挽单
const SOURCE_TYPE_17 = 17
const SOURCE_TYPE_18 = 18 //挽单回访
const SOURCE_TYPE_19 = 19 //转介绍
const SOURCE_TYPE_20 = 20 //转介绍明细外呼
const SOURCE_TYPE_21 = 21 //群发微信失败后呼叫
const SOURCE_TYPE_22 = 22 //编程巩固练习未作答外呼
const SOURCE_TYPE_23 = 23 // 续报预约
const SOURCE_TYPE_24 = 24 // 公海外呼

const SOURCE_TYPE_OTHER_VALUE = 1
const SOURCE_TYPE_HOME_VISIT_VALUE = 2
const SOURCE_TYPE_CLASS_ATTENDANCE_VALUE = 4
const SOURCE_TYPE_HOMEWORK_VALUE = 8
const SOURCE_TYPE_AGAIN_VISIT_VALUE = 16
const SOURCE_TYPE_CONTINUE_COURSE_VALUE = 32
const SOURCE_TYPE_CLASSING_VALUE = 64
const SOURCE_TYPE_COURSE_ONE_VALUE = 128
const SOURCE_TYPE_COURSE_TWO_VALUE = 256
const SOURCE_TYPE_WECHAT_VALUE = 512
const SOURCE_TYPE_10_VALUE = 1024
const SOURCE_TYPE_11_VALUE = 2048
const SOURCE_TYPE_12_VALUE = 4096  //8192 16384 32768
const SOURCE_TYPE_TMK_VALUE = 8192 //16384 32768
const SOURCE_TYPE_14_VALUE = 16384
const SOURCE_TYPE_15_VALUE = 32768
const SOURCE_TYPE_16_VALUE = 65536
const SOURCE_TYPE_17_VALUE = 131072
const SOURCE_TYPE_18_VALUE = 262144
const SOURCE_TYPE_19_VALUE = 524288
const SOURCE_TYPE_20_VALUE = 1048576
const SOURCE_TYPE_21_VALUE = 2097152
const SOURCE_TYPE_22_VALUE = 4194304
const SOURCE_TYPE_23_VALUE = 1 << SOURCE_TYPE_23
const SOURCE_TYPE_24_VALUE = 1 << SOURCE_TYPE_24

var CallSourceValueMap = map[int64]string{
	SOURCE_TYPE_HOME_VISIT:       "家访",
	SOURCE_TYPE_CLASS_ATTENDANCE: "到课",
	SOURCE_TYPE_HOMEWORK:         "作业",
	SOURCE_TYPE_AGAIN_VISIT:      "回访",
	SOURCE_TYPE_CONTINUE_COURSE:  "续报",
	SOURCE_TYPE_CLASSING:         "跟课",
	SOURCE_TYPE_COURSE_ONE:       "课程1",
	SOURCE_TYPE_COURSE_TWO:       "课程2",
	SOURCE_TYPE_WECHAT:           "微信",
	SOURCE_TYPE_10:               "每日一练",
	SOURCE_TYPE_11:               "日常维护",
	SOURCE_TYPE_12:               "退课学员",
	SOURCE_TYPE_OTHER:            "其他",
	SOURCE_TYPE_TMK:              "催定级测",
	SOURCE_TYPE_14:               "摸底测试",
	SOURCE_TYPE_15:               "浣熊催测试",
	SOURCE_TYPE_16:               "挽单",
	SOURCE_TYPE_17:               "答疑",
	SOURCE_TYPE_18:               "挽单回访",
	SOURCE_TYPE_19:               "转介绍",
	SOURCE_TYPE_20:               "转介绍明细外呼",
	SOURCE_TYPE_21:               "群发微信失败后呼叫",
	SOURCE_TYPE_22:               "编程巩固练习未作答外呼",
	SOURCE_TYPE_23:               "续报预约",
	SOURCE_TYPE_24:               "公海外呼",
}

var SourceTypeValueMap = map[int64]int64{
	SOURCE_TYPE_OTHER:            SOURCE_TYPE_OTHER_VALUE,
	SOURCE_TYPE_HOME_VISIT:       SOURCE_TYPE_HOME_VISIT_VALUE,
	SOURCE_TYPE_CLASS_ATTENDANCE: SOURCE_TYPE_CLASS_ATTENDANCE_VALUE,
	SOURCE_TYPE_HOMEWORK:         SOURCE_TYPE_HOMEWORK_VALUE,
	SOURCE_TYPE_AGAIN_VISIT:      SOURCE_TYPE_AGAIN_VISIT_VALUE,
	SOURCE_TYPE_CONTINUE_COURSE:  SOURCE_TYPE_CONTINUE_COURSE_VALUE,
	SOURCE_TYPE_CLASSING:         SOURCE_TYPE_CLASSING_VALUE,
	SOURCE_TYPE_COURSE_ONE:       SOURCE_TYPE_COURSE_ONE_VALUE,
	SOURCE_TYPE_COURSE_TWO:       SOURCE_TYPE_COURSE_TWO_VALUE,
	SOURCE_TYPE_WECHAT:           SOURCE_TYPE_WECHAT_VALUE,
	SOURCE_TYPE_10:               SOURCE_TYPE_10_VALUE,
	SOURCE_TYPE_11:               SOURCE_TYPE_11_VALUE,
	SOURCE_TYPE_12:               SOURCE_TYPE_12_VALUE,
	SOURCE_TYPE_TMK:              SOURCE_TYPE_TMK_VALUE,
	SOURCE_TYPE_14:               SOURCE_TYPE_14_VALUE,
	SOURCE_TYPE_15:               SOURCE_TYPE_15_VALUE,
	SOURCE_TYPE_16:               SOURCE_TYPE_16_VALUE,
	SOURCE_TYPE_17:               SOURCE_TYPE_17_VALUE,
	SOURCE_TYPE_18:               SOURCE_TYPE_18_VALUE,
	SOURCE_TYPE_19:               SOURCE_TYPE_19_VALUE,
	SOURCE_TYPE_20:               SOURCE_TYPE_20_VALUE,
	SOURCE_TYPE_21:               SOURCE_TYPE_21_VALUE,
	SOURCE_TYPE_22:               SOURCE_TYPE_22_VALUE,
	SOURCE_TYPE_23:               SOURCE_TYPE_23_VALUE,
	SOURCE_TYPE_24:               SOURCE_TYPE_24_VALUE,
}

const CALL_RECORD_ROLE_TEACHER = "Agent"
const CALL_RECORD_ROLE_USER = "User"

const CALL_RECORD_ROLE_TEACHER_VALUE = 0
const CALL_RECORD_ROLE_USER_VALUE = 1

const CALLOUT_MODE_TY = 7      //天鹰
const CALLOUT_MODE_VIRTUAL = 8 //虚拟外呼 仅在lpc侧使用
const CALLOUT_MODE_SHIELD = 9  //帮帮盾
const CALLOUT_MODE_AUTO = 10   //自动外呼
const CALLOUT_MODE_SIP = 11    //SIP外呼

const CALLOUT_MODE_TY_STR = "天鹰"        //天鹰
const CALLOUT_MODE_VIRTUAL_STR = "虚拟外呼" //虚拟外呼 仅在lpc侧使用
const CALLOUT_MODE_SHIELD_STR = "帮帮盾"   //帮帮盾
const CALLOUT_MODE_AUTO_STR = "自动外呼"    //自动外呼
const CALLOUT_MODE_SIP_STR = "SIP外呼"
const CALLOUT_BUSINESSTYPE_PUBLICSEA = "publicSea"

var CallModelMap = map[int64]string{
	0:                    "未知",
	CALLOUT_MODE_TY:      CALLOUT_MODE_TY_STR,
	CALLOUT_MODE_VIRTUAL: CALLOUT_MODE_VIRTUAL_STR,
	CALLOUT_MODE_SHIELD:  CALLOUT_MODE_SHIELD_STR,
	CALLOUT_MODE_AUTO:    CALLOUT_MODE_AUTO_STR,
	CALLOUT_MODE_SIP:     CALLOUT_MODE_SIP_STR,
}

const WxCallOutModeRadio = 16
const WxCallOutModeVideo = 17

var WxCallModeMap = map[int64]string{
	0:                  "未知",
	WxCallOutModeRadio: "微信语音",
	WxCallOutModeVideo: "微信视频",
}

const PublicSeaBusinessType = "publicSea"
const PrivateSeaBusinessType = "privateSea"
