package defines

const (
	ListTypeForPersonal = 1 // 个人创建
	ListTypeForAll      = 2 // 全部
)

const (
	AddFolder = iota
	UpdateFolder
)

const (
	AddMessageGroup = iota
	UpdateMessageGroup
)

const ()

func CanProcess(processType int64) (canProcess bool) {
	switch processType {
	case AddMessageGroup:
		return true
	case UpdateMessageGroup:
		return true
	}
	return false
}

const (
	MessageTypeWord          = 0  // 文字
	MessageTypePicture       = 1  // 图片
	MessageTypeVoice         = 2  // 语音
	MessageTypeVideo         = 3  // 视频
	MessageTypeCardLink      = 5  // 卡片链接
	MessageTypeFile          = 8  // 文件
	MessageTypeVideoMaterial = 20 // 视频号
)

const (
	MultiMessageToPerson            = 1 //1v1群发到人
	MultiMessageToPersonByAssistant = 2 //群发小助手到人
	MultiMessageToGroup             = 3 //群发到群
	MultiMessageToGroupByAssistant  = 4 //群发小助手到群
	SingleMessageToPerson           = 5 //单发到人
	SingleMessageToGroup            = 6 //单发到群
	MultiAddGroup                   = 7 //鲲鹏拉群任务
)

const (
	GroupOrderFalse = 1
	GroupOrderTrue  = 2
)

const (
	SubOrderTrue  = 2
	SubOrderFalse = 1
)

const (
	VoiceTypeMP3  = 0
	VoiceTypeOGG  = 1
	VoiceTypeWAV  = 2
	VoiceTypeFLAC = 3
)

var VoiceType2TextMap = map[int64]string{
	VoiceTypeMP3:  ".mp3",
	VoiceTypeOGG:  ".ogg",
	VoiceTypeWAV:  ".wav",
	VoiceTypeFLAC: ".flac",
}

const (
	AuthorityForGroup    = 1 // 团队
	AuthorityForPersonal = 0 // 个人
)

var AuthorityTextMap = map[int64]string{
	AuthorityForGroup:    "团队",
	AuthorityForPersonal: "个人",
}

const (
	AvailableRangeForStudentLevel = "student" // 学生维度使用
	AvailableRangeForGeneral      = "general" // 通用的
)

func GetAvailableRange(contentList []string) string {
	if len(contentList) == 0 {
		return AvailableRangeForGeneral
	}
	for _, content := range contentList {
		if content == WxVarForStudentName {
			return AvailableRangeForStudentLevel
		}
	}

	return AvailableRangeForGeneral
}

const (
	WxVarForStudentName = "#学生名#"
)

func CanConvertVoice(voiceType int64) (canConvert bool) {
	switch voiceType {
	case VoiceTypeMP3:
		return true
	case VoiceTypeOGG:
		return true
	case VoiceTypeWAV:
		return true
	case VoiceTypeFLAC:
		return true
	}
	return false
}

const (
	ChatTypeForPersonal    = 0    //单聊
	ChatTypeForGroup       = 1    //群聊
	SendTypeSignalSop      = 1059 // 单发sop
	SendTypeSignalGroupSop = 1060 // 单发到群sop
	SendTypeMultiPersonSop = 1061 // 群发到人
	SendTypeMultiGroupSop  = 1062 // 群发到群
)

func GetSendTypeBySubType(subType int64) int64 {
	switch subType {
	case MultiMessageToPerson, MultiMessageToPersonByAssistant:
		return SendTypeMultiPersonSop
	case SingleMessageToPerson:
		return SendTypeSignalSop
	case MultiMessageToGroup, MultiMessageToGroupByAssistant:
		return SendTypeMultiGroupSop
	case SingleMessageToGroup:
		return SendTypeSignalGroupSop
	default:
		return 0
	}
}
