package defines

const (
	//人员状态，1-在职，2-预离职，3-已离职未解绑，4-已离职已解绑
	UserStatusOnJob          = 1
	UserStatusReadyLeave     = 2
	UserStatusLeaveNotUnbind = 3
	UserStatusLeaveUnbind    = 4

	//人力岗位
	HrPostTutor   = 1 //辅导老师
	HrPostUnTutor = 2 // 非辅导老师

	IsAll    = -1 // 全部
	IsAllStr = "-1"

	// 辅导费等级
	CostLevelForS = "S"
	CostLevelForA = "A"
	CostLevelForB = "B"
	CostLevelForC = "C"
	CostLevelForD = "D"
)

var UserStatusMap = map[int64]string{
	UserStatusOnJob:          "在职",
	UserStatusReadyLeave:     "预离职",
	UserStatusLeaveNotUnbind: "已离职未解绑",
	UserStatusLeaveUnbind:    "已离职已解绑",
}

var HrPostMap = map[int64]string{
	HrPostTutor: "辅导老师",
}

// 学部
const (
	PrimarySchool = 1
	JuniorSchool  = 20
)

func ContainPrimaryAndJunior(departments []int64) bool {
	exist := make(map[int64]int64)
	for _, department := range departments {
		exist[department] = 1
	}
	_, ok1 := exist[PrimarySchool]
	_, ok2 := exist[JuniorSchool]
	if ok1 || ok2 {
		return true
	}
	return false
}

// 人员状态
const (
	StaffStatusActive         = 1 // 在职
	StaffStatusPreResignation = 2 // 预离职
)

func ContainActiveAndPreResignation(staffStatus int64) bool {
	if staffStatus == StaffStatusActive || staffStatus == StaffStatusPreResignation {
		return true
	}
	return false
}

// 学科
// 语文、数学、英语、物理、化学、生物、地理、政治、历史、科学、语言
const (
	Chinese     = 1
	Mathematics = 2
	English     = 3
	Physics     = 4
	Chemistry   = 5
	Biology     = 6
	Geography   = 9
	Politics    = 7
	History     = 8
	Science     = 16
	Language    = 67
)

var SubjectTextMap = map[int64]string{
	Chinese:     "语文",
	Mathematics: "数学",
	English:     "英语",
	Physics:     "物理",
	Chemistry:   "化学",
	Biology:     "生物",
	Geography:   "地理",
	Politics:    "政治",
	History:     "历史",
	Science:     "科学",
	Language:    "语言",
}

func ContainSubject(subjects []int64) bool {
	for _, subject := range subjects {
		_, ok := SubjectTextMap[subject]
		if ok {
			return true
		}
	}
	return false
}

// 归属
const (
	AscriptionAssistant = 1 // 账号辅导
)

var ValidGrade = map[int64]string{
	IsAll: "全部",
	1:     "小学",
	20:    "初中",
}

var ValidPersonBelong = map[int64]string{
	AscriptionAssistant: "辅导",
}

// 等级分-小学：S=25分，A=15分，B=5分，C=0分，D=0分；
var CostLevelScoreMapForPrimary = map[string]float64{
	CostLevelForS: 25,
	CostLevelForA: 15,
	CostLevelForB: 5,
	CostLevelForC: 0,
	CostLevelForD: 0,
}

// 等级分-初中：S=25分，A=12分，B=3分，C=0分，D=0分；
var CostLevelScoreMapForJunior = map[string]float64{
	CostLevelForS: 25,
	CostLevelForA: 12,
	CostLevelForB: 3,
	CostLevelForC: 0,
	CostLevelForD: 0,
}
