package defines

var Grade2DepartMap = map[int64]int64{
	1:   1,
	11:  1,
	12:  1,
	13:  1,
	14:  1,
	15:  1,
	16:  1,
	2:   2,
	3:   2,
	4:   2,
	21:  2,
	20:  2,
	5:   3,
	6:   3,
	7:   3,
	92:  3,
	30:  3,
	31:  3,
	32:  3,
	33:  3,
	60:  6,
	61:  6,
	62:  6,
	63:  6,
	64:  6,
	70:  7,
	71:  7,
	81:  8,
	82:  8,
	83:  8,
	84:  8,
	100: 8,
	90:  9,
	91:  9,
}

var GradeMap = map[int64]string{
	1:   "小学",
	11:  "一年级",
	12:  "二年级",
	13:  "三年级",
	14:  "四年级",
	15:  "五年级",
	16:  "六年级",
	2:   "初一",
	3:   "初二",
	4:   "初三",
	21:  "预初",
	20:  "初中",
	92:  "高中衔接",
	5:   "高一",
	6:   "高二",
	7:   "高三",
	30:  "高中", //答疑，直播
	31:  "职高一",
	32:  "职高二",
	33:  "职高三",
	50:  "高中", //题库
	60:  "学前",
	61:  "学前班",
	62:  "大班",
	63:  "中班",
	64:  "小班",
	255: "其他",
	70:  "成人", //学段
	71:  "成人", //学部下年级
	80:  "大学",
	81:  "大一",
	82:  "大二",
	83:  "大三",
	84:  "大四",
	100: "研究生",
	90:  "低幼", //学段
	91:  "低幼", //学段下年级
}
