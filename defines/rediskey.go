package defines

const (
	RedisKeyCreateCaptureTask = "create_capture_taskId:%d_successNum"

	CacheKeyRelationCallTime         = "assistantdeskgo:cache:relation_call_time_%s_%s" // 公海池外呼时间zSet, key1=公私海，key2=clueId，score=外呼时间
	CacheKeyRelationCallTimeExSecond = 60 * 60 * 24 * 7                                 // 7d的限制

	CacheKeyRelationSmsSend = "assistantdeskgo:cache:relation_sms_send_%s" // 私海池短信发送次数，key=clueId，value=已发送次数

	CacheKeyRelationClueList          = "assistantdeskgo:cache:clue_list_%s" // 公海列表查询缓存
	CacheKeyRelationClueListExSeconds = 60                                   // 1min缓存
)

const (
	LockKeyRelationCall         = "assistantdeskgo:lock:relation_call_%s" // 公海池线索外呼锁，key=clueId，value=1
	LockKeyRelationCallExSecond = 5                                       // 超时时间

	LockKeyRelationSms         = "assistantdeskgo:lock:relation_sms_%s" // 私海池线索短信锁，key=clueId，value=1
	LockKeyRelationSmsExSecond = 5

	LockKeyRelationRetrieve         = "assistantdeskgo:lock:relation_retrieve_%s" // 公海池线索回捞锁，key=clueId，value=1
	LockKeyRelationRetrieveExSecond = 5                                           // 超时时间
)
