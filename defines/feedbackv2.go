package defines

const (
	NotAttendNotPlayback   = 0 // 未到课且未回放
	AttendPlaybackLt30Min  = 1 // 到课与回放均不足30分钟
	AttendPlaybackGte30Min = 2 // 已到课或已看回放(>=30分钟)
)

const (
	LessonIdListLenMax = 100 // 最大可选章节数
	DefaultFloatPlace  = 2   // 默认保留多少位小数
	SecondsOf30Minutes = 30 * 60
	// 如果相同的报警信息1小时内报过，不再重复报警
	ExplainConfigNotFoundSameNoticeGapSecs = 60 * 60
)

const (
	MasterLevelS = "S"
	MasterLevelA = "A"
	MasterLevelB = "B"
	MasterLevelZ = "Z"
	MasterLevelY = "Y"
)

const (
	MasterStatusAwesome  = "优秀"
	MasterStatusGood     = "合格"
	MasterStatusWeak     = "薄弱"
	MasterStatusNotLearn = "未学"
	MasterStatusUnknown  = "未知"
)

const (
	MasterLevelIntS = 1
	MasterLevelIntA = 2
	MasterLevelIntB = 3
)

var MasterLevelMap = map[int]string{
	MasterLevelIntS: MasterLevelS,
	MasterLevelIntA: MasterLevelA,
	MasterLevelIntB: MasterLevelB,
}

var MasterLevelIntMap = map[string]int{
	MasterLevelS: MasterLevelIntS,
	MasterLevelA: MasterLevelIntA,
	MasterLevelB: MasterLevelIntB,
}

var MasterLevelStatusMap = map[string]string{
	MasterLevelS: MasterStatusAwesome,
	MasterLevelA: MasterStatusGood,
	MasterLevelB: MasterStatusWeak,
	MasterLevelZ: MasterStatusNotLearn,
	MasterLevelY: MasterStatusUnknown,
}

const (
	NotLearnExplainTag  = "未学引导"
	NotLearnExplainText = "询问未到原因，引导学员观看回放"
)

const (
	DingBotNameFeedbackV2 = "feedbackV2"
)
