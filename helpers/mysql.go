package helpers

import (
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"gorm.io/gorm"
)

var MysqlClientFuDao *gorm.DB
var MysqlClientDuxuesc *gorm.DB

func InitMysql() {
	var err error
	for name, dbConf := range conf.RConf.Mysql {
		switch name {
		case "bzr":
			MysqlClientFuDao, err = base.InitMysqlClient(dbConf)
		case "duxuesc":
			MysqlClientDuxuesc, err = base.InitMysqlClient(dbConf)
		}
		if err != nil {
			panic("mysql connect error: %v" + err.<PERSON>rror())
		}
	}
}
