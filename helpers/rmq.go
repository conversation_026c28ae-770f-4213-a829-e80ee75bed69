package helpers

import (
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"go.uber.org/ratelimit"

	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
)

var AutoCallConsumerLimiter ratelimit.Limiter
var AiCallConsumerLimiter ratelimit.Limiter
var AiCallAudioConsumerLimiter ratelimit.Limiter

var AiCallAbstractConsumerLimiter ratelimit.Limiter
var WxCallRecordConsumerLimiter ratelimit.Limiter

var AIAutoTagConsumerLimiter ratelimit.Limiter
var Consumer182001Limiter ratelimit.Limiter

func init() {
	AutoCallConsumerLimiter = ratelimit.New(120, ratelimit.WithoutSlack)
	AiCallConsumerLimiter = ratelimit.New(10, ratelimit.WithoutSlack)
	AiCallAudioConsumerLimiter = ratelimit.New(10, ratelimit.WithoutSlack)
	AiCallAbstractConsumerLimiter = ratelimit.New(10, ratelimit.WithoutSlack)
	WxCallRecordConsumerLimiter = ratelimit.New(20, ratelimit.WithoutSlack)
	Consumer182001Limiter = ratelimit.New(200, ratelimit.WithoutSlack)
}

func InitRmq() {
	if env.GetRunEnv() == env.RunEnvTest && !env.IsDockerPlatform() {
		return
	}

	initMQConf()
	for _, produceConf := range conf.RConf.Rmq.Producer {
		zlog.Debugf(nil, "register Rmq producer: %s", produceConf.Service)
		if err := rmq.InitProducer(produceConf); err != nil {
			panic("register Rmq producer[" + produceConf.Service + "] error: " + err.Error())
		}

		if err := rmq.StartProducer(produceConf.Service); err != nil {
			panic("Rmq StartProducer[" + produceConf.Service + "] error: " + err.Error())
		}
	}
}

func initMQConf() {
	AIAutoTagConsumerLimiter = ratelimit.New(conf.Custom.PreClass.AiAutoConsumeQps)
}
