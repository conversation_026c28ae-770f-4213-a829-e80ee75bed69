package helpers

import (
	"assistantdeskgo/conf"
	"crypto/tls"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/olivere/elastic/v7"
)

var ElasticClient *elastic.Client

const (
	ElasticVersion6 int = 6
	ElasticVersion7 int = 7
)

const (
	ElasticClusterWorkbench   string = "workbench"
	EsDataEngineInstWorkbench        = "dataengine"

	elasticClusterPerformance string = "performance"
	elasticClusterPressure           = elasticClusterPerformance
)

// 线上集群压测集群对应关系。如果没有配，查默认压测集群
var elasticClusterOnline2Pressure = map[string]string{
	ElasticClusterWorkbench: elasticClusterPerformance,
}

var elasticCluster2Client map[string]interface{}

func InitEs() {
	var err error
	elasticCluster2Client = map[string]interface{}{}

	// 根据版本自行选择初始化方法
	for cluster, config := range conf.RConf.Elastic {
		switch config.Version {
		case ElasticVersion6:
			elasticCluster2Client[cluster], err = base.NewESClient(config.ElasticClientConfig)
		case ElasticVersion7:
			elasticCluster2Client[cluster], err = base.NewESClientV7(config.ElasticClientConfig, elastic.SetScheme("https"), elastic.SetHttpClient(&http.Client{
				Transport: &http.Transport{
					TLSClientConfig: &tls.Config{
						InsecureSkipVerify: true,
					},
				},
			}))
		default:
			err = fmt.Errorf("unsupported elastic version: %d", config.Version)
		}

		if err != nil {
			panic(fmt.Sprintf("elastic init failed, service: %s, error: %v", config.Service, err))
		}
	}
}

// GetElasticClientByCluster 根据集群名获取es client
func GetElasticClientByCluster(ctx *gin.Context, cluster string) (interface{}, error) {
	cluster = getRealElasticCluster(ctx, cluster)
	if client, ok := elasticCluster2Client[cluster]; ok {
		return client, nil
	}

	return nil, fmt.Errorf("未知或未初始化的ES集群：%s", cluster)
}

func GetElasticAddsByCluster(ctx *gin.Context, cluster string) (addr string) {
	cluster = getRealElasticCluster(ctx, cluster)
	esConf, ok := conf.RConf.Elastic[cluster]
	if !ok {
		return "未知"
	}
	return esConf.Addr
}

// getRealElasticCluster 若带有压测表示，会替换成该集群对应压测集群，如果未指定压测集群则返回默认压测集群
// 工作台压测还是读线上集群，数据链路压测读压测集群
func getRealElasticCluster(ctx *gin.Context, cluster string) string {
	if IsDataLinkPressure(ctx) {
		if v, ok := elasticClusterOnline2Pressure[cluster]; ok {
			return v
		}
		return elasticClusterPressure
	}

	return cluster
}

// IsDataLinkPressure 是否是 数据链路压测流量，数据链路压测读压测集群
// 标识：X_BD_CALLER_URI:1,/qa/test,2
func IsDataLinkPressure(ctx *gin.Context) bool {
	callerURI := GetCallerURI(ctx)
	arr := strings.Split(callerURI, ",")
	if len(arr) >= 3 {
		if arr[1] == "/qa/test" && arr[2] == "2" {
			return true
		}
	}
	return false
}

// GetCallerURI 获取callerURI
func GetCallerURI(ctx *gin.Context) (callerURI string) {
	if ctx == nil {
		return callerURI
	}
	if ctx.Request != nil {
		// 优先取header
		callerURI = ctx.GetHeader(HttpXBDCallerURI)
		if callerURI == "" {
			callerURI = ctx.GetHeader(HttpXBDCallerURIV2)
		}

		// 然后取query参数（nmq）
		if callerURI == "" {
			callerURI = ctx.Query(HttpUrlPressureCallerKey)
		}
	}

	// 然后取ctx中的k-v （nmq回调）
	if callerURI == "" {
		callerURI = ctx.GetString(HttpUrlPressureCallerKey)
	}
	return callerURI
}

const (
	HttpXBDCallerURI         = "X_BD_CALLER_URI"
	HttpXBDCallerURIV2       = "HTTP_X_BD_CALLER_URI"
	HttpUrlPressureCallerKey = "_caller_uri"
	HttpUrlPressureMarkKey   = "_press_mark"
	ZYBTransportHeader       = "X-Zyb-Ctx-"
	NavigatorOCSURI          = "/qa/test"
)
