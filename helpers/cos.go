package helpers

import (
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/pkg/golib/v2/cos"
)

var Bucket cos.Bucket
var BosBucket cos.Bucket
var BaiduBucket2 cos.Bucket
var ALIBucket cos.Bucket
var ALIBucket2 cos.Bucket

func InitCos() {
	// 按照bucket维度初始化 (注意，这仅是一个示例，如果在一个云下有多个bucket的情况，需要单独处理)
	for service, confData := range conf.RConf.Cos {
		switch confData.Cloud {
		case cos.CloudCosTencent:
			Bucket = cos.NewBucket(confData)
		case cos.CloudCosBaidu:
			switch service {
			case "bos":
				BosBucket = cos.NewBucket(confData)
			case "bos1":
				BaiduBucket2 = cos.NewBucket(confData)
			}
		case cos.CloudCosALI:
			switch service {
			case "athena":
				ALIBucket = cos.NewBucket(confData)
			case "athena2":
				ALIBucket2 = cos.NewBucket(confData)
			}
		}
	}
}
