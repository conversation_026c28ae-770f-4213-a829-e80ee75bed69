package helpers

import (
	"assistantdeskgo/conf"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func Init(engine *gin.Engine) {
	PreInit()
	InitResource(engine)

}

// 基础资源（必须）
func PreInit() {
	// 用于日志中展示模块的名字，开发环境需要手动指定，容器中无需手动指定
	env.SetAppName("assistantdeskgo")

	// 配置加载
	conf.InitConf()

	// 日志初始化
	zlog.InitLog(conf.BasicConf.Log)
}

func Clear() {
	// 服务结束时的清理工作，对应 Init() 初始化的资源
	zlog.CloseLogger()
}

func InitResourceForCron(engine *gin.Engine) {
	InitJob(engine)

	// 按需初始化资源
	InitCos()
	//InitRmq()
	InitMysql()
	InitRedis()
}

// web服务启动所需init的资源
func InitResource(engine *gin.Engine) {
	InitPromMetrics(engine)
	InitMysql()
	InitCos()
	InitGCache()
	if env.GetRunEnv() != env.RunEnvTest || env.IsDockerPlatform() {
		InitKafkaProducer()
		InitRmq()
	}
	InitJob(engine)
	InitRedis()
	InitEs()
	InitKms()
	InitApiClient()
	// 初始化全局变量
}

func Release() {
	if env.GetRunEnv() != env.RunEnvTest || env.IsDockerPlatform() {
		CloseKafkaProducer()
	}
}
