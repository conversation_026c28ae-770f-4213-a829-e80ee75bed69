package middleware

import (
	"assistantdeskgo/api/passport"
	"assistantdeskgo/components"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func CheckStudentLogin() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var studentUid int64
		studentInfo, exists := ctx.Get("studentLoginInfo")
		if !exists {
			zybuss, err := utils.GetZYBUSS(ctx)
			if err != nil || len(zybuss) == 0 {
				base.RenderJsonAbort(ctx, components.ErrorUserNotLogin)
				return
			}

			userInfo, err := passport.SafCheckLogin(ctx, zybuss)
			if err != nil {
				base.RenderJsonAbort(ctx, components.ErrorUserNotLogin)
				return
			}
			if userInfo.Uid <= 0 {
				base.RenderJsonAbort(ctx, components.ErrorUserNotLogin)
				return
			}
			ctx.Set("studentLoginInfo", &userInfo)

			studentUid = userInfo.Uid
		} else {
			userInfo := studentInfo.(passport.SafCheckLoginRsp)
			studentUid = userInfo.Uid
		}

		if studentUid <= 0 {
			base.RenderJsonAbort(ctx, components.ErrorUserNotLogin)
			return
		}
		ctx.Next()
	}
}

func GetLoginStudentInfo(ctx *gin.Context) (*passport.SafCheckLoginRsp, error) {
	studentInfoI, ok := ctx.Get("studentLoginInfo")
	if !ok {
		return nil, errors.New("未查询到登录信息")
	}
	studentInfo := studentInfoI.(*passport.SafCheckLoginRsp)
	return studentInfo, nil
}
