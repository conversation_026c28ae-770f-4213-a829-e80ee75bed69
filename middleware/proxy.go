package middleware

import (
	"assistantdeskgo/conf"
	"github.com/gin-gonic/gin"
	"sync"
)

type entryWrapper struct {
	Domain        string
	NeedCheckAuth bool
	Service       string
}

var onceInitLock sync.Once
var proxyConf = map[string]entryWrapper{}

func Proxy2BackendService() map[string]entryWrapper {
	onceInitLock.Do(func() {
		proxyConf = map[string]entryWrapper{}
		for _, ent := range conf.Custom.ApiProxy {
			for _, service := range ent.Entries {
				proxyConf[service] = entryWrapper{
					Domain:        ent.Domain,
					NeedCheckAuth: true,
					Service:       service,
				}
			}
			for _, service := range ent.EntriesNoIps {
				proxyConf[service] = entryWrapper{
					Domain:        ent.Domain,
					NeedCheckAuth: false,
					Service:       service,
				}
			}
		}
	})

	return proxyConf
}

func GetProxyTargetUrl(ctx *gin.Context) string {
	server := ctx.Param("server")
	module := ctx.Param("module")
	service := ctx.Param("service")
	path := ctx.Param("path")
	subPath := ctx.Param("subpath")

	targetUrl := "/" + server + "/" + module
	if service != "" {
		targetUrl = targetUrl + "/" + service
		if path != "" {
			targetUrl = targetUrl + "/" + path
			if subPath != "" {
				targetUrl = targetUrl + "/" + subPath
			}
		}
	}
	return targetUrl
}
