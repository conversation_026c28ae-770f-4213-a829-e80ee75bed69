package middleware

import (
	"assistantdeskgo/components"
	"bytes"
	"io/ioutil"
	"runtime/debug"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func Recover(ctx *gin.Context) {
	defer CatchRecoverRpc(ctx)
	ctx.Next()
}

// 针对rpc接口的处理
func CatchRecoverRpc(c *gin.Context) {
	// panic捕获
	if err := recover(); err != nil {
		// 请求url
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		if raw != "" {
			path = path + "?" + raw
		}
		// 请求报文
		body, _ := ioutil.ReadAll(c.Request.Body)
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		fields := []zlog.Field{
			zlog.String("requestId", zlog.GetRequestID(c)),
			zlog.String("uri", path),
			zlog.String("refer", c.Request.Referer()),
			zlog.String("clientIp", c.ClientIP()),
			zlog.String("module", env.AppName),
			zlog.String("ua", c.Request.UserAgent()),
			zlog.String("host", c.Request.Host),
			zlog.String("logId", zlog.GetLogID(c)),
			zlog.Any("err", err),
		}
		zlog.InfoLogger(c, "Panic[recover]", fields...)

		base.RenderJsonAbort(c, components.ErrorSystemError)

		debug.PrintStack()
	}
}
