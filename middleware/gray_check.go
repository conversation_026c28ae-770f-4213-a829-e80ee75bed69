package middleware

import (
	"assistantdeskgo/service/gray"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func Gray<PERSON>ey(key string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := 0
		userInfo, err := GetLoginUserInfo(c)
		if err != nil {
			wecomUser, err := GetWecomHelperUser(c)
			if err != nil {
				base.RenderJson(c, 1001, "用户未登录！", "")
				return
			}
			userId = wecomUser.UserId
		} else {
			userId = userInfo.UserId
		}
		if userId <= 0 {
			base.Render<PERSON>son(c, 1001, "用户未登录！", "")
			c.Abort()
			return
		}
		hit := gray.HitGray(c, int64(userId), key)
		if !hit {
			base.RenderJson(c, 1003, "未命中灰度", "")
			c.Abort()
			return
		}

		c.Next()
	}
}
