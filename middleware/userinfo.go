package middleware

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/userprofile"
	"errors"

	"github.com/gin-gonic/gin"
)

// 获取登录信息
func GetLoginUserInfo(ctx *gin.Context) (userInfo *userprofile.UserInfo, err error) {
	userInfoI, ok := ctx.Get("userInfo")
	if !ok {
		err = errors.New("未查询到登录信息")
		return nil, err
	}
	userInfo = userInfoI.(*userprofile.UserInfo)
	return userInfo, nil
}

func GetSelectDeviceInfo(ctx *gin.Context) (mesh.LoginDeviceInfo, error) {
	userInfo, err := GetLoginUserInfo(ctx)
	if err != nil {
		return mesh.LoginDeviceInfo{}, err
	}

	return mesh.GetSelectedDeviceByStaffUid(ctx, userInfo.UserId)
}
