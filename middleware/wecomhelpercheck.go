package middleware

import (
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/userprofile"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type WecomHelperLoginInfo struct {
	UserId    int
	CorpId    string
	RemoteId  string
	DeviceUid int64
	UserName  string
}

const WecomHelperInfo = "wecomHelperInfo"

func WecomHelperCheck() gin.HandlerFunc {
	return func(c *gin.Context) {

		userInfoI, exists := c.Get(WecomHelperInfo)
		userId := 0
		if !exists {
			cookies := c.Request.Cookies()
			remoteId, _ := c.<PERSON><PERSON>("remoteId")
			corpId, _ := c.<PERSON>ie("corpId")
			if len(cookies) == 0 {
				base.RenderJson(c, 1001, "用户未登录", "")
				c.Abort()
				return
			}
			if remoteId == "" || corpId == "" {
				base.RenderJson(c, 1001, "用户未登录(无资产信息)", "")
				c.Abort()
				return
			}

			userID, logErr := userprofile.LoginCheck(c)
			if logErr != nil {
				base.RenderJson(c, 1001, "用户未登录！", "")
				c.Abort()
				return
			}
			user, err := userprofile.GetUserInfo(c, userID)
			if err != nil {
				base.RenderJson(c, 1001, "获取登录用户信息失败", "")
				c.Abort()
				return
			}

			zlog.AddNotice(c, "userprofile user:%+v", user)

			if user == nil || user.UserId == 0 {
				base.RenderJson(c, 1001, "获取登录用户信息失败！", "")
				c.Abort()
				return
			}

			c.Set(WecomHelperInfo, WecomHelperLoginInfo{
				UserId:   user.UserId,
				RemoteId: remoteId,
				CorpId:   corpId,
				UserName: user.UserName,
			})
			userId = user.UserId
		} else {
			userInfo := userInfoI.(WecomHelperLoginInfo)
			userId = userInfo.UserId
		}
		if userId <= 0 {
			base.RenderJson(c, 1001, "登录过期！", "")
			c.Abort()
			return
		}
		//check, _ := userprofile.CheckRole(c, userId)
		//if !check {
		//	base.RenderJson(c, 1001, "用户无权限", "")
		//	c.Abort()
		//	return
		//}
		c.Next()
	}
}

func GetWecomHelperUser(ctx *gin.Context) (WecomHelperLoginInfo, error) {
	wxUser, ok := ctx.Get(WecomHelperInfo)
	if !ok {
		err := errors.New("未查询到登录信息")
		return WecomHelperLoginInfo{}, err
	}
	return wxUser.(WecomHelperLoginInfo), nil
}

func GetWecomHelperDeviceInfo(ctx *gin.Context) (WecomHelperLoginInfo, error) {
	user, err := GetWecomHelperUser(ctx)
	if err != nil {
		return WecomHelperLoginInfo{}, err
	}

	staffInfoMap, err := kunpeng.IdExchangeStaffInfo(ctx, kunpeng.IdExchangeReq{
		IdList: []string{user.RemoteId},
		Type:   3,
		CorpId: user.CorpId,
	})
	staffInfo := staffInfoMap[user.RemoteId]
	if staffInfo.StaffUid == 0 {
		return WecomHelperLoginInfo{}, errors.New("资产信息未找到")
	}
	user.DeviceUid = staffInfo.StaffUid
	return user, nil
}
