package middleware

import (
	"assistantdeskgo/api/userprofile"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

const ipsContextKey = "ips-user-id"
const ipsNameContextKey = "ips-user-name"

// IPSCheck 校验IPS会话
func IPSCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !needLoginCheck(c) {
			c.Next()
			return
		}
		_, exists := c.Get(ipsContextKey)
		if !exists {
			cookies := c.Request.Cookies()
			if len(cookies) == 0 {
				base.RenderJson(c, 1001, "用户未登录", &gin.H{})
				c.Abort()
				return
			}

			userID, userName, logErr := userprofile.LoginCheckWithUname(c)
			if logErr != nil {
				base.RenderJson(c, 1001, "用户未登录！", "")
				c.Abort()
				return
			}
			if userID <= 0 {
				base.<PERSON><PERSON><PERSON><PERSON>(c, 1001, "登录过期！", "")
				c.Abort()
				return
			}
			c.Set(ipsContextKey, int64(userID))
			c.Set(ipsNameContextKey, userName)
		}
		c.Next()
	}
}

func needLoginCheck(ctx *gin.Context) bool {
	wrapper, exist := Proxy2BackendService()[GetProxyTargetUrl(ctx)]
	if !exist {
		return true
	}
	return wrapper.NeedCheckAuth
}

func GetIpsUserInfo(c *gin.Context) int64 {
	userId, exists := c.Get(ipsContextKey)
	if !exists {
		return 0
	}

	return userId.(int64)
}

func GetIpsUserName(c *gin.Context) string {
	userId, exists := c.Get(ipsNameContextKey)
	if !exists {
		return ""
	}

	return userId.(string)
}
