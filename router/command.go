package router

import (
	c "assistantdeskgo/controllers/command"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/command"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"time"
)

/*
		命令行使用方式(可通过执行 go run main.go -h  查看)：
		goweb application

		Usage:
	  		goweb [command]

		Available Commands:
	  		job1    This is a job to do xxx
	  		job2    This is a job to do yyy

Flags:

	  -h, --help   help for goweb


		为了方便，go run main.go 默认启动http服务。
		go run main.go command  启动一个任务，比如，go run main.go job1
*/
func Commands(rootCmd *cobra.Command, engine *gin.Engine) {
	var excelAllocateCmd = &cobra.Command{
		Use:   "excelTask",
		Short: "This is a job to export",
		Run: func(cmd *cobra.Command, args []string) {
			run(engine, c.ExcelTask, args...)
		},
	}
	rootCmd.AddCommand(excelAllocateCmd)

	var newLeadNoticeCmd = &cobra.Command{
		Use:   "newLeadNotice",
		Short: "This is a job to export",
		Run: func(cmd *cobra.Command, args []string) {
			run(engine, c.NewLeadNotice, args...)
		},
	}
	rootCmd.AddCommand(newLeadNoticeCmd)

	var aiSyncDataCmd = &cobra.Command{
		Use:   "aiSyncData",
		Short: "This is a job to export",
		Run: func(cmd *cobra.Command, args []string) {
			run(engine, c.AiSync, args...)
		},
	}
	rootCmd.AddCommand(aiSyncDataCmd)

}

func run(engine *gin.Engine, f func(ctx *gin.Context, args ...string) error, args ...string) {
	// 初始化cron任务所需资源
	helpers.InitResourceForCron(engine)

	// 执行任务
	helpers.Job.RunWithRecovery(f, args...)
}

func Tasks(engine *gin.Engine) {

	startCrontab(engine)

	startCycle(engine)
}

func startCycle(engine *gin.Engine) {
	cycleJob := command.InitCycle(engine)
	cycleJob.AddFunc(time.Minute*5, c.LaxinTagDataFixTaskFallBack)
	cycleJob.AddFunc(time.Minute*10, c.PressureTask)
	cycleJob.Start()
}

func startCrontab(engine *gin.Engine) {
	cronJob := command.InitCrontab(engine)
	if err := cronJob.AddFunc("CRON_TZ=Asia/Shanghai */15 * * * *", c.AiTaskTransform); err != nil {

	}
	cronJob.Start()
}
