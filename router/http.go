package router

import (
	"assistantdeskgo/controllers/api/data"
	"assistantdeskgo/controllers/http/demo"
	"assistantdeskgo/controllers/http/filter"
	"assistantdeskgo/controllers/http/oplog"
	"assistantdeskgo/controllers/http/tool"
	"assistantdeskgo/controllers/http/wxmass"
	"github.com/gin-gonic/gin"
)

func Http(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo")

	// per group middleware! in this case we use
	// m.AddNotice() middleware just in the "courseGroup" group.
	courseGroup := router.Group("/api/demo")
	{
		courseGroup.GET("/demo", demo.Demo)
	}

	apiGroup := router.Group("/api")
	{
		captureGroup := apiGroup.Group("/capture")
		{
			captureGroup.POST("/offlinecallback", tool.OfflineCallback)
			captureGroup.POST("/createtask", tool.CreateCaptureTask)
		}
		metricsGroup := apiGroup.Group("/metrics")
		{
			metricsGroup.POST("/syncmetricscommon", data.SyncMetricsCommon)
		}
		pressureGroup := apiGroup.Group("/pressure")
		{
			pressureGroup.GET("gettaskcsv", tool.DownLoadTaskCsv)
			pressureGroup.POST("createtask", tool.CreateTask)
			pressureGroup.POST("uploaduidstxt", tool.UploadUidsTxt)
			pressureGroup.GET("fail", tool.Fail)
		}
	}

	wxMassGroup := router.Group("/wxmass")
	{
		wxMassGroup.POST("/messagehistory", wxmass.MessageHistory)
		wxMassGroup.GET("/getgroupreceivers", wxmass.GetGroupReceivers)
		wxMassGroup.GET("/getsubtaskdetail", wxmass.GetSubTaskDetail)
		wxMassGroup.GET("/retrymaintask", wxmass.RetryMainTask)
		wxMassGroup.GET("/retrygrouptask", wxmass.RetryGroupTask)
		wxMassGroup.POST("/applist", wxmass.AppList)

		// 常量接口
		wxMassGroup.GET("/gettasktypeoptions", wxmass.GetTaskTypeOptions)
		wxMassGroup.GET("/getscenetypeoptions", wxmass.GetSceneTypeOptions)
	}

	filterGroup := router.Group("/filter")
	{
		filterGroup.POST("/remoteselect", filter.RemoteSelect)
		filterGroup.POST("/pronunciationurl", filter.GetPronunciationUrl)
		filterGroup.GET("/pronunciationurl", filter.GetPronunciationUrl)
		filterGroup.POST("/voiceurl", filter.GetVoiceReportUrl)
		filterGroup.GET("/voiceurl", filter.GetVoiceReportUrl)
	}

	logGroup := router.Group("/oplog")
	{
		logGroup.POST("/add", oplog.AddLog)
		logGroup.POST("/query", oplog.QueryLog)
	}

}
