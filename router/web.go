package router

import (
	"assistantdeskgo/controllers/web"
	"assistantdeskgo/middleware"
	"github.com/gin-gonic/gin"
)

func Web(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo/web")

	router.Use(middleware.CheckStudentLogin())

	sailorGroup := router.Group("/student")
	{
		sailorGroup.GET("getcoursetimetableweek", web.GetCourseTimeTableWeek)
		sailorGroup.GET("getteacherinfo", web.GetTeacherInfo)
	}

}
