package router

import (
	"assistantdeskgo/conf"
	"assistantdeskgo/controllers/mq"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"github.com/gin-gonic/gin"
)

// mq消费者回调入口
func MQ(g *gin.Engine) {
	if env.GetRunEnv() == env.RunEnvTest && !env.IsDockerPlatform() {
		//本地环境，不启动mq
		return
	}
	// rocketMQ 消费回调handler注册 , service 需要在 helpers/init.go 中注册（InitRocketMq中的 rmq.InitRmq）。
	// 一个应用尽可能用一个Topic，而消息子类型则可以用tags来标识。tags可以由应用自由设置，
	// 只有生产者在发送消息设置了tags，消费方在订阅消息时才可以利用tags通过broker做消息过滤
	// 建议不同格式的消息使用不同的Topic, Tag主要用于对相同格式消息的进一步拆分, 方便下游快速过滤出自己需要的消息。
	for _, consumeConf := range conf.RConf.Rmq.Consumer {
		// 初始化消费者
		if err := rmq.InitConsumer(consumeConf); err != nil {
			panic("register rmq[" + consumeConf.Service + "] error: " + err.Error())
		}
	}

	// rmq 消费回调 handler 注册
	// service 参数需要与 resource.yaml 中对应 consumer 配置的 service 字段对应
	if err := rmq.StartConsumer(g, "autoCallConsumer", mq.AutoCall); err != nil {
		panic("Start autoCallConsumer error: " + err.Error())
	}
	//if env.GetRunEnv() != env.RunEnvTest {
	if true {
		//线上才走，避免线下环境无topic报错
		if err := rmq.StartConsumer(g, "callrecordConsumer", mq.CallRecord); err != nil {
			panic("Start callrecordConsumer error: " + err.Error())
		}
		if err := rmq.StartConsumer(g, "audioresultv2Consumer", mq.CallRecordTextTransform); err != nil {
			panic("Start audioresultv2Consumer error: " + err.Error())
		}
		if err := rmq.StartConsumer(g, "airesultConsumer", mq.CallRecordAIResult); err != nil {
			panic("Start airesultConsumer error: " + err.Error())
		}

		if err := rmq.StartConsumer(g, "wxCallRecordConsumer", mq.WxCallRecord); err != nil {
			panic("Start wxCallRecordConsumer182007 error: " + err.Error())
		}
	}

	err := rmq.StartConsumer(g, "self_consume", mq.DeskConsumer)
	if err != nil {
		panic("Start picTaskConsumer error: " + err.Error())
	}

	if err := rmq.StartConsumer(g, "newLeadNotice", mq.NewLead); err != nil {
		panic("Start newLeadNoticeConsumer error: " + err.Error())
	}

	err = rmq.StartConsumer(g, "laxinTagFixImport", mq.LaxinTagFixImportConsumer)
	if err != nil {
		panic("Start laxinTagFixImport error: " + err.Error())
	}

	err = rmq.StartConsumer(g, "aiAutoTag", mq.AIAutoTagConsumer)
	if err != nil {
		panic("Start aiAutoTag error: " + err.Error())
	}

	if err := rmq.StartConsumer(g, "touchLpcMsg", mq.TouchLpcMsg); err != nil {
		panic("Start touchLpcMsgConsumer error: " + err.Error())
	}

	if err := rmq.StartConsumer(g, "onceTaskConsumer", mq.DelayerTaskConsumer); err != nil {
		panic("Start onceTaskConsumer error: " + err.Error())
	}

	err = rmq.StartConsumer(g, "sceneTouchPrepare", mq.SceneTouchPrepare)
	if err != nil {
		panic("Start sceneTouchPrepare error: " + err.Error())
	}

	println("start mq success")
	// start more consumers if needed.

	// kafka 消费回调handler注册
	// DemoSubClient := kafka.InitKafkaSub(g, conf.RConf.KafkaSub["demo"])
	// DemoSubClient.AddSubFunction([]string{"topic1"}, "group1", mq.PrintOneHandler, nil)
	// DemoSubClient.AddSubFunction([]string{"topic2"}, "group2", mq.PrintTwoHandler, nil)
}
