package router

import (
	"assistantdeskgo/controllers/bailingapi"
	"assistantdeskgo/middleware"
	"github.com/gin-gonic/gin"
)

func BailingAi(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo/bailingapi")
	router.Use(middleware.IPSCheck())
	configGroup := router.Group("/config")
	{
		configGroup.GET("/functionlist", bailingapi.FunctionList)
		configGroup.GET("/getservermode", bailingapi.GetServerMode)
		configGroup.POST("/setservermode", bailingapi.SetServerMode)
		configGroup.POST("/checkuserinfo", bailingapi.CheckUserInfo)
	}
	studentGroup := router.Group("/student")
	{
		studentGroup.GET("/keybehavior", bailingapi.KeyBehavior)
	}
	grayGroup := router.Group("/gray")
	{
		grayGroup.POST("/hit", bailingapi.GrayHit)
		grayGroup.GET("/hit", bailingapi.GrayHit)
	}

	kpGroup := router.Group("/kp")
	{
		kpGroup.POST("/online", bailingapi.KpOnline)

	}
	//0转正转发的接口
	forwardGroup := router.Group("/forward")
	{
		forwardGroup.Any("/:server/:module", bailingapi.Forward)
		forwardGroup.Any("/:server/:module/:service", bailingapi.Forward)
		forwardGroup.Any("/:server/:module/:service/:path", bailingapi.Forward)
		forwardGroup.Any("/:server/:module/:service/:path/:subpath", bailingapi.Forward)
	}
}
