package router

import (
	"assistantdeskgo/controllers/api/accompany"
	"assistantdeskgo/controllers/api/apitemplate"
	"assistantdeskgo/controllers/api/assistant"
	"assistantdeskgo/controllers/api/device"
	"assistantdeskgo/controllers/api/feedbackv2"
	"assistantdeskgo/controllers/api/genke"
	"assistantdeskgo/controllers/api/gray"
	"assistantdeskgo/controllers/api/options"
	"assistantdeskgo/controllers/api/preclass"
	"assistantdeskgo/controllers/api/relation"
	"assistantdeskgo/controllers/api/scenetouch"
	"assistantdeskgo/controllers/api/staff"
	"assistantdeskgo/controllers/api/student"
	"assistantdeskgo/controllers/api/studyfeedback"
	"assistantdeskgo/controllers/api/tool"
	"assistantdeskgo/controllers/backend/bailing/interview"
	"assistantdeskgo/controllers/backend/lesson"
	"assistantdeskgo/middleware"
	"github.com/gin-gonic/gin"
)

func Api(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo/api")

	apiGroup := router.Group("/tool")
	{
		apiGroup.GET("autocall", tool.AutoCall)
		apiGroup.GET("encrypt", tool.Encrypt)
		apiGroup.GET("decrypt", tool.Decrypt)
		apiGroup.GET("banxuelist", tool.BanXueList)
		apiGroup.POST("getpinyin", tool.GetPingYin)
		apiGroup.GET("getpresignedurl", tool.GetPreSignedURL)
	}

	grayGroup := router.Group("/gray")
	{
		grayGroup.GET("hit", gray.Hit)
		grayGroup.POST("hit", gray.Hit)
		grayGroup.GET("hitusecookie", middleware.AuthCheck(), gray.HitUseCookie)
		grayGroup.GET("usergroup", gray.UserGroup)
	}

	assistantGroup := router.Group("/assistant")
	{
		assistantGroup.POST("sendgroupmessage", assistant.SendGroupMessage)
		assistantGroup.POST("touchsendwrapper", middleware.AuthCheck(), assistant.TouchSendWrapper)
		assistantGroup.POST("messagecheck", assistant.MessageCheck)
		assistantGroup.POST("list", middleware.AuthCheck(), assistant.List)
		assistantGroup.POST("messagegroupdetail", assistant.MessageGroupDetail)
		assistantGroup.POST("messagegroupdetails", assistant.MessageGroupDetails)
		assistantGroup.POST("checkmessagegrouppermission", assistant.CheckMessageGroupPermission)
		assistantGroup.POST("sidelist", middleware.AuthCheck(), assistant.SideList)

		assistantGroup.POST("/addassistantnotice", assistant.AddAssistantNotice)
	}

	sceneTouch := router.Group("/scenetouch")
	{
		sceneContext := sceneTouch.Group("/scenecontext")
		{
			sceneContext.POST("/options", scenetouch.ContextOptions)
		}
		sceneTouch.POST("/groupfilter/groupbind", scenetouch.GroupFilterGroupBind)
	}

	optionsGroup := router.Group("/options")
	{
		optionsGroup.GET("/getsecondgroupoptions", options.GetSecondGroupOptions)

		// 模版选项
		optionsGroup.GET("/gettplstyleoptions", options.GetTplStyleOptions)
	}

	templateGroup := router.Group("/template")
	{
		// 支付二维码海报
		templateGroup.POST("/getpostertemplatebygroupids", apitemplate.GetPosterTemplateByGroupIds)
	}

	deviceGroup := router.Group("/device")
	{
		deviceGroup.GET("getuserdevicelistbycourse", device.GetUserDeviceListByCourse)
	}

	staffGroup := router.Group("/staff")
	{
		staffGroup.POST("getuserproductlinegroupids", staff.GetUserProductLineGroupIds)
	}
	genkeGroup := router.Group("/genke")
	{
		genkeGroup.GET("wxcallrecord", middleware.AuthCheck(), genke.WxCallRecord)
	}

	ConfigGroup := router.Group("/config")
	{
		ConfigGroup.GET("applist", lesson.GetAppList)
	}

	// 预到课标签
	preClassGroup := router.Group("/preclass")
	{
		preClassGroup.POST("/update", preclass.UpdateMultiPreClassTag)
		preClassGroup.POST("/detail", preclass.GetPreClassDetail)
	}

	// 伴学
	accompanyGroup := router.Group("/accompany")
	{
		accompanyGroup.POST("/taginfo", accompany.GetTagInfo)
		accompanyGroup.POST("/detail", accompany.GetAccompanyDetail)
	}

	studyFeedback := router.Group("/studyfeedback")
	{
		studyFeedback.POST("/getpointbytid", studyfeedback.GetPointByTid)
		studyFeedback.POST("/getexplain", studyfeedback.GetExplain)
		studyFeedback.POST("/addpointtids", middleware.AuthCheck(), studyfeedback.AddPointTids)
		studyFeedback.POST("/addpointexplains", middleware.AuthCheck(), studyfeedback.AddPointExplains)
		studyFeedback.POST("/removepoint", middleware.AuthCheck(), studyfeedback.RemovePoint)
	}

	feedbackV2 := router.Group("/studyfeedbackv2")
	{
		pointExplain := feedbackV2.Group("/pointexplain")
		{
			pointExplain.Any("/list", feedbackv2.List)
			pointExplain.POST("/upsert", middleware.AuthCheck(), feedbackv2.Upsert)
			pointExplain.POST("/delete", middleware.AuthCheck(), feedbackv2.Delete)
		}
	}

	// ai 摘要
	aiAbstract := router.Group("/aiabstract")
	{
		aiAbstract.POST("/get", interview.AiAbstract)
	}

	// 公海
	relationGroup := router.Group("/relation")
	{
		relationGroup.GET("/getlessonlist", relation.GetLessonList)
		relationGroup.POST("/getlearnreportbyclueids", relation.GetLearnReportByClueIds)
		relationGroup.Any("/gettouchcalldatabycluedeviceid", relation.GetTouchCallDataBySingleAssistant)
		relationGroup.Any("/gettouchcalllastaccessTime", relation.GetLastAccessTimeBySingleAssistant)
		relationGroup.Any("/gettouchcalldatabyclueid", relation.GetTouchCallDataByAllAssistant)
		relationGroup.Any("/getoplogbycluedeviceid", relation.GetOpLogByClueDeviceId)
		relationGroup.GET("/resettimeoplogbycluedeviceid", relation.ResetTimeOpLogByClueDeviceId)
	}

	// 维系详情相关
	viewDetailGroup := router.Group("/viewdetail")
	{
		viewDetailGroup.POST("addjsqrecord", tool.AddJSQInterView)
		viewDetailGroup.GET("getjsqrecord", tool.GetJSQInterView)
	}

	studentGroup := router.Group("/student")
	{
		studentGroup.POST("getstudentbyphone", student.GetStudentByPhone)
		studentGroup.POST("getstudentbyuid", student.GetStudentByUid)
	}
}
