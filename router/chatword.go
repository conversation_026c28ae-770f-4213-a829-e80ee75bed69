package router

import (
	"assistantdeskgo/controllers/chatword"
	"assistantdeskgo/defines"
	"assistantdeskgo/middleware"
	"github.com/gin-gonic/gin"
)

func <PERSON>t<PERSON>(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo/chatword", middleware.Recover)

	assistantGroup := router.Group("/assistant")
	{
		assistantGroup.Use(middleware.AuthCheck())
		assistantGroup.Use(middleware.GrayKey(defines.GRAY_CHATWORD_TALK))
		assistantGroup.POST("/createcategory", chatword.CreateCategory)
		assistantGroup.POST("/deletecategory", chatword.DeleteCategory)
		assistantGroup.POST("/allcategory", chatword.AllCategory)
		assistantGroup.POST("/createtalk", chatword.CreateTalk)
		assistantGroup.POST("/hidetalk", chatword.HideTalk)
		assistantGroup.POST("/deletetalk", chatword.DeleteTalk)
		assistantGroup.GET("/listtalk", chatword.AssistantListTalk)
		assistantGroup.POST("/talkdetail", chatword.TalkDetail)
		assistantGroup.POST("/talkgroupupdate", chatword.UpdateGroup)
		assistantGroup.GET("/organizationtree", chatword.OrganizationTree) // 组织树

	}
	workGroup := router.Group("/weworkhelper")
	{
		workGroup.Use(middleware.WecomHelperCheck())
		workGroup.Use(middleware.GrayKey(defines.GRAY_CHATWORD_TALK))
		workGroup.POST("/listcategory", chatword.ListCategory)
		workGroup.POST("/getvariable", chatword.Variable)
		workGroup.POST("/listtalk", chatword.WeworkListTalk)
		workGroup.POST("/feedback", chatword.FeedBack)
		workGroup.POST("/listresource", chatword.ListResource)
		workGroup.POST("/savemd5", chatword.SaveMd5)
		workGroup.POST("/getmd5", chatword.GetMd5)
		workGroup.POST("/tomessagegroup", chatword.ToMessageGroup)
		workGroup.POST("/getstaffuid", chatword.GetStaffUid)
	}

	apiGroup := router.Group("/api")
	{
		apiGroup.POST("/talkdetail", chatword.ListTalkDetail)
		apiGroup.POST("/sendmq", chatword.SendMq)
		apiGroup.POST("/flushData", chatword.FlushGroup)
	}

}
