package router

import (
	"assistantdeskgo/controllers/ai"
	"assistantdeskgo/middleware"
	"github.com/gin-gonic/gin"
)

func Ai(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo")

	aiGroup := router.Group("/ai")
	{
		aiGroup.Use(middleware.AuthCheck())
		aiGroup.GET("/studentcallrecord", ai.StudentCallRecord)
		aiGroup.POST("/studentcallrecord", ai.StudentCallRecord)
		aiGroup.POST("/addcallfeedback", ai.AddFeedBack)
	}
	aiApiGroup := router.Group("/ai/api") //一些工具接口，页面上不会使用
	{
		aiApiGroup.GET("/getcallfeedback", ai.GetFeedBack)
		aiApiGroup.POST("/addCall", ai.AddCallRecord)
		aiApiGroup.POST("/addCallv2", ai.AddCallRecordV2)
		aiApiGroup.POST("/listcallid", ai.ListCallId)
		aiApiGroup.POST("/upgradeversion", ai.UpgradeVersion)
	}

}
