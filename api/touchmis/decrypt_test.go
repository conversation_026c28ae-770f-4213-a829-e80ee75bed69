package touchmis

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestDecrypt(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	ret, err := Decrypt(ctx, "WllCS01TMTAwMDExNzg4MDAwMDAwNTY2MzYxNmM2YzYzNjU2ZTc0NjU3MjJkNzQ2NTczNzQyZTZmNzA2NTZlNzA2YzYxNzQ2NjZmNzI2ZEJlN2RzNmU3MDZjNjE3NDY2MjNhOTZkMDQ0YzM0ZjA3ODI2NWNiNQ==")
	fmt.Println(ret, err)
}
