package touchmis

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	UriDecrypt = "/touchmis/api/decrypt"
)

type DecryptReq struct {
	EncryptStr string `json:"encryptStr" form:"encryptStr"`
}

type DecryptRsp struct {
	Ret string `json:"ret"`
}

func Decrypt(ctx *gin.Context, encryptStr string) (string, error) {
	req := DecryptReq{EncryptStr: encryptStr}

	rsp := &DecryptRsp{}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return "", err
	}

	return rsp.Ret, nil

}
