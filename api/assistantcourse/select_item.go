package assistantcourse

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type GetSelectItemReq struct {
	CourseId int64 `json:"courseId"`
}

type GetSelectItemResp struct {
	Value              string `json:"value"`
	Text               string `json:"text"`
	LessonId           int64  `json:"lessonId"`
	CourseId           int64  `json:"courseId"`
	LongCourseId       int64  `json:"longCourseId"`
	LongCourseLessonId int64  `json:"longCourseLessonId"`
}

func GetSelectItem(ctx *gin.Context, req GetSelectItemReq) (rsp *[]GetSelectItemResp, err error) {
	rsp = &[]GetSelectItemResp{}
	if err = apis.Do(ctx, req, rsp); err != nil {
		zlog.Warnf(ctx, "GetCourseTimeTable failed, params: %+v, err: %+v", req, err)
		return
	}

	return rsp, nil
}
