package agg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiRefreshLockStatus = "/agg/api/relation/refreshlockstatus"

	LockStatusOff = 0 // 未锁定
	LockStatusOn  = 1 // 已锁定
)

type RefreshLockStatusReq struct {
	StudentUid int64 `json:"studentUid" form:"studentUid"`
	CourseId   int64 `json:"courseId" form:"courseId"`
	LockStatus int64 `json:"lockStatus" form:"lockStatus"`
}

type RefreshLockStatusResp struct {
	Result bool `json:"result"`
}

// RefreshLockStatus 公海线索锁定状态变更
func RefreshLockStatus(ctx *gin.Context, req RefreshLockStatusReq) (resp RefreshLockStatusResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "RefreshLockStatus.ral failed, req:%+v, err:%+v", req, err)
		return
	}
	return
}
