package userprofile

import (
	"assistantdeskgo/api/decode"
	"assistantdeskgo/conf"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	getUsersSites         = "/userprofile/api/getuserssites"
	loginCheck            = "/userprofile/login/check"
	pathCheckRole         = "/userprofile/api/checkrole"
	getEditPermissionList = "/userprofile/user/geteditpermissionroutelist"
)

type RealPersonResult struct {
	List     []RealPerson `json:"list"`
	PageId   int          `json:"page_id"`
	PageSize int          `json:"page_size"`
	Total    int          `json:"total"`
}

type RealPerson struct {
	UserId     int    `json:"userId"`
	Name       string `json:"name"`
	Mail       string `json:"mail"`
	Phone      string `json:"phone"`
	UserName   string `json:"userName"`
	UserPhone  string `json:"userPhone"`
	PhoneType  int64  `json:"phoneType"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
}

type UserInfo struct {
	Avatar                 string  `json:"avatar"`
	ThumbnailSquareAvator  string  `json:"thumbnailSquareAvator"`
	Mail                   string  `json:"mail"`
	Name                   string  `json:"name"`
	Phone                  string  `json:"phone"`
	PhoneType              int     `json:"phoneType"`
	UserId                 int     `json:"userId"`
	UserName               string  `json:"userName"`
	UserPhone              string  `json:"userPhone"`
	StaffUid               int     `json:"staffUid"`
	PreSelectedBusinessUid int64   `json:"preSelectedBusinessUid"`
	SelectedBusinessUid    int64   `json:"selectedBusinessUid"`
	BusinessUids           []int64 `json:"businessUids"`
}

/**
根据wxMapId获取资产信息
*/

// 检查用户是否登陆
func LoginCheck(ctx *gin.Context) (userId int, err error) {
	var output struct {
		UserId   int    `json:"userId"`
		UserName string `json:"userName"`
		Uname    string `json:"uname"`
	}
	err = ralRequest(ctx, loginCheck, map[string]interface{}{}, nil, &output)
	if err != nil {
		return
	}
	return output.UserId, nil
}

func LoginCheckWithUname(ctx *gin.Context) (userId int, uname string, err error) {
	var output struct {
		UserId   int    `json:"userId"`
		UserName string `json:"userName"`
		Uname    string `json:"uname"`
	}
	err = ralRequest(ctx, loginCheck, map[string]interface{}{}, nil, &output)
	if err != nil {
		return
	}
	return output.UserId, output.Uname, nil
}

func GetUserInfo(ctx *gin.Context, uid int) (userInfo *UserInfo, err error) {
	data := map[string]interface{}{
		"userId":                     uid,
		"needGetBusinessUids":        "1",
		"needGetSelectedBusinessUid": "1",
	}

	headers := map[string]string{
		"Referer": "/assistantdesk/view/first-line-teacher/my-live",
	}
	var output struct {
		Record *UserInfo
	}
	err = ralRequest(ctx, getUsersSites, data, headers, &output)
	if err != nil {
		return
	}
	return output.Record, nil
}

func GetEditPermissionList(ctx *gin.Context, uid int) (list []string, err error) {
	data := map[string]interface{}{
		"userId": uid,
	}
	var output struct {
		List []string
	}
	err = ralRequest(ctx, getEditPermissionList, data, nil, &output)
	if err != nil {
		return
	}
	return output.List, nil
}

func ralRequest(ctx *gin.Context, urlPath string, data map[string]interface{}, headers map[string]string, output interface{}) error {
	err := encrypt(conf.API.Mesh.AppKey, data)
	if err != nil {
		return err
	}

	passport, _ := ctx.Cookie("ZYBUSS")
	ips, _ := ctx.Cookie("ZYBIPSCAS")
	skip, _ := ctx.Cookie("SKIP")
	dockerIps, _ := ctx.Cookie("DOCKERIPS")

	cookies := map[string]string{
		"ZYBIPSCAS": ips,
		"ZYBUSS":    passport,
		"SKIP":      skip,
		"DOCKERIPS": dockerIps,
	}
	opt := base.HttpRequestOptions{
		RequestBody: data,
		Cookies:     cookies,
		Headers:     headers,
	}
	resp, err := conf.API.UserProfile.HttpPost(ctx, urlPath, opt)

	if err != nil {
		zlog.Warnf(ctx, "ral failed, url[%s] Detail[%+v], err:%s", urlPath, data, err)
		return err
	}

	type LowerCaseResponse struct {
		ErrNo  int         `json:"errNo"`
		ErrStr string      `json:"errstr"`
		Data   interface{} `json:"data"`
	}

	err = decode.DecodeResponse(ctx, resp, &decode.LowerCaseResponse{}, output)
	if err != nil {
		zlog.Warnf(ctx, "decode failed, url[%s] Detail[%+v], err:%s, opt:[%+v]", urlPath, data, err, opt)
	}
	return nil
}

func encrypt(appID string, data map[string]interface{}) error {
	timestamp := time.Now().Unix()
	data["ts"] = timestamp

	appKey, err := genAppKey(appID, data)
	if err != nil {
		return err
	}

	data["appKey"] = appKey
	data["appId"] = appID

	return nil
}

func genAppKey(appID string, data map[string]interface{}) (string, error) {
	for k, v := range data {
		switch v.(type) {
		case string:
		case int64:
			data[k] = strconv.Itoa(int(v.(int64)))
		case int:
			data[k] = strconv.Itoa(v.(int))
		default:
			return "", fmt.Errorf("the type of value in param map is invalid. k:%v, v:%v, data:%v", k, v, data)
		}
	}

	bs, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// 为了和PHP的默认json_encode行为等价, 需要替换一下
	jsonStr := strings.ReplaceAll(string(bs), `/`, `\/`)

	h := md5.New()
	if _, err := io.WriteString(h, appID); err != nil {
		return "", err
	}
	if _, err := io.WriteString(h, jsonStr); err != nil {
		return "", err
	}
	appKey := fmt.Sprintf("%x", h.Sum(nil))

	return appKey, nil
}

// 检查用户是否有接口权限
func CheckRole(ctx *gin.Context, userId int) (hasRole bool, err error) {
	uri := ctx.Request.RequestURI
	uriSps := strings.Split(uri, "?")
	if len(uriSps) == 0 {
		return false, nil
	}
	path := uriSps[0]
	zlog.Info(ctx, uri, uriSps, path)

	return CheckRouteRole(ctx, userId, path)
}

func CheckRouteRole(ctx *gin.Context, userId int, route string) (hasRole bool, err error) {
	var output struct {
		HasRole bool `json:"hasRole"`
	}
	err = ralRequest(ctx, pathCheckRole, map[string]interface{}{
		"userId": userId,
		"route":  route,
	}, nil, &output)
	zlog.Infof(ctx, "CheckRouteRole userId=%v,uri=%v, err=%+v, output=%+v", userId, ctx.Request.RequestURI, err, output)
	if err != nil {
		return false, err
	}
	return output.HasRole, nil
}
