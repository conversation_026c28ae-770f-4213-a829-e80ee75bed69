package userprofile

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	apiGetGroupDetailByIds = "/userprofile/api/getgroupdetailbyids"
)

type GetGroupDetailByIdsReq struct {
	GroupIds     []int64
	GroupIdsJson string `json:"groupIds" form:"groupIds"`
	AuthParams
}

type GetGroupDetailByIdsResp struct {
	List map[string]GetGroupDetailByIdsItem `json:"list"`
}

type GetGroupDetailByIdsItem struct {
	Id         int64   `json:"id"`
	Name       string  `json:"name"`
	ManagerUid int64   `json:"managerUid"`
	Level      int64   `json:"level"`
	ParentId   int64   `json:"parentId"`
	LevelStr   string  `json:"levelStr"`
	Members    []int64 `json:"members"`
}

func GetGroupDetailByIds(ctx *gin.Context, req GetGroupDetailByIdsReq) (respMap map[int64]GetGroupDetailByIdsItem, err error) {
	req.initAuth()
	req.GroupIdsJson, _ = jsoniter.MarshalToString(req.GroupIds)
	respMap = make(map[int64]GetGroupDetailByIdsItem)
	var resp GetGroupDetailByIdsResp
	err = apis.Do(ctx, req, &resp, apis.PhpResponseUnmarshaler())
	if err != nil {
		zlog.Warnf(ctx, "GetGroupDetailByIds.ral fail, req:%+v, err:%+v", req, err)
		return
	}

	for idx, item := range resp.List {
		respMap[item.Id] = resp.List[idx]
	}
	return
}
