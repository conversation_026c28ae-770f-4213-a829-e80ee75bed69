package userprofile

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	apiGetStaffInfoByUIDs = "/userprofile/api/getstaffinfobyuids"
)

type GetStaffInfoByUIDsReq struct {
	UserIds     []int64
	UserIdsJson string `json:"userIds" form:"userIds"`
	AuthParams
}

type GetStaffInfoByUIDsResp struct {
	List map[string]GetStaffInfoByUIDsItem `json:"list"`
}

type GetStaffInfoByUIDsItem struct {
	UserName       string                    `json:"userName"`
	UserPhone      string                    `json:"userPhone"`
	UserId         int64                     `json:"userId"`
	Mail           string                    `json:"mail"`
	UserAscription string                    `json:"user_ascription"`
	Group          []GetStaffInfoByUIDsGroup `json:"group"`
}

type GetStaffInfoByUIDsGroup struct {
	StaffUid    int64 `json:"staffUid"`
	ProductLine int64 `json:"productLine"`
	GroupId     int64 `json:"groupId"`
	IsManager   int64 `json:"isManager"`
}

func GetStaffInfoByUIDs(ctx *gin.Context, req GetStaffInfoByUIDsReq) (respMap map[int64]GetStaffInfoByUIDsItem, err error) {
	req.initAuth()
	req.UserIdsJson, _ = jsoniter.MarshalToString(req.UserIds)
	respMap = make(map[int64]GetStaffInfoByUIDsItem)
	var resp GetStaffInfoByUIDsResp
	err = apis.Do(ctx, req, &resp, apis.PhpResponseUnmarshaler())
	if err != nil {
		zlog.Warnf(ctx, "GetStaffInfoByUIDs.ral fail, req:%+v, err:%+v", req, err)
		return
	}

	for idx, item := range resp.List {
		respMap[item.UserId] = resp.List[idx]
	}
	return
}
