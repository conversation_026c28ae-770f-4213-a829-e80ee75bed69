package userprofile

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtosailor"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
)

const (
	UriUserList = "/userprofile/person/userlist"
)

type GetUserListReq struct {
	UserStatus  int64  `json:"userStatus" form:"userStatus"`   // 人员状态
	PersonType  int64  `json:"personType" form:"personType"`   // 人员类型
	HrPost      int64  `json:"hrPost" form:"hrPost"`           // 人力岗位
	ProductLine int64  `json:"productLine" form:"productLine"` // 人员归属
	UserName    string `json:"userName" form:"userName"`       // 姓名
	UserPhone   int64  `json:"userPhone" form:"userPhone"`     // 真人手机号
	EmailPrefix string `json:"emailPrefix" form:"emailPrefix"` // 邮箱前缀
	StaffUid    string `json:"staffUid" form:"staffUid"`       // 真人uid
	Grade       string `json:"grade" form:"grade"`             // 学部
	GradeLevel  int64  `json:"gradeLevel" form:"gradeLevel"`   // 年级
	Subject     string `json:"subject" form:"subject"`         // 学科
	Pn          int    `json:"pn" form:"pn"`
	Rn          int    `json:"rn" form:"rn"`
}

type GetUserListRsp struct {
	List  []StaffInfo `json:"list"`
	Total int64       `json:"total"`
}

type GetUserListRspV2 struct {
	List  []StaffInfo `json:"list"`
	Total int64       `json:"total"`
}

type StaffInfo struct {
	ID             int64   `json:"id"`
	EmplID         string  `json:"emplId"`
	UserID         int64   `json:"userId"`
	StaffUID       int64   `json:"staffUid"`    // 真人UID
	PersonType     int64   `json:"personType"`  // 人员类型
	UserName       string  `json:"userName"`    // 姓名
	UserPhone      string  `json:"userPhone"`   // 真人手机号
	EntryTime      string  `json:"entryTime"`   // 入职时间
	UserStatus     int64   `json:"userStatus"`  //人员状态
	ProductLine    int64   `json:"productLine"` // 人员归属
	City           []int64 `json:"city"`        // 业务地区
	Grade          []int64 `json:"grade"`       // 学部
	GradeLevel     []int64 `json:"gradeLevel"`  // 年级
	Subject        []int64 `json:"subject"`     // 学科
	ClassPlan      int64   `json:"classPlan"`   // 带班计划
	IsBindBusiness int64   `json:"isBindBusiness"`
	BusnEmail      string  `json:"busnEmail"`
	EmailPrefix    string  `json:"emailPrefix"` // 邮箱前缀
	AssignStatus   string  `json:"assignStatus"`
	HrPost         int64   `json:"hrPost"` // 人力归属
	TrainStatus    int64   `json:"trainStatus"`
}

func getParamVal(value string) int64 {
	if len(value) == 0 {
		return defines.IsAll
	}

	val, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return defines.IsAll
	}

	return val
}
func GetUserList(ctx *gin.Context, req *dtosailor.ListReq) (rsp GetUserListRsp, err error) {
	rn := 20
	if req.Rn > 0 {
		rn = req.Rn
	}

	pn := 0
	if req.Pn > 0 {
		pn = req.Pn - 1
	}
	params := GetUserListReq{
		UserStatus:  getParamVal(req.UserStatus),
		PersonType:  getParamVal(req.PersonType),
		HrPost:      getParamVal(req.HrPost),
		ProductLine: getParamVal(req.ProductLine),
		UserName:    req.UserName,
		UserPhone:   getParamVal(req.UserPhone),
		EmailPrefix: req.EmailPrefix,
		StaffUid:    req.StaffUid,
		GradeLevel:  getParamVal(req.GradeLevel),
		Pn:          pn,
		Rn:          rn,
	}

	// 学部仅查询小学和初中
	gradeStr := req.Grade
	if req.Grade == strconv.Itoa(defines.IsAll) {
		gradeStr = ""
		for key, _ := range defines.ValidGrade {
			if key != defines.IsAll {
				gradeStr += strconv.FormatInt(key, 10) + ","
			}
		}
		gradeStr = strings.Trim(gradeStr, ",")
	}
	params.Grade = gradeStr

	subjectStr := req.Subject
	if req.Subject == strconv.Itoa(defines.IsAll) {
		subjectStr = ""
		for key, _ := range defines.SubjectTextMap {
			subjectStr += strconv.FormatInt(key, 10) + ","
		}
		subjectStr = strings.Trim(subjectStr, ",")
	}
	params.Subject = subjectStr

	passport, _ := ctx.Cookie("ZYBUSS")
	ips, _ := ctx.Cookie("ZYBIPSCAS")
	skip, _ := ctx.Cookie("SKIP")
	dockerIps, _ := ctx.Cookie("DOCKERIPS")

	cookies := map[string]string{
		"ZYBIPSCAS": ips,
		"ZYBUSS":    passport,
		"SKIP":      skip,
		"DOCKERIPS": dockerIps,
	}

	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	rsp = GetUserListRsp{}
	err = apis.Do(ctx, params, &rsp, apis.WithCookies(cookies), apis.WithHeaders(headers), apis.IgnoreInnerError())
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}
