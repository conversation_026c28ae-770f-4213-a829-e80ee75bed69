package userprofile

import (
	"assistantdeskgo/api/apis"
	"net/http"
	"time"
)

const (
	appId = "6D6ADB669D1C692E0499"
)

type AuthParams struct {
	AppId string `json:"appId" form:"appId"`
	Ts    int64  `json:"ts" form:"ts"`
}

func (ap *AuthParams) initAuth() {
	ap.AppId = appId
	ap.Ts = time.Now().Unix()
}

func init() {
	apis.Register(UriGeneralConf, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GeneralConfReq{},
		Encoder:  apis.EncoderForm,
		Response: GeneralConfRsp{},
	})
	apis.Register(UriUserList, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetUserListReq{},
		Encoder:  apis.EncoderForm,
		Response: GetUserListRsp{},
	})
	apis.Register(apiGetStaffInfoByUIDs, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetStaffInfoByUIDsReq{},
		Encoder:  apis.EncoderForm,
		Response: GetStaffInfoByUIDsResp{},
	})
	apis.Register(apiGetGroupDetailByIds, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetGroupDetailByIdsReq{},
		Encoder:  apis.EncoderForm,
		Response: GetGroupDetailByIdsResp{},
	})
}
