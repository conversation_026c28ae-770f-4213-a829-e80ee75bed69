package userprofile

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
	"strconv"
)

const (
	UriGeneralConf = "/userprofile/general/conf"
)

type GeneralConfReq struct {
}

type GeneralConfRsp struct {
	User   UserConf   `json:"user"`
	System SystemConf `json:"system"`
}

type ConfItem struct {
	StrValue string `json:"-"`
	Value    int64  `json:"value"`
	Name     string `json:"name"`
}

func (c *ConfItem) GetValue() int64 {
	val, _ := strconv.ParseInt(c.StrValue, 10, 64)
	return val
}

func (c *ConfItem) GetValueStr() string {
	return strconv.Itoa(int(c.Value))
}

type UserConf struct {
	BusinessTypeMap    []ConfItem            `json:"businessTypeMap"`
	UserAccountTypeMap []ConfItem            `json:"userAccountTypeMap"`
	UserType           []ConfItem            `json:"userType"`
	Nature             []ConfItem            `json:"nature"`
	Status             []ConfItem            `json:"status"`
	Level              []ConfItem            `json:"level"`
	IsSealMap          []ConfItem            `json:"isSealMap"`
	PersonType         []ConfItem            `json:"personType"`
	PersonStatus       []ConfItem            `json:"personStatus"`
	AssignClassPlanMap []ConfItem            `json:"assignClassPlanMap"`
	NoClassLabels      []ConfItem            `json:"noClassLabels"`
	Grade              []ConfItem            `json:"grade"`
	SubjectMap         []ConfItem            `json:"subjectMap"`
	GradeLevelMap      map[string][]ConfItem `json:"gradeLevelMap"`
	City               []ConfItem            `json:"city"`
}

type SystemConf struct {
	TypeMap         []ConfItem `json:"typeMap"`
	ProductLineMap  []ConfItem `json:"productLineMap"`
	PersonBelongMap []ConfItem `json:"personBelongMap"`
	DeviceBelongMap []ConfItem `json:"deviceBelongMap"`
	UseWxType       []ConfItem `json:"useWxType"`
}

func GetGeneralConf(ctx *gin.Context) (*GeneralConfRsp, error) {
	req := &GeneralConfReq{}
	rsp := &GeneralConfRsp{}

	passport, _ := ctx.Cookie("ZYBUSS")
	ips, _ := ctx.Cookie("ZYBIPSCAS")
	skip, _ := ctx.Cookie("SKIP")
	dockerIps, _ := ctx.Cookie("DOCKERIPS")

	cookies := map[string]string{
		"ZYBIPSCAS": ips,
		"ZYBUSS":    passport,
		"SKIP":      skip,
		"DOCKERIPS": dockerIps,
	}
	err := apis.Do(ctx, req, rsp, apis.WithCookies(cookies))
	if err != nil {
		return nil, err
	}
	return rsp, nil
}
