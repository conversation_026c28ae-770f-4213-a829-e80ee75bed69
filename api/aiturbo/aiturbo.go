package aiturbo

import (
	"assistantdeskgo/api"
	"assistantdeskgo/api/apis"
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"assistantdeskgo/defines"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	utils2 "gorm.io/gorm/utils"
	"sync"
)

const (
	PAH_SUBMIT_TEXT_ABSTRACT        = "/aiturbo/aimishu/textabstractOnline"
	PathGetAbstract                 = "/aiturbo/aimishu/selectabstract"
	PAH_SUBMIT_TEXT_ABSTRACT_NEW    = "/aiturbo/aimishu/textabstract"
	PAH_SUBMIT_TEXT_ABSTRACT_NEW_V2 = "/aiturbo/aimishu/textabstractv2"
	PathHideTags                    = "/aiturbo/aimishu/abstractHideTags"
)

type PostSelectAbstractReq struct {
	Source    string `json:"source" form:"source"`
	Token     string `json:"token" form:"token"`
	CallId    string `json:"callId" form:"callId"`
	TeacherId string `json:"teacherID" form:"teacherID"`
	Type      string `json:"type" form:"type"`
}

type PostSelectAbstractRsp struct {
	AbstractContent AbstractContent `json:"AbstractContent"`
	CallId          string          `json:"CallID"`
	CreatedAt       string          `json:"CreatedAt"`
	DeviceNumberIn  string          `json:"DeviceNumberIn"`
	DeviceNumberOut string          `json:"DeviceNumberOut"`
}

type PostSelectAbstractResult struct {
	Content string        `json:"Content"`
	Tags    []AbstractTag `json:"Tags"`
	CallId  string        `json:"CallId"`
}

type AbstractContent struct {
	Abstract string        `json:"abstract"`
	Todo     AbstractTodo  `json:"todo"`
	Tags     []AbstractTag `json:"tags"`
}

type AbstractTodo struct {
	TodoItem     string `json:"todoitem"`
	ReminderTime string `json:"remindertime"`
}

type AbstractTag struct {
	TagKey   string `json:"tagkey"`
	TagInfo  string `json:"taginfo"`
	TagValue string `json:"tagvalue"`
	HideFlag string `json:"hideflag"`
}

type PostTextAbstractReq struct {
	Source                string                       `json:"source" form:"source"`
	Token                 string                       `json:"token" form:"token"`
	Topic                 []string                     `json:"topic" form:"token"`
	TeacherId             string                       `json:"teacherID" form:"teacherID"`
	StudentUid            string                       `json:"studentUID" form:"studentUID"`
	StudentWeChatId       string                       `json:"studentWeChatID" form:"studentWeChatID"`
	DeviceNumber          []DeviceNumber               `json:"devicenumber" from:"devicenumber"`
	Xuebu                 int64                        `json:"xuebu" form:"xuebu"`
	Teacherjs             int64                        `json:"teacherjs" form:"teacherjs"`
	Calltype              int64                        `json:"calltype" form:"calltype"`
	Classname             string                       `json:"classname" form:"classname"`
	CallId                string                       `json:"callid" form:"callid"`
	CourseId              string                       `json:"courseid" form:"courseid"`
	StudentName           string                       `json:"studentname" form:"studentname"`
	ConversationStartTime string                       `json:"conversationstarttime" form:"conversationstarttime"`
	Contents              []PostTextAbstractReqContent `json:"contents"`
	PriceTag              int64                        `json:"priceTag" form:"priceTag"`
}

type DeviceNumber struct {
	Number string `json:"number"`
	Type   string `json:"type"`
}

type PostTextAbstractReqContent struct {
	Role       int    `json:"role" form:"role"`
	Sentenceid int    `json:"sentenceid" form:"sentenceid"`
	Content    string `json:"content" form:"content"`
}

type AbstractResp struct {
	ErrNo  int                     `json:"errNo"`
	ErrMsg string                  `json:"errMsg"`
	Data   []PostSelectAbstractRsp `json:"data"`
}

type HideTagReq struct {
	Source     string   `json:"source"`
	Token      string   `json:"token"`
	CallId     string   `json:"callid"`
	Hidetopics []string `json:"hidetopics"`
	TeacherId  string   `json:"teacherID" form:"teacherID"`
}

type HideTagResp struct {
	CallId     string `json:"callID"`
	DelNo      string `json:"delNo"`
	DelMessage string `json:"delMessage"`
}

func PostTextAbstract(ctx *gin.Context, req PostTextAbstractReq) error {

	var response interface{}
	if err := apis.Do(ctx, req, response); err != nil {
		zlog.Warnf(ctx, "PostTextAbstract request failed, req: %d, err: %+v", req, err)
		return err
	}

	return nil
}

func BatchPostSelectAbstract(ctx *gin.Context, abstractReqList []PostSelectAbstractReq) (data map[string]PostSelectAbstractRsp, err error) {
	ch := make(chan PostSelectAbstractRsp, len(abstractReqList))
	wg := &sync.WaitGroup{}
	for _, req := range abstractReqList {
		wg.Add(1)
		go func(req PostSelectAbstractReq) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "PostSelectAbstract panic, err:%s", r)
				}
			}()
			defer wg.Done()
			abstractInfo, reqErr := PostSelectAbstract(ctx, req)

			if reqErr != nil {
				zlog.Warnf(ctx, "GetGroupInfoByIds failed, CallId:%s, teacherId: %s, err:%s", req.CallId, req.TeacherId, err)
				return
			}
			ch <- abstractInfo
		}(req)
	}
	wg.Wait()
	close(ch)
	result := make(map[string]PostSelectAbstractRsp, len(abstractReqList))
	for abstractInfo := range ch {
		result[abstractInfo.CallId] = abstractInfo
	}
	return result, nil
}

func PostSelectAbstractByCallId(ctx *gin.Context, callId string, deviceUid int64) (result PostSelectAbstractResult, err error) {
	req := PostSelectAbstractReq{
		CallId:    callId,
		Source:    defines.AiTokenQw,
		Token:     defines.AiTokenQw,
		Type:      "getAbs",
		TeacherId: utils2.ToString(deviceUid),
	}
	resp, err := PostSelectAbstract(ctx, req)
	if err != nil {
		return
	}
	result.CallId = resp.CallId
	result.Content = resp.AbstractContent.Abstract
	if len(resp.AbstractContent.Tags) <= 0 {
		return
	}
	for _, tag := range resp.AbstractContent.Tags {
		if tag.HideFlag == "1" {
			continue
		}
		result.Tags = append(result.Tags, tag)
	}
	return
}

func PostSelectAbstract(ctx *gin.Context, req PostSelectAbstractReq) (resp PostSelectAbstractRsp, err error) {
	opt := base.HttpRequestOptions{
		RequestBody: req,
		Encode:      base.EncodeJson,
	}
	result, err := conf.API.AiTurbo.HttpPost(ctx, PathGetAbstract, opt)
	if err != nil {
		return resp, components.ErrorLongLinkSendFail.WrapPrintf(err, "RequestBody=%+v", opt.RequestBody)
	}
	var abstractResp AbstractResp
	if err = json.Unmarshal(result.Response, &abstractResp); err != nil {
		zlog.Errorf(ctx, "http response decode err, err: %s", err.Error())
		return
	}
	if len(abstractResp.Data) <= 0 {
		return
	}
	//实在不知道怎么弄了
	return abstractResp.Data[0], nil
}

func PostTextAbstractV2(ctx *gin.Context, req PostTextAbstractReq) error {
	params := utils.StructToMap(&req, "json")
	var output interface{}
	err := api.RalPost(ctx, &conf.API.AiTurbo, PAH_SUBMIT_TEXT_ABSTRACT_NEW, params, apis.EncoderJson, &output)
	if err != nil {
		zlog.Warnf(ctx, "PostTextAbstractV2 request failed, req: %d, err: %+v", req, err)
	}
	return err
}

func PostTextAbstractV3(ctx *gin.Context, req PostTextAbstractReq) error {
	params := utils.StructToMap(&req, "json")
	var output interface{}
	err := api.RalPost(ctx, &conf.API.AiTurbo, PAH_SUBMIT_TEXT_ABSTRACT_NEW_V2, params, apis.EncoderJson, &output)
	if err != nil {
		zlog.Warnf(ctx, "PostTextAbstractV2 request failed, req: %d, err: %+v", req, err)
	}
	return err
}

func HideTags(ctx *gin.Context, req HideTagReq) (resp HideTagResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		return
	}
	return
}
