package aiturbo

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(PathHideTags, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  HideTagReq{},
		Encoder:  apis.EncoderJson,
		Response: HideTagResp{},
	})
	apis.Register(PAH_SUBMIT_TEXT_ABSTRACT, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  PostTextAbstractReq{},
		Encoder:  apis.EncoderJson,
		Response: map[string]interface{}{},
	})
	apis.Register(PathGetAbstract, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  PostSelectAbstractReq{},
		Encoder:  apis.EncoderJson,
		Response: []PostSelectAbstractRsp{},
	})
}
