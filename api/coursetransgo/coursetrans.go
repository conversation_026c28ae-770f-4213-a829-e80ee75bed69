package coursetransgo

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

const (
	apiGetRelationTransByStaff    = "/coursetransgo/api/trans/get-relation-trans-by-staff"
	apiGetRelationTransByStaffMax = 300 // 例子id限制最大300
)
const (
	TransTypeCourse   = 1 // 有课转化
	TransTypeNoCourse = 2 // 无课转化
	TransTypeLink     = 3 // 售卖链接转化
)

type GetRelationTransByStaffReq struct {
	StaffUid  int64 `json:"staffUid" form:"staffUid"`
	DeviceUid int64 `json:"deviceUid" form:"deviceUid"`
	LeadsId   []int `json:"leadsId" form:"leadsId"`
	IsValid   int64 `json:"isValid" form:"isValid"`
	BeginTime int64 `json:"beginTime" form:"beginTime"`
	EndTime   int64 `json:"endTime" form:"endTime"`
	TransType int64 `json:"transType" form:"transType"`
}
type GetRelationTransByStaffResp struct {
	List  []GetRelationTransByStaffItem `json:"list"`
	Total int64                         `json:"total"`
}

type GetRelationTransByStaffItem struct {
	LeadsId   int64 `json:"leadsId"`
	IsValid   int64 `json:"isValid"`
	SubjectId int64 `json:"subjectId"`
	Refund    int64 `json:"refund"`
	TradeFee  int64 `json:"tradeFee"`
	PvCount   int64 `json:"pvCount"`
	CustomUid int64 `json:"customUid"`
}

func GetRelationTransByStaff(ctx *gin.Context, req GetRelationTransByStaffReq) (resp GetRelationTransByStaffResp, err error) {
	if len(req.LeadsId) > apiGetRelationTransByStaffMax {
		leadsArr := fwyyutils.ChunkArrayInt(req.LeadsId, apiGetRelationTransByStaffMax)
		wg := sync.WaitGroup{}
		ch := make(chan GetRelationTransByStaffResp, len(leadsArr))
		for _, arr := range leadsArr {
			wg.Add(1)
			go func(leadsIds []int) {
				defer wg.Done()
				req.LeadsId = leadsIds
				res, _err := doGetRelationTransByStaff(ctx, req)
				if _err != nil {
					zlog.Warnf(ctx, "GetRelationTransByStaff.failed, req: %+, err: %+v", req, err)
					return
				}

				ch <- res
			}(arr)
		}

		wg.Wait()
		close(ch)
		resp.List = make([]GetRelationTransByStaffItem, 0)
		for res := range ch {
			resp.List = append(resp.List, res.List...)
			resp.Total += res.Total
		}
		return
	}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetRelationTransByStaff failed, req:%+v, err: %+v", req, err)
		return
	}
	return
}

func doGetRelationTransByStaff(ctx *gin.Context, req GetRelationTransByStaffReq) (resp GetRelationTransByStaffResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "doGetRelationTransByStaff failed, req:%+v, err: %+v", req, err)
		return
	}
	return
}
