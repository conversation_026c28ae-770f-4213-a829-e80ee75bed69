package coursetransgo

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiGetLastFromList = "/coursetransgo/api/lastfrom/getlastfromlist"
	// apiGetLastFromListMax = 200 // 分页查询一次最多200，非分页查询不限制
)

type GetLastFromListReq struct {
	LastFromList []string `json:"lastFromList,omitempty"`
	SelectTotal  int64    `json:"selectTotal,omitempty"` // 0=不查总数，1=查询总数
	Pn           int64    `json:"pn,omitempty"`
	Rn           int64    `json:"rn,omitempty"` // 不传LastFromList时，做分页检索，max200
}

type GetLastFromListResp struct {
	List  []*GetLastFromListItem `json:"list"`
	Total int64                  `json:"total"`
}

type GetLastFromListItem struct {
	Id         int64  `json:"id"`
	LastFrom   string `json:"lastFrom"`
	Name       string `json:"name"`
	LabelLevel string `json:"labelLevel"`
	Remark     string `json:"remark"`
	SceneDesc  string `json:"sceneDesc"`
}

func GetLastFromList(ctx *gin.Context, req GetLastFromListReq) (resp GetLastFromListResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetLastFromList.req:%+v, err:%+v", req, err)
		return
	}
	return
}

func GetLastFromMap(ctx *gin.Context, lastFromList []string) (lastFromMap map[string]*GetLastFromListItem, err error) {
	lastFromMap = make(map[string]*GetLastFromListItem)
	if len(lastFromList) == 0 {
		return
	}

	resp, err := GetLastFromList(ctx, GetLastFromListReq{
		LastFromList: lastFromList,
	})
	if err != nil {
		return
	}

	for idx, item := range resp.List {
		lastFromMap[item.LastFrom] = resp.List[idx]
	}
	return
}
