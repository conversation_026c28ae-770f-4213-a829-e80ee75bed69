package coursetransgo

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(apiGetRelationTransByStaff, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetRelationTransByStaffReq{},
		Encoder:  apis.EncoderJson,
		Response: GetRelationTransByStaffResp{},
	})
	apis.Register(apiGetLastFromList, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLastFromListReq{},
		Encoder:  apis.EncoderJson,
		Response: GetLastFromListResp{},
	})
}
