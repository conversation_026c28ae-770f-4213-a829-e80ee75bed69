package coursebase

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	GetKeySenumMapUri = "/coursebase/api/getkeysenummap"
)

type GetKeySenumMapReq struct {
	Keys string `json:"keys" form:"keys"`
}

type GetKeySenumMapRsp struct {
	Subject []NameValuePair `json:"subject"`
}

type NameValuePair struct {
	Value interface{} `json:"value"`
	Name  string      `json:"name"`
}

func GetSubjectIds(ctx *gin.Context) (rsp *GetKeySenumMapRsp, err error) {
	req := GetKeySenumMapReq{Keys: "subject"}

	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "GetSubjectIds failed, err: %+v", err)
		return
	}

	return
}
