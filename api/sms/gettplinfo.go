package sms

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	UriGetTplInfo = "/sms/api/gettplinfo"
)

type GetTplInfoReq struct {
	TplId int64 `json:"tplId"`
}

type GetTplInfoRsp struct {
	TplId        int64  `json:"tplId"`
	Content      string `json:"content"`
	UseTimeBegin int64  `json:"useTimeBegin"`
	UseTimeEnd   int64  `json:"useTimeEnd"`
}

func GetTplInfo(ctx *gin.Context, tplId int64) (rsp *GetTplInfoRsp, err error) {
	params := GetTplInfoReq{TplId: tplId}

	rsp = &GetTplInfoRsp{}
	if err = apis.Do(ctx, params, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}
