package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UrlGetGroupReceivers = "/reach/api/touchtask/getgroupreceivers"
)

type GetGroupReceiversReq struct {
	MainTaskId  int64 `json:"mainTaskId" form:"mainTaskId"`
	GroupTaskId int64 `json:"groupTaskId" form:"groupTaskId"`
}

type GetGroupReceiversResp struct {
	ReceiverId int64 `json:"receiverId" form:"receiverId"` // 接收者
}

func GetGroupReceivers(ctx *gin.Context, req GetGroupReceiversReq) (resp []GetGroupReceiversResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetGroupReceivers failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}

	return
}
