package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UrlGetStatusCodeMap = "/reach/api/touchtask/getstatuscodemap"
)

type GetStatusCodeMapReq struct {
	AssistantUid int64   `json:"assistantUid" form:"assistantUid"`
	StatusCodes  []int64 `json:"statusCodes" form:"statusCodes"`
}

type GetStatusCodeMapResp struct {
	StatusCode int64  `json:"statusCode"`
	StatusText string `json:"statusText"`
	Solution   string `json:"solution"`
}

func GetStatusCodeMap(ctx *gin.Context, assistantUid int64, statusCodes []int64) (respList []GetStatusCodeMapResp, err error) {
	req := GetStatusCodeMapReq{}
	req.AssistantUid = assistantUid
	if statusCodes != nil && len(statusCodes) > 0 {
		req.StatusCodes = statusCodes
	}
	err = apis.Do(ctx, req, &respList)
	if err != nil {
		zlog.Warnf(ctx, "GetStatusCodeMap failed, req: %+v, resp: %+v, err: %+v", req, respList, err)
		return
	}
	return
}
