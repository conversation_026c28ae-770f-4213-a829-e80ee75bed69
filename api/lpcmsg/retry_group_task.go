package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/components"
	"github.com/gin-gonic/gin"
)

const (
	UrlRetryGroupTask = "/reach/api/touchtask/retrygrouptask"
)

type RetryGroupTaskReq struct {
	MainTaskId  int64 `json:"mainTaskId" form:"mainTaskId"`
	GroupTaskId int64 `json:"groupTaskId" form:"groupTaskId"`
}

type RetryGroupTaskResp struct {
	HasRetry bool   `json:"hasRetry"`
	Message  string `json:"message"`
}

func RetryGroupTask(ctx *gin.Context, mainTaskId, groupTaskId int64) (resp RetryGroupTaskResp, err error) {
	req := RetryGroupTaskReq{
		MainTaskId:  mainTaskId,
		GroupTaskId: groupTaskId,
	}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		err = components.DefaultError(err.<PERSON><PERSON><PERSON>())
		return
	}
	return
}
