package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UrlGetSubTaskDetail = "/reach/api/touchtask/getsubtaskdetail"
)

type GetSubTaskDetailReq struct {
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	GroupTaskId  int64  `json:"groupTaskId" form:"groupTaskId"`
	SubTaskId    int64  `json:"subTaskId" form:"subTaskId"`
	ParentTaskId string `json:"parentTaskId,omitempty" form:"parentTaskId"`
}

type GetSubTaskDetailResp struct {
	ReceiverIds []int64                `json:"receiverIds"`
	List        []GetSubTaskDetailData `json:"list"`
}

type GetSubTaskDetailData struct {
	StatusCode   int64            `json:"statusCode"`
	StatusText   string           `json:"statusText"`
	ReceiverList []TaskResultItem `json:"receiverList"`
}

type TaskResultItem struct {
	ReceiverId int64       `json:"receiverId" form:"receiverId"` // 接收者
	RemoteId   string      `json:"remoteId" form:"remoteId"`     // 接收者微信ID
	UniqueId   string      `json:"uniqueId" form:"uniqueId"`     // 接收者微信ID
	ErrCode    int64       `json:"errCode" form:"errCode"`       // 异常编码
	FailedInfo *FailedInfo `json:"failedInfo" form:"failedInfo"` // 失败标签
}

type FailedInfo struct {
	ErrCode    int64       `json:"errCode" form:"errCode"`
	ReceiverId int64       `json:"receiverId" form:"receiverId"`
	Msg        interface{} `json:"msg" form:"msg"`
	Solution   string      `json:"solution" form:"solution"`
}

func GetSubTaskDetail(ctx *gin.Context, req GetSubTaskDetailReq) (resp GetSubTaskDetailResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetSubTaskDetail failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}

	return
}
