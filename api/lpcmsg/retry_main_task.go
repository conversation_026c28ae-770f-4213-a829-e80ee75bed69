package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UrlRetryMainTask = "/reach/api/touchtask/retrymaintask"
)

type RetryMainTaskReq struct {
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	MainTaskId   int64 `json:"mainTaskId" form:"mainTaskId"`
}

type RetryMainTaskResp struct {
	HasRetry bool   `json:"hasRetry"`
	Message  string `json:"message"`
}

func RetryMainTask(ctx *gin.Context, assistantUid, mainTaskId int64) (resp RetryGroupTaskResp, err error) {
	req := RetryMainTaskReq{
		AssistantUid: assistantUid,
		MainTaskId:   mainTaskId,
	}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "RetryMainTask failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	return
}
