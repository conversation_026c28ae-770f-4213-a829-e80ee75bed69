package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UriCancelMainTask = "/reach/api/touchtask/updatemaintaskstatus"

	OperateForCancel = 4 // 取消
	OperateForStop   = 6 // 中止
)

type UpdateTaskStatusReq struct {
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	MainTaskId   int64 `json:"mainTaskId" form:"mainTaskId"`
	Operate      int   `json:"operate" form:"operate"`
}

type UpdateTaskStatusResp struct {
	MainTaskId int64 `json:"mainTaskId"`
	IsSuccess  bool  `json:"isSuccess"`
}

func UpdateTaskStatus(ctx *gin.Context, assistantUid, mainTaskId int64, operate int) (resp *UpdateTaskStatusResp, err error) {
	req := UpdateTaskStatusReq{
		AssistantUid: assistantUid,
		MainTaskId:   mainTaskId,
		Operate:      operate,
	}

	resp = &UpdateTaskStatusResp{}
	err = apis.Do(ctx, req, resp)
	if err != nil {
		zlog.Warnf(ctx, "UpdateTaskStatus failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	return
}
