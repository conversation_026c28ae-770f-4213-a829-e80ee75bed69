package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UrlFindPaged = "/reach/api/touchtask/findpaged"

	TaskStatusNone        = 0  // 待执行
	TaskStatusProcess     = 1  // 执行中
	TaskStatusSuccess     = 2  // 执行完成
	TaskStatusExpired     = 3  // 已失效
	TaskStatusCanceled    = 4  // 已取消
	TaskStatusFailure     = 5  // 执行失败
	TaskStatusStop        = 6  // 已中止
	TaskStatusPartSuccess = 98 // 部分成功

	MainTaskStatusNone         = 0  // 待执行
	MainTaskStatusSplitSuccess = 1  // 拆分完成
	MainTaskStatusSplitFailed  = 2  // 拆分失败
	MainTaskStatusSending      = 3  // 发送中
	MainTaskStatusCanceled     = 4  // 已取消
	MainTaskStatusInCancel     = 5  // 取消中
	MainTaskStatusInStop       = 6  // 中止中
	MainTaskStatusPartStop     = 7  // 部分中止
	MainTaskStatusStop         = 8  // 已中止
	MainTaskStatusSent         = 9  // 下发完成
	MainTaskStatusRetry        = 10 // 重试待发送
	MainTaskStatusStopFailed   = 11 // 中止失败
)

type FindPagedReq struct {
	AssistantUid int64   `json:"assistantUid" form:"assistantUid"`
	BeginTime    int64   `json:"beginTime" form:"beginTime"`
	EndTime      int64   `json:"endTime" form:"endTime"`
	SceneTypes   []int64 `json:"sceneTypes" form:"sceneTypes"`
	TaskType     int64   `json:"taskType" form:"taskType"`
	PageNum      int64   `json:"pageNum" form:"pageNum"`
	PageSize     int64   `json:"pageSize" form:"pageSize"`
}

type FindPagedResp struct {
	Total int64               `json:"total"`
	List  []MainTaskPagedItem `json:"list"`
}

type MainTaskPagedItem struct {
	MainTaskId int64                `json:"mainTaskId"`
	TaskType   int64                `json:"taskType"`
	SceneType  int64                `json:"sceneType"`
	CreateTime int64                `json:"createTime"`
	SendTime   int64                `json:"sendTime"`
	GroupTasks []GroupTaskPagedItem `json:"groupTasks"`
	Status     int64                `json:"status"` // 任务状态; 0: 待拆分, 1: 拆分完成, 2: 拆分失败
}

type GroupTaskPagedItem struct {
	GroupTaskId int64              `json:"groupTaskId"`
	SubTasks    []SubTaskPagedItem `json:"subTasks"`
}

type KpSubTaskItem struct {
	ParentTaskId  string              `json:"parentTaskId"`
	StatusCode    int64               `json:"statusCode"`
	SubType       int64               `json:"subType"`
	SendBeginTime int64               `json:"sendBeginTime"`
	SendEndTime   int64               `json:"sendEndTime"`
	Content       SubMessagePagedItem `json:"content"`
	ReceiverIds   []int64             `json:"receiverIds"`
}

type SubTaskPagedItem struct {
	SubTaskId     int64               `json:"subTaskId"`
	StatusCode    int64               `json:"statusCode"`
	SubType       int64               `json:"subType"`
	RefDataId     string              `json:"refDataId"`
	SendBeginTime int64               `json:"sendBeginTime"`
	SendEndTime   int64               `json:"sendEndTime"`
	AtMembers     []int64             `json:"atMembers"`
	Content       SubMessagePagedItem `json:"content"`
	KpSubTaskItem *KpSubTaskItem      `json:"kpSubTaskItem"`
}

type SubMessagePagedItem struct {
	TplType        int64           `json:"tplType"`
	MsgType        int64           `json:"msgType"`
	MsgContent     interface{}     `json:"msgContent"`
	MsgContents    []MsgContentDto `json:"msgContents"`
	DefaultContent string          `json:"defaultContent"`
}

type MsgContentDto struct {
	MsgType    int64       `json:"msgType"`    // 消息类型
	MsgContent interface{} `json:"msgContent"` // 消息内容
}

func FindPaged(ctx *gin.Context, req FindPagedReq) (resp FindPagedResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "FindPaged failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	return
}
