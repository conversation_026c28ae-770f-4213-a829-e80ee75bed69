package lpcmsg

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(UrlFindPaged, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  FindPagedReq{},
		Encoder:  apis.EncoderForm,
		Response: FindPagedResp{},
	})
	apis.Register(UrlGetGroupReceivers, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetGroupReceiversReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetGroupReceiversResp{},
	})
	apis.Register(UrlGetSubTaskDetail, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetSubTaskDetailReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetSubTaskDetailResp{},
	})
	apis.Register(UrlGetStatusCodeMap, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetStatusCodeMapReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetStatusCodeMapResp{},
	})
	apis.Register(UrlRetryMainTask, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  RetryMainTaskReq{},
		Encoder:  apis.EncoderForm,
		Response: RetryMainTaskResp{},
	})
	apis.Register(UrlRetryGroupTask, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  RetryGroupTaskReq{},
		Encoder:  apis.EncoderForm,
		Response: RetryGroupTaskResp{},
	})
	apis.Register(UriCancelMainTask, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  UpdateTaskStatusReq{},
		Encoder:  apis.EncoderForm,
		Response: UpdateTaskStatusResp{},
	})
}
