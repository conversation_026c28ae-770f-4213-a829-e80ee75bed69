package transcribe

import (
	"assistantdeskgo/conf"
	"assistantdeskgo/defines"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	pathPostTranscribe = "/transcribe"
	RealTime           = "realtime"
	Offline            = "offline"
)

type AudioTask struct {
	AudioUrl     string `json:"audio_url"`
	AudioId      string `json:"audio_id"`
	UseVad       bool   `json:"use_vad"`
	EnableCache  bool   `json:"enable_cache"`
	MultiSpeaker bool   `json:"multi_speaker"`
	Mode         string `json:"mode"`
	ResultType   string `json:"result_type"`
	ReturnType   string `json:"return_type"`
	CallBackUrl  string `json:"callback_url"`
}

type AudioReq struct {
	BusinessType string      `json:"business_type"`
	DirectResult bool        `json:"direct_result"`
	EnableCache  bool        `json:"enable_cache"`
	Tasks        []AudioTask `json:"tasks"`
}

type AudioResponse struct {
	Message []string `json:"message"`
	GroupId string   `json:"group_id"`
}

type AudioReqModel struct {
	MsgId    string `json:"msgId"`
	URL      string `json:"url"`
	Duration int64  `json:"duration"`
}

func ReqTranscribe(ctx *gin.Context, list []AudioReqModel) error {
	var tasks []AudioTask
	for _, l := range list {
		task := AudioTask{}
		task.AudioId = l.MsgId
		task.AudioUrl = l.URL
		task.UseVad = false
		task.MultiSpeaker = true
		task.EnableCache = true
		if l.Duration >= defines.THREE_MINITE_OF_MILLI_SECOND {
			task.Mode = Offline
		} else {
			task.Mode = RealTime //offline  1小时
		}
		task.ResultType = "simple"
		task.ReturnType = "message_queue"
		tasks = append(tasks, task)

	}

	if len(tasks) > 0 {
		audioReq := AudioReq{}
		audioReq.BusinessType = defines.AI_BUSSINE_TYPE
		audioReq.Tasks = tasks
		audioReq.DirectResult = true
		audioReq.EnableCache = true
		opt := base.HttpRequestOptions{
			RequestBody: audioReq,
			Encode:      base.EncodeJson,
		}

		result, err := conf.API.ASR2.HttpPost(ctx, pathPostTranscribe, opt)
		if err != nil {
			zlog.Warn(ctx, "request fail,task=%v,cause=%v", tasks, err)
			return err
		} else {
			var resp AudioResponse
			err = json.Unmarshal(result.Response, &resp)
			if err != nil {
				zlog.Warnf(ctx, "request ASR error,parse fail,cause=%v，value=%v", err, resp)
				return err
			} else {
				zlog.Infof(ctx, "request ASR success,data=%+v,groupid=%v", list, resp.GroupId)
			}
		}
	}
	return nil

}

func ReqTranscribeV2(ctx *gin.Context, list []AudioReqModel) error {
	var tasks []AudioTask
	for _, l := range list {
		task := AudioTask{}
		task.AudioId = l.MsgId
		task.AudioUrl = l.URL
		task.UseVad = false
		task.MultiSpeaker = true
		task.EnableCache = false
		if l.Duration >= defines.THREE_MINITE_OF_MILLI_SECOND {
			task.Mode = Offline
		} else {
			task.Mode = RealTime //offline  1小时
		}
		task.ResultType = "simple"
		task.ReturnType = "message_queue"
		tasks = append(tasks, task)

	}

	if len(tasks) > 0 {
		audioReq := AudioReq{}
		audioReq.BusinessType = defines.AI_BUSSINE_TYPE
		audioReq.Tasks = tasks
		audioReq.DirectResult = true
		audioReq.EnableCache = false
		opt := base.HttpRequestOptions{
			RequestBody: audioReq,
			Encode:      base.EncodeJson,
		}

		result, err := conf.API.ASR2.HttpPost(ctx, pathPostTranscribe, opt)
		if err != nil {
			zlog.Warn(ctx, "request fail,task=%v,cause=%v", tasks, err)
			return err
		} else {
			var resp AudioResponse
			err = json.Unmarshal(result.Response, &resp)
			if err != nil {
				zlog.Warnf(ctx, "request ASR error,parse fail,cause=%v，value=%v", err, resp)
				return err
			} else {
				zlog.Infof(ctx, "request ASR success,data=%+v,groupid=%v", list, resp.GroupId)
			}
		}
	}
	return nil

}
