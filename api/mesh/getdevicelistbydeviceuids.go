package mesh

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/components"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	UriGetDeviceListByDeviceUids = "/mesh/api/device/getdevicelistbydeviceuids"

	AppId        = "6D6ADB669D1C692E0499"
	KpAscription = 1 // 辅导
)

type GetDeviceListByDeviceUidsReq struct {
	DeviceUids   string `json:"deviceUids" form:"deviceUids"`
	AppId        string `json:"appId" form:"appId"`
	Ts           int64  `json:"ts" form:"ts"`
	KpAscription int64  `json:"kpAscription" form:"kpAscription"`
}

type GetDeviceListByDeviceUidsRsq struct {
	DeviceID        int64  `json:"deviceId"`
	DeviceUID       int64  `json:"deviceUid"`
	Nickname        string `json:"nickname"`
	Phone           string `json:"phone"`
	WxNum           string `json:"wxNum"`
	KpAscription    int64  `json:"kpAscription"`
	AssignClassType int64  `json:"assignClassType"`
	WecomUserID     string `json:"wecomUserId"`
	WecomCorpID     string `json:"wecomCorpId"`
}

func GetDeviceListByDeviceUid(ctx *gin.Context, deviceUid int64) (rsp *GetDeviceListByDeviceUidsRsq, err error) {
	if deviceUid == 0 {
		return nil, components.ErrorParamInvalid
	}

	deviceUids := fmt.Sprintf("[%d]", deviceUid)
	req := &GetDeviceListByDeviceUidsReq{
		DeviceUids:   deviceUids,
		AppId:        AppId,
		Ts:           time.Now().Unix(),
		KpAscription: KpAscription,
	}

	ret := make([]GetDeviceListByDeviceUidsRsq, 0)
	if err = apis.Do(ctx, req, &ret); err != nil {
		zlog.Warnf(ctx, "GetDeviceListByDeviceUid failed, deviceUid:%d, err:%s", deviceUid, err)
		return
	}

	if len(ret) > 0 {
		rsp = &ret[0]
	}
	return
}
