package mesh

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetStaffUidList(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/GolandProjects/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	defer helpers.Release()
	res, err := GetStaffInfoList(ctx, []int64{2334760063, 2474119418})
	if err != nil {
		zlog.Warnf(ctx, "mesh.GetStaffInfoList  获取老师信息失败, err:%+v ", err)
		return
	}
	zlog.Infof(ctx, "结果：%+v", res)
}

func TestGetDataPermissionByStaffUid(t *testing.T) {
	env.SetRootPath("../../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()
	ctx := gin.CreateNewContext(engine)

	ret, err := GetDataPermissionByStaffUid(ctx, 3000039349)
	fmt.Println(ret, err)
}

func TestGetGroupDetailByIds(t *testing.T) {
	env.SetRootPath("../../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()
	ctx := gin.CreateNewContext(engine)
	permission, _ := GetDataPermissionByStaffUid(ctx, 3000039349)
	ret, err := GetGroupDetailByIds(ctx, permission.GroupIds)
	fmt.Println(ret, err)
}
