package mesh

import (
	"assistantdeskgo/api/decode"
	"assistantdeskgo/conf"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"time"
)

type DeviceStaffInfo struct {
	DeviceUid int `json:"deviceUid"`
	StaffUid  int `json:"staffUid"`
}

type DeviceInfoList struct {
	List map[string]DeviceInfo `json:"list"`
}

type DeviceInfo struct {
	DeviceId         int64  `json:"deviceId"`
	DeviceUid        int    `json:"deviceUid"`
	StaffUid         int    `json:"staffUid"`
	NickName         string `json:"nickName"`
	WecomUserId      string `json:"wecomUserId"`
	WxcomCorpAgentId int64  `json:"wxcomCorpAgentId"`
	WecomCorpId      string `json:"wecomCorpId"`
	Grade            int    `json:"grade"`
	Phone            string `json:"phone"`
	KpAscriptionList []int  `json:"kpAscriptionList"`
	WxQRCodeUrl      string `json:"wxQRCodeUrl"`
}

type LoginDeviceInfo struct {
	DeviceId        int64  `json:"deviceId"`
	DeviceUid       int64  `json:"deviceUid"`
	Nickname        string `json:"nickname"`
	Phone           string `json:"phone"`
	WxNum           string `json:"wxNum"`
	KpAscription    int64  `json:"kpAscription"`
	AssignClassType int64  `json:"assignClassType"`
}

type StaffInfoList struct {
	List map[string]StaffInfo `json:"list"`
}

type StaffInfo struct {
	StaffUid       int64   `json:"staffUid"`
	DeviceUidList  []int64 `json:"deviceUidList"`
	Grade          []int64 `json:"grade"`          // 学部
	Subject        []int64 `json:"subject"`        // 学科
	EmpStatus      int64   `json:"empStatus"`      // 人员状态：1-在职，2-预离职
	UserAscription int64   `json:"userAscription"` // 人员归属
	Mail           string  `json:"mail"`           // 邮箱
	UserName       string  `json:"userName"`       // 姓名
}

func GetStaffDeviceListByDeviceUid(ctx *gin.Context, deviceUid int) ([]DeviceStaffInfo, error) {

	urlPath := "/mesh/api/device/getstaffdevicelistbydeviceuid"
	params := map[string]interface{}{
		"deviceUid": deviceUid,
		"appId":     conf.API.Mesh.AppKey,
		"ts":        time.Now().Unix(),
	}
	var output []DeviceStaffInfo

	err := ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetStaffDeviceListByDeviceUid fail,deviceUid=%v,err=%v", deviceUid, err)
		return nil, err
	}
	zlog.Infof(ctx, "GetStaffDeviceListByDeviceUid ,deviceUid=%v,result=%v", deviceUid, output)
	return output, nil
}

func GetDeviceInfoListByDeviceUids(ctx *gin.Context, deviceUid int64) (DeviceInfo, error) {

	urlPath := "/mesh/api/device/getdeviceinfolist"
	params := map[string]interface{}{
		"deviceUids": []int64{deviceUid},
		"appId":      conf.API.Mesh.AppKey,
		"ts":         time.Now().Unix(),
	}
	var output DeviceInfoList

	err := ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceInfoListByDeviceUids fail,deviceUid=%v,err=%v", deviceUid, err)
		return DeviceInfo{}, err
	}
	zlog.Infof(ctx, "GetDeviceInfoListByDeviceUids ,deviceUid=%v,result=%v", deviceUid, output)
	return output.List[cast.ToString(deviceUid)], nil
}

func GetDeviceInfoListByDeviceUidList(ctx *gin.Context, deviceUids []int64) (map[string]DeviceInfo, error) {
	urlPath := "/mesh/api/device/getdeviceinfolist"
	params := map[string]interface{}{
		"deviceUids": deviceUids,
		"appId":      conf.API.Mesh.AppKey,
		"ts":         time.Now().Unix(),
	}
	var output DeviceInfoList

	err := ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceInfoListByDeviceUidList fail,deviceUids=%v,err=%v", deviceUids, err)
		return nil, err
	}
	zlog.Infof(ctx, "GetDeviceInfoListByDeviceUidList ,deviceUids=%v,result=%v", deviceUids, output)
	return output.List, nil
}

func GetDeviceInfoListByDevicePhones(ctx *gin.Context, devicePhones []string) (map[string]DeviceInfo, error) {
	urlPath := "/mesh/api/device/getdeviceinfolist"
	params := map[string]interface{}{
		"devicePhones": devicePhones,
		"appId":        conf.API.Mesh.AppKey,
		"ts":           time.Now().Unix(),
	}
	var output DeviceInfoList

	err := ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceInfoListByDevicePhones fail,devicePhones=%v,err=%v", devicePhones, err)
		return nil, err
	}
	zlog.Infof(ctx, "GetDeviceInfoListByDevicePhones ,devicePhones=%v,result=%v", devicePhones, output)

	phoneMap := make(map[string]DeviceInfo, len(output.List))

	for _, info := range output.List {
		phoneMap[info.Phone] = info
	}

	return phoneMap, nil
}

func GetSelectedDeviceByStaffUid(ctx *gin.Context, staffUid int) (LoginDeviceInfo, error) {
	urlPath := "/mesh/api/staff/getselecteddevicebystaffuid"
	params := map[string]interface{}{
		"staffUid": staffUid,
		"appId":    conf.API.Mesh.AppKey,
		"ts":       time.Now().Unix(),
	}
	var output LoginDeviceInfo
	err := ralGet(ctx, urlPath, params, &output)
	if err != nil {
		return output, err
	}
	return output, nil
}

func GetStaffInfoList(ctx *gin.Context, staffUids []int64) (staffUid2Info map[string]StaffInfo, err error) {
	staffUid2Info = make(map[string]StaffInfo)
	if len(staffUids) == 0 {
		return
	}
	urlPath := "/mesh/api/staff/getStaffInfoList"
	params := map[string]interface{}{
		"staffUids": staffUids,
		"appId":     conf.API.Mesh.AppKey,
		"ts":        time.Now().Unix(),
	}
	var output StaffInfoList
	err = ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetStaffInfoList 异常，err：%+v", err)
		return
	}
	staffUid2Info = output.List
	return
}

func GetStaffInfoListByEmails(ctx *gin.Context, emailPrefix []string) (map[string]StaffInfo, error) {
	staffEmail2Info := make(map[string]StaffInfo)
	if len(emailPrefix) == 0 {
		return staffEmail2Info, nil
	}
	urlPath := "/mesh/api/staff/getStaffInfoList"
	params := map[string]interface{}{
		"staffMails": emailPrefix,
		"appId":      conf.API.Mesh.AppKey,
		"ts":         time.Now().Unix(),
	}
	var output StaffInfoList
	err := ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetStaffInfoList 异常，err：%+v", err)
		return nil, err
	}
	for _, info := range output.List {
		staffEmail2Info[info.Mail] = info
	}

	return staffEmail2Info, nil
}

func GetDeviceListByStaffMail(ctx *gin.Context, mail string) (output []DeviceInfo, err error) {
	urlPath := "/mesh/api/device/getdevicelistbystaffmail"
	params := map[string]interface{}{
		"mail":  mail,
		"appId": conf.API.Mesh.AppKey,
		"ts":    time.Now().Unix(),
	}

	err = ralGet(ctx, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceListByStaffMail 异常，err：%+v", err)
	}
	return
}

func ralGet(ctx *gin.Context, urlPath string, params map[string]interface{}, output interface{}) error {
	opt := base.HttpRequestOptions{
		RequestBody: params,
	}
	resp, err := conf.API.Mesh.HttpGet(ctx, urlPath, opt)
	if err != nil {
		zlog.Warnf(ctx, "ral failed, url[%s] err:%s", urlPath, err)
		return err
	}

	var apiResp decode.BzrResponse
	err = decode.DecodeResponse(ctx, resp, &apiResp, output)
	if err != nil {
		zlog.Warnf(ctx, "decode failed, url[%s] err:%s", urlPath, err)
		return err
	}

	if apiResp.ErrNo > 0 {
		zlog.Warnf(ctx, "request failed, url[%s] err:%s", urlPath, err)
		return errors.New(apiResp.ErrStr)
	}

	return nil
}

func GetDeviceInfoByWxUserId(ctx *gin.Context, wxUserId, corpId string) (result DeviceInfo, err error) {
	urlPath := "/mesh/api/device/getdeviceinfolistbywxuserid"
	params := map[string]interface{}{
		"wecomUserId": wxUserId,
		"wecomCorpId": corpId,
		"appId":       conf.API.Mesh.AppKey,
		"ts":          time.Now().Unix(),
	}
	var output DeviceInfoList
	err = ralGet(ctx, urlPath, params, &output)
	if err != nil {
		return result, err
	}
	if len(output.List) <= 0 {
		return
	}
	//此处人管接口返回理论上只有一条数据，但是偏偏用map返回，只能循环去取出一条数据
	for _, deviceInfo := range output.List {
		result = deviceInfo
	}
	return
}
