package mesh

import (
	"assistantdeskgo/api"
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"sync"
	"time"
)

const (
	getDataPermissionPath   = "/mesh/api/permission/GetDataPermissionByStaffUidNew"
	getGroupAllChildrenPath = "/mesh/api/group/getgroupallchildren"

	fudaoProductLine = "1"
	laxinProductLine = "55"

	ApiMaxNum               = 50
	NeedParentTreeForDetail = 1 //获取组织的上级树
)

var productLineList = []string{fudaoProductLine, laxinProductLine}

type permissionResponse struct {
	IsGlobal bool        `json:"isGlobal"`
	Group    interface{} `json:"group"`
}

type GroupInfo struct {
	ProductLine int     `json:"productLine"`
	GroupIds    []int64 `json:"groupIds"`
}

type Permission struct {
	IsGlobal bool `json:"isGlobal,omitempty"`
	GroupIds []int64
}

/**
 * https://yapi.zuoyebang.cc/project/3908/interface/api/268673
 * 根据真人uid和业务模块id获取用户的数据权限
 */
func GetDataPermissionByStaffUid(ctx *gin.Context, staffUid int64) (permission Permission, err error) {

	requestBody := map[string]interface{}{
		"appId":    conf.Custom.Mesh.AppId,
		"moduleId": conf.Custom.Mesh.ModuleId,
		"hasLeaf":  1, // 是否返回叶子节点的组织, 1: 返回
		"staffUid": staffUid,
		"ts":       time.Now().Unix(),
	}

	var res permissionResponse
	err = api.RalPost(ctx, &conf.API.Mesh, getDataPermissionPath, requestBody, base.EncodeForm, &res)

	if err != nil {
		zlog.Warnf(ctx, "[DataPermission_res]  : ", err)
		return
	}
	group, ok := res.Group.(map[string]interface{})
	if ok {
		var resGroupMap map[string]GroupInfo
		err = api.DecodeInterface(ctx, group, &resGroupMap)
		if err != nil {
			return Permission{}, components.ErrorDecode.Wrap(err)
		}
		permission.IsGlobal = res.IsGlobal
		//兼容配置了全量数据,但是没有选辅导侧业务线的情况
		if permission.IsGlobal {
			permission.GroupIds = conf.Custom.Mesh.GroupId
		} else {
			for _, productLine := range productLineList {
				permission.GroupIds = append(permission.GroupIds, resGroupMap[productLine].GroupIds...)
			}
		}
	}
	zlog.Infof(ctx, "GetUserInfo_permission_ret : %v", permission)

	return
}

type groupResponse struct {
	ErrNo  int               `json:"errNo"`
	ErrMsg string            `json:"errMsg"`
	Data   groupTreeResponse `json:"data"`
}

type GroupTree struct {
	Id         int         `json:"id,omitempty"`
	Name       string      `json:"name"`
	ManagerUid int         `json:"managerUid"`
	LearnType  int         `json:"learnType"`
	ConfigId   int         `json:"configId"`
	ParentId   int         `json:"parentId"`
	LevelStr   string      `json:"levelStr"`
	Children   []GroupTree `json:"children,omitempty"`
}
type groupTreeResponse struct {
	GroupTree interface{} `json:"tree"`
}

/**
 * https://yapi.zuoyebang.cc/project/3908/interface/api/252222
 * 根据组织id递归获取全部下级组织树
 */
func GetGroupAllChildren(ctx *gin.Context, groupId int64) (groupTree *GroupTree, err error) {
	opt := base.HttpRequestOptions{
		RequestBody: map[string]interface{}{
			"appId":   conf.Custom.Mesh.AppId,
			"groupId": groupId,
			"ts":      time.Now().Unix(),
		},
		Encode: base.EncodeForm,
	}

	zlog.Debug(ctx, "[GroupTree_groupId] : ", groupId)
	httpRes, err := conf.API.Mesh.HttpPost(ctx, getGroupAllChildrenPath, opt)

	zlog.Debug(ctx, "[GroupTree_groupId]  : ", httpRes)
	if err != nil {
		return
	}

	var res groupResponse
	err = json.Unmarshal(httpRes.Response, &res)
	if err != nil {
		zlog.Warnf(ctx, "GetUserInfo json err:%s,%s", err, httpRes.Response)
		return
	}
	if res.ErrNo != 0 {
		return groupTree, errors.New("获取组织树错误:" + strconv.Itoa(res.ErrNo))
	}

	// 空数组返回nil
	if _, ok := res.Data.GroupTree.([]interface{}); ok {
		return nil, nil
	}

	var data []byte
	if data, err = json.Marshal(res.Data.GroupTree); err != nil {
		return nil, err
	}

	var ret GroupTree
	if err = json.Unmarshal(data, &ret); err != nil {
		return nil, nil
	}
	return &ret, nil
}

type GroupDetailList struct {
	List map[string]GroupDetail `json:"list"`
}

type NextLevelChild struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	ManagerUID int64  `json:"managerUid"`
	LearnType  int64  `json:"learnType"`
	Level      int64  `json:"level"`
	ConfigID   int64  `json:"configId"`
	ParentID   int64  `json:"parentId"`
	LevelStr   string `json:"levelStr"`
}

type ParentTree struct {
	ID         int64       `json:"id"`
	Name       string      `json:"name"`
	ManagerUID int64       `json:"managerUid"`
	LearnType  int64       `json:"learnType"`
	Children   *ParentTree `json:"children"`
}

type GroupDetail struct {
	ID             int64            `json:"id"`
	Name           string           `json:"name"`
	ManagerUID     int64            `json:"managerUid"`
	LearnType      int64            `json:"learnType"`
	Level          int64            `json:"level"`
	ConfigID       int64            `json:"configId"`
	ParentID       int64            `json:"parentId"`
	LevelStr       string           `json:"levelStr"`
	NextLevelChild []NextLevelChild `json:"nextLevelChild"`
	ParentTree     ParentTree       `json:"parentTree"`
}

/**
 * https://yapi.zuoyebang.cc/project/3908/interface/api/250401
 *根据组织ids批量获取组织详情、上级树、儿子组织列表、该组织所有人的uids(不包括离职的人)
 */
func GetGroupDetailByIds(ctx *gin.Context, groupIds []int64) (map[string]GroupDetail, error) {
	groupIdArr := make([]int64, 0, len(groupIds))
	for _, groupId := range groupIds {
		if groupId <= 0 { //剔除异常组织ID
			continue
		}

		groupIdArr = append(groupIdArr, groupId)
	}

	chunks := utils.ChunkArrayInt64(groupIdArr, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[string]GroupDetail)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(gids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetGroupDetailByIds panic err : %+v", r)
				}
			}()
			defer wg.Done()
			ret, err := GetGroupDetailSignal(ctx, gids, NeedParentTreeForDetail)
			if err != nil {
				zlog.Warnf(ctx, "GetGroupDetailByIds failed, groupIds:%+v, err:%s", gids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[string]GroupDetail)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseTeachersBatch panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result, nil
}

func GetGroupDetailSignal(ctx *gin.Context, groupIds []int64, needParentTree int) (map[string]GroupDetail, error) {
	urlPath := "/mesh/api/group/getgroupdetailbyids"
	params := map[string]interface{}{
		"groupIds":       groupIds,
		"appId":          conf.Custom.Mesh.AppId,
		"needParentTree": needParentTree,
		"ts":             time.Now().Unix(),
	}
	var output GroupDetailList

	err := api.RalGet(ctx, &conf.API.Mesh, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetGroupDetailByIds fail,staffUid=%v,err=%v", groupIds, err)
		return nil, err
	}
	zlog.Debugf(ctx, "GetGroupDetailByIds ,staffUid=%v,result=%v", groupIds, output)
	return output.List, nil
}
