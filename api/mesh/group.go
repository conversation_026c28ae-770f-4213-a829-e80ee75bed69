package mesh

import (
	"assistantdeskgo/api"
	"assistantdeskgo/conf"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

type StaffGroupPositionsList struct {
	List map[string]PositionGroup `json:"list"`
}
type PositionGroup struct {
	StaffUid  int     `json:"staffUid"`
	GroupList []Group `json:"groupList"`
}

type Group struct {
	ProductLine      int64  `json:"productLine"`
	GroupId          int64  `json:"groupId"`
	LearnType        int64  `json:"learnType"`
	GroupName        string `json:"groupName"`
	PositionId       int64  `json:"positionId"`
	PositionName     string `json:"positionName"`
	IsManager        int64  `json:"isManager"`
	StaffGroupLeader struct {
		LeaderUid      int64  `json:"leaderUid"`
		LeaderName     string `json:"leaderName"`
		RealLeaderUid  int64  `json:"realLeaderUid"`
		RealLeaderName string `json:"realLeaderName"`
	} `json:"staffGroupLeader"`
}

type ProductLineGroupList struct {
	List []ProductLineGroup `json:"list"`
}

type ProductLineGroup struct {
	Name    string `json:"name"`
	Details string `json:"details"`
	GroupId int64  `json:"groupId"`
}

func GetStaffGroupPositions(ctx *gin.Context, staffUid int64) ([]Group, error) {

	urlPath := "/mesh/api/staff/getstaffgrouppositions"
	params := map[string]interface{}{
		"staffUids": []int64{staffUid},
		"appId":     conf.Custom.Mesh.AppId,
		"ts":        time.Now().Unix(),
	}
	var output StaffGroupPositionsList

	err := api.RalGet(ctx, &conf.API.Mesh, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetStaffGroupPositions fail,staffUid=%v,err=%v", staffUid, err)
		return nil, err
	}
	zlog.Infof(ctx, "GetStaffGroupPositions ,staffUid=%v,result=%v", staffUid, output)
	return output.List[strconv.FormatInt(staffUid, 10)].GroupList, nil
}

func GetGroupDetailById(ctx *gin.Context, groupId int64) (GroupDetail, error) {

	groupMap, err := GetGroupDetailByIds(ctx, []int64{groupId})
	if err != nil {
		return GroupDetail{}, err
	}

	detail, ok := groupMap[strconv.FormatInt(groupId, 10)]
	if !ok {
		return GroupDetail{}, errors.New("组织id不存在")
	}
	return detail, nil

}

func GetAllProductLineGroup(ctx *gin.Context) ([]ProductLineGroup, error) {

	urlPath := "/mesh/api/group/getallproductlinegroup"
	params := map[string]interface{}{
		"appId": conf.Custom.Mesh.AppId,
		"ts":    time.Now().Unix(),
	}
	var output ProductLineGroupList

	err := api.RalGet(ctx, &conf.API.Mesh, urlPath, params, &output)
	if err != nil {
		zlog.Warnf(ctx, "GetAllProductLineGroup fail,err=%v", err)
		return nil, err
	}
	return output.List, nil

}
