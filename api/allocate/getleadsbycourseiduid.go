package allocate

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	ApiGetLeadsByCourseIdUid = "/allocate/api/getleadsbycourseiduid" // https://yapi.zuoyebang.cc/project/5857/interface/api/191170

	StatusExpired = 3 // 失效

	ServiceTypeIn = 1 // 服务中
)

type GetLeadsByCourseIdUidReq struct {
	CourseId int64 `json:"courseId" form:"courseId"`
	StuUid   int64 `json:"stuUid" form:"stuUid"`
}

type GetLeadsByCourseIdUidResp struct {
	StudentUid      int64  `json:"studentUid"`
	CourseId        int64  `json:"courseId"`
	LeadsId         int64  `json:"leadsId"`
	PersonUid       int64  `json:"personUid"`
	ClassId         int64  `json:"classId"`
	Status          int64  `json:"status"`
	TradeTime       int64  `json:"tradeTime"`
	RefundTime      int64  `json:"refundTime"`
	InviterUid      int64  `json:"inviterUid"`
	ExtData         string `json:"extData"`
	UserId          int64  `json:"userId"`
	WxMapId         int64  `json:"wxMapId"`
	SaleMode        int64  `json:"saleMode"`
	AllocTime       int64  `json:"allocTime"`
	ExpireTimeStart int64  `json:"expireTimeStart"`
	ExpireTime      int64  `json:"expireTime"`
	InvalidReason   string `json:"invalidReason"`
	ServiceType     int64  `json:"serviceType"`
	IsDeleted       int64  `json:"isDeleted"`
}

func GetLeadsByCourseIdUid(ctx *gin.Context, req GetLeadsByCourseIdUidReq) (respList []GetLeadsByCourseIdUidResp, err error) {
	err = apis.Do(ctx, req, &respList)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, respList, err)
		return
	}
	return
}
