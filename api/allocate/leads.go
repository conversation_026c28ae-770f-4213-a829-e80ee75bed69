package allocate

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UriGetLeadsByUid = "/allocate/api/getleadsbyuid"

type GetLeadsByUidReq struct {
	StuUid int64 `json:"stuUid"`
}

type GetLeadsByUidRsp struct {
	List []StuLeadsInfo `json:"list"`
}

type StuLeadsInfo struct {
	LeadsId    int   `json:"leadsId"`
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	UserId     int64 `json:"userId"`
	PersonId   int64 `json:"personId"`
}

func GetLeadsByUid(ctx *gin.Context, studentUid int64) (result []StuLeadsInfo, err error) {
	req := GetLeadsByUidReq{
		StuUid: studentUid,
	}
	resp := GetLeadsByUidRsp{}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	result = resp.List
	return
}

func GetCourseIdByLeads(leadsList []StuLeadsInfo, assistantUid int64) int64 {
	var courseId int64
	if len(leadsList) <= 0 {
		return courseId
	}
	for _, leadsInfo := range leadsList {
		if leadsInfo.UserId == assistantUid {
			courseId = leadsInfo.CourseId
			break
		}
	}
	return courseId
}
