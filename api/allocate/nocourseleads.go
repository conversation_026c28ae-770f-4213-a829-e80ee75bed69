package allocate

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const NocourseLeadsUrl = "/allocate/api/getvalidnocourseleadslistbyLeadsIds"
const apiNoCourseLeadsEntrance = "/allocate/api/nocourseleadsentrance"
const getValidNoCourseLeadsListApi = "/allocate/api/getvalidnocourseleadslist"

type NocourseLeadsReq struct {
	LeadsIds []int64 `json:"leadsIds"`
}

type NocourseLeadsRsp struct {
	List []NocourseLeadsData `json:"list"`
}

type NocourseLeadsData struct {
	LeadsId    int   `json:"leadsId"`
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	UserId     int64 `json:"userId"`
	PersonId   int64 `json:"personId"`
}

func GetNocourseLeadsData(ctx *gin.Context, leadsIds []int64) (result []NocourseLeadsData, err error) {
	req := NocourseLeadsReq{
		LeadsIds: leadsIds,
	}
	resp := NocourseLeadsRsp{}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	result = resp.List
	return
}

const (
	LeadsOriginOther        = 0 // 例子录入来源=其他
	LeadsOriginPC           = 1 // 例子录入来源=lpc、pc端
	LeadsOriginCoinExchange = 2 // 金币兑换转介绍录入
	LeadsOriginPCBatch      = 3 // lpc批量回捞于pc端

	LeadsSource = 10 // 公海例子
)

type NoCourseLeadsEntranceReq struct {
	StudentUid    int64  `json:"studentUid" form:"studentUid"`
	CourseId      int64  `json:"courseId" form:"courseId"`
	Grade         int64  `json:"grade" form:"grade"`
	ActivityId    int64  `json:"activityId" form:"activityId"`
	DeviceUid     int64  `json:"deviceUid" form:"deviceUid"`
	SubscribeInfo string `json:"subscribeInfo" form:"subscribeInfo"`
}

type NoCourseLeadsEntranceResp struct {
	Ret bool `json:"ret"`
}

func NoCourseLeadsEntrance(ctx *gin.Context, req NoCourseLeadsEntranceReq) (resp NoCourseLeadsEntranceResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "NoCourseLeadsEntrance.failed, req:%+v, err:%+v", req, err)
		return
	}
	return
}

type GetValidNoCourseLeadsListReq struct {
	DeviceUid int64 `json:"deviceUid" form:"deviceUid"`
	Source    int64 `json:"source" form:"source"`
	Status    int64 `json:"status" form:"status"` // 不传取有效例子，-1查全部例子
}

type GetValidNoCourseLeadsListResp struct {
	List []*GetValidNoCourseLeadsItem `json:"list"`
}

type GetValidNoCourseLeadsItem struct {
	StudentUid int64  `json:"studentUid"`
	LeadsId    int64  `json:"leadsId"`
	CourseId   int64  `json:"courseId"`
	AllocTime  int64  `json:"allocTime"`
	ExtData    string `json:"extData"`
}

func GetValidNoCourseLeadsList(ctx *gin.Context, req GetValidNoCourseLeadsListReq) (resp GetValidNoCourseLeadsListResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetValidNoCourseLeadsList.failed, req:%+v, err:%+v", req, err)
		return
	}
	return
}
