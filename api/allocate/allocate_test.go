package allocate

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func getCtx() *gin.Context {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	return ctx
}

func TestNoCourseLeadsEntrance(t *testing.T) {
	defer helpers.Release()
	ctx := getCtx()

	req := NoCourseLeadsEntranceReq{
		StudentUid:    1,
		CourseId:      2,
		Grade:         0,
		ActivityId:    2,
		DeviceUid:     1,
		SubscribeInfo: "{\"leadsOrigin\":1}",
	}
	_, err := NoCourseLeadsEntrance(ctx, req)
	fmt.Println(err)
}
