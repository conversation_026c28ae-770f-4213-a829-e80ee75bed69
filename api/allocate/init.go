package allocate

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(UriGetLeadsByUid, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLeadsByUidReq{},
		Encoder:  apis.EncoderJson,
		Response: GetLeadsByUidRsp{},
	})
	apis.Register(ApiGetLeadsByCourseIdUid, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetLeadsByCourseIdUidReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetLeadsByCourseIdUidResp{},
	})
	apis.Register(PublicSeaDataUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetPublicSeaDataReq{},
		Encoder:  apis.EncoderJson,
		Response: GetPublicSeaDataRsp{},
	})
	apis.Register(NocourseLeadsUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  NocourseLeadsReq{},
		Encoder:  apis.EncoderJson,
		Response: NocourseLeadsRsp{},
	})
	apis.Register(apiNoCourseLeadsEntrance, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  NoCourseLeadsEntranceReq{},
		Encoder:  apis.EncoderForm,
		Response: NoCourseLeadsEntranceResp{},
	})
	apis.Register(getValidNoCourseLeadsListApi, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetValidNoCourseLeadsListReq{},
		Encoder:  apis.EncoderForm,
		Response: GetValidNoCourseLeadsListResp{},
	})
}
