package allocate

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const PublicSeaDataUrl = "/allocate/api/getpublicseadata"

type GetPublicSeaDataReq struct {
	CourseId   int64 `json:"courseId"`
	StudentUid int64 `json:"studentUid"`
}

type GetPublicSeaDataRsp struct {
	List []PublicSeaData `json:"list"`
}

type PublicSeaData struct {
	LeadsId    int   `json:"leadsId"`
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	UserId     int64 `json:"userId"`
	PersonId   int64 `json:"personId"`
}

func GetPublicSeaData(ctx *gin.Context, studentUid int64) (result []PublicSeaData, err error) {
	req := GetLeadsByUidReq{
		StuUid: studentUid,
	}
	resp := GetPublicSeaDataRsp{}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	result = resp.List
	return
}
