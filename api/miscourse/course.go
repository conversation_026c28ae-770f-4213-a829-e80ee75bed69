package miscourse

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	sid = "66e6ca07bd0f287b854c28a9aefc547f"

	UrlBatchGetGiftCourseInfo = "/miscourse/api/batchgetgiftcourseinfo"
)

type BatchGetGiftCourseInfoParams struct {
	Sid       string `json:"sid" form:"sid"`
	CourseIds string `json:"courseIds" form:"courseIds"`
}

type BatchGetGiftCourseInfoRet map[int64]GiftInfo

type GiftInfo struct {
	GiftCourseIds []int64 `json:"giftCourseIds"`
	MainCourseIds []int64 `json:"mainCourseIds"`
}

// 一次支持200个courseId
func BatchGetGiftCourseInfo(ctx *gin.Context, courseIds []int64) (ret BatchGetGiftCourseInfoRet, err error) {
	param := BatchGetGiftCourseInfoParams{
		Sid:       sid,
		CourseIds: utils.JoinArrayInt64ToString(courseIds, ","),
	}
	err = apis.Do(ctx, param, &ret, apis.PhpResponseUnmarshaler())
	zlog.Infof(ctx, "BatchGetGiftCourseInfo ,CourseIds=%v,result=%v", courseIds, ret)
	return
}
