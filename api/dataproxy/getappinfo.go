package dataproxy

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const UrlGetLastAppLoginInfo = "/dataproxy/app/last-login-info"

type GetLastAppLoginInfoReq struct {
	AppIds     string `json:"appIds"`
	StudentUid int64  `json:"studentUid"`
	Fields     string `json:"fields"`
}

type GetLastAppLoginInfoRsp struct {
	List  []AppLoginInfo `json:"list" mapstructure:"list"`
	Total int64          `json:"total" mapstructure:"total"`
}
type AppLoginInfo struct {
	App          string `json:"app" mapstructure:"app"`
	UserId       int64  `json:"user_id" mapstructure:"user_id"`
	LogTimestamp int64  `json:"log_timestamp" mapstructure:"log_timestamp"`
	AppName      string `json:"app_name" mapstructure:"app_name"`
	MC           string `json:"mc" mapstructure:"mc"`
	CUID         string `json:"cuid" mapstructure:"cuid"`
}

func GetLastAppLoginList(ctx *gin.Context, req GetLastAppLoginInfoReq) (rsp GetLastAppLoginInfoRsp, err error) {
	rsp = GetLastAppLoginInfoRsp{}
	if err = apis.Do(ctx, &req, &rsp, apis.IgnoreInnerError()); err != nil {
	}
	return
}
