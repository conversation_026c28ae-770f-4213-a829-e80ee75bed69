package dataproxy

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

const (
	ApiGetLpcListByStudentsLessons = "/dataproxy/lu/get-lpc-list-by-students-lessons"
)

func getLpcListByStudentsLessonsFields() []string {
	return []string{
		"course_id",
		"lesson_id",
		"student_uid",
		"lesson_start_time",
		"lesson_stop_time",
		"attend_duration",
		"playback_time",
		"leads_id",
		"main_department",
		"last_playback_time",
		"is_playback",
		"is_playback_finish",
		"is_ai_attend",
		"is_ai_finish",
		"play_type",
		"is_lbp_attend",
		"is_lbp_attend_finish",
		"attend",
		"is_attend_finish",
		"is_unlock_playback_attend",
		"is_unlock_playback_finish",
		"main_subject",
	}
}

type GetLpcListByStudentsLessonsReq struct {
	StudentUids string `form:"studentUids"`
	LessonIds   string `form:"lessonIds"`
	IsPlayback  string `form:"isPlayback"`
	Fields      string `form:"fields"`
}

type GetLpcListByStudentsLessonsResp struct {
	List  []GetLpcListByStudentsLessonsInfo `json:"list" mapstructure:"list"`
	Total int64                             `json:"total" mapstructure:"total"`
}

type GetLpcListByStudentsLessonsInfo struct {
	CourseId               int64 `json:"course_id" mapstructure:"course_id"`
	LessonId               int64 `json:"lesson_id" mapstructure:"lesson_id"`
	StudentUid             int64 `json:"student_uid" mapstructure:"student_uid"`
	LessonStartTime        int64 `json:"lesson_start_time" mapstructure:"lesson_start_time"`
	LessonStopTime         int64 `json:"lesson_stop_time" mapstructure:"lesson_stop_time"`
	AttendDuration         int64 `json:"attend_duration" mapstructure:"attend_duration"`
	PlaybackTime           int64 `json:"playback_time" mapstructure:"playback_time"`
	LeadsId                int64 `json:"leads_id" mapstructure:"leads_id"`
	MainDepartment         int64 `json:"main_department" mapstructure:"main_department"`
	LastPlaybackTime       int64 `json:"last_playback_time" mapstructure:"last_playback_time"`
	IsPlayback             int64 `json:"is_playback" mapstructure:"is_playback"`
	IsPlaybackFinish       int64 `json:"is_playback_finish" mapstructure:"is_playback_finish"`
	IsAiAttend             int64 `json:"is_ai_attend" mapstructure:"is_ai_attend"`
	IsAiFinish             int64 `json:"is_ai_finish" mapstructure:"is_ai_finish"`
	PlayType               int64 `json:"play_type" mapstructure:"play_type"`
	IsLbpAttend            int64 `json:"is_lbp_attend" mapstructure:"is_lbp_attend"`
	IsLbpAttendFinish      int64 `json:"is_lbp_attend_finish" mapstructure:"is_lbp_attend_finish"`
	Attend                 int64 `json:"attend" mapstructure:"attend"`
	IsAttendFinish         int64 `json:"is_attend_finish" mapstructure:"is_attend_finish"`
	IsUnlockPlaybackAttend int64 `json:"is_unlock_playback_attend" mapstructure:"is_unlock_playback_attend"`
	IsUnlockPlaybackFinish int64 `json:"is_unlock_playback_finish" mapstructure:"is_unlock_playback_finish"`
	MainSubject            int64 `json:"main_subject" mapstructure:"main_subject"`
}

func GetLpcListByStudentsLessons(ctx *gin.Context, studentUids, lessonIds []int64, fields []string) (resp GetLpcListByStudentsLessonsResp, err error) {
	if len(studentUids) == 0 || len(lessonIds) == 0 {
		return
	}
	if len(fields) == 0 {
		fields = getLpcListByStudentsLessonsFields()
	}

	req := GetLpcListByStudentsLessonsReq{
		StudentUids: fwyyutils.JoinArrayInt64ToString(studentUids, splitComma),
		LessonIds:   fwyyutils.JoinArrayInt64ToString(lessonIds, splitComma),
		Fields:      strings.Join(fields, splitComma),
	}
	if err = apis.Do(ctx, req, &resp); err != nil {
		zlog.Warnf(ctx, "GetLpcListByStudentsLessons request failed, req: %d, err: %+v", req, err)
		return
	}
	return
}
