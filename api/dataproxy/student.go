package dataproxy

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

const (
	apiGetUData = "/dataproxy/student/get-u-data"
)

type GetUDataReq struct {
	StudentUids string `json:"studentUids" form:"studentUids"`
	Fields      string `json:"fields" form:"fields"`
}

type GetUDataResp struct {
	List  []GetUDataItem `json:"list" mapstructure:"list"`
	Total int64          `json:"total" mapstructure:"total"`
}

type GetUDataItem struct {
	StudentUid         int64 `json:"student_uid" mapstructure:"student_uid"`
	LpcTradeNum        int64 `json:"lpc_trade_num" mapstructure:"lpc_trade_num"`
	LpcTradeNum6mon    int64 `json:"lpc_trade_num_6mon" mapstructure:"lpc_trade_num_6mon"`
	LpcTradeNum1y      int64 `json:"lpc_trade_num_1y" mapstructure:"lpc_trade_num_1y"`
	LastLpcTradeTime   int64 `json:"last_lpc_trade_time" mapstructure:"last_lpc_trade_time"`
	BankeTradeTime     int64 `json:"banke_trade_time" mapstructure:"banke_trade_time"`
	LastBankeTradeTime int64 `json:"last_banke_trade_time" mapstructure:"last_banke_trade_time"`
	UserLabel          int64 `json:"user_label" mapstructure:"user_label"`
}

func getUDataFields() []string {
	return []string{
		"student_uid",
		"lpc_trade_num",
		"lpc_trade_num_6mon",
		"lpc_trade_num_1y",
		"last_lpc_trade_time",
		"banke_trade_time",
		"last_banke_trade_time",
		"user_label",
	}
}

func GetUData(ctx *gin.Context, studentUids []int64, fields []string) (respMap map[int64]GetUDataItem, err error) {
	respMap = make(map[int64]GetUDataItem)
	if len(studentUids) == 0 {
		return
	}
	if len(fields) == 0 {
		fields = getUDataFields()
	}

	req := GetUDataReq{
		StudentUids: fwyyutils.JoinArrayInt64ToString(studentUids, ","),
		Fields:      strings.Join(fields, ","),
	}

	var resp GetUDataResp
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetUData.ral failed, req:%+v, err:%+v")
		return
	}

	for _, item := range resp.List {
		respMap[item.StudentUid] = item
	}
	return
}
