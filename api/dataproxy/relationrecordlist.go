package dataproxy

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	PathRecordListUrl = "/dataproxy/relation/get-external-idl-serviceend-record-data-by-assistantUid"
)

type RecordListReq struct {
	Fields     []string               `json:"fields"` // 不传查所有列
	Filter     map[string]interface{} `json:"filter"`
	SelectType int64                  `json:"selectType"` // 0=查list，1=查total，2=查list和total
	Pn         int64                  `json:"pn"`
	Rn         int64                  `json:"rn"`
}

type RecordListResp struct {
	List  []RecordInfo `json:"list" mapstructure:"list"`
	Total int64        `json:"total" mapstructure:"total"`
}

type RecordInfo struct {
	ClueId               string `json:"clue_id" mapstructure:"clue_id"`
	StudentUid           int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId             int64  `json:"course_id" mapstructure:"course_id"`
	LeadsId              int64  `json:"leads_id" mapstructure:"leads_id"`
	PhoneAfter           string `json:"phone_after" mapstructure:"phone_after"`
	AssistantUid         int64  `json:"assistant_uid" mapstructure:"assistant_uid"`
	Batch                string `json:"batch" mapstructure:"batch"`
	RetrieveType         int64  `json:"retrieve_type" mapstructure:"retrieve_type"`
	TransStatus          int64  `json:"trans_status" mapstructure:"trans_status"`
	TransSubject         string `json:"trans_subject" mapstructure:"trans_subject"`
	AddWxStatus          int64  `json:"add_wx_status" mapstructure:"add_wx_status"`
	MainGradeId          int64  `json:"main_grade_id" mapstructure:"main_grade_id"`
	MainSubjectId        int64  `json:"main_subject_id" mapstructure:"main_subject_id"`
	YearSeason           string `json:"year_season" mapstructure:"year_season"`
	SeasonSemester       string `json:"season_semester" mapstructure:"season_semester"`
	LastFrom             string `json:"last_from" mapstructure:"last_from"`
	ProvinceName         string `json:"province_name" mapstructure:"province_name"`
	CityName             string `json:"city_name" mapstructure:"city_name"`
	CityLevel            int64  `json:"city_level" mapstructure:"city_level"`
	UserType             int64  `json:"user_type" mapstructure:"user_type"`
	CallrecordConnectCnt int64  `json:"callrecord_connect_cnt" mapstructure:"callrecord_connect_cnt"`
	CallrecordTotalCnt   int64  `json:"callrecord_total_cnt" mapstructure:"callrecord_total_cnt"`
	AllocateTime         int64  `json:"allocate_time" mapstructure:"allocate_time"`
	CreateTime           int64  `json:"create_time" mapstructure:"create_time"`
	UpdateTime           int64  `json:"update_time" mapstructure:"update_time"`
}

func getRecordListEsFields() []string {
	return []string{
		"clue_id",
		"student_uid",
		"course_id",
		"leads_id",
		"phone_after",
		"assistant_uid",
		"batch",
		"retrieve_type",
		"trans_status",
		"trans_subject",
		"add_wx_status",
		"main_grade_id",
		"main_subject_id",
		"year_season",
		"season_semester",
		"last_from",
		"province_name",
		"city_name",
		"city_level",
		"user_type",
		"callrecord_connect_cnt",
		"callrecord_total_cnt",
		"allocate_time",
		"create_time",
		"update_time",
	}
}

func RecordList(ctx *gin.Context, req RecordListReq) (resp RecordListResp, err error) {
	if len(req.Fields) == 0 {
		req.Fields = getRecordListEsFields()
	}
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "RecordList.ral failed, req:%+v, err:%+v", req, err)
		return
	}
	return
}
