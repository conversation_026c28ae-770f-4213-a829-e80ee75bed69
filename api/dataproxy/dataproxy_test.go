package dataproxy

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func getCtx() *gin.Context {
	env.SetRootPath("../../")
	gin.SetMode(gin.TestMode)
	engine := gin.New()
	helpers.Init(engine)
	return gin.CreateNewContext(engine)
}

func TestGetUData(t *testing.T) {
	ctx := getCtx()

	data, err := GetUData(ctx, []int64{2135495067}, []string{})
	fmt.Println(err)
	fmt.Println(data)
}
