package dataproxy

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

const (
	apiGetLearningReportByStudentCourse = "/dataproxy/cu/get-learningreport-by-student-course"
)

type GetLearningReportByStudentCourseReq struct {
	StudentUids string `form:"studentUids"`
	CourseId    int64  `form:"courseId"`
	Fields      string `form:"fields"`
}

type GetLearningReportByStudentCourseResp struct {
	List  []GetLearningReportByStudentCourseItem `json:"list"`
	Total int64                                  `json:"total"`
}

type GetLearningReportByStudentCourseItem struct {
	StudentUid          int64 `json:"student_uid" mapstructure:"student_uid"`
	CourseId            int64 `json:"course_id" mapstructure:"course_id"`
	SubmissionIdLastest int64 `json:"submission_id_lastest" mapstructure:"submission_id_lastest"`
}

func getLearningReportByStudentCourseFields() []string {
	return []string{
		"student_uid",
		"course_id",
		"submission_id_lastest",
	}
}

func GetLearningReportByStudentCourse(ctx *gin.Context, studentUids []int64, courseId int64, fields []string) (resp GetLearningReportByStudentCourseResp, err error) {
	if len(studentUids) == 0 {
		return
	}
	if len(fields) == 0 {
		fields = getLearningReportByStudentCourseFields()
	}

	req := GetLearningReportByStudentCourseReq{
		StudentUids: fwyyutils.JoinArrayInt64ToString(studentUids, splitComma),
		CourseId:    courseId,
		Fields:      strings.Join(fields, splitComma),
	}
	if err = apis.Do(ctx, req, &resp); err != nil {
		zlog.Warnf(ctx, "GetLearningReportByStudentCourse request failed, req: %d, err: %+v", req, err)
		return
	}
	return
}
