package mercury

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/helpers"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"reflect"
)

const (
	UriObjectGet      = "/mercury/object/get"
	DefaultExpireTime = 60 * 5
	ConfigCacheKeyPre = "assistantdeskgo_mercury_"

	ConfigKeyForWxSend  = "touch_send_wxmessage_config"
	ConfigKeyForDingBot = "assistantdeskgo_ding_bot_config"

	ConfigKeyForPublicSea   = "public_sea_touch_config"
	ConfigKeyForNoticeScope = "deskgo_system_notice_scope"
)

type ConfigInfo struct {
	Val string `json:"val"`
}

type ConfigInfoReq struct {
	Key string `json:"key" form:"key"`
}

func GetConfigForString(ctx *gin.Context, key string, ttl int64) (string, error) {
	configInfo, err := getConfigFromCache(ctx, key)
	if err != nil || len(configInfo.Val) == 0 {
		params := ConfigInfoReq{Key: key}
		configInfo = &ConfigInfo{}
		err = apis.Do(ctx, params, configInfo)
		if err != nil {
			zlog.Warnf(ctx, "mercury get config failed, key: %s, err: %+v", key, err)
			return "", err
		}

		if err = setConfigCache(ctx, configInfo, key, ttl); err != nil {
			zlog.Warnf(ctx, "set mercury config cache failed, key: %s, config: %+v ", key, configInfo)
		}
	}

	return configInfo.Val, nil
}

func GetConfigForJson(ctx *gin.Context, key string, ttl int64, val interface{}) error {
	valType := reflect.ValueOf(val).Type()
	if valType.Kind() != reflect.Ptr {
		return errors.New(fmt.Sprintf("require ptr to val, got %s", valType.Kind()))
	}

	configInfo, err := GetConfigForString(ctx, key, ttl)
	if err != nil {
		return err
	}

	if err = json.Unmarshal([]byte(configInfo), val); err != nil {
		return err
	}

	return nil
}

func setConfigCache(ctx *gin.Context, configInfo *ConfigInfo, key string, ttl int64) error {
	cacheKey := getCacheKey(key)
	if ttl == 0 {
		ttl = DefaultExpireTime
	}
	return helpers.RedisClient.Set(ctx, cacheKey, configInfo.Val, ttl)
}

func getConfigFromCache(ctx *gin.Context, key string) (*ConfigInfo, error) {
	cacheKey := getCacheKey(key)
	data, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	ret := &ConfigInfo{Val: string(data)}
	return ret, nil
}

func getCacheKey(key string) string {
	return ConfigCacheKeyPre + key
}
