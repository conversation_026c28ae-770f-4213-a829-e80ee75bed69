package mercury

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetConfigForString(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	key := "fwyy_touch_limit_test"
	ret, err := GetConfigForString(ctx, key, 100)
	fmt.Println(ret, err)
}

func TestGetConfigForJson(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	type TestConfig struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}
	ret := &TestConfig{}
	key := "test_shyn_0"
	err := GetConfigForJson(ctx, key, 100, ret)
	fmt.Println(ret, err)
}
