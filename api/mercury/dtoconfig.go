package mercury

type SendWxMessage struct {
	RunTime          int64          `json:"runTime"`
	WaitTime         int64          `json:"waitTime"`
	DefaultDelayTime int64          `json:"defaultDelayTime"`
	CdkURLGray       CdkURLGrayInfo `json:"cdkUrlGray"`
}

type CdkURLGrayInfo struct {
	GradeIds  []int64 `json:"gradeIds"`
	CourseIds []int64 `json:"courseIds"`
}

type TouchConfig struct {
	SmsConfig  SmsConfigInfo  `json:"smsConfig"`
	CallConfig CallConfigInfo `json:"callConfig"`
}

type SmsTplConfig struct {
	TplID int64    `json:"tplId"`
	Vars  []string `json:"vars"`
}

type SmsConfigInfo struct {
	Limit     int64          `json:"limit"`
	TplConfig []SmsTplConfig `json:"tplConfig"`
}

type CallLimitInfo struct {
	TimeRange int `json:"timeRange"` // 时间范围, 单位h
	CntLimit  int `json:"cntLimit"`
}

type CallConfigInfo struct {
	PublicSea  LimitConfig `json:"publicSea"`
	PrivateSea LimitConfig `json:"privateSea"`
}

type LimitConfig struct {
	Limit []CallLimitInfo `json:"limit"`
}
