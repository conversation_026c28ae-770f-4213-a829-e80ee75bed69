package eduprobe

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiGetUrl = "/eduprobe/probe/geturl"

	NoCourseId = 1
)

type GetUrlReq struct {
	CourseId int64 `json:"courseId" form:"courseId"`
}

type GetUrlResp struct {
	Url string `json:"url"`
}

func GetUrl(ctx *gin.Context, req GetUrlReq) (resp GetUrlResp, err error) {
	if err = apis.Do(ctx, req, &resp); err != nil {
		zlog.Warnf(ctx, "GetUrl request failed, req: %d, err: %+v", req, err)
		return
	}
	return
}
