package eduprobe

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiGetLearningReportByStudent = "/eduprobe/probe/getlearningreportbystudent"
)

type GetLearningReportByStudentReq struct {
	StudentUids []int64 `json:"studentUids"`
}

type GetLearningReportByStudentResp struct {
	List []GetLearningReportByStudentItem `json:"list"`
}

type GetLearningReportByStudentItem struct {
	StudentUid int64 `json:"student_uid"`
	CourseId   int64 `json:"course_id"`
}

func GetLearningReportByStudent(ctx *gin.Context, req GetLearningReportByStudentReq) (resp GetLearningReportByStudentResp, err error) {
	if err = apis.Do(ctx, req, &resp); err != nil {
		zlog.Warnf(ctx, "GetLearningReportByStudent request failed, req: %d, err: %+v", req, err)
		return
	}
	return
}
