package eduprobe

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(apiGetLearningReportByStudent, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLearningReportByStudentReq{},
		Encoder:  apis.EncoderJson,
		Response: GetLearningReportByStudentResp{},
	})
	apis.Register(apiGetUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetUrlReq{},
		Encoder:  apis.EncoderForm,
		Response: GetUrlResp{},
	})
}
