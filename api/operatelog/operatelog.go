package operatelog

import (
	"assistantdeskgo/api/apis"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func post(ctx *gin.Context, req AddOperateLogReq) {
	var response interface{}
	err := apis.Do(ctx, req, response)
	if err != nil {
		zlog.Warnf(ctx, "AddLog request failed, req: %d, err: %+v", req, err)
	}
}

var (
	concurrency = 3
	sem         = make(chan AddOperateLogReq, concurrency)
)

func AddBaseLog(ctx *gin.Context, relationId, relationType, refer, module, service string, content interface{}, personUid int64, remark string) {
	AddLog(ctx, relationId, relationType, refer, module, service, content, personUid, "", "", 0)
}

func doPost(ctx *gin.Context, req AddOperateLogReq) { //类似线程池的作用
	sem <- req
	{
		// 模拟工作
		post(ctx, req)
	}
	// 释放semaphore
	<-sem
}

func AddLog(ctx *gin.Context, relationId, relationType, refer, module, service string, content interface{}, personUid int64, remark, before string, assistantUid int64) {

	if relationId == "" || relationType == "" || refer == "" || module == "" || service == "" {
		zlog.Warnf(ctx, "AddLog Fail,param err")
		return
	}

	contentStr, err := json.Marshal(content)
	if err != nil {
		zlog.Warnf(ctx, "AddLog Fail,content err=%v", err)
		return
	}

	go doPost(ctx, AddOperateLogReq{
		RelationId:   relationId,
		RelationType: relationType,
		Before:       before,
		Remark:       remark,
		Refer:        refer,
		Module:       module,
		Service:      service,
		Content:      string(contentStr),
		PersonUid:    personUid,
		AssistantUid: assistantUid,
		OperatorTime: time.Now().Unix(),
	})
}
