package operatelog

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

const (
	PATH_ADD_OPERATE_LOG = "/api/v1/operate/log/add"
)

type AddOperateLogReq struct {
	Refer        string `json:"refer" form:"refer"`
	Module       string `json:"module" form:"module"`
	Service      string `json:"service" form:"service"`
	Content      string `json:"content" form:"content"`
	Before       string `json:"before" form:"before"`
	Remark       string `json:"remark" form:"remark"`
	RelationId   string `json:"relationId" form:"relationId"`
	RelationType string `json:"relationType" form:"relationType"`
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	PersonUid    int64  `json:"personUid" form:"personUid"`
	OperatorTime int64  `json:"operatorTime" form:"operatorTime"`
}

func init() {
	apis.Register(PATH_ADD_OPERATE_LOG, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  AddOperateLogReq{},
		Encoder:  apis.EncoderJson,
		Response: map[string]interface{}{},
	})
}
