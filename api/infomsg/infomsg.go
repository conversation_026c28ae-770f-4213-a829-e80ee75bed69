package infomsg

import (
	"assistantdeskgo/api/decode"
	"assistantdeskgo/conf"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	NewLeadTestTplId             = 96
	NewLeadTplId                 = 99
	AssistantDeskAppId           = "assistantdesk"
	AssistantDeskAppTestSecret   = "2042200265142450140"
	AssistantDeskAppOnlineSecret = "2042201634498882885"
)

func GetSecret() (string, int) {
	if env.GetRunEnv() == env.RunEnvOnline || env.GetRunEnv() == env.RunEnvTips {
		return AssistantDeskAppOnlineSecret, NewLeadTplId
	} else {
		return AssistantDeskAppTestSecret, NewLeadTestTplId
	}
}

// 接入文档 https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=442415696

type SendMsgParams struct {
	TplId        int      `json:"tplId"`
	Level        int      `json:"level"`        // 1 快速(开通权限支持) 2 普通
	TplArgs      string   `json:"tplArgs"`      // 模版参数
	Receivers    string   `json:"receivers"`    //uname、phone、email、empId、ipsUid、dingUid
	ReceiverType string   `json:"receiverType"` //uname、phone、email、empId、ipsUid、dingUid
	LinkUrl      string   `json:"linkUrl,omitempty"`
	Attach       []string `json:"attach,omitempty"`
	Cc           []string `json:"cc,omitempty"`
	Bcc          []string `json:"bcc,omitempty"`
	SendTime     int      `json:"sendTime,omitempty"` // 指定消息发送时间
	SendFrom     string   `json:"sendFrom,omitempty"`
}

type Task struct {
	TaskId int `json:"taskId"`
}

func SendMsg(ctx *gin.Context, params SendMsgParams, token string) error {

	urlPath := "/infomsg/v1/api/msg/sendMsg"
	var output = Task{}
	headers := map[string]string{
		"appId": AssistantDeskAppId,
		"token": token,
	}
	err := ralPostJson(ctx, urlPath, params, headers, &output)
	if err != nil {
		zlog.Warnf(ctx, "SendMsg fail,err=%v", err)
		return err
	}
	zlog.Infof(ctx, "InfoMsgSendMsg result=%v", output)
	return nil
}

type Token struct {
	Token string `json:"token"`
}

func GetToken(ctx *gin.Context) (string, error) {
	urlPath := "/infomsg/v1/api/auth/token"
	secret, _ := GetSecret()
	params := map[string]interface{}{
		"appId":     AssistantDeskAppId,
		"appSecret": secret,
	}
	var output = Token{}

	err := ralPostJson(ctx, urlPath, params, nil, &output)
	if err != nil {
		zlog.Warnf(ctx, "InfoMsgGetToken fail, err=%v", err)
		return output.Token, err
	}
	return output.Token, nil
}

func ralPostJson(ctx *gin.Context, urlPath string, params interface{}, headers map[string]string, output interface{}) error {
	opt := base.HttpRequestOptions{
		RequestBody: params,
		Encode:      base.EncodeJson,
	}

	if headers != nil {
		opt.Headers = headers
	}

	resp, err := conf.API.InfoMsg.HttpPost(ctx, urlPath, opt)
	if err != nil {
		zlog.Warnf(ctx, "ral failed, url[%s] err:%s", urlPath, err)
		return err
	}

	var apiResp decode.BzrResponse
	err = decode.DecodeResponse(ctx, resp, &apiResp, output)
	if err != nil {
		zlog.Warnf(ctx, "decode failed, url[%s] err:%s", urlPath, err)
		return err
	}

	if apiResp.ErrNo > 0 {
		zlog.Warnf(ctx, "request failed, url[%s] err:%s", urlPath, err)
		return errors.New(apiResp.ErrStr)
	}

	return nil
}
