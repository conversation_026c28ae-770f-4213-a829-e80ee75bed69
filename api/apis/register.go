package apis

import (
	"net/http"
	"reflect"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/base"
)

type ApiConfig struct {
	uri          string
	Method       string
	Request      interface{}
	requestType  reflect.Type
	Encoder      string
	Response     interface{}
	responseType reflect.Type
}

func (c *ApiConfig) NewApi() *Api {
	return &Api{
		Request:  reflect.New(c.requestType).Interface(),
		Response: reflect.New(c.responseType).Interface(),
		conf:     c,
		baseOptions: base.HttpRequestOptions{
			RequestBody: c.Request,
			Encode:      c.Encoder,
		},
	}
}

func (c *ApiConfig) NewApiNoReqRsp() *Api {
	return &Api{
		conf: c,
		baseOptions: base.HttpRequestOptions{
			Encode: c.Encoder,
		},
	}
}

func Register(uri string, conf ApiConfig) {
	err := register(uri, &conf)
	if err != nil {
		panic(err)
	}
}

type RegisterInfo struct {
	Uri  string
	Conf ApiConfig
}

func BatchRegister(registerInfos []RegisterInfo) {
	for _, registerInfo := range registerInfos {
		Register(registerInfo.Uri, registerInfo.Conf)
	}
}

// func Get(uri string) (*Api, error) {
// 	conf := getRegisteredApi(uri)
// 	if conf == nil {
// 		return nil, ErrNoSuchUri.Sprintf(uri)
// 	}

// 	api := conf.NewApi()
// 	return api, nil
// }

var (
	registeredApi = map[string]*ApiConfig{}
	registeredReq = map[reflect.Type]*ApiConfig{}
)

func register(uri string, conf *ApiConfig) error {
	conf.uri = uri

	if conf.Method != http.MethodGet && conf.Method != http.MethodPost {
		return ErrInvalidMethod.Sprintf(conf.Method)
	}

	if conf.Request == nil {
		conf.Request = ""
		conf.Encoder = base.EncodeRaw
	}

	conf.requestType = reflect.TypeOf(conf.Request)
	if conf.requestType.Kind() == reflect.Ptr {
		conf.requestType = conf.requestType.Elem()
	}

	if conf.Response == nil {
		return ErrRequireResponse.Sprintf(uri)
	}

	conf.responseType = reflect.TypeOf(conf.Response)
	if conf.responseType.Kind() == reflect.Ptr {
		conf.responseType = conf.responseType.Elem()
	}

	registeredApi[uri] = conf
	registeredReq[conf.requestType] = conf
	return nil
}

func getRegisteredApi(uri string) *ApiConfig {
	if strings.Contains(uri, "?") {
		uri = strings.Split(uri, "?")[0]
	}

	return registeredApi[uri]
}
