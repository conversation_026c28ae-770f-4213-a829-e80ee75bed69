package apis

import "git.zuoyebang.cc/pkg/golib/v2/base"

// https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=155601339
// 参数检查错误: 4999800+
// 内部逻辑错误: 5999800+
var (
	ErrNoSuchUri = base.Error{
		ErrNo:  4999800,
		ErrMsg: "no such uri:%s",
	}

	ErrInvalidMethod = base.Error{
		ErrNo:  4999801,
		ErrMsg: "invalid method:%s, want GET/POST",
	}

	ErrRequireResponse = base.Error{
		ErrNo:  4999802,
		ErrMsg: "require a response for %s",
	}

	ErrNoClientForApi = base.Error{
		ErrNo:  4999803,
		ErrMsg: "no client for %s",
	}

	ErrRequirePtrToTApi = base.Error{
		ErrNo:  4999804,
		ErrMsg: "require ptr to tapi, got %s",
	}

	ErrRequireTApiAsStruct = base.Error{
		ErrNo:  4999805,
		ErrMsg: "require tapi as struct, got %s",
	}

	ErrCanNotAddrClient = base.Error{
		ErrNo:  4999806,
		ErrMsg: "can not get addr of %s",
	}

	ErrCanNotGetInterfaceOf = base.Error{
		ErrNo:  4999807,
		ErrMsg: "can not get interface of %s",
	}

	ErrIsNotApiClientType = base.Error{
		ErrNo:  4999808,
		ErrMsg: "%s is not type of *base.ApiClient",
	}

	ErrNoSuchField = base.Error{
		ErrNo:  4999809,
		ErrMsg: "no such field: %s",
	}

	ErrDecodeJson = base.Error{
		ErrNo:  4999810,
		ErrMsg: "decode <%s> err:%s",
	}
	ErrType = base.Error{
		ErrNo:  4999811,
		ErrMsg: "want:%s, got:%s",
	}
	ErrPreEncode = base.Error{
		ErrNo:  4999812,
		ErrMsg: "preEncode err:%s",
	}
	ErrNoSuchType = base.Error{
		ErrNo:  4999813,
		ErrMsg: "no such type %s",
	}
	ErrReqType = base.Error{
		ErrNo:  4999814,
		ErrMsg: "req must be struct or ptr to struct :%T",
	}
	ErrEncodeQuery = base.Error{
		ErrNo:  4999815,
		ErrMsg: "encode query %+v err:%+w",
	}
	ErrEncodeForm = base.Error{
		ErrNo:  4999816,
		ErrMsg: "encode form %+v err:%+w",
	}
	ErrRspType = base.Error{
		ErrNo:  4999817,
		ErrMsg: "rsp must be nil or ptr:%T",
	}
	ErrorAPI = base.Error{
		ErrNo:  5000,
		ErrMsg: "加载失败了，请重新操作",
	}
)
