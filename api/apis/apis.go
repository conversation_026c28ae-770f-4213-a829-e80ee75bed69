package apis

import (
	"net/http"
	"reflect"

	"github.com/gin-gonic/gin"
)

// MUST register first
func Do(ctx *gin.Context, req, rsp interface{}, ops ...Option) error {
	err := checkType(req, rsp)
	if err != nil {
		return err
	}

	var (
		typ  = reflect.Indirect(reflect.ValueOf(req)).Type()
		conf = registeredReq[typ]
	)

	if conf == nil {
		return ErrNoSuchType.Sprintf(typ.Name())
	}

	api := conf.NewApiNoReqRsp()
	api.Request = req
	api.Response = rsp
	return api.Do(ctx, ops...)
}

func Get(ctx *gin.Context, url string, req, rsp interface{}, ops ...Option) error {
	return request(ctx, http.MethodGet, url, req, rsp, ops...)
}

func Post(ctx *gin.Context, url string, req, rsp interface{}, ops ...Option) error {
	return request(ctx, http.MethodPost, url, req, rsp, ops...)
}

func request(ctx *gin.Context, method, url string, req, rsp interface{}, ops ...Option) error {
	err := checkType(req, rsp)
	if err != nil {
		return err
	}

	conf := &ApiConfig{
		uri:     url,
		Method:  method,
		Encoder: defaultEncoder(method),
	}

	api := conf.NewApiNoReqRsp()
	api.Request = req
	api.Response = rsp
	return api.Do(ctx, ops...)
}

func isStruct(i interface{}) bool {
	return reflect.TypeOf(i).Kind() == reflect.Struct
}

func isPtrToStruct(i interface{}) bool {
	typ := reflect.TypeOf(i)
	return typ.Kind() == reflect.Ptr && typ.Elem().Kind() == reflect.Struct
}

func isPtr(i interface{}) bool {
	typ := reflect.TypeOf(i)
	return typ.Kind() == reflect.Ptr
}

func checkType(req, rsp interface{}) error {
	if !isStruct(req) && !isPtrToStruct(req) {
		return ErrReqType.Sprintf(req)
	}

	if rsp != nil && !isPtr(rsp) {
		return ErrRspType.Sprintf(rsp)
	}
	return nil
}
