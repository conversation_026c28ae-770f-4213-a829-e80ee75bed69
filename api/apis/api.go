package apis

import (
	"encoding/json"
	"net/http"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Api struct {
	Request     interface{}
	Response    interface{}
	conf        *ApiConfig
	baseOptions base.HttpRequestOptions
	client      *base.ApiClient
	RspWrapper  ResponseWrapper

	ignoreErrNo         bool
	ignoreInnerError    bool
	responseUnmarshaler func([]byte, interface{}) error
}

func (a *Api) Do(ctx *gin.Context, ops ...Option) error {
	for _, op := range ops {
		op(a)
	}

	if a.client == nil {
		a.client = getApiClient(a.conf.uri)
		if a.client == nil {
			return ErrNoClientForApi.Sprintf(a.conf.uri)
		}
	}

	var (
		rst *base.ApiResult
	)

	err := a.preProcess()
	if err != nil {
		zlog.Warnf(ctx, err.<PERSON>rror())
		return err
	}

	switch a.conf.Method {
	case http.MethodGet:
		rst, err = a.client.HttpGet(ctx, a.conf.uri, a.baseOptions)
	case http.MethodPost:
		rst, err = a.client.HttpPost(ctx, a.conf.uri, a.baseOptions)
	default:
		return ErrInvalidMethod.Sprintf(a.conf.Method)
	}

	if err != nil {
		zlog.Warnf(ctx, "call api:%s, err:%s", a.conf.uri)
		return ErrorAPI
	}

	if rst.HttpCode != 200 {
		zlog.Warnf(ctx, "call api:%s, return http code:%d", a.conf.uri, rst.HttpCode)
		return ErrorAPI
	}

	zlog.Infof(ctx, "call api:%s response:%s", a.conf.uri, string(rst.Response))

	a.RspWrapper.unmarshaler = a.responseUnmarshaler
	a.RspWrapper.Data = a.Response
	err = json.Unmarshal(rst.Response, &a.RspWrapper)
	if err != nil {
		err = ErrDecodeJson.Sprintf(string(rst.Response), err)
		zlog.Warnf(ctx, err.Error())
		return err
	}

	if !a.ignoreInnerError {
		if a.RspWrapper.InnerError != nil {
			err = ErrDecodeJson.Sprintf(string(a.RspWrapper.InnerData), err)
			zlog.Warnf(ctx, err.Error())
			return err
		}
	}

	if !a.ignoreErrNo {
		err = a.RspWrapper.ErrNoAsError()
		if err != nil {
			zlog.Warnf(ctx, err.Error())
			return err
		}
	}

	return nil
}

func (a *Api) preProcess() error {
	err := a.preEncode()
	if err != nil {
		return err
	}
	// more

	return nil
}

func (a *Api) preEncode() error {
	switch a.baseOptions.Encode {
	case EncoderPre:
		return a.encodePre()
	case EncoderQuery:
		return a.encodeQuery()
	case EncoderForm:
		return a.encodeForm()
	case EncoderJson:
		return a.encodeJson()
	}
	return nil
}

func (a *Api) encodePre() error {
	if a.baseOptions.Encode != EncoderPre {
		return nil
	}

	r, ok := a.Request.(preEncoder)
	if !ok {
		return ErrType.Sprintf(preEncoderType.String(), a.conf.requestType.String())
	}

	data, err := r.Encode()
	if err != nil {
		return ErrPreEncode.Sprintf(err.Error())
	}

	a.baseOptions.Encode = EncoderRawByte
	a.baseOptions.RequestBody = data

	return nil
}

func (a *Api) encodeQuery() error {
	data, err := encodeQuery(a.Request)
	if err != nil {
		return ErrEncodeQuery.Sprintf(a.Request, err)
	}

	a.baseOptions.Encode = EncoderRawByte
	a.baseOptions.RequestBody = data

	return nil
}

func (a *Api) encodeForm() error {
	if !isStruct(a.Request) && !isPtrToStruct(a.Request) {
		return nil
	}

	data, err := encodeForm(a.Request)
	if err != nil {
		return ErrEncodeForm.Sprintf(a.Request, err)
	}

	a.baseOptions.Encode = EncoderRawByte
	a.baseOptions.RequestBody = data

	return nil
}

func (a *Api) encodeJson() error {
	a.baseOptions.RequestBody = a.Request
	return nil
}
