package apis

import (
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	jsoniter "github.com/json-iterator/go"
	"github.com/json-iterator/go/extra"
)

type Option func(o *Api)

func WithRequest(r interface{}) Option {
	return func(o *Api) {
		o.Request = r
		o.baseOptions.RequestBody = r
	}
}

func WithEncode(encoder string) Option {
	return func(o *Api) {
		o.baseOptions.Encode = encoder
	}
}

func WithResponse(r interface{}) Option {
	return func(o *Api) {
		o.Response = r
	}
}

func WithHeaders(m map[string]string) Option {
	return func(o *Api) {
		o.baseOptions.Headers = m
	}
}

func AddHeader(k, v string) Option {
	return func(o *Api) {
		if o.baseOptions.Headers == nil {
			o.baseOptions.Headers = make(map[string]string)
		}
		o.baseOptions.Headers[k] = v
	}
}

func WithApiClient(c *base.ApiClient) Option {
	return func(o *Api) {
		o.client = c
	}
}

func WithCookies(m map[string]string) Option {
	return func(o *Api) {
		o.baseOptions.Cookies = m
	}
}

func AddCookie(k, v string) Option {
	return func(o *Api) {
		if o.baseOptions.Cookies == nil {
			o.baseOptions.Cookies = make(map[string]string)
		}
		o.baseOptions.Cookies[k] = v
	}
}

func IgnoreErrNo() Option {
	return func(o *Api) {
		o.ignoreErrNo = true
	}
}

func IgnoreInnerError() Option {
	return func(o *Api) {
		o.ignoreInnerError = true
	}
}

func ResponseUnmarshaler(f func([]byte, interface{}) error) Option {
	return func(o *Api) {
		o.responseUnmarshaler = f
	}
}

var (
	phpTolerance sync.Once
)

func PhpResponseUnmarshaler() Option {
	phpTolerance.Do(func() {
		extra.RegisterFuzzyDecoders()
	})
	return func(o *Api) {
		o.responseUnmarshaler = jsoniter.Unmarshal
	}
}
