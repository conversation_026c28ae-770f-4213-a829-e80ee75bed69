package apis

import (
	"net/http"
	"net/url"
	"reflect"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gorilla/schema"
)

const (
	EncoderPre     = "_pre"
	EncoderQuery   = "_query"
	EncoderJson    = base.EncodeJson
	EncoderForm    = base.EncodeForm
	EncoderMcPack  = base.EncodeMcPack
	EncoderRaw     = base.EncodeRaw
	EncoderRawByte = base.EncodeRawByte
)

var (
	preEncoderType = reflect.ValueOf((*preEncoder)(nil)).Elem()
)

type preEncoder interface {
	Encode() ([]byte, error)
}

// func encodeUrl(i interface{}) ([]byte, error) {
// 	vs, err := query.Values(i)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return []byte(vs.Encode()), nil
// }

func encodeQuery(i interface{}) ([]byte, error) {
	return encodeWithTag(i, "query")
}

func encodeForm(i interface{}) ([]byte, error) {
	return encodeWithTag(i, "form")
}

func encodeWithTag(i interface{}, tag string) ([]byte, error) {
	var (
		vs  = url.Values{}
		enc = schema.NewEncoder()
	)
	enc.SetAliasTag(tag)

	err := enc.Encode(i, vs)
	if err != nil {
		return nil, err
	}
	return []byte(vs.Encode()), nil
}

func defaultEncoder(method string) string {
	switch method {
	case http.MethodGet:
		return EncoderQuery
	case http.MethodPost:
		return EncoderForm
	}
	return EncoderRawByte
}
