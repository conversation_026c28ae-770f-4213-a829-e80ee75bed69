package apis

import (
	"reflect"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/base"
)

var (
	rootsToClient = map[string]*base.ApiClient{}
	pathToClient  = map[string]*base.ApiClient{}
)

func BindPathRootsToClient(roots []string, c *base.ApiClient) {
	bindPathRootsToClient(roots, c)
}

func bindPathRootsToClient(roots []string, c *base.ApiClient) {
	for _, r := range roots {
		rootsToClient[r] = c
	}
}

func BindPathRootsToTagName(roots []string, tapi interface{}, tagName string) {
	client, err := getApiClientFieldByTag(tapi, "yaml", tagName)
	if err != nil {
		panic(err)
	}

	bindPathRootsToClient(roots, client)
}

func BindPathToClient(path string, c *base.ApiClient) {
	bindPathToClient(path, c)
}

func bindPathToClient(path string, c *base.ApiClient) {
	path = strings.Split(path, "?")[0]
	pathToClient[path] = c
}

func BindPathToTagName(path string, tapi interface{}, tagName string) {
	client, err := getApiClientFieldByTag(tapi, "yaml", tagName)
	if err != nil {
		panic(err)
	}

	bindPathToClient(path, client)
}

func getApiClient(uri string) *base.ApiClient {
	uri = strings.Split(uri, "?")[0]

	c, ok := pathToClient[uri]
	if ok {
		return c
	}

	paths := strings.Split(uri, "/")
	if len(paths) < 2 {
		return nil
	}

	c, ok = rootsToClient[paths[1]]
	if ok {
		return c
	}
	return nil
}

func getApiClientFieldByTag(tapi interface{}, tag, name string) (*base.ApiClient, error) {
	var (
		value = reflect.ValueOf(tapi)
		typ   = value.Type()
	)

	if typ.Kind() != reflect.Ptr {
		return nil, ErrRequirePtrToTApi.Sprintf(typ.String())
	}

	value = value.Elem()
	typ = typ.Elem()

	if typ.Kind() != reflect.Struct {
		return nil, ErrRequireTApiAsStruct.Sprintf(typ.String())
	}

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		tagValue := field.Tag.Get(tag)
		if tagValue != name {
			continue
		}

		val := value.Field(i)

		if val.Kind() != reflect.Ptr {
			// field is struct,convert to ptr
			if !val.CanAddr() {
				return nil, ErrCanNotAddrClient.Sprintf(name)
			}
			val = val.Addr()
		}

		if !val.CanInterface() {
			return nil, ErrCanNotGetInterfaceOf.Sprintf(name)
		}
		v, ok := val.Interface().(*base.ApiClient)
		if !ok {
			return nil, ErrIsNotApiClientType.Sprintf(name)
		}

		return v, nil
	}

	return nil, ErrNoSuchField.Sprintf(name)
}
