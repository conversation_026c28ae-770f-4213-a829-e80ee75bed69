package apis

import (
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/base"
)

type ResponseWrapper struct {
	ResponseInner
	Data        interface{}
	InnerError  error
	unmarshaler func([]byte, interface{}) error
}

func (r *ResponseWrapper) UnmarshalJSON(in []byte) error {
	if r.unmarshaler == nil {
		r.unmarshaler = json.Unmarshal
	}

	err := r.unmarshaler(in, &r.ResponseInner)
	if err != nil {
		return err
	}

	r.retrieveErr()
	if r.Data != nil {
		r.InnerError = r.unmarshaler(r.InnerData, r.Data)
	}

	return nil
}

func (r *ResponseWrapper) ErrNoAsError() error {
	if r.ErrNo != 0 {
		return base.Error{
			ErrNo:  r.ErrNo,
			ErrMsg: r.ErrMsg,
		}
	}
	return nil
}

func (r *ResponseWrapper) DataDecodeError() error {
	if r.InnerError == nil {
		return nil
	}

	return ErrDecodeJson.Sprintf(string(r.InnerData), r.InnerError.Error())
}

func (r *ResponseWrapper) retrieveErr() {
	if r.ErrNo2 != 0 {
		r.ErrNo = r.ErrNo2
	}

	if r.ErrMsg2 != "" {
		r.ErrMsg = r.ErrMsg2
	} else if r.ErrStr != "" {
		r.ErrMsg = r.ErrStr
	} else if r.ErrStr2 != "" {
		r.ErrMsg = r.ErrStr2
	}
}

type ResponseInner struct {
	ErrNo  int `json:"errNo"`
	ErrNo2 int `json:"err_no"`

	ErrMsg  string `json:"errMsg"`
	ErrMsg2 string `json:"err_msg"`
	ErrStr  string `json:"errStr"`
	ErrStr2 string `json:"errstr"`

	InnerData json.RawMessage `json:"data"`
}
