package duxuesc

type GetLeadsRemarkListReq struct {
	LeadsId  int64 `json:"leadsId" form:"leadsId"`
	CourseId int64 `json:"courseId" form:"courseId"`
	LpcUid   int64 `json:"lpcUid" form:"lpcUid"`
}

type GetLeadsRemarkListResp struct {
	List []GetLeadsRemarkInfo `json:"list"`
}

type GetLeadsRemarkInfo struct {
	Remark    string `json:"remark"`
	Time      int64  `json:"time"`
	Intention string `json:"intention"`
	IsCall    int64  `json:"isCall"`
	LeadsId   int64  `json:"leadsId"`
}
