package duxuesc

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiGetLeadsRemarkList = "/duxuesc/api/getleadsremarklist"
)

func GetLeadsRemarkList(ctx *gin.Context, req GetLeadsRemarkListReq) (resp GetLeadsRemarkListResp, err error) {
	err = apis.Do(ctx, req, &resp, apis.PhpResponseUnmarshaler())
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsRemarkList.ral failed, req:%+v,err:%+v", req, err)
		return
	}
	return
}
