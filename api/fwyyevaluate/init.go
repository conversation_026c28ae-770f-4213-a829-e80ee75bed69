package fwyyevaluate

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/conf"
	"net/http"
)

func checkApiClient() {
	if conf.API.FwyyEvaluate.Service == "" {
		panic("service fwyyEvaluate uninitialized")
	}
}

func init() {
	apis.Register(UrlAddDownloadTask, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  AddDownloadTaskReq{},
		Encoder:  apis.EncoderJson,
		Response: AddDownloadTaskRsp{},
	})
}
