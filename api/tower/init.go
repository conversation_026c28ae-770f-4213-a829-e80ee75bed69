package tower

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(UriGetCourseInfo, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetCourseInfoReq{},
		Encoder:  apis.EncoderJson,
		Response: GetCourseInfoRsp{},
	})
	apis.Register(UriGetCourseBindByDeviceUids, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetCourseBindByDeviceUidReq{},
		Encoder:  apis.EncoderJson,
		Response: GetCourseBindByDeviceUidResp{},
	})
	apis.Register(UriGetBatchCourseExpireTime, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CourseExpireTimeReq{},
		Encoder:  apis.EncoderJson,
		Response: CourseExpireTimeRsp{},
	})

	apis.Register(UriGetCourseBindByCourseIds, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetCourseBindByCourseIdsReq{},
		Encoder:  apis.EncoderForm,
		Response: GetCourseBindByCourseIdsRsp{},
	})
}
