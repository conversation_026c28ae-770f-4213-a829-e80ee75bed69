package longlink

import (
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	//推送长连接消息到客户端
	SendLongLinkMsgPath = "/MsgCommitService/MsgCommit"
)

type SendLongLinkReq struct {
	Product         string  `json:"product"`
	CmdNo           int     `json:"cmdno"`
	MsgExpireTime   int64   `json:"msg_expire_time"`
	MsgData         string  `json:"msg_data"`
	MsgCleanType    int     `json:"msg_clean_type,omitempty"`
	MsgDeliveryType int     `json:"msg_delivery_type,omitempty"`
	ToUids          []int64 `json:"to_uids"`
}

func (this *SendLongLinkReq) ToMap() map[string]interface{} {
	res := make(map[string]interface{})
	res["product"] = this.Product
	res["cmdno"] = this.CmdNo
	res["msg_expire_time"] = this.MsgExpireTime
	res["msg_data"] = this.MsgData
	res["msg_clean_type"] = this.MsgCleanType
	res["msg_delivery_type"] = this.MsgDeliveryType
	res["to_uids"] = this.ToUids
	return res
}

type MqsResp struct {
	ErrNo  int    `json:"err_no"`
	ErrStr string `json:"err_msg"`
	MsgId  int64  `json:"msg_id"`
}

func SendLongLinkMsg(ctx *gin.Context, sendData SendLongLinkReq) (int64, error) {
	opt := base.HttpRequestOptions{
		RequestBody: sendData.ToMap(),
		Encode:      base.EncodeJson,
	}
	zlog.Infof(ctx, "SendLongLinkMsg req:%#v", sendData)
	result, err := conf.API.LongLink.HttpPost(ctx, SendLongLinkMsgPath, opt)
	if err != nil {
		return 0, components.ErrorLongLinkSendFail.WrapPrintf(err, "RequestBody=%+v", opt.RequestBody)
	}
	var mqsResp MqsResp
	if err = json.Unmarshal(result.Response, &mqsResp); err != nil {
		zlog.Errorf(ctx, "http response decode err, err: %s", err.Error())
		return 0, err
	}
	if mqsResp.ErrNo != 0 {
		zlog.Errorf(ctx, "mqsResp err, errNo:%d, errMsg:%s", mqsResp.ErrNo, mqsResp.ErrStr)
		return 0, errors.New(fmt.Sprintf("call MsgCommit error,errNo=%d, errMsg=%s", mqsResp.ErrNo, mqsResp.ErrStr))
	}

	return mqsResp.MsgId, nil
}
