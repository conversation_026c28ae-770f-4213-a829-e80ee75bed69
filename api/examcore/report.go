package examcore

import (
	"assistantdeskgo/api/apis"
	"fmt"
	"github.com/gin-gonic/gin"
)

const (
	GetReportUri = "/examcore/v1/report/geturl"
)

type GetReportUrlReq struct {
	AnswerKeys []string `json:"answerKeys"`
	AppId      int64    `json:"appId"`
	AppSecret  string   `json:"appSecret"`
}

type GetReportUrlRsp struct {
	List map[string]GetReportUrlRspItem `json:"list"`
}

type GetReportUrlRspItem struct {
	ReportId int64  `json:"reportId"`
	URL      string `json:"url"`
	AnswerId string `json:"answerId"`
	Status   int    `json:"status"`
}

func GetReportUrl(ctx *gin.Context, req GetReportUrlReq) (rsp *GetReportUrlRsp, err error) {
	rsp = &GetReportUrlRsp{}
	req.AppId = 10000010
	req.AppSecret = "22f0da0c"
	if err = apis.Do(ctx, req, &rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

func BuildAnswerKeys(examId int64, studentUids []int64) []string {
	res := make([]string, 0)
	for _, stuId := range studentUids {
		res = append(res, fmt.Sprintf("%+v_%+v", examId, stuId))
	}
	return res
}
