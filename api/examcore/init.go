package examcore

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(GetReportUri, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetReportUrlReq{},
		Encoder:  apis.EncoderJson,
		Response: GetReportUrlRsp{},
	})
	apis.Register(GetBindInfoUri, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetBindInfoReq{},
		Encoder:  apis.EncoderJson,
		Response: GetBindInfoRsp{},
	})
	apis.Register(GetExamInfoUri, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetExamInfoReq{},
		Encoder:  apis.EncoderJson,
		Response: GetExamInfoRsp{},
	})
}
