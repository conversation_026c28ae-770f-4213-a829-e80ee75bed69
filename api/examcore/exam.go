package examcore

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/components"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

const GetStuExamUrl = ""

func FormatBindStr(bindId int64, bindType int, examType int) string {
	bindMap := map[int]string{
		components.BuyTypeLesson:  "lesson",
		components.BuyTypeCourse:  "course",
		components.BuyTypeCpu:     "cpu",
		components.BuyTypeOutline: "outline",
	}
	bindName := bindMap[bindType]
	bindKey := fmt.Sprintf("%+v_%+v", bindName, bindId)
	return fmt.Sprintf("%+v:%+v", bindKey, examType)
}

const (
	// GetBindInfoUri https://yapi.zuoyebang.cc/project/2535/interface/api/96286
	GetBindInfoUri = "/examcore/v1/getrelation"
	GetExamInfoUri = "/examcore/v1/getexam"

	BindStrsMaxLen = 500
)

type GetBindInfoReq struct {
	BindStrs  []string `json:"bindStrs"` // 最多500个
	AppId     int64    `json:"appId"`
	AppSecret string   `json:"appSecret"`
}

type GetBindInfoRsp struct {
	List map[string]map[string]GetBindInfoItem `json:"list"` // bindKey, examId
}

type GetBindInfoItem struct {
	RelationId   int64                  `json:"relationId"`
	BindKey      string                 `json:"bindKey"`
	ExamId       string                 `json:"examId"`
	ExamType     int                    `json:"examType"`
	RelationType int                    `json:"relationType"`
	UserKv       map[string]interface{} `json:"useKv"`
	JudgeRoute   interface{}            `json:"judgeRoute"`
	OperatorUid  int64                  `json:"operatorUid"`
	OperatorName string                 `json:"operatorName"`
	CreateTime   int64                  `json:"createTime"`
	UpdateTime   int64                  `json:"updateTime"`
}

type GetExamInfoReq struct {
	AppId           int64    `json:"appId"`
	AppSecret       string   `json:"appSecret"`
	ExamIds         []string `json:"examIds"`
	ExamStuKeys     []string `json:"examStuKeys"`
	HasQuestionList int      `json:"hasQuestionList"`
	HasPkgList      int      `json:"hasPkgList"`
}

type GetExamInfoRsp struct {
	Count int                        `json:"count"`
	List  map[string]GetExamInfoItem `json:"list"`
}

type GetExamInfoItem struct {
	Exam struct {
		ExamId       int64                   `json:"examId"`
		ExamType     int                     `json:"examType"`
		ExamTypeName string                  `json:"examTypeName"`
		Title        string                  `json:"title"`
		TidList      map[string]ExamTimuItem `json:"tidList"`
		TotalScore   int                     `json:"totalScore"`
		AnswerDimen  int                     `json:"answerDimen"`
		Props        interface{}             `json:"props"`
		UserKv       interface{}             `json:"userKv"`
		Grade        int                     `json:"grade"`
		Subject      int                     `json:"subject"`
		UpdateTime   int64                   `json:"updateTime"`
		CreateTime   int64                   `json:"createTime"`
		CreateUid    int64                   `json:"createUid"`
		CreatorName  string                  `json:"creatorName"`
	} `json:"exam"`
	ExamStu      map[string]map[string]ExamTimuItem `json:"examStu"`
	QuestionList interface{}                        `json:"questionList"`
	PkgList      []interface{}                      `json:"pkgList"`
}

type ExamTimuItem struct {
	Score          int   `json:"score"`
	Sort           int   `json:"sort"`
	Tid            int64 `json:"tid"`
	TimuSourceType int   `json:"timuSourceType"`
	Type           int   `json:"type"`
}

func GetBindInfo(ctx *gin.Context, req GetBindInfoReq) (*GetBindInfoRsp, error) {
	req.AppId = 10000010
	req.AppSecret = "22f0da0c"
	rsp := &GetBindInfoRsp{}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

func GetExamInfo(ctx *gin.Context, req GetExamInfoReq) (*GetExamInfoRsp, error) {
	examIds := req.ExamIds
	hasQuestionList := req.HasQuestionList
	if len(examIds) == 0 || (hasQuestionList != 0 && hasQuestionList != 1) {
		return nil, errors.New("getExamInfo param error")
	}

	rsp := &GetExamInfoRsp{}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

func BatchGetExamInfo(ctx *gin.Context, req GetExamInfoReq) (resp map[string]GetExamInfoItem, err error) {
	examIdsArr := utils.ChunkArrayString(req.ExamIds, 100)
	resp = make(map[string]GetExamInfoItem)
	ch := make(chan *GetExamInfoRsp, len(examIdsArr))
	wg := &sync.WaitGroup{}
	for _, ids := range examIdsArr {
		wg.Add(1)
		go func(idChunk []string) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "BatchGetExamInfo panic, err:%s", r)
				}
			}()
			defer wg.Done()
			reqChunk := GetExamInfoReq{
				AppId:           req.AppId,
				AppSecret:       req.AppSecret,
				ExamIds:         idChunk,
				ExamStuKeys:     req.ExamStuKeys,
				HasQuestionList: req.HasQuestionList,
				HasPkgList:      req.HasPkgList,
			}
			rsp, reqErr := GetExamInfo(ctx, reqChunk)
			if reqErr != nil {
				zlog.Warnf(ctx, "BatchGetExamInfo failed, err:%s", err)
				return
			}
			ch <- rsp
		}(ids)
	}
	wg.Wait()
	var mu sync.Mutex
	for rsp := range ch {
		mu.Lock()
		for examId, examDetail := range rsp.List {
			resp[examId] = examDetail
		}
		mu.Unlock()
	}
	close(ch)
	return resp, nil
}
