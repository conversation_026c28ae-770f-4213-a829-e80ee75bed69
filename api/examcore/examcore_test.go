package examcore

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestExamReport(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	ansKeys := BuildAnswerKeys(200526400, []int64{2135398111, 2135417814, 2135533012})

	req := GetReportUrlReq{ansKeys, 0, ""}

	rsp, err := GetReportUrl(ctx, req)
	fmt.Println(rsp, err)
}

func TestGetBindInfo(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/fudao_bzr/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	req := GetBindInfoReq{
		BindStrs: []string{"lesson_525158:7"},
	}
	info, _ := GetBindInfo(ctx, req)
	fmt.Println(info)
}
