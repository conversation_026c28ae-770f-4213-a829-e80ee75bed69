package plum

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UriGetTouchInfoByCourseId = "/plum/api/gettouchinfobycourseid"
)

type GetTouchInfoByCourseIdReq struct {
	CourseId int64 `json:"courseId" form:"courseId"`
}

type GetTouchInfoByCourseIdRsp struct {
	BusinessLine int64 `json:"businessLine"`
	DepartmentId int64 `json:"departmentId"`
}

func GetTouchInfoByCourseId(ctx *gin.Context, courseId int64) (*GetTouchInfoByCourseIdRsp, error) {
	params := GetTouchInfoByCourseIdReq{CourseId: courseId}

	rsp := &GetTouchInfoByCourseIdRsp{}
	if err := apis.Do(ctx, params, rsp); err != nil {
		zlog.Warnf(ctx, "GetTouchInfoByCourseId request failed, courseId: %d, err: %+v", courseId, err)
		return nil, err
	}

	return rsp, nil
}
