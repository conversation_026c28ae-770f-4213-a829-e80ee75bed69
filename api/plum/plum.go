package plum

import (
	"assistantdeskgo/api/apis"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	GetScListByCourseIds = "/plum/api/getsclistbycourseids"
	GetLeadsByCA         = "/plum/api/getvalidleadsbycourseanddeviceid"
)

type GetLeadsByCaReq struct {
	CourseId          int64 `json:"courseId" form:"courseId"`
	AssistantDeviceId int64 `json:"assistantDeviceId" form:"assistantDeviceId"`
	Page              int64 `json:"page" form:"page"`
	PageSize          int64 `json:"pageSize" form:"pageSize"`
}

type GetLeadsByCaRsp struct {
	List  []LeadsData `json:"list""`
	Total int64       `json:"total"`
}

type LeadsData struct {
	CustomUid int64 `json:"customUid"`
	LeadsId   int64 `json:"leadsId"`
	CourseId  int64 `json:"courseId"`
}

func GetLeadsByCa(ctx *gin.Context, courseId, assistantUid, page, pageSize int64) (*GetLeadsByCaRsp, error) {
	param := GetLeadsByCaReq{
		CourseId:          courseId,
		AssistantDeviceId: assistantUid,
		Page:              page,
		PageSize:          pageSize,
	}
	rsp := &GetLeadsByCaRsp{}
	if err := apis.Do(ctx, param, rsp); err != nil {
		zlog.Warnf(ctx, "GetTouchInfoByCourseId request failed, param: %+v, err: %+v", param, err)
		return nil, err
	}

	return rsp, nil
}

type GetScListByCourseIdsReq struct {
	CourseIds string `json:"courseIds" form:"courseIds"`
}

type GetScListByCourseIdsRsp struct {
	MaterialInfo    MaterialInfo  `json:"materialInfo"`
	CourseWxMapList []CourseWxMap `json:"courseWxMapList"`
}

type MaterialInfo struct {
	Id       int64 `json:"id"`
	CourseId int64 `json:"courseId"`
}

type CourseWxMap struct {
	KpUid     int64 `json:"kpUid"`
	PersonUid int64 `json:"personUid"`
}

func GetAssistantsByCourseIds(ctx *gin.Context, courseIds []int64) (map[int64]GetScListByCourseIdsRsp, error) {
	marshal, _ := json.Marshal(courseIds)
	params := GetScListByCourseIdsReq{
		CourseIds: string(marshal),
	}
	rsp := &map[int64]GetScListByCourseIdsRsp{}
	if err := apis.Do(ctx, params, rsp); err != nil {
		zlog.Warnf(ctx, "GetScListByCourseIds request failed, courseId: %s, err: %+v", marshal, err)
		return nil, err
	}

	return *rsp, nil
}
