package plum

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(UriGetTouchInfoByCourseId, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetTouchInfoByCourseIdReq{},
		Encoder:  apis.EncoderForm,
		Response: GetTouchInfoByCourseIdRsp{},
	})
	apis.Register(GetScListByCourseIds, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetScListByCourseIdsReq{},
		Encoder:  apis.EncoderForm,
		Response: GetScListByCourseIdsRsp{},
	})
	apis.Register(GetLeadsByCA, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetLeadsByCaReq{},
		Encoder:  apis.EncoderForm,
		Response: GetLeadsByCaRsp{},
	})
}
