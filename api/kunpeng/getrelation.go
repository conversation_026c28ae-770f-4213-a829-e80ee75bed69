package kunpeng

import (
	"assistantdeskgo/api/apis"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/kunpeng"
	"github.com/gin-gonic/gin"
)

const (
	UrlGetWXStudentRelation = "/kpstaff/oapi/friend/getwxstudentrelation"
)

type GetWXStudentRelationReq struct {
	StaffUid string `json:"staffUid" form:"staffUid"` //真人uid
	WeixinId string `json:"weixinId" form:"weixinId"` // 微信id
	AppId    int64  `json:"appId" form:"appId"`
	AuthInfo
}

type GetWXStudentRelationRes struct {
	StudentUid int `json:"studentUid"`
}

func GetWXStudentRelation(ctx *gin.Context, assistantUid, weixinId string) (rsp *GetWXStudentRelationRes, err error) {
	apiReq := &GetWXStudentRelationReq{
		StaffUid: assistantUid,
		WeixinId: weixinId,
		AppId:    kunpeng.AppID,
	}
	apiReq.AuthInfo = getAuthParams()

	rsp = &GetWXStudentRelationRes{}

	if err = apis.Do(ctx, apiReq, rsp, apis.IgnoreInnerError()); err != nil {
		return nil, err
	}
	return
}
