package kunpeng

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sync"
)

const (
	UriGetGroupInfo = "/kpapi/wxwork/group/getinfo"
	batchLimit      = 50
)

type GetGroupInfoReq struct {
	UserId string `json:"userId" form:"userId"` //资产ID
	CorpId string `json:"corpId" form:"corpId"` //资产ID
	ChatId string `json:"chatId" form:"chatId"` // 群ID列表
	AuthInfo
}

type GetGroupInfoRsp struct {
	GroupID      int64  `json:"groupId"`
	GroupName    string `json:"groupName"`
	Avatar       string `json:"avatar"`
	Announcement string `json:"announcement"`
	Owner        int64  `json:"owner"`
	CreateTime   int64  `json:"createTime"`
	MemberCount  int64  `json:"memberCount"`
	GroupType    int64  `json:"groupType"`
}

func GetGroupInfo(ctx *gin.Context, req GetGroupInfoReq) (rsp *GetGroupInfoRsp, err error) {
	req.AuthInfo = getAuthParams()
	rsp = &GetGroupInfoRsp{}
	if err = apis.Do(ctx, req, rsp, apis.IgnoreInnerError()); err != nil {
		return nil, err
	}
	return
}

func BatchGetGroupInfoByIds(ctx *gin.Context, groupIds []int64, deviceInfo *mesh.GetDeviceListByDeviceUidsRsq) (resp map[string]*GetGroupInfoRsp, err error) {
	resp = make(map[string]*GetGroupInfoRsp)
	groupIdArr := utils.ChunkArrayInt64(groupIds, batchLimit)
	groups := make([]*GetGroupInfoRsp, 0)
	for _, ids := range groupIdArr {
		groups, err = getGroupInfoByIds(ctx, ids, deviceInfo)
		if err != nil {
			return
		}

		for _, groupInfo := range groups {
			groupId := cast.ToString(groupInfo.GroupID)
			resp[groupId] = groupInfo
		}
	}
	return
}

func getGroupInfoByIds(ctx *gin.Context, groupIds []int64, deviceInfo *mesh.GetDeviceListByDeviceUidsRsq) (resp []*GetGroupInfoRsp, err error) {
	ch := make(chan *GetGroupInfoRsp, len(groupIds))
	wg := &sync.WaitGroup{}
	for _, groupId := range groupIds {
		wg.Add(1)
		go func(groupId int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetGroupInfoByIds panic, err:%s", r)
				}
			}()
			defer wg.Done()

			req := GetGroupInfoReq{
				UserId: deviceInfo.WecomUserID,
				CorpId: deviceInfo.WecomCorpID,
				ChatId: cast.ToString(groupId),
			}

			groupInfo, reqErr := GetGroupInfo(ctx, req)

			if reqErr != nil {
				zlog.Warnf(ctx, "GetGroupInfoByIds failed, groupId:%d, err:%s", groupId, err)
				return
			}
			ch <- groupInfo
		}(groupId)
	}
	wg.Wait()
	close(ch)

	result := make([]*GetGroupInfoRsp, 0)
	for groupInfo := range ch {
		result = append(result, groupInfo)
	}

	return result, nil
}
