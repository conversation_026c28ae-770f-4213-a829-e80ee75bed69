package kunpeng

import "github.com/gin-gonic/gin"

type GetWxInfoByUidRsp struct {
	Id             int64         `json:"id"`
	StaffUid       int64         `json:"staffUid"`
	UserId         string        `json:"userId"`
	CorpId         string        `json:"corpId"`
	StudentUid     int64         `json:"studentUid"`
	RemoteId       int64         `json:"remoteId"`
	RelationType   int64         `json:"relationType"`
	Deleted        int64         `json:"deleted"`
	CreateTime     int64         `json:"createTime"`
	UpdateTime     string        `json:"updateTime"`
	ExtFlag        int64         `json:"extFlag"`
	ExtData        []interface{} `json:"extData"`
	WeixinId       string        `json:"weixinId"`
	Remark         string        `json:"remark"`
	WeixinName     string        `json:"weixinName"`
	WeixinNickName string        `json:"weixinNickName"`
	WeixinAvatar   string        `json:"weixinAvatar"`
}

type GetWxInfoByUidReq struct {
	StaffUid int64   `json:"staffUid" form:"staffUid"` //资产ID
	Uids     []int64 `json:"uids" form:"uids"`         //remoteId
	AppId    int     `json:"appId" form:"appId"`       // 鲲鹏ID
	AuthInfo
}

const (
	pathGetStudentRelation = "/kpstaff/oapi/friend/getwxinfobyuid"
)

func GetWxInfoByUid(ctx *gin.Context, req GetWxInfoByUidReq) ([]GetWxInfoByUidRsp, error) {
	req.AuthInfo = getAuthParams()
	params := map[string]interface{}{
		"staffUid":  req.StaffUid,
		"uids":      req.Uids,
		"appId":     2,
		"timeStamp": req.TimeStamp,
		"from":      req.From,
		"fromSign":  req.FromSign,
	}

	var output []GetWxInfoByUidRsp
	err := ralGet(ctx, pathGetStudentRelation, params, &output)
	if err != nil {
		return nil, err
	}
	return output, nil

}
