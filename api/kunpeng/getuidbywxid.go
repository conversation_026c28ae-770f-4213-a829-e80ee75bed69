package kunpeng

import (
	"assistantdeskgo/api"
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UriGetStudentIdByWxId = "/kpstaff/oapi/friend/getuidbywxid"
)

type GetUidByWxIdRsp struct {
	Id           int64         `json:"id"`
	StaffUid     int64         `json:"staffUid"`
	UserId       string        `json:"userId"`
	CorpId       string        `json:"corpId"`
	StudentUid   int64         `json:"studentUid"`
	RemoteId     int64         `json:"remoteId"`
	RelationType int64         `json:"relationType"`
	Deleted      int64         `json:"deleted"`
	CreateTime   int64         `json:"createTime"`
	UpdateTime   string        `json:"updateTime"`
	ExtFlag      int64         `json:"extFlag"`
	ExtData      []interface{} `json:"extData"`
	WeixinId     string        `json:"weixinId"`
}

type GetUidByWxIdReq struct {
	StaffUid int64    `json:"staffUid" form:"staffUid"` //资产ID
	WxIds    []string `json:"wxIds" form:"wxIds"`       //remoteId
	AppId    int      `json:"appId" form:"appId"`       // 鲲鹏ID
	AuthInfo
}

func GetUidByWxId(ctx *gin.Context, req GetUidByWxIdReq) (rsp []GetUidByWxIdRsp, err error) {
	req.AuthInfo = getAuthParams()
	params := map[string]interface{}{
		"staffUid":  req.StaffUid,
		"wxIds":     req.WxIds,
		"appId":     2,
		"timeStamp": req.TimeStamp,
		"from":      req.From,
		"fromSign":  req.FromSign,
	}
	rsp = []GetUidByWxIdRsp{}
	err = ralGet(ctx, UriGetStudentIdByWxId, params, &rsp)
	if err != nil {
		return nil, err
	}
	return
}

func ralGet(ctx *gin.Context, url string, params map[string]interface{}, output interface{}) error {

	opt := base.HttpRequestOptions{
		RequestBody: params,
		Encode:      base.EncodeForm,
	}
	resp, err := conf.API.KpStaff.HttpGet(ctx, url, opt)
	if err != nil {
		return err
	}

	var apiResp api.BzrResponse
	err = api.DecodeResponse(ctx, resp, &apiResp, output) //decode
	if err != nil {
		zlog.Warnf(ctx, "RalGet decode failed, url[%s] Detail[%+v], err:%s", url, params, err)
		return components.ErrorDecode.Wrap(err)
	}
	return err
}
