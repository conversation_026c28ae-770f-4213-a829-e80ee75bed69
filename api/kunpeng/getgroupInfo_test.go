package kunpeng

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetGroupInfo(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	req := GetGroupInfoReq{
		UserId:   "qw240411000002",
		CorpId:   "ww55f2721cf084c185",
		ChatId:   "10866563570715867",
		AuthInfo: AuthInfo{},
	}
	rsp, err := GetGroupInfo(ctx, req)
	fmt.Println(rsp, err)
}

func TestBatchGetGroupInfoByIds(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	deviceInfo, _ := mesh.GetDeviceListByDeviceUid(ctx, 2556667001)
	rsp, err := BatchGetGroupInfoByIds(ctx, []int64{10866563570715867}, deviceInfo)
	fmt.Println(rsp, err)
}
