package kunpeng

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	UriGetMaterialList = "/kpapi/wxwork/message/getmateriallist"
	DefaultPageNum     = 5
)

type GetMaterialListReq struct {
	CorpID   string `json:"corpId" form:"corpId"`       // 企微corp
	UserID   string `json:"userId" form:"userId"`       // 员工ID
	StaffUid string `json:"staffUid" form:"staffUid"`   // 资产ID
	SndTime  int64  `json:"startTime" form:"startTime"` // 开始时间
	EndTime  int64  `json:"endTime" form:"endTime"`     // 接收时间
	AuthInfo
}

type GetMaterialListRsp struct {
	List []MaterialInfo `json:"list"`
}

type MaterialContent struct {
	Avatar       string `json:"avatar"`
	CoverURL     string `json:"coverUrl"`
	Desc         string `json:"desc"`
	Extras       string `json:"extras"`
	FeedType     int64  `json:"feedType"`
	Nickname     string `json:"nickname"`
	ThumbURL     string `json:"thumbUrl"`
	URL          string `json:"url"`
	Eid          string `json:"eid"`
	ExpireTime   string `json:"expireTime"`
	ThumbPid     string `json:"thumbPid"`
	ShowThumbURL string `json:"showThumbUrl"`
}

type MaterialInfo struct {
	MsgType    int64           `json:"msgType"`
	MsgContent MaterialContent `json:"msgContent"`
	MaterialID int64           `json:"materialId"`
}

func GetMaterialList(ctx *gin.Context, req *GetMaterialListReq) (rsp *GetMaterialListRsp, err error) {
	req.AuthInfo = getAuthParams()
	rsp = &GetMaterialListRsp{}
	if err = apis.Do(ctx, req, rsp, apis.IgnoreInnerError()); err != nil {
		return nil, err
	}
	return
}
