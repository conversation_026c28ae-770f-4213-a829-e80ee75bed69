package kunpeng

import (
	"assistantdeskgo/api/apis"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/kunpeng"
	"github.com/gin-gonic/gin"
)

const (
	UriGetBelonger = "/kpstaff/oapi/friend/getbelonger"
)

type GetBelongerReq struct {
	StaffUid   string `json:"staffUid" form:"staffUid"`     //真人uid
	StudentUid string `json:"studentUid" form:"studentUid"` //学生ID
	WeixinId   string `json:"weixinId" form:"weixinId"`     // 微信id
	AppId      int64  `json:"appId" form:"appId"`
	AuthInfo
}

type GetBelongerRes struct {
	Belonger    int    `json:"belonger" form:"belonger"`       //belonger
	BelongerStr string `json:"belongerStr" form:"belongerStr"` //belongerStr
}

func GetBelonger(ctx *gin.Context, assistantUid, studentUid, weixinId string) (rsp *GetBelongerRes, err error) {
	apiReq := &GetBelongerReq{
		StaffUid:   assistantUid,
		StudentUid: studentUid,
		WeixinId:   weixinId,
		AppId:      kunpeng.AppID,
	}
	apiReq.AuthInfo = getAuthParams()

	rsp = &GetBelongerRes{}

	if err = apis.Do(ctx, apiReq, rsp, apis.IgnoreInnerError()); err != nil {
		return nil, err
	}
	return
}
