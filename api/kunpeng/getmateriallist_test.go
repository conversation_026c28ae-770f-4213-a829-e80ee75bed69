package kunpeng

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"github.com/gin-gonic/gin"
	"testing"
	"time"
)

func TestGetMaterialList(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	req := GetMaterialListReq{
		CorpID:   "ww55f2721cf084c185",
		UserID:   "qw231225000002",
		StaffUid: "f4ae8cf427ac1ad007c638e82faad9e2",
		SndTime:  1,
		EndTime:  time.Now().Unix(),
		AuthInfo: AuthInfo{},
	}

	ret, err := GetMaterialList(ctx, &req)
	fmt.Println(ret, err)
}

func TestEncode(t *testing.T) {
	str, _ := utils.EncodeUid(4300285971)
	id, _ := utils.DecodeUid(str)
	fmt.Println(str, id)
	str1, _ := utils.EncodeUid(1237)
	id1, _ := utils.DecodeUid(str1)
	fmt.Println(str1, id1)
}
