package kunpeng

import (
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
)

const (
	UriGetFileUrl = "/kpapi/basic/getfileurl"
)

type GetFileUrlReq struct {
	FileNameList []string `json:"fileNameList"`
	AuthInfo
}

type GetFileUrlRsp struct {
	ErrNo  int               `json:"errNo"`
	ErrStr string            `json:"errstr"`
	Data   map[string]string `json:"data"`
}

func (this *GetFileUrlReq) ToMap() map[string]interface{} {
	res := make(map[string]interface{})
	res["fileNameList"] = this.FileNameList
	res["timeStamp"] = this.TimeStamp
	res["fromSign"] = this.FromSign
	res["from"] = this.From
	return res
}

func GetFileUrl(ctx *gin.Context, fileName string) (response string, err error) {
	sendData := GetFileUrlReq{
		FileNameList: []string{fileName},
	}
	sendData.AuthInfo = getAuthParams()
	opt := base.HttpRequestOptions{
		RequestBody: sendData.ToMap(),
		Encode:      base.EncodeForm,
		Headers:     map[string]string{"Content-Type": "application/x-www-form-urlencoded"},
	}
	zlog.Infof(ctx, "GetFileUrl req:%#v", sendData)
	result, err := conf.API.KunPeng.HttpPost(ctx, UriGetFileUrl, opt)
	if err != nil {
		return response, components.ErrorSystemError.WrapPrintf(err, "RequestBody=%+v", opt.RequestBody)
	}
	var resp GetFileUrlRsp
	if err = json.Unmarshal(result.Response, &resp); err != nil {
		zlog.Errorf(ctx, "http response decode err, err: %s", err.Error())
		return response, err
	}
	if resp.ErrNo != 0 {
		zlog.Errorf(ctx, "mqsResp err, errNo:%d, errMsg:%s", resp.ErrNo, resp.ErrStr)
		return response, errors.New(fmt.Sprintf("call MsgCommit error,errNo=%d, errMsg=%s", resp.ErrNo, resp.ErrStr))
	}
	if response, ok := resp.Data[fileName]; ok {
		return response, nil
	}

	return "", nil
}
