package kunpeng

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/conf"
	"assistantdeskgo/utils"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"sync"
)

const (
	UriGetGroupList = "/kpapi/wxwork/group/getlist"
	ApiMaxNum       = 50
)

type GetGroupListReq struct {
	UserId     string   `json:"userId" form:"userId"`         //资产ID
	CorpId     string   `json:"corpId" form:"corpId"`         //资产ID
	ChatIdList []string `json:"chatIdList" form:"chatIdList"` // 群ID列表
	Page       int64    `json:"page" form:"page"`             //页码
	PageSize   int64    `json:"pageSize" form:"pageSize"`     //每页条数
	AuthInfo
}

type GetGroupListRsp struct {
	List  []GroupInfo `json:"list"`
	Total int64       `json:"total"`
}

type GroupInfo struct {
	GroupName   string       `json:"groupName"`
	GroupAvatar string       `json:"avatar"`
	GroupId     int64        `json:"groupId"`
	MemberList  []MemberInfo `json:"memberList"`
}

type MemberInfo struct {
	RemoteId     int64  `json:"remoteId"`
	WorkGroupId  int64  `json:"workGroupId"`
	ShowNickName string `json:"showNickName"`
	NickName     string `json:"nickName"`
}

func GetGroupListByIds(ctx *gin.Context, groupIdList []int64, deviceInfo mesh.GetDeviceListByDeviceUidsRsq) (resp map[string]GroupInfo, err error) {
	groupIds := utils.Int64ArrayToStringArray(groupIdList)
	chunks := utils.ChunkArrayInt64(groupIdList, ApiMaxNum)

	wg := &sync.WaitGroup{}
	tmpData := &sync.Map{}
	for _, chunk := range chunks {
		wg.Add(1)
		groupIds = utils.Int64ArrayToStringArray(chunk)
		go func(groupIds []string) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetGroupListByIds panic, err:%s", r)
				}
			}()
			defer wg.Done()
			req := GetGroupListReq{
				CorpId:     deviceInfo.WecomCorpID,
				UserId:     deviceInfo.WecomUserID,
				ChatIdList: groupIds,
				Page:       1,
				PageSize:   ApiMaxNum,
			}
			singleRet, er := GetGroupList(ctx, req)

			if er != nil {
				zlog.Warnf(ctx, "GetGroupListByIds failed, groupIds:%+v, err:%s", groupIds, err)
				return
			}
			for _, item := range singleRet.List {
				tmpData.Store(item.GroupId, item)
			}
		}(groupIds)
	}
	wg.Wait()

	result := make(map[string]GroupInfo)
	tmpData.Range(func(key, value interface{}) bool {
		k, _ := key.(int64)
		ke := strconv.FormatInt(k, 10)
		if v, ok := value.(GroupInfo); ok {
			result[ke] = v
		}
		return true
	})

	return result, nil
}

func GetGroupList(ctx *gin.Context, req GetGroupListReq) (rsp GetGroupListRsp, err error) {
	req.AuthInfo = getAuthParams()
	//rsp = &GetGroupListRsp{}
	//if err = apis.Do(ctx, req, rsp, apis.IgnoreInnerError()); err != nil {
	//	return nil, err
	//}
	//return
	param := map[string]interface{}{
		"userId":     req.UserId,
		"corpId":     req.CorpId,
		"chatIdList": req.ChatIdList,
		"page":       req.Page,
		"pageSize":   req.PageSize,
		"timeStamp":  req.TimeStamp,
		"fromSign":   req.FromSign,
		"from":       req.From,
	}

	opt := base.HttpRequestOptions{
		RequestBody: param,
		Encode:      base.EncodeForm,
	}

	get, err := conf.API.KunPeng.HttpGet(ctx, UriGetGroupList, opt)
	if err != nil {
		return
	}
	var res struct {
		ErrNo  int             `json:"errNo"`
		ErrStr string          `json:"errStr"`
		Data   json.RawMessage `json:"data"`
	}

	if err = json.Unmarshal(get.Response, &res); err != nil {
		return
	}
	if res.ErrNo != 0 {
		return rsp, base.Error{ErrNo: res.ErrNo, ErrMsg: res.ErrStr}
	}
	if err = json.Unmarshal(res.Data, &rsp); err != nil {
		return
	}

	return rsp, nil
}
