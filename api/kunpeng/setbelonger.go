package kunpeng

import (
	"assistantdeskgo/api/apis"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/kunpeng"
	"github.com/gin-gonic/gin"
)

const (
	UriSetBelonger = "/kpstaff/oapi/friend/setbelonger"
)

type BelongerReq struct {
	StaffUid   string `json:"staffUid" form:"staffUid"`     //真人uid
	StudentUid string `json:"studentUid" form:"studentUid"` //学生ID
	WeixinId   string `json:"weixinId" form:"weixinId"`     // 微信id
	Belonger   int64  `json:"belonger" form:"belonger"`
	AppId      int64  `json:"appId" form:"appId"`
	AuthInfo
}

func SetBelonger(ctx *gin.Context, assistantUid, studentUid, weixinId string, belonger int64) (rsp *map[string]interface{}, err error) {
	apiReq := &BelongerReq{
		StaffUid:   assistantUid,
		StudentUid: studentUid,
		WeixinId:   weixinId,
		Belonger:   belonger,
		AppId:      kunpeng.AppID,
	}
	apiReq.AuthInfo = getAuthParams()

	if err = apis.Do(ctx, apiReq, rsp, apis.IgnoreInnerError()); err != nil {
		return nil, err
	}
	return
}
