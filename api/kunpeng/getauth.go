package kunpeng

import (
	"assistantdeskgo/api/apis"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

const (
	SignFrom  = "assistantdesk"
	SignToken = "kpTtDykh"

	AppID = 2 // 企微
)

type AuthInfo struct {
	TimeStamp int64  `json:"timeStamp" form:"timeStamp"`
	FromSign  string `json:"fromSign" form:"fromSign"`
	From      string `json:"from" form:"from"`
}

// getAuthParams 获取鉴权信息
func getAuthParams() AuthInfo {
	timestamp := time.Now().Unix()
	ret := AuthInfo{
		TimeStamp: timestamp,
		FromSign:  getSign(timestamp, SignFrom, SignToken),
		From:      SignFrom,
	}
	return ret
}

// getSign 获取签名信息
func getSign(timeStamp int64, from, token string) string {
	str := fmt.Sprintf("%d%s%s", timeStamp, from, token)
	passSha1 := sha1.New()
	passSha1.Write([]byte(str))
	sha1Value := hex.EncodeToString(passSha1.Sum(nil))

	h := md5.New()
	h.Write([]byte(sha1Value))
	cipherStr := h.Sum(nil)
	return strings.ToUpper(hex.EncodeToString(cipherStr))
}

func getHeader() apis.Option {
	headers := make(map[string]string)

	headers = map[string]string{
		"refer":        SignFrom,
		"Content-Type": "application/x-www-form-urlencoded",
	}

	return apis.WithHeaders(headers)
}
