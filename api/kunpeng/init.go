package kunpeng

import (
	"net/http"

	"assistantdeskgo/api/apis"
)

func init() {
	apis.Register(UriGetFileUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetFileUrlReq{},
		Encoder:  apis.EncoderJson,
		Response: map[string]string{},
	})
	apis.Register(UriGetMaterialList, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetMaterialListReq{},
		Encoder:  apis.EncoderForm,
		Response: GetMaterialListRsp{},
	})
	apis.Register(UriGetGroupList, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetGroupListReq{},
		Encoder:  apis.EncoderQuery,
		Response: GetGroupListRsp{},
	})
	apis.Register(UriGetGroupInfo, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetGroupInfoReq{},
		Encoder:  apis.EncoderForm,
		Response: GetGroupInfoRsp{},
	})
	apis.Register(UriSetBelonger, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  BelongerReq{},
		Encoder:  apis.EncoderForm,
		Response: map[string]interface{}{},
	})
	apis.Register(UriGetBelonger, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetBelongerReq{},
		Encoder:  apis.EncoderForm,
		Response: GetBelongerRes{},
	})
	apis.Register(UrlGetWXStudentRelation, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetWXStudentRelationReq{},
		Encoder:  apis.EncoderForm,
		Response: GetWXStudentRelationRes{},
	})
}
