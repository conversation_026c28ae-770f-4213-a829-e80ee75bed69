package kunpeng

import (
	"assistantdeskgo/api"
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type IdExchangeReq struct {
	IdList []string `json:"idList" from:"idList"`
	Type   int64    `json:"type" form:"type"`     //remoteId
	CorpId string   `json:"cropId" form:"cropId"` // 鲲鹏ID
	AuthInfo
}

type IdExchangeStaffInfoRep struct {
	StaffUid int64  `json:"staffUid" from:"staffUid"`
	UserId   string `json:"userid" form:"userid"` //remoteId
	CorpId   string `json:"cropId" form:"cropId"` // 鲲鹏ID

}

const (
	pathIdExchange = "/kpapi/wxwork/basic/idexchange"
)

func IdExchange(ctx *gin.Context, req IdExchangeReq) (rsp map[string]interface{}, err error) {
	req.AuthInfo = getAuthParams()
	params := map[string]interface{}{
		"idList":    req.IdList,
		"type":      req.Type,
		"corpId":    req.CorpId,
		"appId":     2,
		"timeStamp": req.TimeStamp,
		"from":      req.From,
		"fromSign":  req.FromSign,
	}
	var output map[string]interface{}
	err = kpApiRalGet(ctx, pathIdExchange, params, &output)
	if err != nil {
		return nil, err
	}
	return output, nil

}

func IdExchangeStaffInfo(ctx *gin.Context, req IdExchangeReq) (map[string]IdExchangeStaffInfoRep, error) {
	req.AuthInfo = getAuthParams()
	params := map[string]interface{}{
		"idList":    req.IdList,
		"type":      req.Type,
		"corpId":    req.CorpId,
		"appId":     2,
		"timeStamp": req.TimeStamp,
		"from":      req.From,
		"fromSign":  req.FromSign,
	}
	var output map[string]IdExchangeStaffInfoRep
	err := kpApiRalGet(ctx, pathIdExchange, params, &output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func kpApiRalGet(ctx *gin.Context, url string, params map[string]interface{}, output interface{}) error {

	opt := base.HttpRequestOptions{
		RequestBody: params,
		Encode:      base.EncodeForm,
	}
	resp, err := conf.API.KunPeng.HttpGet(ctx, url, opt)
	if err != nil {
		return err
	}

	var apiResp api.BzrResponse
	err = api.DecodeResponse(ctx, resp, &apiResp, output) //decode
	if err != nil {
		zlog.Warnf(ctx, "RalGet decode failed, url[%s] Detail[%+v], err:%s", url, params, err)
		return components.ErrorDecode.Wrap(err)
	}
	return err
}
