package decode

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
)

// 解码: 我们需要几种解码?
// 1. 正常解码: 这种情况是接口的字段类型都是正确的. 比如  data 约定了一定要数组或map, 具体字段一定是 int 或 string等. 这种情况直接
// 使用标准库的 json 解码就可以
// 2. 兼容 data 的解码, 这种情况有数据的时候, data 是个 map, 但是空的时候, 返回 []. 直接使用 json 解码就会有问题
// 3. 兼容字段类型错误的解码. 这种情况需要能正确的转换回来. 比如本来是int, 但是可能返回 string (带引号的数字)
// 4. 能同时做到2和3

type Response interface {
	GetErrNo() int
	GetErrStr() string
	GetData() interface{}
}

// 辅导侧大部分是这种结构
type BzrResponse struct {
	ErrNo  int         `json:"errNo"`
	ErrStr string      `json:"errStr"`
	Data   interface{} `json:"data"`
}

func (b BzrResponse) GetErrNo() int {
	return b.ErrNo
}

func (b BzrResponse) GetErrStr() string {
	return b.ErrStr
}

func (b BzrResponse) GetData() interface{} {
	return b.Data
}

// dataproxy是这种结构
type DataResponse struct {
	ErrNo  int         `json:"errNo"`
	ErrStr string      `json:"errMsg"`
	Data   interface{} `json:"data"`
}

func (b DataResponse) GetErrNo() int {
	return b.ErrNo
}

func (b DataResponse) GetErrStr() string {
	return b.ErrStr
}

func (b DataResponse) GetData() interface{} {
	return b.Data
}

// 课中有些是这样的, errstr 全是小写的
type LowerCaseResponse struct {
	ErrNo  int         `json:"errNo"`
	ErrStr string      `json:"errstr"`
	Data   interface{} `json:"data"`
}

func (b LowerCaseResponse) GetErrNo() int {
	return b.ErrNo
}

func (b LowerCaseResponse) GetErrStr() string {
	return b.ErrStr
}

func (b LowerCaseResponse) GetData() interface{} {
	return b.Data
}

type AttendTimeLine struct {
	Uid   int   `json:"uid"`
	Times []int `json:"times"`
}
type AttendSingleRet struct {
	StudentList []AttendTimeLine `json:"studentList"`
}

// 长链接是这种结构
type LongServiceResponse struct {
	ErrNo  int             `json:"err_no"`
	ErrStr string          `json:"err_msg"`
	Data   AttendSingleRet `json:"data"`
}

func (b LongServiceResponse) GetErrNo() int {
	return b.ErrNo
}

func (b LongServiceResponse) GetErrStr() string {
	return b.ErrStr
}

func (b LongServiceResponse) GetData() interface{} {
	return b.Data
}

// 解码 JSON 数据
// TODO: 这个可能在返回 data 为 [] 的时候会有问题
func DecodeResponseJSON(res *base.ApiResult, output Response) (errno int, err error) {
	if output == nil {
		return 0, errors.New("param error")
	}

	if res.HttpCode != http.StatusOK {
		return 0, fmt.Errorf("http code invalid, %d", res.HttpCode)
	}

	if err = json.Unmarshal(res.Response, &output); err != nil {
		zlog.Errorf(res.Ctx, "http response decode err, err: %s", err)
		return errno, err
	}

	errno = output.GetErrNo()
	if errno != 0 {
		zlog.Errorf(res.Ctx, "http response errNo:%d, errStr:%s", errno, output.GetErrStr())
		return errno, err
	}

	return errno, nil
}

// 解码成一般形式, data 是 interface{}
func DecodeResponseGeneral(res *base.ApiResult, respStruct Response) error {
	if respStruct == nil {
		return errors.New("param error")
	}

	if res.HttpCode != http.StatusOK {
		return fmt.Errorf("http code invalid, %d", res.HttpCode)
	}

	if err := json.Unmarshal(res.Response, respStruct); err != nil {
		zlog.Errorf(res.Ctx, "http response is not json. json decode failed, resp:%s, struct:(%T), err: %s",
			res.Response, respStruct, err)
		return err
	}

	errno := respStruct.GetErrNo()
	if errno != 0 {
		var err error
		if respStruct.GetErrStr() != "" {
			err = errors.New(respStruct.GetErrStr())
		} else {
			err = errors.New("errNo is not 0")
		}
		zlog.Warnf(res.Ctx, "http response errNo != 0, errNo:%d, errStr:%s, error:%s, body:%s", errno, respStruct.GetErrStr(), err, res.Response)
		return err
	}

	return nil
}

// 对 API 返回结果解码, 这个函数只支持接口正确情况下 data 是map的形式, 接口错误可以是 []
// 解析 map 类型的 data. map 字段需要是可以是不严格类型
func DecodeResponse(ctx *gin.Context, res *base.ApiResult, respStruct Response, output interface{}) error {
	if res != nil && res.Ctx == nil {
		res.Ctx = ctx
	}

	_, err := decodeDataMapImp(res, respStruct, output, decodeMapFuncCompatible2)
	return err
}

// 解析 map 类型的 data. map 字段需要是严格类型
func DecodeDataMapStrict(ctx *gin.Context, res *base.ApiResult, respStruct Response, output interface{}) error {
	if res != nil && res.Ctx == nil {
		res.Ctx = ctx
	}

	_, err := decodeDataMapImp(res, respStruct, output, decodeMapFuncStrict)
	return err
}

// 解析 map 类型的 data. map 字段需要是可以是不严格类型
func DecodeDataMapCompatible(ctx *gin.Context, res *base.ApiResult, respStruct Response, output interface{}) error {
	if res != nil && res.Ctx == nil {
		res.Ctx = ctx
	}

	_, err := decodeDataMapImp(res, respStruct, output, decodeMapFuncCompatible2)
	return err
}

type decodeMapFunc func(*gin.Context, map[string]interface{}, interface{}) error

// 解码 map data. 如果一个接口返回结果的 data 是个map 用这个函数. data 也可以是空数组. 兼容PHP
// 但是 data 对应的 struct 是严格类型的
func decodeDataMapImp(res *base.ApiResult, respStruct Response, output interface{}, decode decodeMapFunc) (errno int, err error) {
	if err := DecodeResponseGeneral(res, respStruct); err != nil {
		return respStruct.GetErrNo(), err
	}

	errno = respStruct.GetErrNo()
	err = DecodeInterface(res.Ctx, respStruct.GetData(), output)
	return errno, err
}

func decodeMapFuncStrict(ctx *gin.Context, data map[string]interface{}, output interface{}) error {
	if err := mapstructure.Decode(data, output); err != nil {
		zlog.Warnf(ctx, "mapstructure decode failed, data:%+v, %s", data, err)
		return err
	}

	return nil
}

// 解码 map data. 如果一个接口返回结果的 data 是个map 用这个函数. data 也可以是空数组. 兼容PHP
// 但是 data 对应的 struct 是兼容类型的
func decodeMapFuncCompatible(ctx *gin.Context, data map[string]interface{}, output interface{}) error {
	var bytes []byte
	var err error
	if bytes, err = json.Marshal(data); err != nil {
		zlog.Warnf(ctx, "json mrshal data failed, data:%+v, %s", data, err)
		return err
	}

	if err = json.Unmarshal(bytes, output); err != nil {
		zlog.Warnf(ctx, "json unmarshal data failed, bytes:%s, %s", bytes, err)
		return err
	}

	return nil
}

// 解码 map data. 如果一个接口返回结果的 data 是个map 用这个函数. data 也可以是空数组. 兼容PHP
// 但是 data 对应的 struct 是兼容类型的
func decodeMapFuncCompatible2(ctx *gin.Context, data map[string]interface{}, output interface{}) error {
	config := mapstructure.DecoderConfig{
		WeaklyTypedInput: true,
		Result:           output,
	}
	decoder, err := mapstructure.NewDecoder(&config)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure newDecoder failed, %s", err)
		return err
	}

	err = decoder.Decode(data)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure decode failed, err:%s", err)
	}

	return err
}

func decodeSlice(ctx *gin.Context, input []interface{}, output interface{}) error {
	config := mapstructure.DecoderConfig{
		WeaklyTypedInput: true,
		Result:           output,
	}
	decoder, err := mapstructure.NewDecoder(&config)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure newDecoder failed, %s", err)
		return err
	}

	err = decoder.Decode(input)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure decode failed, err:%s", err)
	}

	return err
}

// 解码 map data. 如果一个接口返回结果的 data 是个map 用这个函数. data 也可以是空数组. 兼容PHP
// 但是 data 对应的 struct 是严格类型的
func DecodeInterface(ctx *gin.Context, data interface{}, output interface{}) error {
	if data == nil {
		zlog.Infof(ctx, "data is null")
		return nil
	}

	switch data.(type) {
	// process data interface{}
	case []interface{}:
		sliceRet := data.([]interface{})
		// PHP 返回空对象也是个空的数组
		if len(sliceRet) == 0 {
			return nil
		} else {
			err := decodeSlice(ctx, sliceRet, output)
			return err
		}
	case map[string]interface{}:
		mapRet := data.(map[string]interface{})
		if err := decodeMapFuncCompatible2(ctx, mapRet, output); err != nil {
			return err
		}
	default:
		err := errors.New("can not support data type")
		zlog.Warnf(ctx, "can not support data type, (%T), %+v", data, data)
		return err
	}

	return nil
}
