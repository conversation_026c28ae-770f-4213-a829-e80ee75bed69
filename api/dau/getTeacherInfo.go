package dau

import (
	"assistantdeskgo/api/zbcore"
	"assistantdeskgo/utils"
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	ApiMaxNum = 100

	module                = "dau"
	entityTeacher         = "teacher"
	apiGetKVByTeacherUids = "getKVByTeacherUids"
)

type TeacherInfo struct {
	TeacherUid    int    `json:"teacherUid" mapstructure:"teacherUid"`
	TeacherName   string `json:"teacherName" mapstructure:"teacherName"`
	TeacherAvatar string `json:"teacherAvatar" mapstructure:"teacherAvatar"`
}

func getTeacherFields() []string {
	return []string{
		"teacherUid",
		"teacherName",
		"teacherAvatar",
	}
}

func GetTeachers(ctx *gin.Context, teacherUids []int64, fields []string) (map[int64]TeacherInfo, error) {
	if len(teacherUids) == 0 {
		return make(map[int64]TeacherInfo), nil
	}

	//if len(fields) == 0 {
	//	fields = getTeacherFields()
	//} else {
	//	if !utils.InArrayString("teacherUid", fields) {
	//		fields = append(fields, "teacherUid")
	//	}
	//}

	if len(teacherUids) > ApiMaxNum {
		// concurrency
		return getTeacherBaseInBatchByUids(ctx, teacherUids, fields), nil
	} else {
		return getKVByTeacherUids(ctx, teacherUids, fields)
	}
}

// 分批根据Uid获取老师信息接口
func getTeacherBaseInBatchByUids(ctx *gin.Context, studentUids []int64, fields []string) map[int64]TeacherInfo {
	chunks := utils.ChunkArrayInt64(studentUids, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int64]TeacherInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(uids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "getTeacherBaseInBatchByUids panic, err:%s", r)
				}
			}()
			defer wg.Done()
			singleRet, err := getKVByTeacherUids(ctx, uids, fields)
			if err != nil {
				zlog.Warnf(ctx, "getKVByTeacherUids failed, teacherUid:%+v, err:%s", uids, err)
				return
			}

			ch <- singleRet
		}(chunk)
	}

	result := make(map[int64]TeacherInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "getTeacherBaseInBatchByUids panic, err:%s", r)
			}
		}()
		defer colWg.Done()
		for stuMap := range ch {
			for stuUid := range stuMap {
				result[stuUid] = stuMap[stuUid]
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

// 老师UidKv接口
func getKVByTeacherUids(ctx *gin.Context, teacherUids []int64, fields []string) (map[int64]TeacherInfo, error) {
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, module, entityTeacher, apiGetKVByTeacherUids, false, env.GetAppName())
	arrParams := map[string]interface{}{
		"teacherUid": teacherUids,
		"fields":     fields,
	}

	apiOut := make(map[int64]TeacherInfo)
	apiResp, err := zbcore.PostDau(ctx, arrParams, arrHeader, &apiOut)
	zlog.Debugf(ctx, "apiResp:%+v, err:%+v", apiResp, err)
	return apiOut, err
}
