package dau

import (
	"assistantdeskgo/api/zbcore"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

const (
	entityStudent = "student"
)

type StudentInfo struct {
	StudentUid    int    `json:"studentUid" mapstructure:"studentUid"`
	StudentName   string `json:"studentName" mapstructure:"studentName"`
	Sex           int    `json:"sex" mapstructure:"sex"`
	Grade         int    `json:"grade" mapstructure:"grade"`
	Phone         string `json:"phone" mapstructure:"phone"`
	Guardian      string `json:"guardian" mapstructure:"guardian"`
	GuardianPhone string `json:"guardianPhone" mapstructure:"guardianPhone"`
	ParentWebchat string `json:"parentWebchat" mapstructure:"parentWebchat"`
	FatherPhone   string `json:"fatherPhone" mapstructure:"fatherPhone"`
	MotherPhone   string `json:"motherPhone" mapstructure:"motherPhone"`
	Area          string `json:"area" mapstructure:"area"`
	School        string `json:"school" mapstructure:"school"`
	RegisterPhone string `json:"registerPhone" mapstructure:"registerPhone"`
	Avatar        string `json:"avatar" mapstructure:"avatar"`
	Uname         string `json:"uname" mapstructure:"uname"`
	RegTime       string `json:"regTime" mapstructure:"regTime"`
}

// 获取学生的字段
func getStudentFields() []string {
	return []string{
		"studentUid",
		"studentName",
		"sex",
		"grade",
		"phone",
		"guardian",
		"guardianPhone",
		"parentWebchat",
		"fatherPhone",
		"motherPhone",
		"area",
		"school",
		"registerPhone", //注册手机号(平台)
		"avatar",        //头像(平台)
		"uname",         //昵称(平台)
		"regTime",       //注册时间(平台)
	}
}

// 获取学生信息
func GetStudents(ctx *gin.Context, studentUids []int64, fields []string) (map[int64]StudentInfo, error) {
	if len(studentUids) == 0 {
		return make(map[int64]StudentInfo), nil
	}

	if len(fields) == 0 {
		fields = getStudentFields()
	} else {
		if !utils.InArrayString("studentUid", fields) {
			fields = append(fields, "studentUid")
		}
	}

	if len(studentUids) > ApiMaxNum {
		// concurrency
		return getStudentBaseInBatchByUids(ctx, studentUids, fields), nil
	} else {
		return getKVByStudentUids(ctx, studentUids, fields)
	}
}

// 分批根据Uid获取学生信息接口 , 每个批次处理最多50人,用于大量并发获取
func getStudentBaseInBatchByUids(ctx *gin.Context, studentUids []int64, fields []string) map[int64]StudentInfo {
	chunks := utils.ChunkArrayInt64(studentUids, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int64]StudentInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(uids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "getStudentBaseInBatchByUids panic, err:%s", r)
				}
			}()
			defer wg.Done()
			singleRet, err := getKVByStudentUids(ctx, uids, fields)
			if err != nil {
				zlog.Warnf(ctx, "getKVByStudentUids failed, studentUid:%+v, err:%s", uids, err)
				return
			}

			ch <- singleRet
		}(chunk)
	}

	result := make(map[int64]StudentInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "getStudentBaseInBatchByUids panic, err:%s", r)
			}
		}()
		defer colWg.Done()
		for stuMap := range ch {
			for stuUid := range stuMap {
				result[stuUid] = stuMap[stuUid]
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

// 学生UidKv接口 studentUid 学生uid数组
func getKVByStudentUids(ctx *gin.Context, studentUids []int64, fields []string) (map[int64]StudentInfo, error) {
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, module, entityStudent, "getKVByStudentUids", false, env.GetAppName())
	arrParams := map[string]interface{}{
		"studentUid": studentUids,
		"fields":     fields,
	}

	apiOut := make(map[int64]StudentInfo)
	apiResp, err := zbcore.PostDau(ctx, arrParams, arrHeader, &apiOut)
	zlog.Debugf(ctx, "apiResp:%+v, err:%+v", apiResp, err)
	return apiOut, err
}
