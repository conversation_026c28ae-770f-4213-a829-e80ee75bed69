package dau

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetStudents(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)

	studentIds := []int64{2135361819}
	rsp, err := GetStudents(ctx, studentIds, nil)
	if err != nil {
		return
	}
	t.Log(fwyyutils.MarshalIgnoreError(rsp))
}
