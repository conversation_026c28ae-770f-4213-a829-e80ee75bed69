package assistantdesk

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	DeskBridgeUri = "/assistantdesk/api/fwyytool/apibridge"
)

type DeskBridgeReq struct {
	Function    string `json:"function" form:"function"`
	Variate     string `json:"variate" form:"variate"`
	CourseId    int64  `json:"courseId" form:"courseId"`
	LessonId    int64  `json:"lessonId" form:"lessonId"`
	SubjectId   int64  `json:"subjectId" form:"subjectId"`
	StudentUids string `json:"studentUids" form:"studentUids"`
}

type DeskBridgeRsp struct {
	Res interface{} `json:"res" form:"res"`
}

func DeskBridgeQuery(ctx *gin.Context, req DeskBridgeReq) (rsp *DeskBridgeRsp, err error) {
	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "<PERSON><PERSON><PERSON><PERSON> failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}
