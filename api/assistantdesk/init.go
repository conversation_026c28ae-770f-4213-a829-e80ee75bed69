package assistantdesk

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

func init() {
	apis.Register(UriGetKey, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetKeyReq{},
		Encoder:  apis.EncoderForm,
		Response: map[string]map[string]string{},
	})
	apis.Register(UriGetCourseTimeTable, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetCourseTimeTableReq{},
		Encoder:  apis.EncoderForm,
		Response: GetCourseTimeTableRsp{},
	})
	apis.Register(UrlWxStudentList, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  WxStudentListReq{},
		Encoder:  apis.EncoderJson,
		Response: WxStudentListRsp{},
	})
	apis.Register(DeskBridgeUri, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  DeskBridgeReq{},
		Encoder:  apis.EncoderJson,
		Response: DeskBridgeRsp{},
	})
	apis.Register(ApiGetStudentCallRecordInfo, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetStudentCallRecordInfoReq{},
		Encoder:  apis.EncoderForm,
		Response: GetStudentCallRecordInfoResp{},
	})
}
