package assistantdesk

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UrlWxStudentList = "/assistantdesk/api/filter/wxstudentlist"

	IsAPI = 1
)

type WxStudentListReq struct {
	PersonUid    int64 `json:"personUid" form:"personUid"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	CourseId     int64 `json:"courseId" form:"courseId"`
	IsApi        int64 `json:"isApi" form:"isApi"`
}

type WxStudentListRsp struct {
	StudentList        []Student `json:"studentList" mapstructure:"studentList"`
	NotBindStudentList []Student `json:"notBindStudentList" mapstructure:"notBindStudentList"`
}

type Student struct {
	StudentUid  int64  `json:"studentUid"`
	Reason      string `json:"reason"`
	StudentName string `json:"studentName"`
	Uname       string `json:"uname"`
	Phone       string `json:"phone"`
	RegPhone    string `json:"regPhone"`
}

func WxStudentList(ctx *gin.Context, staffUid, assistantUid, courseId int64) (rsp *WxStudentListRsp, err error) {
	param := WxStudentListReq{
		PersonUid:    staffUid,
		AssistantUid: assistantUid,
		CourseId:     courseId,
		IsApi:        IsAPI,
	}
	rsp = &WxStudentListRsp{}
	if err = apis.Do(ctx, param, rsp); err != nil {
		zlog.Warnf(ctx, "WxStudentList failed, param:%+v err: %s", param, err.Error())
		return
	}
	return
}
