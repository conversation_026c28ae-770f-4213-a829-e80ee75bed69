package assistantdesk

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	ApiGetStudentCallRecordInfo = "/assistantdesk/api/sop/getstudentcallrecordinfo"
)

type GetStudentCallRecordInfoReq struct {
	AssistantUid  int64 `json:"assistantUid" form:"assistantUid"`
	StudentUid    int64 `json:"studentUid" form:"studentUid"`
	CourseId      int64 `json:"courseId" form:"courseId"`
	BacktraceTime int64 `json:"backtraceTime" form:"backtraceTime"` // 追溯时间；单位：s
}

type GetStudentCallRecordInfoResp struct {
	CallList       []CallInfo       `json:"callList"`
	CallRecordList []CallRecordInfo `json:"callRecordList"`
	CallCountInfo  CallCountInfo    `json:"callCountInfo"`
}

type CallInfo struct {
	Name      string `json:"name"`
	Phone     string `json:"phone"`
	Md5Phone  string `json:"md5Phone"`
	City      string `json:"city"`
	CityLevel string `json:"cityLevel"`
}

type CallRecordInfo struct {
	Name           string `json:"name"`
	StartTime      string `json:"startTime"`
	Duration       int64  `json:"duration"`
	CallResult     string `json:"callResult"`
	CallId         int64  `json:"callId"`
	CallMode       int64  `json:"callMode"`
	FromPhone      string `json:"fromPhone"`
	SourceTypeName string `json:"sourceTypeName"`
	SourceType     int64  `json:"sourceType"`
}

type CallCountInfo struct {
	TotalNum    int64   `json:"totalNum"`
	SuccessNum  int64   `json:"successNum"`
	SuccessRate float64 `json:"successRate"`
}

func GetStudentCallRecordInfo(ctx *gin.Context, req GetStudentCallRecordInfoReq) (resp GetStudentCallRecordInfoResp, err error) {
	err = apis.Do(ctx, req, &resp, apis.PhpResponseUnmarshaler())
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}
	return
}
