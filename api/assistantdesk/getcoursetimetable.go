package assistantdesk

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	UriGetCourseTimeTable = "/assistantdesk/api/student/getcoursetimetable"
)

type GetCourseTimeTableReq struct {
	StartDay   string `json:"startDay" form:"startDay"`
	EndDay     string `json:"endDay" form:"endDay"`
	StudentUid int64  `json:"studentUid" form:"studentUid"`
}

type GetCourseTimeTableRsp struct {
	CourseList []CourseTimeInfo `json:"courseList"`
	CourseDays []string         `json:"courseDays"`
}

type CourseTimeInfo struct {
	CourseName  string `json:"courseName"`
	CourseId    int64  `json:"courseId"`
	LessonName  string `json:"lessonName"`
	StartTime   int64  `json:"startTime"`
	EndTime     int64  `json:"endTime"`
	SubjectName string `json:"subjectName"`
	SubjectID   int64  `json:"subjectId"`
}

func GetCourseTimeTable(ctx *gin.Context, req GetCourseTimeTableReq) (rsp *GetCourseTimeTableRsp, err error) {
	rsp = &GetCourseTimeTableRsp{}
	if err = apis.Do(ctx, req, rsp); err != nil {
		zlog.Warnf(ctx, "GetCourseTimeTable failed, params: %+v, err: %+v", req, err)
		return
	}

	return rsp, nil
}
