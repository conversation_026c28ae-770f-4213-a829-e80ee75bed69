package assistantdesk

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetKeys(t *testing.T) {
	env.SetRootPath("../../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()
	ctx := gin.CreateNewContext(engine)

	ret, err := GetKeysForString(ctx, AppKey)
	fmt.Println(ret, err)

	var data map[string]map[string]AppInfo
	err = GetKeyForJson(ctx, AppKey, &data)
	fmt.Println(data, err)

	var lessonRet map[string]LessonTypeDto
	err = GetKeyForJsonMap(ctx, LessonPlayTypeKey, &lessonRet)
	fmt.Println(lessonRet, err)
}

type LessonTypeDto struct {
	Name string `json:"name"`
}
