package assistantdesk

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/api/decode"
	"assistantdeskgo/conf"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/ratelimit"
	"sync"
)

const (
	UriGetKey = "/assistantdesk/api/keyconst/getkey"

	batchGetKeyLimit = 50

	AppKey = "app_key"

	CourseAppKey = "course_app_key"

	CourseBanxueTagKey             = "course_banxue_tag_key"
	LessonPlayTypeKey              = "lesson_play_type_key"
	AllocateCourseTagDepartmentKey = "allocate_course_tag_department_key"
	YearKey                        = "year_key"
	SeasonMapKey                   = "season_map_key"
	GradeKey                       = "grade_key"
	SubjectKey                     = "subject_key"
)

var KeyMap = map[string]string{
	"subject_key": "subject",
}

type GetKeyReq struct {
	Key string `json:"key" form:"key"`
}

type AppInfo struct {
	Name     string `json:"name"`
	IconURL  string `json:"iconUrl"`
	IconType string `json:"iconType"`
}

type CourseAppInfo struct {
	Source        []int64 `json:"source"`
	NewCourseType []int64 `json:"newCourseType"`
	Grade         []int64 `json:"grade"`
	Subject       []int64 `json:"subject"`
	CourseIds     []int64 `json:"courseIds"`
}

func GetKeysForString(ctx *gin.Context, keys ...string) (ret map[string]map[string]string, err error) {
	limit := ratelimit.New(batchGetKeyLimit)
	wg := &sync.WaitGroup{}
	ch := make(chan map[string]map[string]string)
	for _, key := range keys {
		searchKey := key
		wg.Add(1)
		go func() {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "getKey panic err : %+v", r)
				}
			}()
			defer wg.Done()

			limit.Take() // 添加限流, 避免并发太高
			keyMap, keyErr := SignalGetKey(ctx, searchKey)
			if keyErr != nil {
				zlog.Warnf(ctx, "getKey failed, key:%+v, err:%s", searchKey, keyErr)
				return
			}

			ch <- keyMap
		}()
	}

	formatWg := &sync.WaitGroup{}
	formatWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "getKey panic err : %+v", r)
			}
		}()
		defer formatWg.Done()

		for keyMap := range ch {
			if keyMap == nil {
				continue
			}

			for key, value := range keyMap {
				ret[key] = value
			}
		}
	}()

	wg.Wait()
	close(ch)
	formatWg.Wait()

	return
}

func SignalGetKey(ctx *gin.Context, key string) (map[string]map[string]string, error) {
	req := &GetKeyReq{Key: key}
	rsp := make(map[string]map[string]string)
	err := apis.Do(ctx, req, &rsp)
	if err != nil {
		return nil, err
	}

	index := key
	if value, ok := KeyMap[index]; ok {
		index = value
	}

	ret := make(map[string]map[string]string)
	ret[key] = rsp[index]

	return ret, nil
}

func GetKeyForJson(ctx *gin.Context, key string, ret interface{}) (err error) {
	param := map[string]interface{}{
		"key": key,
	}

	opt := base.HttpRequestOptions{
		RequestBody: param,
		Encode:      base.EncodeForm,
	}

	resp, err := conf.API.AssistantDesk.HttpGet(ctx, UriGetKey, opt)

	var apiResp decode.BzrResponse
	err = decode.DecodeResponse(ctx, resp, &apiResp, ret)
	if err != nil {
		zlog.Warnf(ctx, "decode failed, url[%s] err:%s", UriGetKey, err)
		return err
	}

	if apiResp.ErrNo > 0 {
		zlog.Warnf(ctx, "request failed, url[%s] err:%s", UriGetKey, err)
		return errors.New(apiResp.ErrStr)
	}

	return nil
}

func GetKeyForJsonMap(ctx *gin.Context, key string, ret interface{}) error {
	req := &GetKeyReq{Key: key}
	rsp := make(map[string]json.RawMessage)
	err := apis.Do(ctx, req, &rsp)
	if err != nil {
		return err
	}

	index := key
	if value, ok := KeyMap[index]; ok {
		index = value
	}

	tmpRawMessage, exist := rsp[index]
	if !exist {
		return fmt.Errorf("key not found, key: %s", key)
	}

	err = json.Unmarshal(tmpRawMessage, ret)
	if err != nil {
		return fmt.Errorf("json unmarshal failed, key:%s, err:%s", key, err)
	}
	return nil
}
