package message

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/infra/pkg/navigator"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	UriSendGroupMessage           = "/muse/wechat/api/sendgroupmessage"
	UriSendGroupMessageCheck      = "/muse/wechat/api/sendgroupmessagecheck"
	UriSendGroupMessageMultiCheck = "/muse/wechat/api/sendgroupmessagemulticheck"

	NeedOrderSend = 2 // 顺序发送
	NoOrderSend   = 1 // 不需要顺序发送
)

//type SendGroupMessageReq struct {
//	AssistantUID  int64              `json:"assistantUid"`
//	StudentUids   []int64            `json:"studentUids"`
//	RemoteIds     []string           `json:"remoteIds"`
//	GroupOrder    int64              `json:"groupOrder"`
//	MessageGroups []MessageGroupInfo `json:"messageGroups"`
//}

type MessageGroupInfo struct {
	SubOrder    int64         `json:"subOrder"`
	ReceiverIds []int64       `json:"receiverIds"`
	DelayTime   int64         `json:"delayTime"`
	MessageList []MessageInfo `json:"messageList"`
}

type SendGroupMessageReq struct {
	AssistantUID  int64          `json:"assistantUid"`
	SubType       int64          `json:"subType"`
	GroupOrder    int64          `json:"groupOrder"`
	SendType      int64          `json:"sendType"`
	TaskType      int64          `json:"taskType"`
	SendTime      int64          `json:"sendTime"`
	MessageGroups []MessageGroup `json:"messageGroups"`
	UniqueId      string         `json:"uniqueId"`
}

type MessageGroup struct {
	SourceInfo       SourceInfo       `json:"sourceInfo"`
	Ability          int              `json:"ability"`
	Strategy         int              `json:"strategy"`
	LabelStudentList map[string][]int `json:"labelStudentList"`
	SubOrder         int64            `json:"subOrder"`
	Receivers        Receiver         `json:"receivers"`
	DelayTime        int64            `json:"delayTime"`
	MessageList      []MessageInfo    `json:"messageList"`
}

type SourceInfo struct {
	CourseId int64 `json:"courseId"`
	LessonId int64 `json:"lessonId"`
}

type Receiver struct {
	StudentUids []int64  `json:"studentUids"`
	RemoteIds   []string `json:"remoteIds"`
	ChatId      string   `json:"chatId"`
}

type AtMembers struct {
	StudentUids []int64  `json:"studentUids"`
	RemoteIds   []string `json:"remoteIds"`
}

type MessageInfo struct {
	DelayTime  int64       `json:"delayTime"`
	AtMembers  AtMembers   `json:"atMembers"`
	MsgType    int64       `json:"msgType"`
	MsgContent interface{} `json:"msgContent"`
}

type SendGroupMessageRsp struct {
	TaskId int64 `json:"taskId"`
}

type SendGroupMessageCheckReq struct {
	AssistantUid  int64   `json:"assistantUid" form:"assistantUid"`
	StudentUids   []int64 `json:"studentUids" form:"studentUids"`
	ChatType      int     `json:"chatType" form:"chatType"`
	GroupRemoteId string  `json:"groupRemoteId" form:"groupRemoteId"`
}

type SendGroupMessageCheckRsp struct {
	Status      int64  `json:"status" form:"status"`
	Reason      string `json:"reason" form:"reason"`
	UsedLimit   int64  `json:"usedLimit" form:"usedLimit"`
	NoticeLimit int64  `json:"noticeLimit" form:"noticeLimit"`
}

func SendGroupMessage(ctx *gin.Context, req SendGroupMessageReq) (rsp *SendGroupMessageRsp, err error) {
	// 压测数据对学生ID和老师ID进行偏移
	if navigator.IsPressure(ctx) {
		req.AssistantUID = int64(navigator.ShiftUid(uint64(req.AssistantUID), utils.GetPressShiftTimes(ctx)))
		groups := make([]MessageGroup, 0, len(req.MessageGroups))
		for _, messageGroup := range req.MessageGroups {
			messageGroup.Receivers.StudentUids = utils.ShiftUidForArray(messageGroup.Receivers.StudentUids, utils.GetPressShiftTimes(ctx))

			for index, message := range messageGroup.MessageList {
				message.AtMembers.StudentUids = utils.ShiftUidForArray(message.AtMembers.StudentUids, utils.GetPressShiftTimes(ctx))
				messageGroup.MessageList[index] = message
			}
			groups = append(groups, messageGroup)
		}
		req.MessageGroups = groups
	}

	req.UniqueId = uuid.New().String()

	rsp = &SendGroupMessageRsp{}
	if err = apis.Do(ctx, req, rsp, getHeader(ctx), apis.IgnoreInnerError()); err != nil {
		return nil, err
	}

	return rsp, nil
}

func SendGroupMessageCheck(ctx *gin.Context, req SendGroupMessageCheckReq) (rsp *SendGroupMessageCheckRsp, err error) {
	rsp = &SendGroupMessageCheckRsp{}
	if err = apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

type MultiCheckReq struct {
	MessageCount     int64              `json:"messageCount" form:"messageCount"`
	WxIdCount        int64              `json:"wxIdCount" form:"wxIdCount"`
	GroupCount       int64              `json:"groupCount" form:"groupCount"`
	AssistantUid     int64              `json:"assistantUid" form:"assistantUid"`
	PersonUid        int64              `json:"personUid" form:"personUid"`
	Ability          int64              `json:"ability" form:"ability"`
	CourseId         int64              `json:"courseId" form:"courseId"`
	TaskType         int64              `json:"taskType" form:"taskType"`
	IsSkipTaskCnt    int64              `json:"isSkipTaskCnt" form:"isSkipTaskCnt"`
	StudentUid2WxIds map[int64][]string `json:"studentUid2WxIds" form:"studentUid2WxIds"`
}

type MultiCheckRsp struct {
	Status             int64   `json:"status"`
	Reason             string  `json:"reason"`
	DisableStudentList []int64 `json:"disableStudentList"`
	UsedLimit          int64   `json:"usedLimit"`
	NoticeLimit        int64   `json:"noticeLimit"`
	NotOnlineWordUrl   string  `json:"notOnlineWordUrl"`
}

func MultiCheck(ctx *gin.Context, req MultiCheckReq) (rsp *MultiCheckRsp, err error) {
	rsp = &MultiCheckRsp{}
	if err = apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

func getHeader(ctx *gin.Context) apis.Option {
	headers := make(map[string]string)

	if navigator.IsPressure(ctx) {
		headers[utils.HTTP_X_BD_CALLER_INFO] = ctx.GetHeader(utils.HTTP_X_BD_CALLER_INFO)
		headers[navigator.HTPP_XBD_CALLER_URI] = ctx.GetHeader(navigator.HTPP_XBD_CALLER_URI)
	}

	return apis.WithHeaders(headers)
}
