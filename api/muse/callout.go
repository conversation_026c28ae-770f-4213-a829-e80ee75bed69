package muse

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/defines"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

const (
	UriCallOut    = "/muse/call/api/callOut"
	GetRecordLink = "/muse/call/api/getrecordlink"
)

type CallOutReq struct {
	CallID       int64  `json:"callId" form:"callId"`
	StaffUID     int64  `json:"staffUid" form:"staffUid"`
	FromPhone    string `json:"fromPhone" form:"fromPhone"`
	ToPhone      string `json:"toPhone" form:"toPhone"`
	CourseID     int64  `json:"courseId" form:"courseId"`
	Uname        string `json:"uname" form:"uname"`
	DepartmentID int64  `json:"departmentId" form:"departmentId"`
	DeviceUID    int64  `json:"deviceUid" form:"deviceUid"`
	SubjectID    int64  `json:"subjectId" form:"subjectId"`
	CallMode     int64  `json:"callMode" form:"callMode"`
	ExtData      string `json:"extData" form:"extData"`
	TemplateID   int64  `json:"templateId" form:"templateId"`
	Line         int64  `json:"line" form:"line"`
}

type GetRecordLinkReq struct {
	Path    string `json:"path" form:"path"`
	Type    string `json:"type" form:"type"`
	Day     int64  `json:"day" form:"day"`
	IsInner int64  `json:"isInner" form:"isInner"`
}

type CallOutRsq struct {
	ErrNo  int         `json:"errNo"`
	ErrStr string      `json:"errstr"`
	Data   interface{} `json:"data"`
}
type GetRecordLinkRsq struct {
	Link string `json:"link"`
}

func CallOut(ctx *gin.Context, req CallOutReq) (rsp *CallOutRsq, err error) {
	req.ToPhone = strings.TrimSpace(req.ToPhone)
	req.FromPhone = strings.TrimSpace(req.FromPhone)
	req.Uname = strings.TrimSpace(req.Uname)

	if req.CallID == 0 || len(req.ToPhone) == 0 {
		return nil, errors.New(fmt.Sprintf("muse_call_out_failed, params invalid, callId: %d, toPhone: %s", req.CallID, req.ToPhone))
	}

	if req.CallMode == defines.CallOutModeBBD && (req.StaffUID == 0 || len(req.FromPhone) == 0 || len(req.Uname) == 0) {
		return nil, errors.New(fmt.Sprintf("muse_call_out_failed, params invalid, staffUid: %d, fromPhone: %s, uname: %s", req.StaffUID, req.FromPhone, req.Uname))
	}

	// 业务线默认为辅导
	if req.Line != defines.BusinessLineForFuDao && req.Line != defines.BusinessLineForLPC {
		req.Line = defines.BusinessLineForFuDao
	}

	rsp = &CallOutRsq{}
	if err = apis.Do(ctx, req, rsp, apis.PhpResponseUnmarshaler()); err != nil {
		return nil, err
	}

	return rsp, nil
}

func GetLink(ctx *gin.Context, req GetRecordLinkReq) (rsp GetRecordLinkRsq, err error) {
	if err = apis.Do(ctx, req, &rsp, apis.PhpResponseUnmarshaler()); err != nil {
		zlog.Warnf(ctx, "GetLink fail,req=%v,err=%v", req, err)
	}
	return
}
