package muse

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const apiAddCallRecord = "/muse/call/api/addcallrecord"

type AddCallRecordReq struct {
	SourceType   int64  `json:"sourceType" form:"sourceType"`
	Line         int64  `json:"line" form:"line"`
	FromUid      int64  `json:"fromUid" form:"fromUid"`
	DeviceUid    int64  `json:"deviceUid" form:"deviceUid"`
	PersonUid    int64  `json:"personUid" form:"personUid"`
	FromPhone    string `json:"fromPhone" form:"fromPhone"`
	ToUid        int64  `json:"toUid" form:"toUid"`
	ToPhone      string `json:"toPhone" form:"toPhone"`
	IsCommon     int64  `json:"isCommon" form:"isCommon"`
	CallMode     int64  `json:"callMode" form:"callMode"`
	BusinessType string `json:"businessType" form:"businessType"`
	BusinessKey  string `json:"businessKey" form:"businessKey"`
}

type AddCallRecordResp struct {
	CallId int64 `json:"callId"`
}

func AddCallRecord(ctx *gin.Context, req AddCallRecordReq) (resp AddCallRecordResp, err error) {
	err = apis.Do(ctx, req, &resp, apis.PhpResponseUnmarshaler())
	if err != nil {
		zlog.Warnf(ctx, "AddCallRecord.error, req:%+v, err:%+v", req, err)
		return
	}
	return
}
