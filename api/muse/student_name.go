package muse

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	MuseGetFamilyLastName = "/muse/wechat/api/getfamilylastname"
)

type MuseGetFamilyLastNameReq struct {
	StudentName string `json:"studentName" form:"studentName"`
}

type MuseGetFamilyLastNameRsp struct {
	StudentName string `json:"studentName" form:"studentName"`
}

func GetFamilyLastName(ctx *gin.Context, req MuseGetFamilyLastNameReq) (rsp MuseGetFamilyLastNameRsp, err error) {
	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetLink fail,req=%v,err=%v", req, err)
	}
	return
}
