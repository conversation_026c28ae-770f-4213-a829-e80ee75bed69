package muse

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
)

const (
	UrlAddRemind    = "/muse/remind/desk/deskadd"
	UrlDeleteRemind = "/muse/remind/desk/deskfinish"
	UrlRemindInfo   = "/muse/remind/desk/deskinfo"

	RemindTypeAssistant = "assistant"
	RemindTypeLpc       = "lpc"
)

const FromTypeDesk = 0
const FromTypeAi = 1

type AddRemindReq struct {
	Uid        int64
	AppType    string
	FromType   int //0,工作台待办；1，摘要待办
	Content    string
	RemindTime string
	RemindId   int64
	Students   []StudentInfo
}

type StudentInfo struct {
	CourseId   int64 `json:"courseId"`
	StudentUid int64 `json:"studentUid"`
}

type AddRemindApiReq struct {
	Uid        int64  `json:"uid"`
	AppType    string `json:"appType"`
	FromType   int    `json:"fromType"`
	Content    string `json:"content"`
	RemindTime string `json:"remindTime"`
	Type       int    `json:"type"`
	RemindId   int64  `json:"remindId,omitempty"`
	Students   string `json:"students"`
}

type AddRemindApiRsp struct {
	RemindId int64 `json:"remindId"`
}

type DeleteRemindApiReq struct {
	Uid      int64 `json:"uid"`
	RemindId int64 `json:"remindId"`
}

type DeleteRemindApiRsp struct {
}

type RemindInfoRsp struct {
	Info RemindInfo `json:"info"`
}

type RemindInfo struct {
	RemindId        int64         `json:"remindId"`
	CurrentRemindId string        `json:"currentRemindId"`
	CreateUid       int64         `json:"createUid"`
	Content         string        `json:"content"`
	Type            int           `json:"type"`
	RemindTime      string        `json:"remindTime"`
	PosRemindTime   string        `json:"posRemindTime"`
	FromType        int           `json:"fromType"`
	Students        []StudentInfo `json:"students"`
	Status          int           `json:"status"`
}

type GetRemindReq struct {
	RemindId  int64  `json:"remindId" form:"remindId"`
	CreateUid int64  `json:"createUid" form:"createUid"`
	AppType   string `json:"appType" form:"appType"`
}

func AddRemind(ctx *gin.Context, req *AddRemindReq) (rsp *AddRemindApiRsp, err error) {
	studStr, err := jsoniter.MarshalToString(req.Students)
	if err != nil {
		return nil, errors.WithMessagef(err, "[AddRemind] Marshal students failed, students: %v", req.Students)
	}

	apiReq := &AddRemindApiReq{
		Uid:        req.Uid,
		AppType:    req.AppType,
		FromType:   req.FromType,
		Content:    req.Content,
		RemindTime: req.RemindTime,
		Type:       1,
		Students:   studStr,
	}

	if req.RemindId > 0 {
		apiReq.RemindId = req.RemindId
	}

	rsp = &AddRemindApiRsp{}
	err = apis.Do(ctx, apiReq, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "AddRemind failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}

func DeleteRemind(ctx *gin.Context, req *DeleteRemindApiReq) (rsp *DeleteRemindApiRsp, err error) {
	rsp = &DeleteRemindApiRsp{}
	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "DeleteRemind failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}

func BatchGetRemindMapByIds(ctx *gin.Context, data []GetRemindReq) (result map[int64]RemindInfo, err error) {
	if len(data) <= 0 {
		return
	}
	result = make(map[int64]RemindInfo)
	for _, req := range data {
		remindInfo, tmpErr := GetRemindById(ctx, req)
		if tmpErr != nil {
			return
		}
		result[req.RemindId] = remindInfo.Info
	}
	return result, nil
}

func GetRemindById(ctx *gin.Context, req GetRemindReq) (rsp RemindInfoRsp, err error) {
	if req.RemindId <= 0 {
		return
	}
	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "GetRemindById failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}
