package muse

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	MuseBridgeUri = "/muse/wechat/api/apibridge"
)

type MuseBridgeReq struct {
	Function    string `json:"function" form:"function"`
	Variate     string `json:"variate" form:"variate"`
	CourseId    int64  `json:"courseId" form:"courseId"`
	LessonId    int64  `json:"lessonId" form:"lessonId"`
	SubjectId   int64  `json:"subjectId" form:"subjectId"`
	StudentUids string `json:"studentUids" form:"studentUids"`
}

type MuseBridgeRsp struct {
	Res interface{} `json:"res" form:"res"`
}

func MuseBridgeQuery(ctx *gin.Context, req MuseBridgeReq) (rsp *MuseBridgeRsp, err error) {
	rsp = &MuseBridgeRsp{}
	err = apis.Do(ctx, req, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "MuseBridge<PERSON><PERSON>y failed, req: %+v, resp: %+v, err: %+v", req, rsp, err)
		return
	}
	return
}
