package muse

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestExamReport(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	var req MuseBridgeReq
	req.Function = "getBottomTestUrlList"
	req.CourseId = 540325
	req.StudentUids = "2135479924,2135482782,2135578812"

	rsp, err := MuseBridgeQuery(ctx, req)
	fmt.Println(rsp, err)
}
