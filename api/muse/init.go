package muse

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/api/muse/message"
	"net/http"
)

func init() {
	apis.Register(UrlRemindInfo, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetRemindReq{},
		Encoder:  apis.EncoderForm,
		Response: []RemindInfoRsp{},
	})
	apis.Register(UriCallOut, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CallOutReq{},
		Encoder:  apis.EncoderForm,
		Response: CallOutRsq{},
	})

	apis.Register(message.UriSendGroupMessageCheck, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  message.SendGroupMessageCheckReq{},
		Encoder:  apis.EncoderJson,
		Response: message.SendGroupMessageCheckRsp{},
	})

	apis.Register(message.UriSendGroupMessage, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  message.SendGroupMessageReq{},
		Encoder:  apis.EncoderJson,
		Response: message.SendGroupMessageRsp{},
	})

	apis.Register(UrlGetWxInfoByUids, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetWxInfoByUidsReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetWxInfoByUidsResp{},
	})

	apis.Register(message.UriSendGroupMessageMultiCheck, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  message.MultiCheckReq{},
		Encoder:  apis.EncoderJson,
		Response: message.MultiCheckRsp{},
	})

	apis.Register(GetRecordLink, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetRecordLinkReq{},
		Encoder:  apis.EncoderForm,
		Response: GetRecordLinkRsq{},
	})

	apis.Register(UrlAddRemind, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  AddRemindApiReq{},
		Encoder:  apis.EncoderJson,
		Response: AddRemindApiRsp{},
	})

	apis.Register(UrlDeleteRemind, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  DeleteRemindApiReq{},
		Encoder:  apis.EncoderJson,
		Response: DeleteRemindApiRsp{},
	})

	apis.Register(MuseBridgeUri, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  MuseBridgeReq{},
		Encoder:  apis.EncoderJson,
		Response: MuseBridgeRsp{},
	})
	apis.Register(MuseGetFamilyLastName, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  MuseGetFamilyLastNameReq{},
		Encoder:  apis.EncoderJson,
		Response: MuseGetFamilyLastNameRsp{},
	})
	apis.Register(apiAddCallRecord, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  AddCallRecordReq{},
		Encoder:  apis.EncoderForm,
		Response: AddCallRecordResp{},
	})
}
