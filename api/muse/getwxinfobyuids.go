package muse

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	UrlGetWxInfoByUids = "/muse/wechat/api/getwxinfobyuids"
)

type GetWxInfoByUidsReq struct {
	StaffUid    int64  `json:"staffUid" form:"staffUid"`
	StudentUids string `json:"studentUids" form:"studentUids"`
}

type GetWxInfoByUidsResp struct {
	Id           int64  `json:"id"`
	StaffUid     int64  `json:"staffUid"`
	UserId       string `json:"userId"`
	CorpId       string `json:"corpId"`
	StudentUid   int64  `json:"studentUid"`
	RemoteId     int64  `json:"remoteId"`
	RelationType int64  `json:"relationType"`
	Deleted      int64  `json:"deleted"`
	CreateTime   int64  `json:"createTime"`
	UpdateTime   string `json:"updateTime"`
	WeiXinId     int64  `json:"weixinId"`
	Remark       string `json:"remark"`
}

func GetWxInfoByUids(ctx *gin.Context, staffUid int64, studentUids []int64) (respMap map[int64]map[int64]GetWxInfoByUidsResp, err error) {
	respMap = make(map[int64]map[int64]GetWxInfoByUidsResp)
	if len(studentUids) == 0 {
		return
	}
	req := GetWxInfoByUidsReq{}
	req.StaffUid = staffUid
	req.StudentUids, err = jsoniter.MarshalToString(studentUids)
	if err != nil {
		return
	}
	resp := make([]GetWxInfoByUidsResp, 0, len(studentUids))
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetWxInfoByUids Ral error, req: %+v, resp: %+v, err: %+v ", req, resp, err)
		return
	}

	if len(resp) == 0 {
		return
	}

	// stuUid => [wxId => data ]
	for _, wxInfoByUidsResp := range resp {
		if _, ok := respMap[wxInfoByUidsResp.StudentUid]; !ok {
			respMap[wxInfoByUidsResp.StudentUid] = make(map[int64]GetWxInfoByUidsResp)
		}
		respMap[wxInfoByUidsResp.StudentUid][wxInfoByUidsResp.RemoteId] = wxInfoByUidsResp
	}
	return
}
