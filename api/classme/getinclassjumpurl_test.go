package classme

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetInClassJumpUrl(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	req := GetInClassJumpUrlReq{
		LessonId:     458851,
		CourseId:     518434,
		AssistantUid: 2700076703,
		AppId:        "airclass",
		IsBanxue:     0,
	}

	rsp, err := GetInClassJumpUrl(ctx, req)
	fmt.Println(rsp, err)

	title, description, icon, err := utils.GetHtmlBaseInfo(rsp.JumpUrl)
	fmt.Println(title, description, icon, err)
}
