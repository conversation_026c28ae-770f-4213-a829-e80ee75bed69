package classme

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	//UriGetInClassJumpUrl https://yapi.zuoyebang.cc/project/8183/interface/api/838520
	UriGetInClassJumpUrl = "/classmeinner/api/getinclassjumpurl"
)

type GetInClassJumpUrlReq struct {
	LessonId     int64  `json:"lessonId" form:"lessonId"`
	CourseId     int64  `json:"courseId" form:"courseId"`
	AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
	AppId        string `json:"appId" form:"appId"`
	IsBanxue     int64  `json:"isBanxue" form:"isBanxue"`
}

type GetInClassJumpUrlRsp struct {
	JumpUrl string `json:"jumpUrl"`
}

// GetInClassJumpUrl 获取直播间链接
func GetInClassJumpUrl(ctx *gin.Context, req GetInClassJumpUrlReq) (rsp *GetInClassJumpUrlRsp, err error) {
	rsp = &GetInClassJumpUrlRsp{}

	if err = apis.Do(ctx, req, rsp, getHeader(), apis.IgnoreInnerError()); err != nil {
		return nil, err
	}

	return rsp, nil
}

func getHeader() apis.Option {
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	return apis.WithHeaders(headers)
}
