package exercise

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const TaskListUrl = "/exercise-api/track/task"

type TaskListReq struct {
	CourseId int64  `json:"courseId" form:"courseId"`
	AppId    string `json:"appId" form:"appId"`
}

type TaskListResp struct {
	TaskId     int64   `json:"taskId"`
	ExamId     string  `json:"examId"`
	TaskName   string  `json:"taskName"`
	CourseId   int64   `json:"courseId"`
	LessonIds  []int64 `json:"lessonIds"`
	UnlockTime int64   `json:"unlockTime"`
	Status     int     `json:"status"` //1 未解锁 2 已解锁
	ExamUrl    string  `json:"examUrl"`
	ResultUrl  string  `json:"resultUrl"`
}

func GetTaskList(ctx *gin.Context, req TaskListReq) (resp []TaskListResp, err error) {
	err = apis.Do(ctx, req, &resp, apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetTaskList failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}

	return
}
