package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const GetCardSendLimitUrl = "/touchmisgo/wechat/card/sendlimit"

type CardSendLimitReq struct {
	AssistantUID int64  `json:"assistantUid" form:"assistantUid"` // 资产uid
	SourceType   string `json:"sourceType" form:"sourceType"`     // 业务唯一标识类型,  privateSea: 私海
	SourceKey    string `json:"sourceKey" form:"sourceKey"`       // 业务唯一标识
}

type CardSendLimitRes struct {
	DailyLimit int64 `json:"dailyLimit"`
	UsedCnt    int64 `json:"usedCnt"`
}

func CardSendLimit(ctx *gin.Context, req CardSendLimitReq) (*CardSendLimitRes, error) {
	rsp := &CardSendLimitRes{}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}
	zlog.Infof(ctx, "[CardSendLimit] req: %+v, rsp: %+v", req, rsp)

	return rsp, nil

}
