package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

const GetCallRecordListUrl = "/touchmisgo/call/record/list"
const (
	ApiMax = 200
)
const (
	CallResultAccess = 2 //1: 正在呼叫, 2:已接通, 3:未接通
)

type GetCallRecordListReq struct {
	FromUid      int64    `json:"fromUid" form:"fromUid"`
	ToUid        []int64  `json:"toUid" form:"toUid"`
	CourseId     int64    `json:"courseId" form:"courseId"`
	CallMode     []int64  `json:"callMode"  form:"callMode"`
	StartTime    int64    `json:"startTime"  form:"startTime"`
	EndTime      int64    `json:"endTime"  form:"endTime"`
	SourceType   int      `json:"sourceType"  form:"sourceType"`
	Fields       []string `json:"fields" form:"fields"`
	PageNum      int      `json:"pn" form:"pn"`
	PageSize     int      `json:"rn" form:"rn"`
	BusinessKey  []string `json:"businessKey" form:"businessKey"`
	BusinessType string   `json:"businessType" form:"businessType"`
}

type GetCallRecordListResp struct {
	CallRecordList []CallRecordLisInfo `json:"list"`
	PageInfo
}

type CallRecordLisInfo struct {
	CallId        int64  `json:"callId"`        // 通话记录id(全局唯一)
	SourceType    int64  `json:"sourceType"`    // 通话来源类型
	FromUid       int64  `json:"fromUid"`       // 主叫方的uid
	FromPhone     string `json:"fromPhone"`     // 主叫方的电话
	KmsFromPhone  string `json:"kmsFromPhone"`  // 主叫方的电话
	HashFromPhone string `json:"hashFromPhone"` // 主叫方的电话
	ToUid         int64  `json:"toUid"`         // 被叫方的uid
	KmsToPhone    string `json:"kmsToPhone"`    // 被叫方的电话
	HashToPhone   string `json:"hashToPhone"`   // 被叫方的电话
	ToPhone       string `json:"toPhone"`       // 被叫方的电话
	ResourceType  string `json:"resourceType"`  // 文件资源存储类型
	Line          int64  `json:"line"`          // 业务线 1 LPC, 2 辅导
	CallMode      int64  `json:"callMode"`      // 外呼类型 9: 帮帮盾, 10: ivr, 11: sip外呼
	StartTime     int64  `json:"startTime"`     // 开始拨打电话的时间
	StopTime      int64  `json:"stopTime"`      // 结束拨打电话的时间
	Duration      int64  `json:"duration"`      // 通话时长
	CallType      int64  `json:"callType"`      // 通话类型 1-呼出 2-呼入
	CallResult    int64  `json:"callResult"`    // 通话结果 1-正在呼叫 2-呼叫正常结束（已接通） 3-呼叫异常结束（未接通）
	RecordFile    string `json:"recordFile"`    // 语音文件
	TriggerType   int64  `json:"triggerType"`   // 触发类型 1-正常触发 2-自己触发
	CourseId      int64  `json:"courseId"`      // 课程id
	LessonId      int64  `json:"lessonId"`      // 课节id
	ClassId       int64  `json:"classId"`       // 班级id
	LearnSeason   string `json:"learnSeason"`   // 学季
	ServerId      int64  `json:"serverId"`      // 服务商id
	ExtData       string `json:"extData"`       // 扩展属性json
	UploadTime    int64  `json:"uploadTime"`    // 录音文件回传时间
	DeviceUid     int64  `json:"deviceUid"`     // 业务账号uid
	PersonUid     int64  `json:"personUid"`     // 真人uid
	CreateTime    int64  `json:"createTime"`    // 创建时间
	UpdateTime    int64  `json:"updateTime"`    // 更新时间
	BusinessKey   string `json:"businessKey"`   // key
	BusinessType  string `json:"businessType"`  // type

}

type GetCallRecordHistoryResp struct {
	CallRecordList []CallRecordLisInfo `json:"list" mapstructure:"list"`
	PageInfo
}

func GetCallRecordList(ctx *gin.Context, req GetCallRecordListReq) ([]CallRecordLisInfo, error) {
	req.PageSize = ApiMax
	req.PageNum = 0
	single, err := GetCallRecordListSingle(ctx, req)
	if err != nil {
		return nil, err
	}
	if single.Total <= ApiMax {
		return single.CallRecordList, err
	}

	page := single.Total / ApiMax
	if single.Total%ApiMax > 0 {
		page += 1
	}

	wg := &sync.WaitGroup{}
	ch := make(chan []CallRecordLisInfo)
	for i := 1; i < int(page); i++ {
		wg.Add(1)
		req.PageNum = i
		go func(req GetCallRecordListReq) {
			defer wg.Done()

			sRet, err := GetCallRecordListSingle(ctx, req)
			if err != nil {
				return
			}

			ch <- sRet.CallRecordList
		}(req)
	}

	result := make([]CallRecordLisInfo, 0)

	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer colWg.Done()
		for sRet := range ch {
			result = append(result, sRet...)
		}
	}()
	wg.Wait()
	close(ch)
	colWg.Wait()

	return result, nil
}

func GetCallRecordListSingle(ctx *gin.Context, req GetCallRecordListReq) (rsp GetCallRecordHistoryResp, err error) {
	if req.PageSize > ApiMax || len(req.ToUid) <= 0 || req.PageNum < 0 {
		return rsp, errors.New("参数错误")
	}

	if err = apis.Do(ctx, req, &rsp); err != nil {
		zlog.Warnf(ctx, "GetCallRecordListSingle.error,req:%+v, err:%+v", req, err)
		return
	}
	return
}
