package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiSmsSend = "/touchmisgo/sms/send"
)

type SmsSendReq struct {
	AssistantUid int64    `json:"assistantUid"`
	StaffUid     int64    `json:"staffUid"`
	CourseId     int64    `json:"courseId"`
	LessonId     int64    `json:"lessonId"`
	StudentId    int64    `json:"studentId"`
	TplParams    []string `json:"tplParams"`
	TplId        int64    `json:"tplId"`
	SendType     int64    `json:"sendType"`
	StudentPhone int64    `json:"studentPhone"`
	Ext          []string `json:"ext"`
	SceneId      int64    `json:"sceneId"`
	BusinessLine int64    `json:"businessLine"`
	DepartmentId int64    `json:"departmentId"`
}

type SmsSendResp struct {
	Res  bool   `json:"res"`
	Type int64  `json:"type"`
	Msg  string `json:"msg"`
}

func SmsSend(ctx *gin.Context, req SmsSendReq) (resp SmsSendResp, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "SmsSend.failed, req:%+v, err:%+v", req, err)
		return
	}
	return
}
