package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	UriSendGroupMsg = "/touchmisgo/wxmessage/sendgroupmessage"
)

type SendGroupMsgReq struct {
	AssistantUID int64     `json:"assistantUid" form:"assistantUid"` // 资产uid
	PersonUid    int64     `json:"personUid" form:"personUid"`       // 真人uid
	SubType      int64     `json:"subType" form:"subType"`           // 发送类型
	SceneType    int64     `json:"sceneType" form:"sceneType"`       // 场景ID
	TaskType     int       `json:"taskType" form:"taskType"`         // 任务类型
	SendTime     int64     `json:"sendTime" form:"sendTime"`         // 发送时间
	IsDelay      int64     `json:"isDelay" form:"isDelay"`           // 延迟是延迟任务
	Contents     []Content `json:"contents" form:"contents"`         // 发送内容
}

type Content struct {
	Ability          int64               `json:"ability" form:"ability"`
	Strategy         int                 `json:"strategy" form:"strategy"`
	SourceInfo       []Source            `json:"sourceInfo" form:"sourceInfo"`
	LabelStudentList map[string][]int64  `json:"labelStudentList"`
	ReceiverList     []Receiver          `json:"receiverList" form:"receiverList" binding:"required"`
	MessageList      []MessageInfo       `json:"messageList" form:"messageList" binding:"required"`
	Ext              *ExtDetail          `json:"ext" form:"ext"`
	VariableValues   []VariableValueItem `json:"variableValues" form:"variableValues"`
}

type Receiver struct {
	StudentUid    int64    `json:"studentUid" form:"studentUid"`
	ChatId        string   `json:"chatId" form:"chatId"`
	RemoteIds     []string `json:"remoteIds" form:"remoteIds"`
	AtStudentUids []int64  `json:"atStudentUids" form:"atStudentUids"`
}

type AtMembers struct {
	StudentUids []int64  `json:"studentUids"`
	RemoteIds   []string `json:"remoteIds"`
}

type MessageInfo struct {
	DelayTime  int64       `json:"delayTime"`
	AtMembers  AtMembers   `json:"atMembers"`
	MsgType    int64       `json:"msgType"`
	MsgContent interface{} `json:"msgContent"`
}

type ExtDetail struct {
	ExtType string      `json:"extType"`
	ExtInfo interface{} `json:"extInfo"`
}

type VariableValueItem struct {
	VariableName  string           `json:"variableName" form:"variableName"`
	StudentValues map[int64]string `json:"studentValues" form:"studentValues"`
	SingleValues  string           `json:"singleValues" form:"singleValues"`
}

type Source struct {
	Type string `json:"type" form:"type"` // course, lesson
	Key  string `json:"key" form:"key"`
}

type SendGroupMsgRsp struct {
	TaskId int64 `json:"taskId"`
}

func SendGroupMsg(ctx *gin.Context, req SendGroupMsgReq) (*SendGroupMsgRsp, error) {
	rsp := &SendGroupMsgRsp{}
	cookies := ctx.Request.Cookies()
	cookieMap := map[string]string{}
	for _, cookie := range cookies {
		cookieMap[cookie.Name] = cookie.Value
	}
	if err := apis.Do(ctx, req, rsp, apis.WithCookies(cookieMap)); err != nil {
		return nil, err
	}

	return rsp, nil

}
