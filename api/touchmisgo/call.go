package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const GetCallRecordInfoUrl = "/touchmisgo/call/record/info"
const GetCallRecordHistoryUrl = "/touchmisgo/call/record/history"

type GetCallRecordInfoReq struct {
	CallIds []int64 `json:"callIds"`
}

type GetCallRecordHistoryReq struct {
	FromUid    int64   `json:"fromUid"`
	ToUid      []int64 `json:"toUid"`
	CourseId   int64   `json:"courseId"`
	CallMode   int     `json:"callMode"`
	StartTime  int64   `json:"startTime"`
	EndTime    int64   `json:"endTime"`
	SourceType int     `json:"sourceType"`
	Pn         int     `json:"pn"`
	Rn         int     `json:"rn"`
}

type GetCallRecordInfo struct {
	CallId        int64  `json:"callId"`
	SourceType    int64  `json:"sourceType"`
	FromUid       int64  `json:"fromUid"`
	FromPhone     string `json:"fromPhone"`
	HashFromPhone string `json:"hashFromPgone"`
	ToUid         int64  `json:"toUid"`       //被叫方ID
	HashToPhone   string `json:"hashToPhone"` //被叫方手机号
	ToPhone       string `json:"toPhone"`     //被叫方手机号
	ResourceType  string `json:"resourceType"`
	Line          int    `json:"line"`       //业务线
	CallMode      int    `json:"callMode"`   //外呼类型 9: 帮帮盾, 10: ivr, 11: sip外呼
	StartTime     int64  `json:"startTime"`  //开始外呼时间
	StopTime      int64  `json:"stopTime"`   //通话结束时间
	Duration      int64  `json:"duration"`   //通话时长
	CallType      int    `json:"callType"`   //外呼类型
	CallResult    int    `json:"callResult"` //外呼结果
	RecordFile    string `json:"recordFile"` //录音文件
	TriggerType   int    `json:"triggerType"`
	CourseId      int64  `json:"courseId"`     //课程ID
	LessonId      int64  `json:"lessonId"`     //章节ID
	BusinessType  string `json:"businessType"` //业务类型, privateSea 私海
	BusinessKey   string `json:"businessKey"`  //业务key
	ServerId      int64  `json:"serverId"`     //
	DeviceUid     int64  `json:"deviceUid"`    //资产ID
	PersonUid     int64  `json:"personUid"`    //真人ID
	CreateTime    int64  `json:"createTime"`
	UpdateTime    int64  `json:"updateTime"`
}

type PageInfo struct {
	Pn    int64 `json:"pn"`
	Rn    int64 `json:"rn"`
	Total int64 `json:"total"`
}

type CallRecordInfo struct {
	CallId        int64  `json:"callId"`        // 通话记录id(全局唯一)
	SourceType    int64  `json:"sourceType"`    // 通话来源类型
	FromUid       int64  `json:"fromUid"`       // 主叫方的uid
	FromPhone     string `json:"fromPhone"`     // 主叫方的电话
	KmsFromPhone  string `json:"kmsFromPhone"`  // 主叫方的电话
	HashFromPhone string `json:"hashFromPhone"` // 主叫方的电话
	ToUid         int64  `json:"toUid"`         // 被叫方的uid
	KmsToPhone    string `json:"kmsToPhone"`    // 被叫方的电话
	HashToPhone   string `json:"hashToPhone"`   // 被叫方的电话
	ToPhone       string `json:"toPhone"`       // 被叫方的电话
	ResourceType  string `json:"resourceType"`  // 文件资源存储类型
	ResourseType  string `json:"resourseType"`  // 文件资源存储类型 兼容老数据, 老的字段拼写有误
	Line          int64  `json:"line"`          // 业务线 1 LPC, 2 辅导
	CallMode      int64  `json:"callMode"`      // 外呼类型 9: 帮帮盾, 10: ivr, 11: sip外呼
	StartTime     int64  `json:"startTime"`     // 开始拨打电话的时间
	StopTime      int64  `json:"stopTime"`      // 结束拨打电话的时间
	Duration      int64  `json:"duration"`      // 通话时长
	CallType      int64  `json:"callType"`      // 通话类型 1-呼出 2-呼入
	CallResult    int64  `json:"callResult"`    // 通话结果 1-正在呼叫 2-呼叫正常结束（已接通） 3-呼叫异常结束（未接通）
	RecordFile    string `json:"recordFile"`    // 语音文件
	TriggerType   int64  `json:"triggerType"`   // 触发类型 1-正常触发 2-自己触发
	CourseId      int64  `json:"courseId"`      // 课程id
	LessonId      int64  `json:"lessonId"`      // 课节id
	ClassId       int64  `json:"classId"`       // 班级id
	LearnSeason   string `json:"learnSeason"`   // 学季
	ServerId      int64  `json:"serverId"`      // 服务商id
	ExtData       string `json:"extData"`       // 扩展属性json
	UploadTime    int64  `json:"uploadTime"`    // 录音文件回传时间
	DeviceUid     int64  `json:"deviceUid"`     // 业务账号uid
	PersonUid     int64  `json:"personUid"`     // 真人uid
	CreateTime    int64  `json:"createTime"`    // 创建时间
	UpdateTime    int64  `json:"updateTime"`    // 更新时间
}

type GetCallRecordHistoryRsp struct {
	CallRecordList []CallRecordInfo `json:"list"`
	PageInfo
}

func GetCallRecordByCallId(ctx *gin.Context, req GetCallRecordInfoReq) (record map[int64]GetCallRecordInfo, err error) {
	if err = apis.Do(ctx, req, &record); err != nil {
		return
	}
	return
}

func GetCallRecordHistory(ctx *gin.Context, req GetCallRecordHistoryReq) (result GetCallRecordHistoryRsp, err error) {
	if err = apis.Do(ctx, req, &result); err != nil {
		return
	}
	return
}
