package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/defines"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const GetCardSendUrl = "/touchmisgo/wechat/card/send"

type CardSendRes struct {
	Code   int64  `json:"code"`
	Msg    string `json:"msg"`
	TaskId int64  `json:"taskId"`
}

type CardSendReq struct {
	StudentList  []CanSendInfoReq `json:"studentList" form:"studentList"`
	AssistantUID int64            `json:"assistantUid" form:"assistantUid"` // 资产uid
	SourceType   string           `json:"sourceType" form:"sourceType"`     // 业务唯一标识类型,  privateSea: 私海
	SourceKey    string           `json:"sourceKey" form:"sourceKey"`       // 业务唯一标识
}

type CanSendInfoReq struct {
	StudentUid    int64 `json:"studentUid" form:"studentUid"`
	RemoteId      int64 `json:"remoteId" form:"remoteId"`
	RobotRemoteId int64 `json:"robotRemoteId" form:"robotRemoteId"`
}

func WechatCardSend(ctx *gin.Context, req CardSendReq) (*CardSendRes, error) {
	rsp := &CardSendRes{}
	if len(req.StudentList) > defines.CreateCardTaskLimit {
		return rsp, errors.New(fmt.Sprintf("学生数量不能超过%d个 %+v", defines.CreateCardTaskLimit, req.StudentList))
	}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}
	zlog.Infof(ctx, "[WechatCardSend] req: %+v, rsp: %+v", req, rsp)

	return rsp, nil

}
