package touchmisgo

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetSmsTplInfo(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	req := &GetTplInfoReq{
		TplList: []TplInfo{
			{
				TplId: 6177,
			}, {
				TplId: 6213,
				Vars:  []string{"courseName"},
			},
			{
				TplId: 5898,
				Vars:  []string{"courseName", "subject", "assistantWxNum"},
			},
		},
	}
	ret, err := GetSmsTplInfo(ctx, req)
	t.Log(ret, err)
}
