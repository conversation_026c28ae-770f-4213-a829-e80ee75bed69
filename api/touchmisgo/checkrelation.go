package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/defines"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
)

const GetCheckRelationUrl = "/touchmisgo/wechat/check/relation"

type CheckRelationRes struct {
	BindStudentUids   []int64 `json:"bindStudentUids"`
	UnbindStudentUids []int64 `json:"unbindStudentUids"`
}

type CheckRelationReq struct {
	AssistantUID int64   `json:"assistantUid" form:"assistantUid"` // 资产uid
	StudentUids  []int64 `json:"studentUids" form:"studentUids"`   // 任务类型
}

func WechatCheckRelation(ctx *gin.Context, req CheckRelationReq) (*CheckRelationRes, error) {
	rsp := &CheckRelationRes{}
	if len(req.StudentUids) > defines.CreateCardTaskLimit {
		return rsp, errors.New(fmt.Sprintf("学生数量不能超过%d个 %+v", defines.CreateCardTaskLimit, req.StudentUids))
	}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}

	return rsp, nil

}
