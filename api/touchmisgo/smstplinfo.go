package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	UriGetTplInfo = "/touchmisgo/sms/tplinfo"
)

type GetTplInfoReq struct {
	TplList []TplInfo `json:"tplList" form:"tplList"`
}

type TplInfo struct {
	TplId int64    `json:"tplId" form:"tplId"`
	Vars  []string `json:"vars" form:"vars"`
}

type GetTplInfoRsp struct {
	Content      string   `json:"content"`
	TplID        int64    `json:"tplId"`
	Vars         []string `json:"vars"`
	VarCnt       int64    `json:"varCnt"`
	UseTimeBegin int64    `json:"useTimeBegin"`
	UseTimeEnd   int64    `json:"useTimeEnd"`
}

func GetSmsTplInfo(ctx *gin.Context, req *GetTplInfoReq) (map[int64]GetTplInfoRsp, error) {
	ret := make(map[int64]GetTplInfoRsp)
	err := apis.Post(ctx, UriGetTplInfo, req, &ret, apis.WithEncode(apis.EncoderJson), apis.IgnoreInnerError())
	return ret, err
}
