package touchmisgo

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/defines"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
)

const GetCardCheckUrl = "/touchmisgo/wechat/card/check"

type WeChatCheckRes struct {
	AssistantUid   int64            `json:"assistantUid"`
	StudentUids    []int64          `json:"studentUids"`
	CanNotSendList []CanNotSendInfo `json:"canNotSendList"`
	CanSendList    []CanSendInfo    `json:"canSendList"`
	SendLimit      int64            `json:"sendLimit"`
	//FilterCntInfo  FilterCntInfo    `json:"filterCnt"`
}

type CanNotSendInfo struct {
	StudentUid    int64  `json:"studentUid"`
	StudentName   string `json:"studentName"`
	StudentPhone  string `json:"studentPhone"`
	RemoteId      int64  `json:"remoteId"`
	RobotRemoteId int64  `json:"robotRemoteId"`
	Tips          string `json:"tips"`
}

type CanSendInfo struct {
	StudentUid    int64  `json:"studentUid"`
	StudentName   string `json:"studentName"`
	StudentPhone  string `json:"studentPhone"`
	RemoteId      int64  `json:"remoteId"`
	RobotRemoteId int64  `json:"robotRemoteId"`
}

type FilterCntInfo struct {
	InvalidSendTimes int64 `json:"invalidSendTimes"`
	TeacherNoUseTime int64 `json:"teacherNoUseTime"`
	AlreadyFriends   int64 `json:"alreadyFriends"`
	MoUseTime        int64 `json:"noUseTime"`
	NoRobot          int64 `json:"noRobot"`
	NoRemoteId       int64 `json:"noRemoteId"`
	NoRobotInfo      int64 `json:"noRobotInfo"`
}

type CardCheckMsgReq struct {
	AssistantUID int64   `json:"assistantUid" form:"assistantUid"` // 资产uid
	StudentUids  []int64 `json:"studentUids" form:"studentUids"`   // 任务类型
	SourceType   string  `json:"sourceType" form:"sourceType"`     // 业务唯一标识类型,  privateSea: 私海
	SourceKey    string  `json:"sourceKey" form:"sourceKey"`       // 业务唯一标识
}

func WechatCardCheck(ctx *gin.Context, req CardCheckMsgReq) (*WeChatCheckRes, error) {
	rsp := &WeChatCheckRes{}
	if len(req.StudentUids) > defines.CreateCardTaskLimit {
		return rsp, errors.New(fmt.Sprintf("学生数量不能超过%d个 %+v", defines.CreateCardTaskLimit, req.StudentUids))
	}
	if err := apis.Do(ctx, req, rsp); err != nil {
		return nil, err
	}

	return rsp, nil

}
