package assistantai

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UrlGetKpOnline = "/assistant-ai/bailing/api/getkponline"

type GetKpOnlineReq struct {
	CorpId string `json:"corpId"`
	UserId string `json:"userId"`
}

type GetKpOnlineRsp struct {
	IsOnline bool `json:"isOnline"`
}

func GetKpOnline(ctx *gin.Context, req GetKpOnlineReq) (rsp GetKpOnlineRsp, err error) {
	rsp = GetKpOnlineRsp{}
	err = apis.Do(ctx, &req, &rsp, getAssistantHeader(ctx), apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetKeyBehavior failed, req: %+v, resp: %+v, err: %+v", req, req, err)
		return
	}
	return
}
