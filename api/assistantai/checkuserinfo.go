package assistantai

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UrlCheckUserInfo = "/assistant-ai/bailing/api/checkuserinfo"

type CheckUserInfoReq struct {
	CorpId  string `json:"corpId" form:"corpId"`
	UserId  string `json:"userId" form:"userId"`
	IpsId   int64  `json:"ipsId" form:"type"`
	IpsName string `json:"ipsName" form:"ipsName"`
}

type CheckUserInfoRsp struct {
	IsValid bool `json:"isValid"`
}

func CheckUserInfo(ctx *gin.Context, req CheckUserInfoReq) (rsp CheckUserInfoRsp, err error) {
	rsp = CheckUserInfoRsp{}
	err = apis.Do(ctx, &req, &rsp, getAssistantHeader(ctx), apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetKeyBehavior failed, req: %+v, resp: %+v, err: %+v", req, req, err)
		return
	}
	return
}
