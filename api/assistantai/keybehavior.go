package assistantai

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UrlKeyBehavior = "/assistant-ai/bailing/api/keybehavior"

type KeyBehaviorReq struct {
	CourseId   int64 `json:"courseId,omitempty"`
	StudentUid int64 `json:"studentUid,omitempty"`
}

type KeyBehaviorRsp struct {
	GroupList  []KeyBehaviorGroupData `json:"groupList"`
	CourseId   int64                  `json:"courseId"`
	LeadsId    int64                  `json:"leadsId"`
	StudentUid int64                  `json:"studentUid"`
}

type KeyBehaviorGroupData struct {
	GroupId   string `json:"groupId"`
	GroupName string `json:"groupName"`
	IsLight   int64  `json:"isLight"`
}

func GetKeyBehavior(ctx *gin.Context, req KeyBehaviorReq) (rsp KeyBehaviorRsp, err error) {
	rsp = KeyBehaviorRsp{}
	err = apis.Do(ctx, &req, &rsp, getAssistantHeader(ctx), apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetKeyBehavior failed, req: %+v, resp: %+v, err: %+v", req, req, err)
		return
	}
	return
}
