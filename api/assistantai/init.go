package assistantai

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
	"net/http"
)

func init() {
	apis.Register(UrlFunctionList, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  FunctionListReq{},
		Encoder:  apis.EncoderJson,
		Response: []string{},
	})
	apis.Register(UrlKeyBehavior, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  KeyBehaviorReq{},
		Encoder:  apis.EncoderJson,
		Response: KeyBehaviorRsp{},
	})
	apis.Register(UrlGetServerMode, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetServerModeReq{},
		Encoder:  apis.EncoderJson,
		Response: GetServerModeRsp{},
	})
	apis.Register(UrlSetServerMode, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  SetServerModeReq{},
		Encoder:  apis.EncoderJson,
		Response: SetServerModeRsp{},
	})
	apis.Register(UrlGetKpOnline, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetKpOnlineReq{},
		Encoder:  apis.EncoderJson,
		Response: GetKpOnlineRsp{},
	})
	apis.Register(UrlCheckUserInfo, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  CheckUserInfoReq{},
		Encoder:  apis.EncoderJson,
		Response: CheckUserInfoRsp{},
	})
}

const proxyModuleHeader = "X-PROXY-MODULE"
const proxyIpsUidHeader = "X-PROXY-IPS-UID"
const ipsContextKey = "ips-user-id"

func getAssistantHeader(c *gin.Context) apis.Option {
	userId := int64(0)
	userIdVal, ok := c.Get(ipsContextKey)
	if ok {
		userId = userIdVal.(int64)
	}

	headers := map[string]string{
		proxyModuleHeader: "assistantdeskgo",
		proxyIpsUidHeader: utils.ToString(userId),
	}
	return apis.WithHeaders(headers)
}
