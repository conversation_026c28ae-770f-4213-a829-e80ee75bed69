package assistantai

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UrlFunctionList = "/assistant-ai/bailing/api/functionlist"

type FunctionListReq struct {
	Type         int64  `json:"type"`
	CorpId       string `json:"corpId"`
	UserId       string `json:"userId"`
	AssistantUid int64  `json:"assistantUid" `
	PersonUid    int64  `json:"personUid"`
}

func GetFunctionList(ctx *gin.Context, req FunctionListReq) (respList []string, err error) {
	err = apis.Do(ctx, &req, &respList, getAssistantHeader(ctx), apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByUid failed, req: %+v, resp: %+v, err: %+v", req, respList, err)
		return
	}
	return
}
