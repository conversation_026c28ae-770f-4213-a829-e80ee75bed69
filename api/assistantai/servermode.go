package assistantai

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const UrlSetServerMode = "/assistant-ai/bailing/api/setservermode"
const UrlGetServerMode = "/assistant-ai/bailing/api/getservermode"

type SetServerModeReq struct {
	Mode    int64  `json:"mode"`
	IpsId   int64  `json:"ipsId"`
	IpsName string `json:"ipsName"`
}

type SetServerModeRsp struct {
}

type GetServerModeReq struct {
	IpsId   int64  `json:"ipsId"`
	IpsName string `json:"ipsName"`
}

type GetServerModeRsp struct {
	List    []GetServerModeRespData `json:"list"`
	LocalId int64                   `json:"localId"`
	IpsId   int64                   `json:"ipsId"`
	IpsName string                  `json:"ipsName"`
}
type GetServerModeRespData struct {
	Name    string `json:"name"`
	Mode    int64  `json:"mode"`
	Select  int64  `json:"select"`
	Default int64  `json:"default"`
	Disable int64  `json:"disable"`
}

//type GetServerModeData struct {
//	Name    string `json:"name"`
//	Mode    int64  `json:"mode"`
//	Select  int64  `json:"select"`
//	Default int64  `json:"default"`
//}

func GetServerMode(ctx *gin.Context, req GetServerModeReq) (rsp GetServerModeRsp, err error) {
	rsp = GetServerModeRsp{}
	err = apis.Do(ctx, &req, &rsp, getAssistantHeader(ctx), apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetKeyBehavior failed, req: %+v, resp: %+v, err: %+v", req, req, err)
		return
	}
	return
}

func SetServerMode(ctx *gin.Context, req SetServerModeReq) (rsp SetServerModeRsp, err error) {
	rsp = SetServerModeRsp{}
	err = apis.Do(ctx, &req, &rsp, getAssistantHeader(ctx), apis.IgnoreInnerError())
	if err != nil {
		zlog.Warnf(ctx, "GetKeyBehavior failed, req: %+v, resp: %+v, err: %+v", req, req, err)
		return
	}
	return
}
