package api

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	EncodeForm = "form"
	EncodeJson = "json"
)

func RalPost(ctx *gin.Context, client *base.ApiClient, path string, params map[string]interface{}, encode string, output interface{}) error {
	if encode == "" {
		encode = EncodeForm
	}
	opt := base.HttpRequestOptions{
		RequestBody: params,
		Encode:      encode,
	}
	return RalPostWithOpt(ctx, client, path, opt, nil, output)
}

func RalPostWithOpt(ctx *gin.Context, client *base.ApiClient, path string, opt base.HttpRequestOptions, apiResp Response, output interface{}) error {
	zlog.Infof(ctx, "RalPostWithCookieAndHeader url=%v,opt=%v", path, opt)
	resp, err := client.HttpPost(ctx, path, opt)
	if err != nil {
		zlog.Warnf(ctx, "RalPost failed, url[%s] Detail[%+v], err:%s", path, opt, err)
		return err
	}

	if apiResp == nil {
		apiResp = &BzrResponse{}
	}
	err = DecodeResponse(ctx, resp, apiResp, output)
	if err != nil {
		zlog.Warnf(ctx, "RalPost decode failed, url[%s] Detail[%+v], err:%s", path, opt, err)
	}

	return err
}

func RalGet(ctx *gin.Context, client *base.ApiClient, path string, params map[string]interface{}, output interface{}) error {
	zlog.Infof(ctx, "RalGet url=%v,param=%v", path, params)
	opt := base.HttpRequestOptions{
		RequestBody: params,
	}
	resp, err := client.HttpGet(ctx, path, opt)
	if err != nil {
		zlog.Warnf(ctx, "RalGet failed, url[%s] Detail[%+v], err:%s", path, opt, err)
		return err
	}

	var apiResp BzrResponse
	err = DecodeResponse(ctx, resp, &apiResp, output)
	if err != nil {
		zlog.Warnf(ctx, "RalGet decode failed, url[%s] Detail[%+v], err:%s", path, params, err)
	}

	return err
}

// 判断httpcode, 框架层面没有处理
func ApiHttpCode(ctx *gin.Context, res *base.ApiResult) (err error) {
	if res.HttpCode != http.StatusOK {
		zlog.Warnf(ctx, "api http code not 200", res)
		return err
	}
	return
}
