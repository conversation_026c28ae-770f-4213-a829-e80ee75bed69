package dal

import (
	"assistantdeskgo/api/zbcore"
	"assistantdeskgo/utils"
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	CourseModule         = "dal"
	CourseEntity         = "course"
	LessonEntity         = "lesson"
	CourseConcurrencyMax = 100 // 取课程的最大并必度
)

type CourseInfo struct {
	CourseId      int64   `json:"courseId" mapstructure:"courseId"`
	CourseName    string  `json:"courseName" mapstructure:"courseName"`
	TeacherUids   []int64 `json:"teacherUids" mapstructure:"teacherUids" `
	StopTime      int64   `json:"stopTime" mapstructure:"stopTime"`
	MainSubjectId int64   `json:"mainSubjectId" mapstructure:"mainSubjectId"`
	StartTime     int64   `json:"startTime" mapstructure:"startTime"`
	SkuId         int64   `json:"skuId" mapstructure:"skuId"`
	IsOwnPackage  int64   `json:"isOwnPackage"`
	Source        int64   `json:"source"`
	MainGradeId   int64   `json:"mainGradeId" mapstructure:"mainGradeId"`
	//BrandId               int           `json:"brandId" mapstructure:"brandId"`
	//Content               string        `json:"content" mapstructure:"content"` // JSON
	//CoreLessonCnt         int           `json:"coreLessonCnt" mapstructure:"coreLessonCnt"`
	//CoreLessonCompleteCnt int           `json:"coreLessonCompleteCnt" mapstructure:"coreLessonCompleteCnt"`
	//CourseTags            []interface{} `json:"courseTags" mapstructure:"courseTags"`
	//CourseType            int           `json:"courseType" mapstructure:"courseType"`
	CpuId int64 `json:"cpuId" mapstructure:"cpuId"`
	//DeleteResason         string        `json:"deleteResason" mapstructure:"deleteResason"`
	//DeleteTime            int           `json:"deleteTime" mapstructure:"deleteTime"`
	//FirstLessonTime       int           `json:"firstLessonTime" mapstructure:"firstLessonTime"`
	//FormatShow            int           `json:"formatShow" mapstructure:"formatShow"`
	//Grades                []int         `json:"grades" mapstructure:"grades"`
	//HasMaterial           int           `json:"hasMaterial" mapstructure:"hasMaterial"`
	//IsInner               int           `json:"isInner" mapstructure:"isInner"`
	//LastLessonStopTime    int           `json:"lastLessonStopTime" mapstructure:"lastLessonStopTime"`
	LearnSeason int `json:"learnSeason" mapstructure:"learnSeason"`
	//LectureSendTime       int           `json:"lectureSendTime" mapstructure:"lectureSendTime"`
	//LessonCnt             int           `json:"lessonCnt" mapstructure:"lessonCnt"`
	//LessonCompleteCnt     int           `json:"lessonCompleteCnt" mapstructure:"lessonCompleteCnt"`
	NewCourseType int64 `json:"newCourseType" mapstructure:"newCourseType"`
	//NumberPeriods         string        `json:"numberPeriods" mapstructure:"numberPeriods"`
	//OnlineFormatTime      string        `json:"onlineFormatTime" mapstructure:"onlineFormatTime"`
	OnlineFormatTimeAll string `json:"onlineFormatTimeAll" mapstructure:"onlineFormatTimeAll"`
	//Season                int           `json:"season" mapstructure:"season"`
	//Status                int           `json:"status" mapstructure:"status"`
	//Subjects              []int         `json:"subjects" mapstructure:"subjects"`
	//VipClass              int           `json:"vipClass" mapstructure:"vipClass"`
	//Year                  int           `json:"year" mapstructure:"year"`
	ServiceInfo []ServiceInfo `json:"serviceInfo" mapstructure:"serviceInfo"`
	LessonInfo  LessonInfo    `json:"lessonInfo"`
}

type CourseLessonInfo struct {
	CourseInfo `mapstructure:",squash"`
	LessonList map[int]LessonInfo `mapstructure:"lessonList"`
}

func getCourseAllFields() []string {
	return []string{
		"courseId",
		"courseName",
		"grades",     //array [2,4   Zb_Const_GradeSubject 课程年纪,学科枚举值
		"subjects",   //array [3,4]
		"courseType", //int 课程类型   Zb_Const_Course 枚举类
		"season",     //1、2、3、4 春暑秋寒
		"numberPeriods",
		"learnSeason",           //int 学季Id    Zb_Const_LearnSeason  学季枚举信息
		"year",                  //学年
		"firstLessonTime",       //第一章节开课时间
		"lastLessonStopTime",    //最后一节课结束时间
		"status",                //课程状态       Zb_Const_Course  枚举类
		"isInner",               //是否内部课
		"onlineFormatTime",      //课程上课时间(格式化好的与app端同步)
		"onlineFormatTimeAll",   //上课时间格式化，展示频率最高的时间
		"lessonCnt",             //有效章节总数
		"lessonCompleteCnt",     //有效已上章节数量
		"coreLessonCnt",         //有效主题课章节数量
		"coreLessonCompleteCnt", //有效主题课已上章节数量
		"hasMaterial",           //是否有教材
		"mainGradeId",           //int 主年级字段
		"mainSubjectId",         //int 主学科字段
		"vipClass",              // VIP课程
		"finishTime",            // 结束时间
		"lectureSendTime",       // 教材发送时间
		"formatShow",            // content展示方式是html还是格式化信息
		"deleteTime",            // 删除时间
		"deleteReason",          // 删除原因
		"cpuId",                 // cpuID
		"courseTags",            //课程标签
		"brandId",               //课程类型
		"content",               //内容
		"startTime",             //开始时间
		"newCourseType",         //newCourseType
		"serviceInfo",
		"isOwnPackage",
	}
}

// 根据课程Id获取课程信息和章节信息
func GetCourseLessonInfoByCourseId(ctx *gin.Context, courseId int64, courseFields []string, lessonFields []string) (CourseLessonInfo, error) {
	if courseId <= 0 {
		return CourseLessonInfo{}, nil
	}
	ret, err := GetCourseLessonInfoByCourseIds(ctx, []int64{courseId}, courseFields, lessonFields)
	if err != nil {
		return CourseLessonInfo{}, err
	}

	if info, ok := ret[courseId]; ok {
		return info, nil
	}

	return CourseLessonInfo{}, nil
}

func GetCourseLessonInfoByCourseIds(ctx *gin.Context, courseIds []int64, courseFields, lessonFields []string) (map[int64]CourseLessonInfo, error) {
	if len(courseIds) == 0 {
		return nil, base.Error{ErrNo: 500, ErrMsg: "courseIds不能为空"}
	}

	if len(courseIds) > CourseConcurrencyMax {
		// 批量并发获取
		return courseLessonBatch(ctx, courseIds, courseFields, lessonFields), nil
	} else {
		return courseLessonSingle(ctx, courseIds, courseFields, lessonFields)
	}
}
func courseLessonBatch(ctx *gin.Context, courseIds []int64, courseFields, lessonFields []string) map[int64]CourseLessonInfo {
	chunks := utils.ChunkArrayInt64(courseIds, CourseConcurrencyMax)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int64]CourseLessonInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(cids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "courseLessonBatch panic err : %+v", r)
				}
			}()
			defer wg.Done()
			ret, err := courseLessonSingle(ctx, cids, courseFields, lessonFields)
			if err != nil {
				zlog.Warnf(ctx, "courseLessonSingle failed, courseIds:%+v, err:%s", cids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[int64]CourseLessonInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseLessonBatch panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

func courseLessonSingle(ctx *gin.Context, courseIds []int64, courseFields, lessonFields []string) (map[int64]CourseLessonInfo, error) {
	if len(courseFields) == 0 {
		courseFields = getCourseAllFields()
	}

	if len(lessonFields) == 0 {
		lessonFields = getLessonAllFields()
	}

	var courseLessonMap map[int64]CourseLessonInfo
	err := getKVByCourseId(ctx, courseIds, courseFields, lessonFields, nil, &courseLessonMap)
	if err != nil {
		return nil, err
	}

	return courseLessonMap, nil
}

// 课程详情接口
// courseIds      待查询课程ID json list [123, 234, 345]
// courseFields   接口返回的Course字段
// lessonFields   接口返回的Lesson字段
// materialFields 接口返回的Material字段
func getKVByCourseId(ctx *gin.Context, courseIds []int64, courseFields []string, lessonFields []string,
	materialFields []string, output interface{}) error {

	if len(courseFields) == 0 {
		courseFields = getCourseAllFields()
	}

	if len(lessonFields) == 0 {
		lessonFields = []string{}
	}

	if len(materialFields) == 0 {
		materialFields = []string{}
	}

	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, CourseEntity, "getKV", false, env.GetAppName())
	params := map[string]interface{}{
		"courseIds":      courseIds,
		"courseFields":   courseFields,
		"lessonFields":   lessonFields,
		"materialFields": materialFields,
	}

	_, err := zbcore.PostDal(ctx, params, arrHeader, output)
	if err != nil {
		zlog.Warnf(ctx, "getKVByCourseId failed, err:%s", err)
		return err
	}

	return nil
}
