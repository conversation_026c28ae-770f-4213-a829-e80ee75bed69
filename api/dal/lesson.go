package dal

import (
	"assistantdeskgo/api/zbcore"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
)

type LessonInfo struct {
	LessonId       int           `json:"lessonId" mapstructure:"lessonId"`
	LessonName     string        `json:"lessonName" mapstructure:"lessonName"`
	StartTime      int           `json:"startTime" mapstructure:"startTime"`
	StopTime       int           `json:"stopTime" mapstructure:"stopTime"`
	FinishTime     int           `json:"finishTime" mapstructure:"finishTime"`
	Status         int           `json:"status" mapstructure:"status"`
	OutlineId      int           `json:"outlineId" mapstructure:"outlineId"`
	CourseId       []int         `json:"courseId" mapstructure:"courseId"`
	LessonType     int           `json:"lessonType" mapstructure:"lessonType"`
	Mode           int           `json:"mode" mapstructure:"mode"`
	HasHomework    int           `json:"hasHomework" mapstructure:"hasHomework"`
	HasPlayback    int           `json:"hasPlayback" mapstructure:"hasPlayback"`
	PreviewNoteUri interface{}   `json:"previewNoteUri" mapstructure:"previewNoteUri"`
	ClassNoteUri   interface{}   `json:"classNoteUri" mapstructure:"classNoteUri"`
	ReopenLessonId int           `json:"reopenLessonId" mapstructure:"reopenLessonId"`
	FileList       interface{}   `json:"fileList" mapstructure:"fileList"`
	ServiceInfo    []ServiceInfo `json:"serviceInfo" mapstructure:"serviceInfo"`
	StageTest      interface{}   `json:"stageTest" mapstructure:"stageTest"`
	PlayType       int           `json:"playType" mapstructure:"playType"`
	XXGJClassType  int           `json:"XXGJClassType" mapstructure:"XXGJClassType"`
	T007Tag        int           `json:"t007Tag" mapstructure:"t007Tag"`
}
type ServiceInfo struct {
	ContainerId     int                    `json:"containerId" mapstructure:"containerId"`
	ContainerType   int                    `json:"containerType" mapstructure:"containerType"`
	ServiceProvider int                    `json:"serviceProvider" mapstructure:"serviceProvider"`
	ServiceId       int                    `json:"serviceId" mapstructure:"serviceId"`
	ServiceName     string                 `json:"serviceName" mapstructure:"serviceName"`
	ServiceStatus   int                    `json:"serviceStatus" mapstructure:"serviceStatus"`
	ExtData         map[string]interface{} `json:"extData" mapstructure:"extData"`
}

func getLessonAllFields() []string {
	return []string{
		"lessonId",
		"courseId",       // 课程获取int | 章节获取 array [55424,33152]
		"lessonName",     // 章节名称
		"startTime",      // 规定章节开始时间
		"stopTime",       // 规定章节结束时间
		"status",         // 章节状态 Zb_Const_Lesson 枚举值
		"lessonType",     // 章节类型,班课只有两种类型[1:主题课 ,2:提升课]
		"hasHomework",    // 是否有作业(测试系统汇聚) //此字段2020-11-15之后测试系统将不再维护，请以Api_Examcore::getRelation接口数据为准
		"hasPlayback",    // 是否有回放(回放系统汇聚)
		"finishTime",     // 老师关播时间,实际结束时间(下课命令)
		"previewNoteUri", // 课前预习,学习资料
		"classNoteUri",   // 课堂笔记
		"reopenLessonId", // 重开章节
		"fileList",       // 重开章节
		"outlineId",      // 大纲ID
		"reopenLessonId", // 重开章节
		"serviceInfo",    // 服务字段 ！！！注意：该字段只有通过章节Id查询才会得到，getLessonBaseByLessonIds
		"stageTest",      // 阶段测试
		"playType",       // 章节类型
		"XXGJClassType",  //自习室
		"t007Tag",        // 007录播课标签
	}
}

type DateCnt struct {
	Total   int `json:"total"`
	PageMax int `json:"pageMax"`
}

func GetAllKVByDate(ctx *gin.Context, date string, courseFields, lessonFields []string) ([]CourseInfo, error) {
	output, err := GetDateCnt(ctx, date)
	if err != nil {
		return nil, err
	}
	page := output.Total / output.PageMax
	if output.Total%output.PageMax > 0 {
		page++
	}
	ret := []CourseInfo{}
	for i := 0; i < page; i++ {
		arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, LessonEntity, "getKVByDate", false, env.GetAppName())
		params := map[string]interface{}{
			"date":         date,
			"courseFields": courseFields,
			"lessonFields": lessonFields,
			"pn":           i * output.PageMax,
			"rn":           output.PageMax,
		}
		curPageRet := []CourseInfo{}
		_, err = zbcore.PostDal(ctx, params, arrHeader, &curPageRet)
		if err != nil {
			return ret, err
		}
		ret = append(ret, curPageRet...)
	}
	return ret, nil
}

func GetDateCnt(ctx *gin.Context, date string) (output DateCnt, err error) {
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, LessonEntity, "getDateCnt", false, env.GetAppName())
	params := map[string]interface{}{
		"date": date,
	}

	_, err = zbcore.PostDal(ctx, params, arrHeader, &output)
	return
}
