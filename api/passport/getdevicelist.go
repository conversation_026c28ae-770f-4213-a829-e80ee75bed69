package passport

import (
	"encoding/json"
	"net/http"

	"assistantdeskgo/api/apis"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	GetDeviceListUrl = "/session/innerapi/getuserlog"
)

func init() {
	apis.Register(GetDeviceListUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetDeviceListReq{},
		Encoder:  apis.EncoderForm,
		Response: GetDeviceListRes{},
	})
}

type GetDeviceListReq struct {
	Uid  int64 `json:"uid" form:"uid"`
	Days int64 `json:"days" form:"days"`
}

type GetDeviceListRes struct {
	Total    int    `json:"total"`
	InfoList []Info `json:"infoList"`
}
type Info struct {
	AppID     string `json:"app_id"`
	MaskPhone string `json:"mask_phone"`
	Type      int    `json:"type"`
	UIP       string `json:"uip"`
	CtxStr    string `json:"ctx"`
	CtxObj    Ctx    `json:"ctxObj"`
	DetailStr string `json:"detail"`
	DetailOjb Detail `json:"detailObj"`
	Ts        int64  `json:"ts"`
	Date      string `json:"date"`
	LType     string `json:"ltype"`
	SubLType  string `json:"sub_ltype"`
	Path      string `json:"path"`
	Ret       string `json:"ret"`
}
type Ctx struct {
	Device string `json:"device"`
	AppID  string `json:"app_id"`
	OS     string `json:"os"`
	CUID   string `json:"cuid"`
	Vcname string `json:"vcname"`
	VC     string `json:"vc"`
}
type Detail struct {
	LoginFrom string `json:"loginFrom"`
	UA        string `json:"ua"`
	Refer     string `json:"refer"`
	LogID     string `json:"logId"`
}

func GetDeviceList(ctx *gin.Context, assistantUid, days int64) (rsp *GetDeviceListRes, err error) {
	req := &GetDeviceListReq{
		Uid:  assistantUid,
		Days: days,
	}
	rsp = &GetDeviceListRes{}
	if err = apis.Do(ctx, req, rsp, apis.IgnoreInnerError()); err != nil {
		return nil, err
	}
	for i := range rsp.InfoList {
		rspinfo := &rsp.InfoList[i]
		if len(rspinfo.CtxStr) > 0 {
			json.Unmarshal([]byte(rspinfo.CtxStr), &rspinfo.CtxObj)
			if err != nil {
				zlog.Warnf(ctx, "rspinfo.CtxStr Error unmarshalling JSON:", err)
				return
			}
		}
		if len(rspinfo.DetailStr) > 0 {
			json.Unmarshal([]byte(rspinfo.DetailStr), &rspinfo.DetailOjb)
			if err != nil {
				zlog.Warnf(ctx, "rspinfo.DetailStr Error unmarshalling JSON:", err)
				return
			}
		}
	}
	return
}
