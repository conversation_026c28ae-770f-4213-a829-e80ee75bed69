package passport

import (
	"net/http"

	"assistantdeskgo/api/apis"

	"github.com/gin-gonic/gin"
)

const (
	UriSafCheckLogi = "/session/api/safchecklogin"
)

func init() {
	apis.Register(UriSafCheckLogi, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  SafCheckLoginReq{},
		Encoder:  apis.EncoderJson,
		Response: SafCheckLoginRsp{},
	})
}

type SafCheckLoginReq struct {
}

type SafCheckLoginRsp struct {
	Uid   int64  `json:"uid"`
	Phone string `json:"phone"`
}

func SafCheckLogin(ctx *gin.Context, cookie string) (rsp SafCheckLoginRsp, err error) {
	req := SafCheckLoginReq{}
	err = apis.Post(ctx, UriSafCheckLogi, &req, &rsp, apis.WithEncode(apis.EncoderJson), apis.IgnoreInnerError(), apis.AddCookie("ZYBUSS", cookie))
	return
}
