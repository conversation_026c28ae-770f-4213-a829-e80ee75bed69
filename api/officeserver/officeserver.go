package officeserver

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/dto/dtocreatepictask"
	"errors"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	TriggerTypeOnline  = "online"
	TriggerTypeOffline = "offline"

	TaskTypeUrl      = "url"
	TaskTypeTemplate = "template"
	TaskTypePdf      = "pdf"
	TaskTypeWord     = "word"

	// NotAllowErrInConsole url不允许控制台报错
	NotAllowErrInConsole = 1

	UriCreateCapture = "/office-server/v2/offline/url2capture"
	UriGetTask       = "/office-server/task/get"

	StatusTaskSuccess = "success"

	RenderDoneWayWindow     = "window" // 默认的页面渲染完成标志参数
	renderDoneContentFinish = "finish" // 当renderDoneWay为window时候，需要传递传递window.status等于的内容，比如finish, 默认 finish

	CaptureParamGrayKey = "assistantdeskgo_capture_param_gray"
)

type ParamCreateCapture struct {
	Url                    string  `form:"url"`
	TemplateId             int     `form:"templateID"`
	ExpectMinuteTime       int     `form:"expectMinuteTime"`
	Width                  int     `form:"width"`
	Height                 int     `form:"height"`
	TaskType               string  `form:"taskType"`
	CallBackUrl            string  `form:"callbackURL"`
	OccurErrCountInConsole *int    `form:"occurErrCountInConsole,omitempty"`
	Merge                  bool    `form:"merge"`
	Span                   int     `form:"span"`
	Resolution             float64 `form:"resolution"`
	NeedText               bool    `form:"needText"`
	Cookies                string  `form:"cookies"`

	// renderDoneWindowKey 这个用的少，没写在文档里面，支持这个功能
	RenderDoneWindowKey *string `form:"renderDoneWindowKey,omitempty"`
	RenderDoneWay       *string `form:"renderDoneWay,omitempty"`
	RenderDoneContent   *string `form:"renderDoneContent,omitempty"`
	// preEval可能在页面渲染之前执行
	PreEval *string `form:"preEval,omitempty"`
	Eval    *string `form:"eval,omitempty"`
}

type RetCreateCapture struct {
	DwnURLs []string `json:"dwnURLs"`
	TaskId  string   `json:"taskID"`
}

func CreateCapture(ctx *gin.Context, param ParamCreateCapture) (RetCreateCapture, error) {
	var resp RetCreateCapture
	zlog.Infof(ctx, "CreateCapture_param: %+v", param)
	err := apis.Do(ctx, param, &resp)
	return resp, err
}

func GenerateOffLineUrlParam(ctx *gin.Context, childTask dtocreatepictask.ChildTaskItem, callbackUrl string) ParamCreateCapture {

	extData := map[string]interface{}{}
	var sceneType int64 = 0
	_ = jsoniter.UnmarshalFromString(childTask.ExtData, &extData)
	if sceneTypeInf, ok := extData["sceneType"]; ok {
		sceneType = cast.ToInt64(sceneTypeInf)
	}

	// 接口文档：https://docs.zuoyebang.cc/doc/1822982038866239620_01_0001_000_b75f3db0a4b54f1987db50eef55df971
	// 接口负责人：尚义龙
	param := ParamCreateCapture{
		Url:         childTask.PicUrl,
		TaskType:    TaskTypeUrl,
		CallBackUrl: callbackUrl,
		Cookies:     childTask.Cookies,
	}
	// 含义是当页面出现错误大于N次的时候，停止任务 并返回错误。默认是无限制，值是数字（N）
	occurErr := 1
	param.OccurErrCountInConsole = &occurErr

	switch sceneType {
	case touchmis.SendTypeBottomTestReport, touchmis.SendTypeBottomTestReportFd:
		// 摸底测不加参数
	case touchmis.SendTypeStageResultFeedbackCard:
		js := "app.style.overflow = 'unset'"
		param.Eval = &js
	case touchmis.SendTypeClassReport, touchmis.SendTypeLearnReportFd, touchmis.SendTypeLearnReportCambridgeEnglish:
		// 课堂报告增加精彩瞬间板块，为了解决此前课堂报告截图的一些小问题，C端前端对页面进行了优化：
		// 1. 设置了overflow样式，不需要注入js代码进行截图，并解决了白底问题
		// 2. 增加了__capture__status__的finish事件，用于截图的触发
		// 后端传的时候加个参数 renderDoneWindowKey = "__capture__status__"
		key := "__capture__status__"
		renderContent := renderDoneContentFinish
		param.RenderDoneWindowKey = &key
		param.RenderDoneContent = &renderContent
		if captureParamHitGray(ctx, "classReport", childTask.ParentTaskId) {
			// 出现工单，部分课堂报告截图空白，发现是少传了参数 renderDoneWay=window
			// 但是，加上这个参数又直接不能生成截图了，因为C端前端实现的__capture__status__的逻辑有问题
			// 截图参数这一块实在太容易出问题了，出现问题又是偶发的，不易复现，所以加个灰度开关
			renderWay := RenderDoneWayWindow
			param.RenderDoneWay = &renderWay
			// C端前端觉得__capture__status__的逻辑有点重，不需要在用户本地运行
			// 所以让需要截图时在url上加个needCapture=1参数
			param.Url += "&needCapture=1"
		}
	default:
		// 默认加这些参数，以保证与之前的逻辑兼容
		renderWay := RenderDoneWayWindow
		renderContent := renderDoneContentFinish
		param.RenderDoneWay = &renderWay
		param.RenderDoneContent = &renderContent
	}

	if childTask.Width > 0 {
		param.Width = childTask.Width
	}
	if childTask.Height > 0 {
		param.Height = childTask.Height
	}
	if param.ExpectMinuteTime <= 0 {
		param.ExpectMinuteTime = 0
	}
	paramStr, _ := jsoniter.MarshalToString(param)
	zlog.Debugf(ctx, "GenerateOffLineUrlParam sceneType:%+v, childTask:%+v, param:%+v", sceneType, childTask, paramStr)
	return param
}

func captureParamHitGray(ctx *gin.Context, confName string, key int) bool {
	grayConf := make(map[string]int)
	err := mercury.GetConfigForJson(ctx, CaptureParamGrayKey, mercury.DefaultExpireTime, &grayConf)
	if err != nil {
		zlog.Infof(ctx, "GetConfigForJson fai,key:%+v, err:%+v", CaptureParamGrayKey, err)
		return false
	}
	if percent, ok := grayConf[confName]; ok {
		return (key % 100) < percent
	}
	return false
}

type ParamGetTask struct {
	TaskIds string `json:"taskIDs" form:"taskIDs"`
}

type RetGetTask struct {
	List []TaskInfo `json:"list"`
}

type TaskInfo struct {
	TaskId    string `json:"taskID"`
	Status    string `json:"status"`
	DwnUrl    string `json:"dwnUrl"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Duration  int    `json:"duration"`
}

func GetTaskDetail(ctx *gin.Context, taskId string) (RetGetTask, error) {
	var resp RetGetTask
	param := ParamGetTask{TaskIds: taskId}
	err := apis.Do(ctx, param, &resp)
	if resp.List[0].Status != StatusTaskSuccess {
		err = errors.New("office-server 任务失败，taskId:" + taskId)
	}
	return resp, err
}
