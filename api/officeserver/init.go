package officeserver

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/conf"
	"net/http"
)

func checkApiClient() {
	if conf.API.OfficeServer.Service == "" {
		panic("service officeServer uninitialized")
	}
}

func init() {
	//checkApiClient()
	apis.Register(UriCreateCapture, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  ParamCreateCapture{},
		Encoder:  apis.EncoderForm,
		Response: RetCreateCapture{},
	})
	apis.Register(UriGetTask, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  ParamGetTask{},
		Encoder:  apis.EncoderForm,
		Response: RetGetTask{},
	})
}
