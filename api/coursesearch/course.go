package coursesearch

import (
	"assistantdeskgo/api/apis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	SearchUrl = "/coursesearch/api/query"
)

func DoSearch(ctx *gin.Context, req Param) (resp *CourseList, err error) {
	err = apis.Do(ctx, req, &resp)
	if err != nil {
		zlog.Warnf(ctx, "GetGroupReceivers failed, req: %+v, resp: %+v, err: %+v", req, resp, err)
		return
	}

	return
}
