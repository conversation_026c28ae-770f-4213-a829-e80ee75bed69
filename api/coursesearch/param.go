package coursesearch

import "encoding/json"

const (
	ArrCondsOpAnd = "and"
	ArrCondsOpOr  = "or"

	OrderDesc = "desc"

	AggType = 0

	DocTypeCourse = 2
)

type Param struct {
	ArrConds  string `json:"arrConds" form:"arrConds"`
	Pn        int64  `json:"pn" form:"pn"`
	Rn        int64  `json:"rn" form:"rn"`
	ArrFields string `json:"arrFields" form:"arrFields"`
	OrderBy   string `json:"orderBy" form:"orderBy"`
	DocType   int64  `json:"docType" form:"docType"`
}

type ArrConds struct {
	Op   string `json:"op" form:"op"`
	Aggs []Agg  `json:"aggs" form:"aggs"`
}

type Agg struct {
	Type  int64 `json:"type" form:"type"`
	Conds Cond  `json:"conds" form:"conds"`
}

type Cond struct {
	Key   string      `json:"key" form:"key"`
	Value interface{} `json:"value" form:"value"`
	Exps  string      `json:"exps" form:"exps"`
}

func NewParam() *Param {
	return &Param{}
}

func NewConds() *ArrConds {
	return &ArrConds{}
}

func (a *ArrConds) OpAnd() *ArrConds {
	a.Op = ArrCondsOpAnd
	a.Aggs = []Agg{}
	return a
}

func (a *ArrConds) OpOr() *ArrConds {
	a.Op = ArrCondsOpOr
	a.Aggs = []Agg{}
	return a
}

func (a *ArrConds) AddCond(key, exps string, value interface{}) *ArrConds {
	a.Aggs = append(a.Aggs, Agg{
		Type: AggType,
		Conds: Cond{
			Key:   key,
			Value: value,
			Exps:  exps,
		},
	})
	return a
}

func (p *Param) BuildConds(conds ArrConds) *Param {
	marshal, _ := json.Marshal(conds)
	p.ArrConds = string(marshal)
	return p
}

func (p *Param) SetFields(fields []string) *Param {
	marshal, _ := json.Marshal(fields)
	p.ArrFields = string(marshal)
	return p
}

func (p *Param) SetDocType(docType int64) *Param {
	p.DocType = docType
	return p
}

func (p *Param) SetOrderBy(orderKey, orderType string) *Param {
	orderMap := map[string]string{
		orderKey: orderType,
	}
	marshal, _ := json.Marshal(orderMap)
	p.OrderBy = string(marshal)
	return p
}

func (p *Param) SetPage(pageNum, pageSize int64) *Param {
	p.Pn = pageNum
	p.Rn = pageSize
	return p
}

type Course struct {
	CourseId   int64  `json:"courseId"`
	GradeId    int64  `json:"gradeId"`
	SubjectId  int64  `json:"subjectId"`
	CourseName string `json:"courseName"`
}

type CourseList struct {
	List []Course `json:"list"`
}
