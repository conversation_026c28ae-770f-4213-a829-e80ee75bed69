package moatapi

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const (
	pathGoodsSkuKvBySkuIdV2 = "/newgoodsplatform/sku/getgoodsskukvbyskuidv2"
)

type GetGoodsSkuKvReq struct {
}

type GetGoodsSkuKvInfo struct {
	SkuId            int64  `json:"skuId"`
	SkuName          string `json:"skuName"`
	ShopId           int    `json:"shopId"`
	ThirdId          int64  `json:"thirdId"`
	CategoryId       int    `json:"categoryId"`
	Status           int    `json:"status"`
	SpecTagsMap      map[string]TagsItem
	SpecTags         []TagsItem `json:"specTags"`
	Version          int        `json:"version"`
	AttributeTagsMap map[string]TagsItem
	AttributeTags    []TagsItem `json:"attributeTags"`
}

type TagsItem struct {
	Key    string          `json:"key"`
	Name   string          `json:"name"`
	Values []TagsValueItem `json:"values"`
}

type TagsValueItem struct {
	Code     string `json:"code"`
	CodeName string `json:"codeName"`
}

func GetGoodsSkuKvBySkuIdV2(ctx *gin.Context, skuIds []int64) (skuId2Info map[int64]GetGoodsSkuKvInfo, err error) {
	params := map[string]interface{}{
		"skuIds": skuIds,
	}
	finalParam := getSignV2(params, APP_KEY, APP_SECRET)
	req := GetGoodsSkuKvReq{}
	rsp := make([]GetGoodsSkuKvInfo, 0)
	err = apis.Post(ctx, pathGoodsSkuKvBySkuIdV2, &req, &rsp, apis.WithEncode(apis.EncoderForm), apis.WithRequest(finalParam))
	skuId2Info = make(map[int64]GetGoodsSkuKvInfo)
	for _, _item := range rsp {
		specTagsMap := map[string]TagsItem{}
		for _, _specTagsItem := range _item.SpecTags {
			specTagsMap[_specTagsItem.Key] = _specTagsItem
		}
		_item.SpecTagsMap = specTagsMap

		attributeTagsMap := map[string]TagsItem{}
		for _, _attributeTagsItem := range _item.AttributeTags {
			attributeTagsMap[_attributeTagsItem.Key] = _attributeTagsItem
		}
		_item.AttributeTagsMap = attributeTagsMap
		skuId2Info[_item.SkuId] = _item
	}
	return
}
