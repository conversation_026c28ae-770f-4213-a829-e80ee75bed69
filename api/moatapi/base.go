package moatapi

import (
	"bytes"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	jsoniter "github.com/json-iterator/go"
	"reflect"
	"sort"
	"strings"
)

const (
	APP_KEY    = "gx_sell"
	APP_SECRET = "c608ff0b95d29f7dbee2b1970a93d973"
)

func getSignV2(params map[string]interface{}, appKey string, appSecret string) map[string]interface{} {
	signBuf := bytes.NewBuffer(nil)
	signBuf.WriteString(appKey)
	params["appkey"] = appKey

	var arrParamMap map[string]string
	newOpts := make(map[string]interface{})
	optKeys := make([]string, 0)
	for k, v := range params {
		if v == nil {
			continue
		}
		optKeys = append(optKeys, k)
		switch v.(type) {
		case string:
			newOpts[k] = v.(string)
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
			newOpts[k], _ = jsoniter.MarshalToString(v)
		default:
			if reflect.TypeOf(v).Kind().String() == reflect.Slice.String() {
				newOpts[k] = v
				if arrParamMap == nil {
					arrParamMap = make(map[string]string)
				}
				arrParamMap[k], _ = jsoniter.MarshalToString(v)
			} else {
				newOpts[k], _ = jsoniter.MarshalToString(v)
			}
		}
	}

	sort.Strings(optKeys)
	for _, k := range optKeys {
		signBuf.WriteString(k)
		if arrParamMap != nil {
			if v, ok := arrParamMap[k]; ok {
				signBuf.WriteString(v)
				continue
			}
		}
		signBuf.WriteString(newOpts[k].(string))
	}
	signBuf.WriteString(appSecret)
	s := signBuf.String()
	sign := strings.ToLower(utils.Md5(s))
	newOpts["sign"] = sign
	return newOpts
}
