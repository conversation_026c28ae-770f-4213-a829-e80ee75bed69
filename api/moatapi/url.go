package moatapi

import (
	"assistantdeskgo/conf"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const UrlGetSHortUrl = "/su/api/createshorturl"

const (
	URL_APP_KEY    = "assistantdesk"
	URL_APP_SECRET = "86ec2d3211ac6e7ff5fcbd95fbf4e2c0"
)

type GetShortUrlReq struct {
	AppKey  int64  `json:"appKey" form:"appKey"`
	LongUrl string `json:"longUrl" form:"longUrl"`
	Sign    string `json:"sign" form:"sign"`
}

type GetShortUrlRsp struct {
	ShortUrl string `json:"shortUrl"`
}

// GetInClassJumpUrl 获取直播间链接
func GetShortUrl(ctx *gin.Context, longUrl string) (shortUrl string, err error) {
	params := map[string]interface{}{
		"longUrl": longUrl,
	}
	finalParam := getSignV2(params, URL_APP_KEY, URL_APP_SECRET)

	opt := base.HttpRequestOptions{
		RequestBody: finalParam,
	}

	res, err := conf.API.Moat.HttpPost(ctx, "/su/api/createshorturl", opt)
	if err != nil {
		zlog.Error(ctx, fmt.Sprintf("[GenShortURL] opt=%+v", opt), err.Error())
		return "", err
	}

	var r struct {
		ErrStr string `json:"errStr"`
		ErrNo  int    `json:"errNo"`
		Data   struct {
			ShortURL  interface{} `json:"shortUrl"`
			ShortCode string      `json:"shortCode"`
		} `json:"data"`
	}
	if e := jsoniter.Unmarshal(res.Response, &r); e != nil {
		zlog.Error(ctx, fmt.Sprintf("[GenShortURL] opt=%+v", opt), e.Error())
		return "", e
	}

	if v, ok := r.Data.ShortURL.(bool); ok && !v {
		zlog.Error(ctx, errors.New("generate short URL false"))
		return "", errors.New("generate short URL false")
	}

	shortURL, _ := r.Data.ShortURL.(string)
	return shortURL, nil
}
