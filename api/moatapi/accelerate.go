package moatapi

import (
	"assistantdeskgo/api/apis"
	"github.com/gin-gonic/gin"
)

const UriGetOrderByUserAndSku = "/accelerate/instant/sku"

type InstantSkuInfoReq struct {
}

type InstantSkuInfoRsp struct {
	UserId     int `json:"userId"`
	SkuId      int `json:"skuId"`
	OrderId    int `json:"orderId"`
	SellStatus int `json:"sellStatus"`
	Quantity   int `json:"quantity"`
}

func GetOrderByUserAndSku(ctx *gin.Context, uid, skuId int64) (instantSkuInfoRsp []InstantSkuInfoRsp, err error) {
	params := map[string]interface{}{
		"userId": uid,
		"skuIds": []int64{skuId},
	}
	finalParam := getSignV2(params, APP_KEY, APP_SECRET)
	req := InstantSkuInfoReq{}
	err = apis.Post(ctx, UriGetOrderByUserAndSku, &req, &instantSkuInfoRsp, apis.WithEncode(apis.EncoderForm), apis.WithRequest(finalParam))
	return
}
