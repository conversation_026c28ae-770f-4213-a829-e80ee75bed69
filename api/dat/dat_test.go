package dat

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func init() {
	env.SetRootPath("../../")
	gin.SetMode(gin.ReleaseMode)

	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()

}

func TestGetCourseTeachersByCourseId(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	teacherIds, err := GetCourseTeachersByCourseId(ctx, 537439)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(teacherIds)
}
