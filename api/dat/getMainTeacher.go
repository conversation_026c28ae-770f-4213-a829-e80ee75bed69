package dat

import (
	"assistantdeskgo/api/zbcore"
	"assistantdeskgo/utils"
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	CourseModule = "dat"
	CourseEntity = "courseTeacher"
	ApiMaxNum    = 50
)

type CourseInfo struct {
	teacherUids []int `json:"teacherUids" mapstructure:"teacherUids"`
}

func GetCourseTeachersByCourseId(ctx *gin.Context, courseId int64) ([]int, error) {
	if courseId == 0 {
		return nil, base.Error{ErrNo: 500, ErrMsg: "courseIds不能为空"}
	}

	tmpResult, err := courseTeachersSingle(ctx, []int64{courseId})
	if err != nil {
		return nil, err
	}

	teacherMap, exist := tmpResult[courseId]
	if !exist {
		return []int{}, nil
	}

	teacherIds, exist := teacherMap["teacherUids"]
	if !exist {
		return []int{}, nil
	}
	return teacherIds, nil
}

func GetCourseTeachersByCourseIds(ctx *gin.Context, courseIds []int64) (map[int64]map[string][]int, error) {
	if len(courseIds) == 0 {
		return nil, base.Error{ErrNo: 500, ErrMsg: "courseIds不能为空"}
	}

	if len(courseIds) > ApiMaxNum {
		// 批量并发获取
		return courseTeachersBatch(ctx, courseIds), nil
	} else {
		return courseTeachersSingle(ctx, courseIds)
	}
}

func courseTeachersBatch(ctx *gin.Context, courseIds []int64) map[int64]map[string][]int {
	chunks := utils.ChunkArrayInt64(courseIds, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int64]map[string][]int)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(cids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "courseTeachersBatch panic err : %+v", r)
				}
			}()
			defer wg.Done()
			ret, err := courseTeachersSingle(ctx, cids)
			if err != nil {
				zlog.Warnf(ctx, "courseTeachersSingle failed, courseIds:%+v, err:%s", cids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[int64]map[string][]int)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseTeachersBatch panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

func courseTeachersSingle(ctx *gin.Context, courseIds []int64) (map[int64]map[string][]int, error) {
	courseTeachersMap := make(map[int64]map[string][]int)
	err := getKVByCourseId(ctx, courseIds, &courseTeachersMap)
	if err != nil {
		return nil, err
	}

	return courseTeachersMap, nil
}

func getKVByCourseId(ctx *gin.Context, courseIds []int64, output interface{}) error {

	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, CourseEntity, "getKV", false, env.GetAppName())
	params := map[string]interface{}{
		"courseIds": courseIds,
		"fields":    []string{"teacherUids"},
	}

	_, err := zbcore.PostDat(ctx, params, arrHeader, output)
	if err != nil {
		zlog.Warnf(ctx, "getKVByCourseId failed, err:%s", err)
		return err
	}

	return nil
}
