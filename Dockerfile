FROM image-docker.zuoyebang.cc/privbase/go-builder:1.23-alpine as builder

ARG APP_NAME
ENV APP_NAME=$APP_NAME

WORKDIR $GOPATH/${APP_NAME}/

COPY go.mod $GOPATH/${APP_NAME}/
COPY go.sum $GOPATH/${APP_NAME}/
RUN go mod download
COPY . $GOPATH/${APP_NAME}/

COPY ./encoder /usr/local/bin/
RUN go build -o /usr/local/bin/assistantdeskgo main.go

FROM image-docker.zuoyebang.cc/base/go-runner:2.0

ARG APP_NAME
ENV APP_NAME $APP_NAME

WORKDIR /usr/local/bin/

COPY --from=builder /usr/local/bin/assistantdeskgo /usr/local/bin/
COPY --from=builder /usr/local/bin/encoder /usr/local/bin/
RUN chmod 777 /usr/local/bin/encoder

RUN apk --no-cache add ffmpeg

CMD ["/usr/local/bin/assistantdeskgo"]
