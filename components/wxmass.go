package components

import (
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
)

// 任务类型

const (
	TaskTypeInstance = 1
	TaskTypeTiming   = 2
)

// 任务场景类型

const (
	SceneIdMultiToMan    = 1
	SceneIdSingleToMan   = 2
	SceneIdMultiToGroup  = 3
	SceneIdSingleToGroup = 4
)

func GetTaskTypeOptions() map[int64]interface{} {
	return map[int64]interface{}{
		TaskTypeInstance: "实时任务",
		TaskTypeTiming:   "定时任务",
	}
}

func GetSceneTypeBySceneId(sceneId int64) map[int64]interface{} {
	switch sceneId {
	case SceneIdMultiToMan:
		return getSceneTypeToMMultiOptions()
	case SceneIdSingleToMan:
		return getSceneTypeToMSingleOptions()
	case SceneIdMultiToGroup:
		return getSceneTypeToGMultiOptions()
	case SceneIdSingleToGroup:
		return getSceneTypeToGSingleOptions()
	}
	return map[int64]interface{}{}
}

func GetSceneTypeDescBySceneTypeId(sceneType int64) interface{} {
	getWithSceneId := func(sceneId int64) interface{} {
		tmpSceneMap := GetSceneTypeBySceneId(sceneId)
		if tmpDesc, exist := tmpSceneMap[sceneType]; exist {
			return tmpDesc
		}
		return nil
	}

	for _, sceneId := range []int64{SceneIdMultiToMan, SceneIdSingleToMan, SceneIdMultiToGroup, SceneIdSingleToGroup} {
		if result := getWithSceneId(sceneId); result != nil {
			return result
		}
	}
	return nil
}

func getSceneTypeToMMultiOptions() map[int64]interface{} {
	return map[int64]interface{}{
		touchmis.SendTypePreattend:                   "一键预到课",
		touchmis.SceneTypeToMMultiAddGroup:           "快速拉群",
		touchmis.SceneTypeMultiToPersonSop:           "群发到人",
		touchmis.SendTypeFeedbackInClassGroup:        "群发课中反馈",
		touchmis.SendTypeAttend:                      "催到课",
		touchmis.SendTypeRemindClass:                 "跟课系统催到课",
		touchmis.SendTypeRemindPlayback:              "跟课系统催回放",
		touchmis.SendTypeBanxueAttend:                "伴学催到课",
		touchmis.SendTypeConfirmBeforeClass:          "开课前确认到课方式",
		touchmis.SendTypeLiveAttend:                  "直播催到课",
		touchmis.SendTypeBottomTestReport:            "群发摸底测报告（LPC）",
		touchmis.SendTypeBottomTestReportFd:          "群发摸底测报告（辅导）",
		touchmis.SendTypeClassReport:                 "群发课堂报告(lpc)",
		touchmis.SendTypeLearnReportFd:               "群发课堂报告（辅导）",
		touchmis.SendTypeStageResultFeedbackCard:     "阶段测结果反馈（辅导）",
		touchmis.SendTypePronunciationReport:         "群发纠音报告",
		touchmis.SendTypeBybTplCitation:              "个人奖状（表扬榜）",
		touchmis.SendTypeBybTplCertificate:           "证书（表扬榜）",
		touchmis.SendTypeLearnReportCambridgeEnglish: "课堂报告（剑桥英语）",
		touchmis.SendTypePronunciationReportGroup:    "群发个性化纠音报告(自定义分组)",
		touchmis.SendTypeVoiceReportGroup:            "群发配音小达人报告(自定义分组)",
	}
}

func getSceneTypeToMSingleOptions() map[int64]interface{} {
	return map[int64]interface{}{
		touchmis.SceneTypeToMSingleSopGroup:    "SOP消息组",
		touchmis.SendTypeFeedbackInClassPerson: "单人课中反馈",
	}
}

func getSceneTypeToGMultiOptions() map[int64]interface{} {
	return map[int64]interface{}{
		touchmis.SceneTypeMultiToGroupSop: "SOP消息组",
	}
}

func getSceneTypeToGSingleOptions() map[int64]interface{} {
	return map[int64]interface{}{
		touchmis.SceneTypeSingleToGroupSop: "SOP消息组",
	}
}
