package components

const (
	DefaultPageNum = 1
	DefaultSizeNum = 10
	DefaultStatus  = -1

	MaxGoroutineNum = 10

	LaxinTagFixTemplateColumn = 6

	CreateBatchSize = 100
	GetBatchSize    = 500

	ValidateErrCourseId                  = "课程 id 不合法"
	ValidateErrFirstOrSecondLine         = "一级/二级团队枚举不合法"
	ValidateErrAssistantOrPersonNotExist = "资产/真人不存在"
	ValidateErrAssistantPersonNoMatch    = "真人-资产关联关系不一致"
	ValidateErrDuplicateData             = "与文件中数据重复"
	ValidateErrDataIsExist               = "当前有效的数据已存在，需要先删除后再提交"
	ValidateErrDataNotExist              = "当前删除的数据不存在"

	ResultFileNameCreate = "laxinTagFixDataImportCreateResult"
	ResultFileNameDelete = "laxinTagFixDataImportDeleteResult"

	Emailsuffix = "@zuoyebang.com"

	MessageTypeWord            = 0  // 文字
	MessageTypePicture         = 1  // 图片
	MessageTypeVoice           = 2  // 语音
	MessageTypeVideo           = 3  // 视频
	MessageTypeCardLink        = 5  // 卡片链接
	MessageTypeFile            = 8  // 文件
	MessageTypeVideoMaterial   = 20 // 视频号
	MessageTypeCreatePicture   = 31 // 生成图片并发送
	MessageTypePictureTemplate = 32 // 发送图片模板

	BuyTypeLesson  = 0
	BuyTypeCourse  = 1
	BuyTypeCpu     = 2
	BuyTypeOutline = 3

	ExamTypePracticeInClass  = 1  //课中练习  别名：互动题
	ExamTypePreview          = 5  //课前预习 小学预习 小学课前预习 (18年二月时废弃,18年12月开始在小学横屏版本重新启用)   as 预习
	ExamTypePosttestMore     = 13 //初高中预习测试      as  预习
	ExamTypePracticeStrength = 7  //巩固练习
	ExamTypePreTest          = 3  //报前测试
	ExamTypePostTest         = 4  //报后测试
	ExamTypeStage            = 9  //阶段性测试
	ExamTypeSurvey           = 20 //摸底测
	ExamTypeTestInClass      = 10 //堂堂测
	ExamTypePracticeSimilar  = 33 //巩固练习相似题

	TaskTypeSendMsg       = 1 // 发送消息
	TaskTypeCreatePicture = 2 // 创建图片并发送消息

	TouchTypeForPublicSea  = "publicSea"
	TouchTypeForPrivateSea = "privateSea"
)

// 时间常量
const (
	FiveMin = 5 * 60
	Minute  = 60 * 1
	Hour    = Minute * 60
)

func GetLaxinTagFixTemplateHead() []string {
	return []string{"课程ID", "资产手机号", "真人邮箱前缀", "一级服务团队", "二级服务团队", "处理结果-此处无需填写"}
}
