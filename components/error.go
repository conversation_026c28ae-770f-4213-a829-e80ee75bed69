package components

import "git.zuoyebang.cc/pkg/golib/v2/base"

var ErrorUserNotLogin = base.Error{
	ErrNo:  3,
	ErrMsg: "用户未登录",
}

// 5000000-5999999 内部逻辑错误
var ErrorSystemError = base.Error{
	ErrNo:  5000,
	ErrMsg: "system internal error",
}

var ErrorParamInvalid = base.Error{
	ErrNo:  4000,
	ErrMsg: "参数错误",
}

var ErrorParamInvalidFormat = base.Error{
	ErrNo:  4001,
	ErrMsg: "参数错误, msg: %s",
}

var ErrorDbInsert = base.Error{
	ErrNo:  3101,
	ErrMsg: "db insert error: %s",
}
var ErrorDbUpdate = base.Error{
	ErrNo:  3102,
	ErrMsg: "db update error: %s",
}
var ErrorDbSelect = base.Error{
	ErrNo:  3103,
	ErrMsg: "db get error: %s",
}

var ErrorCosUpload = base.Error{
	ErrNo:  3600,
	ErrMsg: "cos upload error: %s",
}
var ErrorCosDownload = base.Error{
	ErrNo:  3602,
	ErrMsg: "cos download error: %s",
}
var ErrorCosGetData = base.Error{
	ErrNo:  3603,
	ErrMsg: "cos getMetaData error: %s",
}

var ErrorRedisGet = base.Error{
	ErrNo:  3201,
	ErrMsg: "redis get error: %s",
}
var ErrorRedisSet = base.Error{
	ErrNo:  3202,
	ErrMsg: "redis set error: %s",
}

var ErrorEsPing = base.Error{
	ErrNo:  3501,
	ErrMsg: "es ping error",
}
var ErrorEsGetVersion = base.Error{
	ErrNo:  3502,
	ErrMsg: "es getVersion error",
}
var ErrorEsInsert = base.Error{
	ErrNo:  3503,
	ErrMsg: "es insert error",
}
var ErrorEsQuery = base.Error{
	ErrNo:  3504,
	ErrMsg: "es query error",
}
var ErrorEsUpdate = base.Error{
	ErrNo:  3505,
	ErrMsg: "es update error",
}
var ErrorEsDel = base.Error{
	ErrNo:  3506,
	ErrMsg: "es del error",
}

var ErrorAPIGetUserInfoV1 = base.Error{
	ErrNo:  3000,
	ErrMsg: "call GetUserInfoV2 error: %s",
}
var ErrorAPIGetUserInfoV2 = base.Error{
	ErrNo:  3001,
	ErrMsg: "call GetUserInfoV2 error: %s",
}
var ErrorAPIGetUserCourseV1 = base.Error{
	ErrNo:  3002,
	ErrMsg: "call getUserCourse error: %s",
}
var ErrorAPIGetUserCourseV2 = base.Error{
	ErrNo:  3003,
	ErrMsg: "call getUserCourse error: %s",
}
var ErrorAPIGetCourseInfo = base.Error{
	ErrNo:  3004,
	ErrMsg: "call GetCourseInfo error: %s",
}

var ErrorExcelRowParamCountError = base.Error{
	ErrNo:  3739,
	ErrMsg: "excel文件当前行列数不合法",
}

var ErrorExcelColError = base.Error{
	ErrNo:  3749,
	ErrMsg: "excel文件当前行第%d列错误:%s",
}

var ErrorLongLinkSendFail = base.Error{
	ErrNo:  3759,
	ErrMsg: "长连接消息发送失败",
}

var ErrorDecode = base.Error{
	ErrNo:  11007,
	ErrMsg: "decode fail",
}

var ErrorUrlInvalid = base.Error{
	ErrNo:  11008,
	ErrMsg: "url invalid",
}

var ErrorAssistantUidInvalid = base.Error{
	ErrNo:  11009,
	ErrMsg: "资产id错误",
}

var ErrorGray = base.Error{
	ErrNo:  11009,
	ErrMsg: "不在灰度名单中",
}

var ErrorLeadsCheck = base.Error{
	ErrNo:  12001,
	ErrMsg: "例子检查失败",
}

var ErrorSmsConfig = base.Error{
	ErrNo:  12002,
	ErrMsg: "无可用的短信模版",
}

var ErrorHasBeenLocked = base.Error{
	ErrNo:  12003,
	ErrMsg: "%s",
}

var ErrorUseExceed = base.Error{
	ErrNo:  12003,
	ErrMsg: "使用次数超过限制",
}

var ErrorUseFailed = base.Error{
	ErrNo:  12004,
	ErrMsg: "当前无法使用，请刷新后重试",
}

var ErrorGetStudentInfo = base.Error{
	ErrNo:  12005,
	ErrMsg: "学生信息获取失败",
}

var ErrorCanNotRetrieve = base.Error{
	ErrNo:  12006,
	ErrMsg: "不满足回捞条件",
}

var ErrorInvalidPhone = base.Error{
	ErrNo:  12007,
	ErrMsg: "手机号获取失败",
}

func DefaultError(msg string) error {
	return base.Error{
		ErrNo:  999999,
		ErrMsg: msg,
	}
}
