# Trae 项目规则 - assistantdeskgo

本文档旨在为 `assistantdeskgo` 项目提供一套统一的开发规则和最佳实践，以确保代码质量、可维护性和团队协作效率。

## 1. 项目概览

- **主要语言**: Go
- **主要Web框架**: Gin (`github.com/gin-gonic/gin`)
- **项目结构特点**:
    - `api/`: 存放对外接口定义、请求响应结构体以及与外部服务交互的客户端代码。
    - `components/`: 存放项目通用组件。
    - `conf/`: 存放项目配置相关代码。
    - `controllers/`: 存放HTTP请求处理器 (Handlers)。
    - `data/`: 存放数据持久化和访问相关代码 (如数据库、缓存操作)。
    - `defines/`: 存放项目范围内的常量、枚举等定义。
    - `dto/`: (Data Transfer Objects) 存放用于在不同层之间传输数据的结构体。
    - `helpers/`: 存放辅助函数和工具类。
    - `libs/`: 存放项目内部共享库或对第三方库的封装。
    - `middleware/`: 存放 Gin 中间件。
    - `models/`: 存放数据库表对应的GORM模型或其他数据模型。
    - `router/`: 存放路由配置。
    - `service/`: 存放核心业务逻辑。
    - `utils/`: 存放通用工具函数。
    - `go.mod`, `go.sum`: Go模块管理文件。
    - `main.go`: 项目入口文件。

## 2. Go语言编码规范

遵循Go社区的通用编码规范，包括但不限于：

- **命名规范**:
    - 包名：小写，简洁，不使用下划线或混合大小写。
    - 变量、函数、类型、常量：遵循驼峰命名法 (CamelCase)。公开的（Exported）标识符首字母大写，私有的（unexported）标识符首字母小写。
    - 接口名：通常以 `er` 结尾 (例如 `Reader`, `Writer`)，或者如果接口只包含一个方法，则方法名加上 `er` (例如 `Stringer`)。
- **代码格式**:
    - 使用 `gofmt` 或 `goimports` 自动格式化代码。
    - 缩进：使用制表符 (tab)。
- **注释**:
    - 公开的函数、类型、常量和变量都应该有文档注释。
    - 注释应该清晰、简洁，解释“为什么”而不是“做什么”。
- **错误处理**:
    - 函数返回错误时，错误信息应该是具体的，便于调试。
    - 优先使用 `errors.New` 或 `fmt.Errorf` 创建错误。
    - 错误信息应以小写字母开头，不以标点符号结尾。
- **包管理**:
    - 使用 Go Modules进行依赖管理。
    - 及时更新 `go.mod` 和 `go.sum`。

## 3. Gin框架使用规范与最佳实践

基于 `context7` 查询到的 Gin 文档，推荐以下实践：

- **路由 (Routing)**:
    - 使用路由分组 (`router.Group()`) 来组织相关的路由，提高可读性和可维护性。例如，可以按模块或API版本进行分组。
    - 路由路径使用小写字母和连字符 (`-`)（kebab-case）或下划线 (`_`)。
- **请求处理 (Request Handling)**:
    - **数据绑定 (Binding)**: 
        - 使用 `c.ShouldBindJSON()`, `c.ShouldBindXML()`, `c.ShouldBindQuery()`, `c.ShouldBindUri()`, `c.ShouldBind()` 等方法将请求数据绑定到结构体。
        - 在结构体标签中使用 `binding:"required"` 等进行数据校验。
        - 对于URI参数绑定，可以使用 `c.ShouldBindUri(&person)`。
        - 明确区分查询参数绑定 (`ShouldBindQuery`) 和表单/JSON/XML数据绑定 (`ShouldBind`, `ShouldBindJSON`, `ShouldBindXML`)。
    - **获取参数**: 
        - 使用 `c.Query()` 获取查询参数，`c.DefaultQuery()` 获取带默认值的查询参数。
        - 使用 `c.PostForm()` 获取表单参数，`c.DefaultPostForm()` 获取带默认值的表单参数。
    - **文件上传**: 
        - 使用 `c.FormFile()` 处理单个文件上传，`c.MultipartForm()` 处理多个文件上传。
        - 使用 `c.SaveUploadedFile()` 保存文件。
        - 注意设置 `router.MaxMultipartMemory` 来限制上传文件占用的内存。
- **响应处理 (Response Handling)**:
    - 使用 `c.JSON()`, `c.XML()`, `c.YAML()`, `c.String()`, `c.Data()` 等方法发送响应。
    - 使用 `c.SecureJSON()` 防止JSON劫持，它会在响应前添加安全前缀。
    - 使用 `c.PureJSON()` 发送字面JSON，不进行HTML字符编码。
    - 统一错误响应格式，例如：`c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})`。
- **中间件 (Middleware)**:
    - 合理使用中间件处理通用逻辑，如日志、认证、恢复 (Recovery)、CORS等。
    - Gin默认提供了 `gin.Logger()` 和 `gin.Recovery()` 中间件。
    - 可以创建自定义中间件，并使用 `router.Use()` (全局)、`group.Use()` (分组) 或在单个路由上应用。
    - 自定义恢复中间件：`gin.CustomRecovery()` 可以捕获panic并自定义处理逻辑。
- **日志 (Logging)**:
    - Gin的默认日志中间件 (`gin.Logger()`) 提供了基本的请求日志。
    - 可以通过 `gin.LoggerWithConfig()` 或 `gin.LoggerWithFormatter()` 自定义日志格式和行为。
    - 可以配置跳过某些路径的日志记录 (`loggerConfig.SkipPaths`) 或基于自定义逻辑跳过 (`loggerConfig.Skip`)。
    - 可以禁用控制台日志颜色：`gin.DisableConsoleColor()`。
- **错误处理与Panic恢复**:
    - `gin.Recovery()` 中间件可以捕获panic并返回500错误，防止服务崩溃。
    - 在业务逻辑中，应妥善处理错误并返回合适的HTTP状态码和错误信息。
- **配置与启动**:
    - 可以创建不带默认中间件的Gin实例：`r := gin.New()`。
    - 支持优雅停机 (Graceful Shutdown)，使用 `http.Server.Shutdown()` 结合 `os.Signal` 来处理。
    - 支持运行多个Gin服务，例如使用 `golang.org/x/sync/errgroup`。
    - 支持HTTPS，包括使用Let's Encrypt (`autotls` 包)。
- **安全性**:
    - 配置信任的代理服务器 (`router.SetTrustedProxies()`) 以正确获取客户端IP。
    - 使用 `c.SecureJSON()`。
- **模板渲染**:
    - 支持HTML模板渲染，可以使用 `router.LoadHTMLGlob()` 或 `router.LoadHTMLFiles()` 加载模板。
    - 支持使用Go的 `embed` 包将模板和静态资源嵌入到单个二进制文件中。

## 4. 项目特定规则

- **目录结构**: 遵循当前项目已建立的目录结构。新功能或模块应在相应的目录下创建。
    - 例如，新的API接口相关代码应放在 `api/` 下的子目录，业务逻辑放在 `service/` 下，数据模型放在 `models/` 下。
- **DTO使用**: 
    - 在 `service` 层与 `controller` 层之间，以及 `service` 层与 `api` (外部调用) 层之间，推荐使用DTO进行数据传输，以实现层间解耦。
    - DTO应定义在 `dto/` 目录下，并按模块组织。
- **错误处理**: 
    - `service` 层应向上层 (`controller`) 返回明确的错误类型或错误码，便于上层统一处理和响应。
    - 避免在 `service` 层直接返回 `gin.H` 或HTTP状态码。
- **日志**: 
    - 使用项目统一的日志库 (例如 `git.zuoyebang.cc/pkg/golib/v2/zlog`)。
    - 在关键操作、错误发生时记录详细日志，包括上下文信息 (如请求参数、用户ID等)。
- **配置管理**: 
    - 配置文件应统一管理，例如在 `conf/` 目录下。
    - 避免硬编码配置项。
- **数据库操作**: 
    - 如果使用GORM，模型定义在 `models/` 目录。
    - 数据库操作逻辑应封装在 `data/` 层或相关的 `service` 中，避免在 `controller` 直接操作数据库。
- **第三方库使用**: 
    - 优先使用 `go.mod` 中已有的依赖。
    - 如需引入新的第三方库，需进行评估并与团队沟通。

## 5. Git工作流与版本控制

- **分支策略**: (待团队讨论确定，例如 Gitflow, GitHub Flow)
- **提交信息**: 
    - 遵循 Conventional Commits 规范 (例如 `feat: add new feature`, `fix: resolve a bug`, `docs: update documentation`)。
    - 提交信息应清晰、简洁，描述本次提交的内容。
- **代码审查 (Code Review)**: 
    - 所有代码变更都应经过至少一位其他团队成员的审查。
    - 审查重点包括代码质量、逻辑正确性、是否符合规范、测试覆盖等。

## 6. 测试

- **单元测试**: 
    - `service` 层和 `utils` 等核心逻辑模块应编写单元测试。
    - 测试用例应覆盖正常场景和边界条件。
- **集成测试**: 
    - 对涉及多个组件或外部依赖的场景编写集成测试。
- **测试覆盖率**: (待团队讨论确定目标)

## 7. 文档

- **API文档**: (待团队讨论确定工具和规范，例如 Swagger/OpenAPI)
- **代码注释**: 如上文Go语言编码规范所述。
- **项目文档**: 本规则文件 (`project_rules.md`) 应保持更新。

---

本文档将根据项目发展和团队反馈持续更新。