# Created by .ignore support plugin (hsz.mobi)
### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
### Example user template template
### Example user template

# IntelliJ project files
.idea
out
gen

# VsCode project files
.vscode/*

.DS_Store

*.log
*.zip

log/*
*.swp
*.swo

go.sock

gx-manage
/conf/secret

go_build_main_go

*.csv