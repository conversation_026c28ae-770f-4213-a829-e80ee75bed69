package mqc

//import (
//	"fmt"
//	"testing"
//
//	"git.zuoyebang.cc/pkg/golib/v2/rmq"
//	"github.com/gin-gonic/gin"
//	"github.com/stretchr/testify/assert"
//	"golang.org/x/sync/errgroup"
//)
//
//type message struct {
//	tag string
//}
//
//func (m *message) WithTag(s string) rmq.Message {
//	m.tag = s
//	return m
//}
//
//func (m *message) WithShard(s string) rmq.Message {
//	return m
//}
//
//func (m *message) WithDelay(level rmq.DelayLevel) rmq.Message {
//	return m
//}
//
//func (m *message) Send() (msgID string, err error) {
//	panic("implement me")
//}
//
//func (m *message) GetContent() []byte {
//	return []byte("{}")
//}
//
//func (m *message) GetTag() string {
//	return m.tag
//}
//
//func (m *message) GetShard() string {
//	return ""
//}
//
//func (m *message) GetID() string {
//	return "123123"
//}
//
//var _ rmq.Message = (*message)(nil)
//
//func TestMQC_RegisterConsumer(t *testing.T) {
//	mqc := New()
//	g := new(errgroup.Group)
//	for i := 0; i < 4; i++ {
//		i := i
//		g.Go(func() error {
//			mqc.RegisterConsumer("10001", func(ctx *gin.Context, msg rmq.Message) error {
//				fmt.Println(i, string(msg.GetContent()))
//				if i == 2 {
//					return fmt.Errorf("error: %d", i)
//				}
//				return nil
//			})
//			return nil
//		})
//	}
//	_ = g.Wait()
//
//	msg := &message{}
//	msg.WithTag("10001")
//
//	err := mqc.ServeMQ(new(gin.Context), msg)
//	t.Log(err)
//}
//
//func TestMQC_Panic(t *testing.T) {
//	mqc := New()
//	mqc.RegisterConsumer("10001", func(ctx *gin.Context, msg rmq.Message) error {
//		_ = msg.GetContent()[12121]
//		return nil
//	})
//	msg := &message{}
//	msg.WithTag("10001")
//
//	err := mqc.ServeMQ(new(gin.Context), msg)
//	assert.Nil(t, err)
//}
//
//func BenchmarkMQC_ServeMQ(b *testing.B) {
//	b.ReportAllocs()
//	mqc := New()
//	mqc.RegisterConsumer("10001", func(ctx *gin.Context, msg rmq.Message) error {
//		return nil
//	})
//	mqc.RegisterConsumer("10001", func(ctx *gin.Context, msg rmq.Message) error {
//		return nil
//	})
//
//	msg := &message{}
//	msg.WithTag("10001")
//	for i := 0; i < b.N; i++ {
//		_ = mqc.ServeMQ(new(gin.Context), msg)
//	}
//}
