package mqc

import (
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"runtime"
	"sync"
)

// ConsumeFunc 注册的实际处理消息回调方法
type ConsumeFunc func(ctx *gin.Context, msg rmq.Message) error

func New() *MQC {
	return &MQC{
		consumers: make(map[string][]ConsumeFunc),
	}
}

type MQC struct {
	consumers map[string][]ConsumeFunc // consumers registered with tag
	mu        sync.RWMutex             // protected consumers
}

// RegisterConsumer 注册对应命令号的消费处理方法
func (m *MQC) RegisterConsumer(tag string, fn ConsumeFunc) {
	m.mu.Lock()
	m.consumers[tag] = append(m.consumers[tag], fn)
	m.mu.Unlock()
}

// ServeMQ 执行消息消费
func (m *MQC) ServeMQ(ctx *gin.Context, msg rmq.Message) error {
	fields := []zap.Field{
		zap.String("tag", msg.GetTag()),
		zap.ByteString("content", msg.GetContent()),
		zap.String("msgId", msg.GetID()),
	}
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 1<<16)
			buf = buf[:runtime.Stack(buf, false)]
			zlog.Errorf(ctx, "panic recovered: %s\n", r, buf)
		}
		zlog.InfoLogger(ctx, "ServeMQ", fields...)
	}()

	tag := msg.GetTag()

	m.mu.RLock()
	consumers, found := m.consumers[tag]
	m.mu.RUnlock()
	if !found {
		return nil
	}
	g := new(errgroup.Group)
	for idx := range consumers {
		idx := idx
		g.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					buf := make([]byte, 1<<16)
					buf = buf[:runtime.Stack(buf, false)]
					zlog.Errorf(ctx, "panic recovered: %s\n", r, buf)
				}
			}()
			return consumers[idx](ctx, msg)
		})
	}

	if err := g.Wait(); err != nil {
		fields = append(fields, zap.Error(err))
		return err
	}
	return nil
}
