package codec

import (
	"fmt"
	"go/format"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Result struct {
	ColumnName    string
	ColumnType    string
	DataType      string
	ColumnKey     string
	ColumnComment string
}

func GenerateModel(ctx *gin.Context, db *gorm.DB, table string) error {
	zlog.Info(ctx, "Generate model for table ", table)
	var cols []Result
	sql := "select column_name, column_type, data_type, column_key, column_comment from information_schema.columns where table_schema = 'mark' and table_name = ? order by ordinal_position"
	if err := db.Raw(sql, table).Scan(&cols).Error; err != nil {
		return err
	}

	fmt.Println(genSource(table, cols))
	return nil
}

func genSource(table string, cols []Result) string {
	var s strings.Builder

	typeName := strings.TrimPrefix(UpperCamelCase(table), "Tbl")

	_, _ = fmt.Fprint(&s, "package models\n\n")
	_, _ = fmt.Fprintf(&s, "type %s stru {\n", typeName)
	for _, col := range cols {
		fieldName := UpperCamelCase(col.ColumnName)
		if strings.HasSuffix(fieldName, "Id") {
			fieldName = fieldName[:len(fieldName)-2] + "ID"
		}
		jsonName := LowerCamelCase(col.ColumnName)

		unsigned := strings.Contains(col.ColumnType, "unsigned")
		var typeName string
		switch col.DataType {
		case "int", "tinyint", "smallint":
			typeName = "int"
			if strings.HasSuffix(col.ColumnName, "_time") {
				typeName = "UnixTime"
			}
		case "bigint":
			if unsigned {
				if strings.HasSuffix(col.ColumnName, "id") {
					typeName = "ID"
				} else {
					typeName = "uint64"
				}
			} else {
				typeName = "int64"
			}
		case "varchar", "text", "mediumtext":
			typeName = "string"
		case "timestamp":
			typeName = "UnixTime"
		default:
			panic("Unknown column type " + col.DataType)
		}

		gormTag := col.ColumnName
		if col.ColumnKey == "PRI" {
			gormTag = "primaryKey;column:" + col.ColumnName
		} else if col.ColumnName == "create_time" {
			gormTag = col.ColumnName + ";autoCreateTime;<-:create"
		} else if col.ColumnName == "update_time" {
			gormTag = col.ColumnName + ";autoUpdateTime"
		}

		_, _ = fmt.Fprintf(&s, "    %s %s `gorm:\"column:%s\" json:\"%s\"` // %s\n", fieldName, typeName, gormTag, jsonName, col.ColumnComment)
	}

	_, _ = fmt.Fprintf(&s, `}

func (t %s) TableName() string {
    return %s 
}`, typeName, UpperCamelCase(table))

	formatted, err := format.Source([]byte(s.String()))
	if err != nil {
		return s.String()
	} else {
		return string(formatted)
	}
}

// taken from https://github.com/stoewer/go-strcase/
// isLower checks if a character is lower case. More precisely it evaluates if it is
// in the range of ASCII character 'a' to 'z'.
func isLower(ch rune) bool {
	return ch >= 'a' && ch <= 'z'
}

// toLower converts a character in the range of ASCII characters 'A' to 'Z' to its lower
// case counterpart. Other characters remain the same.
func toLower(ch rune) rune {
	if ch >= 'A' && ch <= 'Z' {
		return ch + 32
	}
	return ch
}

// isLower checks if a character is upper case. More precisely it evaluates if it is
// in the range of ASCII characters 'A' to 'Z'.
func isUpper(ch rune) bool {
	return ch >= 'A' && ch <= 'Z'
}

// toLower converts a character in the range of ASCII characters 'a' to 'z' to its lower
// case counterpart. Other characters remain the same.
func toUpper(ch rune) rune {
	if ch >= 'a' && ch <= 'z' {
		return ch - 32
	}
	return ch
}

// isSpace checks if a character is some kind of whitespace.
func isSpace(ch rune) bool {
	return ch == ' ' || ch == '\t' || ch == '\n' || ch == '\r'
}

// isDelimiter checks if a character is some kind of whitespace or '_' or '-'.
func isDelimiter(ch rune) bool {
	return ch == '-' || ch == '_' || isSpace(ch)
}

// iterFunc is a callback that is called fro a specific position in a string. Its arguments are the
// rune at the respective string position as well as the previous and the next rune. If curr is at the
// first position of the string prev is zero. If curr is at the end of the string next is zero.
type iterFunc func(prev, curr, next rune)

// stringIter iterates over a string, invoking the callback for every single rune in the string.
func stringIter(s string, callback iterFunc) {
	var prev rune
	var curr rune
	for _, next := range s {
		if curr == 0 {
			prev = curr
			curr = next
			continue
		}

		callback(prev, curr, next)

		prev = curr
		curr = next
	}

	if len(s) > 0 {
		callback(prev, curr, 0)
	}
}

// UpperCamelCase converts a string into camel case starting with a upper case letter.
func UpperCamelCase(s string) string {
	return camelCase(s, true)
}

// LowerCamelCase converts a string into camel case starting with a lower case letter.
func LowerCamelCase(s string) string {
	return camelCase(s, false)
}

func camelCase(s string, upper bool) string {
	s = strings.TrimSpace(s)
	buffer := make([]rune, 0, len(s))

	stringIter(s, func(prev, curr, next rune) {
		if !isDelimiter(curr) {
			if isDelimiter(prev) || (upper && prev == 0) {
				buffer = append(buffer, toUpper(curr))
			} else if isLower(prev) {
				buffer = append(buffer, curr)
			} else {
				buffer = append(buffer, toLower(curr))
			}
		}
	})

	return string(buffer)
}
