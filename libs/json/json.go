//go:build !jsoniter
// +build !jsoniter

package json

import (
	"encoding/json"

	"git.zuoyebang.cc/pkg/golib/v2/utils"
)

var (
	// Marshal is exported by json package.
	Marshal = json.Marshal
	// Unmarshal is exported by json package.
	Unmarshal = json.Unmarshal
	// MarshalIndent is exported by json package.
	MarshalIndent = json.MarshalIndent
	// NewDecoder is exported by json package.
	NewDecoder = json.NewDecoder
	// NewEncoder is exported by json package.
	NewEncoder = json.NewEncoder
	// Valid is exported by json package
	Valid = json.Valid
)

// MarshalToString is exported by jsonx package.
func MarshalToString(v interface{}) (string, error) {
	b, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return utils.BytesToString(b), nil
}

// UnmarshalFromString is exported by jsonx package.
func UnmarshalFromString(s string, v interface{}) error {
	b := utils.StringToBytes(s)
	return json.Unmarshal(b, &v)
}
