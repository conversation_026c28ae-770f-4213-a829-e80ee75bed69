package utils

import (
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CatchPanic recover from a panic.
// panic发生时, 支持传入回调方法来处理资源回收等操作
func CatchPanic(c *gin.Context, cleanups ...func()) {
	// panic捕获
	if r := recover(); r != nil {
		fields := []zlog.Field{
			zlog.String("logId", zlog.GetLogID(c)),
			zlog.String("requestId", zlog.GetRequestID(c)),
			zlog.String("module", env.GetAppName()),
			zap.Stack("stack"),
		}
		//MQ回调处理场景，context.Request == nil
		if c.Request != nil {
			// 请求url
			path := c.Request.URL.Path
			raw := c.Request.URL.RawQuery
			if raw != "" {
				path = path + "?" + raw
			}
			fields = append(fields,
				zlog.String("url", path),
				zlog.String("refer", c.Request.Referer()),
				zlog.String("host", c.Request.Host),
				zlog.String("ua", c.Request.UserAgent()),
				zlog.String("clientIp", utils.GetClientIp(c)),
			)
		}
		zlog.ErrorLogger(c, fmt.Sprintf("Panic[recover](%v)", r), fields...)

		for _, fn := range cleanups {
			fn()
		}
	}
}
