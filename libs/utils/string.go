package utils

import (
	"bytes"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"github.com/pkg/errors"
	"strings"
)

// 高效拼接字符串
func MergeString(args ...string) string {
	var build strings.Builder

	//把入参的所有字符串拼接
	for i := 0; i < len(args); i++ {
		build.WriteString(args[i])
	}

	return build.String()
}

// JsonObjectToString json对象转换成字符串
func JsonObjectToString(input interface{}, enableEscapeHTML bool) (string, error) {
	output := ""
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(enableEscapeHTML) //是否转义html中特殊字符
	if err := jsonEncoder.Encode(input); err != nil {
		return output, errors.Wrap(err, "json encode fail")
	}
	output = utils.BytesToString(bf.Bytes())
	//如上方法后，jsonEncoder.Encode后会多出一个回车，手动移出
	output = strings.TrimRight(output, "\n")
	return output, nil
}
