package utils

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"io"
	"net/http"
	"os"
	"time"

	"assistantdeskgo/libs/consts"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// ProxyCallBackendFunc 实际调用后端服务逻辑，params为当前请求参数
type ProxyCallBackendFunc func(ctx *gin.Context, params map[string]interface{}) (*base.ApiResult, error)

// ProxyServiceCallHandler 用户代理转发UI层请求到后端服务层.
func ProxyServiceCallHandler(backendFunc ProxyCallBackendFunc) func(ctx *gin.Context) {
	return func(ctx *gin.Context) {
		if ctx.Request.Method != http.MethodPost && ctx.Request.Method != http.MethodGet {
			base.RenderJsonFail(ctx, errors.New("only GET or POST method are supported"))
			return
		}
		// 获取所有请求参数
		var params map[string]interface{}
		if ctx.Request.Method == http.MethodGet {
			values := ctx.Request.URL.Query()
			params = make(map[string]interface{}, len(values))
			for k := range values {
				params[k] = values.Get(k)
			}
		} else if ctx.Request.Method == http.MethodPost {
			if ctx.ContentType() == binding.MIMEJSON {
				//解析JSON请求
				if ctx.Request.Body == nil {
					base.RenderJsonFail(ctx, errors.New("json body is empty"))
					return
				}
				decoder := json.NewDecoder(ctx.Request.Body)
				//if enabledecoderusenumber {
				//	decoder.usenumber()
				//}
				//if enabledecoderdisallowunknownfields {
				//	decoder.disallowunknownfields()
				//}
				if err := decoder.Decode(&params); err != nil {
					base.RenderJsonFail(ctx, err)
					return
				}
			} else if err := ctx.Request.ParseForm(); err != nil {
				base.RenderJsonFail(ctx, err)
				return
			} else {
				values := ctx.Request.Form
				params = make(map[string]interface{}, len(values))
				for k := range values {
					params[k] = values.Get(k)
				}
			}
		} else {
			base.RenderJsonFail(ctx, errors.New("only GET or POST method are supported"))
			return
		}

		start := time.Now()
		result, err := backendFunc(ctx, params)
		zlog.AddNotice(ctx, "proxyApiCost", fmt.Sprintf("%vms", time.Since(start).Milliseconds()))

		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}
		if result.HttpCode != http.StatusOK {
			zlog.AddNotice(ctx, "backendHttpCode", result.HttpCode)
			base.RenderJsonFail(ctx, errors.New(utils.BytesToString(result.Response)))
			return
		}
		ctx.Data(http.StatusOK, consts.HttpJsonContentType, result.Response)
	}
}

// DecorateHttpOptions decorate the http request options
// 1. 串联tips环境访问标识
func DecorateHttpOptions(ctx *gin.Context, options *base.HttpRequestOptions) {
	if ctx.Request == nil {
		return
	}
	passthroughsCookies := []string{
		"__tips__",  //tips环境标识
		"ZYBIPSCAS", //ips cookie
	}
	for _, cn := range passthroughsCookies {
		if v, err := ctx.Cookie(cn); err == nil {
			if options.Cookies == nil {
				options.Cookies = make(map[string]string)
			}
			if _, found := options.Cookies[cn]; found {
				continue
			}
			options.Cookies[cn] = v
		}
	}
}

// GetEnvName 获取ship环境容器名 SHIP_ENV_NAME
func GetEnvName(ctx *gin.Context) (name string) {
	switch env.GetRunEnv() {
	case env.RunEnvTest:
		name = os.Getenv("SHIP_ENV_NAME")
	case env.RunEnvTips:
		name = "tips"
	case env.RunEnvOnline:
		name = "online"
	default:
		name = "unknown"
	}
	zlog.Infof(ctx, "envName: %s shipEnvName: %s runEnv: %v", name, os.Getenv("SHIP_ENV_NAME"), env.GetRunEnv())
	return name
}

// NewReaderFromRemoteFile Create io.Reader from the remote url
func NewReaderFromRemoteFile(ctx *gin.Context, url string) (io.Reader, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	var buf bytes.Buffer
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return nil, err
	}
	return bufio.NewReader(&buf), nil
}

// NewReaderFromRemoteFile Create io.Reader from the remote url
func NewReadSeekerFromRemoteFile(ctx *gin.Context, url string) (*bytes.Reader, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	var buf bytes.Buffer
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return nil, err
	}

	return bytes.NewReader(buf.Bytes()), nil
}

type baseResp struct {
	Code    int                 `json:"errCode"`
	Message string              `json:"errMsg"`
	Data    jsoniter.RawMessage `json:"data"`
}

func HttpRequest(ctx *gin.Context, c *base.ApiClient, method string, path string, opt base.HttpRequestOptions, result interface{}) error {
	if c == nil {
		return errors.New("invalid api client")
	}
	var (
		resp *base.ApiResult
		err  error
	)

	switch method {
	case http.MethodPost:
		resp, err = c.HttpPost(ctx, path, opt)
	case http.MethodGet:
		resp, err = c.HttpGet(ctx, path, opt)
	default:
		return errors.New("unsupported method")
	}
	if err != nil {
		return err
	}
	if resp.HttpCode != http.StatusOK {
		return fmt.Errorf("http failed, code: %d", resp.HttpCode)
	}
	var r baseResp
	if err = jsoniter.Unmarshal(resp.Response, &r); err != nil {
		return err
	}
	if r.Code != 0 || r.Message != "succ" {
		return errors.New(r.Message)
	}
	if result == nil {
		return err
	}

	return jsoniter.Unmarshal(r.Data, result)

}
