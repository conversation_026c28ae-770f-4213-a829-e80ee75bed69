package data

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

// 本地cache使用示例
func Set2Cache(ctx *gin.Context) {
	helpers.Cache1.Set("test", "testValue", time.Second*2)
	helpers.Cache2.Set("curCourseID", "7788", time.Second*2)
}

func GetFromCache(ctx *gin.Context) {
	v, e := helpers.Cache1.Get("test")
	zlog.Infof(ctx, "%v %v", v, e)

	v, e = helpers.Cache2.Get("curCourseID")
	zlog.Infof(ctx, "%v %v", v, e)
}
