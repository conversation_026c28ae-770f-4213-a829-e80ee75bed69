package data

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const key = "k_string_1"

func RedisExample(ctx *gin.Context) (err error) {

	err = helpers.RedisClient.Set(ctx, key, key, 30)
	if err != nil {
		return components.ErrorRedisSet.WrapPrintf(err, "key=%s, value=%s", key, key)
	}

	b, err := helpers.RedisClient.Get(ctx, key)
	if err != nil {
		return components.ErrorRedisGet.WrapPrintf(err, "key=%s, value=%s", key, key)
	}

	zlog.Infof(ctx, "redis get %s return: %s", key, string(b))

	keys := []string{"TestRedis_MSet_MGet_K1", "TestRedis_MSet_MGet_K2"}
	values := []string{"TestRedis_MSet_MGet_V1", "TestRedis_MSet_MGet_V2"}

	err = helpers.RedisClient.MSet(ctx, keys[0], values[0], keys[1], values[1])
	if err != nil {
		return components.ErrorRedisSet.WrapPrintf(err, "key=%+v, value=%+v", keys, values)
	}

	return nil
}
