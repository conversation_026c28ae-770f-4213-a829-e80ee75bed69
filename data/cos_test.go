package data_test

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestUploadFile(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/GolandProjects/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	//filePath := "/Users/<USER>/GolandProjects/assistantdeskgo/tips测试.csv"
	//dwUrl, err := helpers.BosBucket.UploadLocalFile(ctx, filePath, "fudao_61a7370cdf3126eb852bff854c0b8ce5", "csv", true)
	//if err != nil {
	//	zlog.Warnf(ctx, "TestUploadFile err, err:%+v ", err)
	//	return
	//}
	//zlog.Infof(ctx, "结果：%+v", dwUrl)
	/*	filePath := "/Users/<USER>/GolandProjects/assistantdeskgo/水手分模版文件开发1.csv"
		dwUrl, err := helpers.BosBucket.UploadLocalFile(ctx, filePath, "sailorscorekaifa1", "csv", true)
		if err != nil {
			zlog.Warnf(ctx, "TestUploadFile err, err:%+v ", err)
			return
		}
		zlog.Infof(ctx, "结果：%+v", dwUrl)
	*/
	content, err := helpers.BaiduBucket2.DownloadContent(ctx, "fudao_61a7370cdf3126eb852bff854c0b8ce5.csv")
	zlog.Infof(ctx, "结果：%+v, err :%+v", string(content), err)
}

func TestUploadFile2(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/GolandProjects/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	filePath := "/Users/<USER>/Downloads/fwyy_laxin_tag_fix_data_template.csv"
	dwUrl, err := helpers.BaiduBucket2.UploadLocalFile(ctx, filePath, "fwyy_laxin_tag_fix_data_template", "csv", true)
	if err != nil {
		zlog.Warnf(ctx, "TestUploadFile err, err:%+v ", err)
		return
	}
	zlog.Infof(ctx, "结果：%+v", dwUrl)

	content, err := helpers.BaiduBucket2.DownloadContent(ctx, "fwyy_laxin_tag_fix_data_template.csv")
	zlog.Infof(ctx, "结果：%+v, err :%+v", string(content), err)
}

func TestUploadFile3(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/GolandProjects/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)

	resContent := [][]string{
		{"课程ID", "资产手机号", "真人邮箱前缀", "一级服务团队", "二级服务团队", "处理结果-此处无需填写"},
		{"修正涉及的课程ID", "此处填写排班老师的资产手机号", "此处填写排班老师的真人邮箱前缀", "仅支持下述有限枚举：督学LPC/进校LPC/辅导老师/其他", "仅支持下述有限枚举：小学/初中/高中/编程/写字/运动/鲸准练/其他", "处理结果：更新成功/更新失败-$具体失败原因"},
		{"230478", "17501066666", "zhangwenwen01", "进校LPC", "小学", ""},
	}

	csvFileName := "fwyy_laxin_tag_fix_data_upload_tmplate"
	csvFilePath := "/Users/<USER>/Downloads/" + csvFileName + ".csv"
	if err := utils.WriteToCsv(csvFilePath, resContent, true); err != nil {
		zlog.Warnf(ctx, "laxinTagFixImportResult,WriteToCsv error,err:%s,return", err)
		return
	}

	filePath := "/Users/<USER>/Downloads/fwyy_laxin_tag_fix_data_upload_tmplate.csv"
	dwUrl, err := helpers.BaiduBucket2.UploadLocalFile(ctx, filePath, "fwyy_laxin_tag_fix_data_upload_tmplate", "csv", true)
	if err != nil {
		zlog.Warnf(ctx, "TestUploadFile err, err:%+v ", err)
		return
	}
	zlog.Infof(ctx, "结果：%+v", dwUrl)

	content, err := helpers.BaiduBucket2.DownloadContent(ctx, "fwyy_laxin_tag_fix_data_upload_tmplate.csv")
	zlog.Infof(ctx, "结果：%+v, err :%+v", string(content), err)
}
