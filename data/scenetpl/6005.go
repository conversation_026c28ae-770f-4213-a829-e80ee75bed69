package scenetpl

var FeedbackGroupTpl = map[string][]string{
	"分组1":  {"模板4", "模板3", "模板1", "模板4"},
	"分组2":  {"模板4", "模板3", "模板2", "模板4"},
	"分组3":  {"模板4", "模板3", "", "模板4"},
	"分组4":  {"模板3", "模板2", "模板1", "模板3"},
	"分组5":  {"模板3", "模板2", "模板2", "模板3"},
	"分组6":  {"模板3", "模板2", "", "模板3"},
	"分组7":  {"模板2", "模板2", "模板1", "模板2"},
	"分组8":  {"模板2", "模板2", "模板2", "模板2"},
	"分组9":  {"模板2", "模板2", "", "模板2"},
	"分组10": {"模板1", "模板1", "模板1", "模板1"},
	"分组11": {"模板1", "模板1", "模板2", "模板1"},
	"分组12": {"模板1", "模板1", "", "模板1"},
}

var StageTestTpl = map[string][]string{
	"模板1": {"#学生名#", "家长您好！\n这次阶段自评确实具有挑战性，涵盖了新课改的思维题型，虽然孩子因为一些疏忽丢了一些分，但我们看到他在面对难题，像",
		"#阶段测优秀知识目标#", "时，展现出的韧性和努力，这本身就是一种成长！"},
	"模板2": {"#学生名#", "家长您好！\n想跟您聊聊咱们家孩子这次阶段测评的情况，还有接下来咱们一起努力的方向。\n" +
		"首先，孩子在考试中，对试卷里考察的", "#阶段测优秀知识目标#", "，掌握得还不错，主讲老师也说孩子是很有潜力的，未来有很大提升空间。\n" +
		"不过，从试卷来看，孩子的基础知识还有一些地方需要加强，我们与", "#主讲老师名称#", "老师共同分析，错因可能是：\n", "#阶段测薄弱知识目标#"},
	"模板3": {"#学生名#", "家长您好！\n本次阶段测评中，", "#学生名#", "同学表现良好，", "#阶段测题目总数#", "道题中答对了", "#阶段测正确题目数#", "道题，整体成绩值得肯定。现将关键信息同步给您，助力孩子持续进步！\n" +
		"从试卷分析来看，孩子对", "#阶段测优秀知识目标#", "知识点上掌握比较扎实，这些知识点也很具有挑战性，但是孩子都做对了，主讲老师也说孩子是很有潜力的。\n" +
		"对于部分错题，我们与", "#主讲老师名称#", "老师共同分析，错因可能是：\n", "#阶段测薄弱知识目标#"},
	"模板4": {"#学生名#", "家长您好！\n告诉您一个好消息，孩子这次阶段自评考了", "#阶段测分数#", "分！一定要替我和", "#主讲老师名称#", "老师表扬一下孩子！\n" +
		"在本次阶段自评中，孩子对", "#阶段测优秀知识目标#", "知识点掌握得尤为优秀，成绩超过了班里的大多数同学，非常棒！"},
}

var DuringCourseTpl = map[string][]string{
	"模板1": {"但是，孩子在近期学习中也有表现不错的地方~ 比如：", "#优点表扬#"},
	"模板2": {"此外，孩子近期学习表现积极，比如：", "#优点表扬#"},
	"模板3": {"其实，孩子的优秀并非偶然，在过往的学习过程中，他能做到", "#优点表扬#"},
}

var InClassTpl = map[string][]string{
	"模板1": {"在我们过往学习的", "#课中知识目标数量#", "个知识点中，孩子对", "#课中优秀知识目标#", "知识点掌握得格外优秀，正确率在85%以上呢！"},
	"模板2": {"累计学习的", "#课中知识目标数量#", "个知识点中，孩子在", "#课中良好知识目标#", "表现良好，值得点赞！"},
}

var LearnSuggestTpl = map[string][]string{
	"模板1": {"最后，我也和", "#主讲老师名称#", "老师也在默默关注孩子，相信只要咱们一起努力，孩子一定能有大进步。我也会配合您，一起帮孩子提高。期待他下次能考个好成绩！\n" +
		"麻烦您点链接查收阶段自评报告", "#阶段测报告链接#"},
	"模板2": {"此次检测不仅帮助孩子完成了清晰的自我定位分析，还明确了提升方向，同时错题是提升能力的关键，建议家长引导孩子多练习成长手册中的“变式巩固”题目，强化薄弱环节。同时，提醒孩子：\n" +
		"①计算时步骤清晰，完成后验算；\n" +
		"②审题时圈画关键词，选择题和填空题简要写步骤；\n" +
		"③解答题先分析问题，再列式、计算、检查。\n" +
		"麻烦您点链接查收阶段自评报告", "#阶段测报告链接#"},
	"模板3": {"此次检测不仅帮助孩子完成了清晰的自我定位分析，还明确了提升方向，同时错题是提升能力的关键，建议家长引导孩子多练习成长手册中的“变式巩固”题目，强化薄弱环节。同时，提醒孩子：\n" +
		"①计算时步骤清晰，完成后验算；\n" +
		"②审题时圈画关键词，选择题和填空题简要写步骤；\n" +
		"③解答题先分析问题，再列式、计算、检查。\n" +
		"麻烦您点链接查收阶段自评报告", "#阶段测报告链接#"},
	"模板4": {"基于孩子的优秀表现和潜力，我们建议在后续学习中，可以适当增加一些更具挑战性的题目，帮助孩子进一步巩固优势，拓展思维能力~我们特别推荐成长手册中的“变式巩固”后6道题和“提升进阶”题目，这些题目非常适合能力较强的孩子，有效激发孩子的数学潜力。\n" +
		"麻烦您点链接查收阶段自评报告", "#阶段测报告链接#"},
}
