package data

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"context"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"reflect"
	"strconv"
	"time"
)

func EsInfo(ctx *gin.Context) error {

	info, code, err := helpers.ElasticClient.Ping("http://10.116.252.14:9200/").Do(ctx)
	if err != nil {
		return components.ErrorEsPing.Wrap(err)
	}

	zlog.Debug(ctx, "ElasticSearch returned with code:", code, " and version:", info.Version.Number)

	esVersion, err := helpers.ElasticClient.ElasticsearchVersion("http://10.116.252.14:9200/")
	if err != nil {
		return components.ErrorEsGetVersion.Wrap(err)
	}
	zlog.Infof(ctx, "ElasticSearch version: %s", esVersion)
	return nil
}

// Tweet is a structure used for serializing/deserialize data in ElasticSearch.
type Tweet struct {
	User     string                `json:"user"`
	Message  string                `json:"message"`
	Retweets int                   `json:"retweets"`
	Image    string                `json:"image,omitempty"`
	Created  time.Time             `json:"created,omitempty"`
	Tags     []string              `json:"tags,omitempty"`
	Location string                `json:"location,omitempty"`
	Suggest  *elastic.SuggestField `json:"suggest_field,omitempty"`
}

func Insert(ctx *gin.Context) error {

	tweet1 := Tweet{User: "olivere", Message: "Take Five", Retweets: 0}
	put1, err := helpers.ElasticClient.Index().
		Index("twitter").
		Id("1").
		BodyJson(tweet1).
		Do(ctx)
	if err != nil {
		return components.ErrorEsInsert.Wrap(err)
	}
	zlog.Infof(ctx, "Indexed tweet %s to index %s, type %s", put1.Id, put1.Index, put1.Type)

	tweet2 := `{"user" : "olivere", "message" : "It's a Raggy Waltz"}`
	put2, err := helpers.ElasticClient.Index().
		Index("twitter").
		Type("tweet").
		Id("2").
		BodyString(tweet2).
		Do(ctx)
	if err != nil {
		return components.ErrorEsInsert.Wrap(err)
	}
	zlog.Infof(ctx, "Indexed tweet %s to index %s, type %s", put2.Id, put2.Index, put2.Type)
	return nil
}

func Bulk(ctx *gin.Context) error {
	var doCtx context.Context
	if ctx != nil {
		doCtx = ctx
	} else {
		doCtx = context.Background()
	}

	bulkRequest := helpers.ElasticClient.Bulk()
	for i := 0; i < 100; i++ {
		tweet := Tweet{User: "olivere", Message: "this is message: " + strconv.Itoa(i)}
		req := elastic.NewBulkIndexRequest().Index("twitter").Type("tweet").Id("_id").Doc(tweet)

		bulkRequest = bulkRequest.Add(req)
	}
	bulkResponse, err := bulkRequest.Do(doCtx)
	if err != nil {
		return components.ErrorEsQuery.Wrap(err)
	}
	if bulkResponse == nil || bulkResponse.Errors {
		return components.ErrorEsQuery.Sprintf("bulkResponse got errors")
	}
	for _, vs := range bulkResponse.Items {
		for k, v := range vs {
			zlog.Info(ctx, "k = %s and v = %v", k, v)
		}
	}

	return nil
}

func Query(ctx *gin.Context) error {

	get1, err := helpers.ElasticClient.Get().
		Index("twitter").
		Id("1").
		Do(ctx)
	if err != nil {
		return components.ErrorEsQuery.Wrap(err)
	}
	if get1.Found {
		zlog.Infof(ctx, "Got document %s in version %d from index %s, type %s", get1.Id, get1.Version, get1.Index, get1.Type)
	}

	termQuery := elastic.NewTermQuery("user", "olivere")
	searchResult, err := helpers.ElasticClient.Search().
		Index("twitter").
		Query(termQuery).
		From(0).Size(10).
		Pretty(true).
		Do(ctx)
	if err != nil {
		return components.ErrorEsQuery.Wrap(err)
	}

	zlog.Infof(ctx, "Query took %d milliseconds", searchResult.TookInMillis)

	// Each is a convenience function that iterates over hits in a search result.
	// It makes sure you don't need to check for nil values in the response.
	// However, it ignores errors in serialization. If you want full control
	// over iterating the hits, see below.
	var ttyp Tweet
	for _, item := range searchResult.Each(reflect.TypeOf(ttyp)) {
		if t, ok := item.(Tweet); ok {
			zlog.Infof(ctx, "Tweet by %s: %s", t.User, t.Message)
		}
	}

	zlog.Infof(ctx, "Found a total of %d tweets", searchResult.TotalHits())

	if searchResult.Hits.TotalHits.Value > 0 {
		zlog.Infof(ctx, "Found a total of %d tweets", searchResult.Hits.TotalHits)

		for _, hit := range searchResult.Hits.Hits {
			// hit.Index contains the name of the index
			// Deserialize hit.Source into a Tweet (could also be just a map[string]interface{}).
			var t Tweet
			err := json.Unmarshal(hit.Source, &t)
			if err != nil {

				zlog.Error(ctx, "Tweet by %s got error: %s", t.User, err.Error())
				continue
			}

			zlog.Infof(ctx, "Tweet by %s: %s", t.User, t.Message)
		}
	} else {

		zlog.Info(ctx, "Found no tweets")
	}

	return nil
}

func Update(ctx *gin.Context) error {

	update, err := helpers.ElasticClient.Update().Index("twitter").Id("1").
		Upsert(map[string]interface{}{"retweets": 0}).
		Do(ctx)
	if err != nil {
		return components.ErrorEsUpdate.Wrap(err)
	}
	zlog.Infof(ctx, "New version of tweet %q is now %d", update.Id, update.Version)
	return nil
}

func Delete(ctx *gin.Context) error {

	deleteIndex, err := helpers.ElasticClient.DeleteIndex("twitter").Do(ctx)
	if err != nil {
		return components.ErrorEsDel.Wrap(err)
	}
	if !deleteIndex.Acknowledged {
		zlog.Warn(ctx, "Not acknowledged")
	}

	return nil
}
