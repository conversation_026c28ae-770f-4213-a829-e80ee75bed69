package data

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func CosTestCase(ctx *gin.Context) (err error) {
	if err := getTempToken(ctx); err != nil {
		return err
	}
	if err = uploadFile(ctx); err != nil {
		return err
	}
	if err = uploadContent(ctx); err != nil {
		return err
	}
	if err = download(ctx); err != nil {
		return err
	}
	if err = getImageMeta(ctx); err != nil {
		return err
	}
	if err = getImgUrl(ctx); err != nil {
		return err
	}
	return nil
}

/*
原php函数迁移
根据pid 获取图片的下载原图连接和缩略图链接
*/
func getImgUrl(ctx *gin.Context) error {
	pid := "zyb10_ce5492df60b0f4475752a93ddada327c"
	imgUrl := helpers.Bucket.GetImageUrlByPid(ctx, pid, "jpg")
	zlog.Debugf(ctx, "image url: %s", imgUrl)

	thumbnailURL := helpers.Bucket.GetThumbnailUrlByPid(ctx, pid, "w/50/h/50", "jpg", "png")
	zlog.Debugf(ctx, "thumbnail image url: %s", thumbnailURL)
	return nil
}

/*
原php GetImageMeta函数，返回图片的meta信息（宽／高）
pid 为上传时系统默认生成的图片编号
*/
func getImageMeta(ctx *gin.Context) error {
	pid := "zyb10_ce5492df60b0f4475752a93ddada327c"
	m, err := helpers.Bucket.GetImageMeta(ctx, pid, "jpg")
	if err != nil {
		return components.ErrorCosGetData.Wrap(err)
	}
	zlog.Debugf(ctx, "cos.GetImageMeta: %+v", m)
	return nil
}

/*
上传内容到cos
如果 fileName 不为空, 则文件名为：fileName.fileType
如果 fileName 为空，系统默认生成 fileName = prefix_md5(content).fileType ,
*/
func uploadContent(ctx *gin.Context) error {
	content := "this is test content to upload"
	u, err := helpers.Bucket.UploadFileContent(ctx, content, "test_content", "txt")
	if err != nil {
		return components.ErrorCosUpload.Wrap(err)
	}
	zlog.Debug(ctx, "upload success, url : ", u)
	return nil
}

/*
上传本地文件到cos
如果 fileName 不为空, 则文件名为：fileName.fileType
如果 fileName 为空，系统默认生成 fileName = prefix_md5(localFile)(_w_h).fileType ,
其中 prefix_md5(localFile)_w_h 为后面用到的pid
*/
func uploadFile(ctx *gin.Context) error {
	f := "/Users/<USER>/GO_CODE/smallImg.png"
	u, err := helpers.Bucket.UploadLocalFile(ctx, f, "test_gs", "png", true)
	if err != nil {
		return components.ErrorCosUpload.Wrap(err)
	}
	zlog.Debug(ctx, "upload success, url : ", u)
	return nil
}

/*
下载文件到本地
srcFileName 为文件在cos上存储的fileName
*/
func download(ctx *gin.Context) error {

	fileName := "test_content.txt"

	dst := "./tcos.txt"
	err := helpers.Bucket.Download2Local(ctx, fileName, dst)
	if err != nil {
		return components.ErrorCosDownload.Wrap(err)
	}
	return nil
}

func getTempToken(ctx *gin.Context) error {
	token, err := helpers.Bucket.GetTempKeys(ctx, 30*time.Minute)
	if err != nil {
		return err
	}
	zlog.Info(ctx, "token is: ", token)
	return nil
}
