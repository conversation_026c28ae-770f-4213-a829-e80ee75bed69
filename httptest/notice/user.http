@urlPrefix=assistantdeskgo/notice/user

### 获取未读通知列表（顶部轮播栏）
GET {{domain}}/{{urlPrefix}}/getunreadnoticelist
Content-Type: application/json
Cookie: {{cookie}}

### 获取最新通知列表（顶部轮播栏）
GET {{domain}}/{{urlPrefix}}/gettopnoticelist
Content-Type: application/json
Cookie: {{cookie}}

### 获取通知详情
GET {{domain}}/{{urlPrefix}}/getnoticedetail?noticeId=47
Content-Type: application/json
Cookie: {{cookie}}

### 通知点赞/点踩
POST {{domain}}/{{urlPrefix}}/feedback
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 20,
    "type": 1,
    "feedbackType": 1,
    "feedbackContent": "hello"
}

### 发表评论
POST {{domain}}/{{urlPrefix}}/comment
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 20,
    "parentId": 0,
    "comment": "hello"
}

### 评论点赞/取消点赞
POST {{domain}}/{{urlPrefix}}/commentlike
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 7,
    "commentId": 3
}

### 获取用户通知列表
GET {{domain}}/{{urlPrefix}}/getnoticelist?readType=1&classId=
Content-Type: application/json
Cookie: {{cookie}}

### 通知稍后再看
POST {{domain}}/{{urlPrefix}}/noticereadlater
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 20
}

### 更新通知阅读时间
POST {{domain}}/{{urlPrefix}}/readtime
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 20,
    "type": 1,
    "readTime": 5563
}
