@urlPrefix=assistantdeskgo/notice/manager

### 新增通知
POST {{domain}}/{{urlPrefix}}/save
Content-Type: application/json
Cookie: {{cookie}}

{
    "title": "测试0313",
    "classId": 2,
    "status": 2,
    "scope": 2,
    "scopeGroup": "123",
    "scopeUid": "2579599987",
    "profilePhoto": "测试",
    "content": "asdadadasdsadasd",
    "videoAddr": "www.baidu.com",
    "abstract": "这是一个摘要",
    "commentFlag": true,
    "mockReadNum": 100,
    "mockContentLikeNum": 50,
    "mockContentDisLikeNum": 10,
    "mockVideoLikeNum": 30,
    "mockVideoDisLikeNum": 5,
    "mockCommentNum": 20
}

### 更新通知
POST {{domain}}/{{urlPrefix}}/save
Content-Type: application/json
Cookie: {{cookie}}

{
    "id": 19,
    "title": "测试0226",
    "classId": 2,
    "status": 2,
    "scope": 1,
    "scopeGroup": "",
    "scopeUid": "2579599987",
    "profilePhoto": "测试test",
    "content": "asdadadasdsadasd",
    "videoAddr": "www.baidu.com",
    "abstract": "这是一个摘要",
    "commentFlag": true
}

### 获取通知列表
POST {{domain}}/{{urlPrefix}}/list
Content-Type: application/json
Cookie: {{cookie}}

{
    "page": 1,
    "size": 10
}

### 编辑通知
GET {{domain}}/{{urlPrefix}}/get?id=13
Content-Type: application/json
Cookie: {{cookie}}

### 获取通知详情(带评论)
GET {{domain}}/{{urlPrefix}}/detail?id=13
Content-Type: application/json
Cookie: {{cookie}}

### 删除通知
POST {{domain}}/{{urlPrefix}}/delete
Content-Type: application/json
Cookie: {{cookie}}

{
    "id": 7
}

### 修改通知状态
POST {{domain}}/{{urlPrefix}}/changestatus
Content-Type: application/json
Cookie: {{cookie}}

{
    "id": 7,
    "status": 2
}

### 获取预期发送人数
POST {{domain}}/{{urlPrefix}}/expectednum
Content-Type: application/json
Cookie: {{cookie}}

{
    "scope": 1,
    "scopeGroup": "1"
}

### 获取预期发送列表
POST {{domain}}/{{urlPrefix}}/expectedlist
Content-Type: application/json
Cookie: {{cookie}}

{
    "scope": 1,
    "scopeGroup": "10261",
    "region":[],
    "grade":[],
    "subject":[],
    "gradeLevel":[],
    "page":75,
    "size":10
}

### 发送列表
POST {{domain}}/{{urlPrefix}}/unreadlist
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 22,
    "type": 1,
    "feedbackType": 0,
    "uid": 0,
    "staffName": "郑宪春",
    "productLine": [2],
    "groupId": "",
    "region":[],
    "grade":[],
    "subject":[],
    "gradeLevel":[],
    "startTime": 0,
    "endTime": 0,
    "page":1,
    "size":10
}

### 获取通知类型字典
GET  {{domain}}/assistantdeskgo/notice/export/noticelist
Content-Type: application/json
Cookie: {{cookie}}

### 获取bos临时token
GET {{domain}}/assistantdeskgo/notice/common/getbostoken
Content-Type: application/json
Cookie: {{cookie}}