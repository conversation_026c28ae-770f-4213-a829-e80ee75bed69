@urlPrefix=assistantdeskgo/notice/comment

### 获取评论管理页通知列表
POST {{domain}}/{{urlPrefix}}/getnoticelist
Content-Type: application/json
Cookie: {{cookie}}

{}

### 获取评论统计（全部总数、精选总数）
GET {{domain}}/{{urlPrefix}}/count?noticeId=7
Content-Type: application/json
Cookie: {{cookie}}

### 获取评论列表
POST {{domain}}/{{urlPrefix}}/list
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 48,
    "filterType": 1,
    "timeRange": 0
}

### 回复评论
POST {{domain}}/{{urlPrefix}}/reply
Content-Type: application/json
Cookie: {{cookie}}

{
    "noticeId": 8,
    "parentId": 0,
    "comment": "这是一条评论"
}

### 设置精选评论
POST {{domain}}/{{urlPrefix}}/setselected
Content-Type: application/json
Cookie: {{cookie}}

{
    "id": 1,
    "isSelected": false
}

### 删除评论
POST {{domain}}/{{urlPrefix}}/delete
Content-Type: application/json
Cookie: {{cookie}}

{
    "id": 1
}