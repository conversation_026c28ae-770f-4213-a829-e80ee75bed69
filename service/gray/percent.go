package gray

import (
	"assistantdeskgo/api/mercury"
	duxuescDao "assistantdeskgo/models/duxuesc"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type SwitchAbstractTextGray struct {
	PriceTag []int64 `json:"price_tag"`
	Full     int64   `json:"full"`
}

func SwitchAbstractText(ctx *gin.Context, id int64, key string) bool {
	if id <= 0 {
		return false
	}
	var data SwitchAbstractTextGray

	err := mercury.GetConfigForJson(ctx, key, GRAY_TTL, &data)
	if err != nil {
		return false
	}

	if data.Full == 1 {
		return true
	}

	if utils.InArrayInt64(id, data.PriceTag) {
		return true
	}

	return false
}

type SyncConfig struct {
	DoubleWrite int64 `json:"double_write"`
}

func DoubleWrite(ctx *gin.Context, key string) bool {

	var config SyncConfig
	err := duxuescDao.ConfigDao.GetConfigByKey(ctx, key, &config, nil)
	if err != nil {
		return false
	}

	zlog.Infof(ctx, "Aisync Config=%+v", config)
	return config.DoubleWrite == 1
}
