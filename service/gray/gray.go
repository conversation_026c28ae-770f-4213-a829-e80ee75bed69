package gray

import (
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	duxuescDao "assistantdeskgo/models/duxuesc"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
)

type MercuryGrayConfig struct {
	StaffId  []int64 `json:"staffId"`
	GroupId  []int64 `json:"groupId"`
	NotStaff []int64 `json:"notStaff"`
	Full     int64   `json:"full"`
}

const GRAY_TTL = 300

const RedisHitGray = "assistantdeskgo:hitgray_%v_%v_%v"

type RedisHitGrayDto struct {
	Hit   bool `json:"hit"`
	Cache bool `json:"cache"`
}

func HitGray(ctx *gin.Context, staffUid int64, key string) bool {
	redisKey := fmt.Sprintf(RedisHitGray, defines.CONFIG_SOURCE_MERCURY, key, staffUid)
	redisV, _ := helpers.RedisClient.Get(ctx, redisKey)
	var redisHit RedisHitGrayDto
	_ = jsoniter.Unmarshal(redisV, &redisHit)
	zlog.Infof(ctx, "HitGray FromRedis,key=%v,cache=%v,hit=%v", redisKey, redisHit.Cache, redisHit.Hit)
	if redisHit.Cache {
		return redisHit.Hit
	}

	h := doHitGray(ctx, staffUid, key)
	redisHit.Cache = true
	redisHit.Hit = h
	redisSetVal, _ := jsoniter.Marshal(redisHit)
	helpers.RedisClient.Set(ctx, redisKey, string(redisSetVal), defines.SECOND_OF_MINUTE)
	return h
}

func doHitGray(ctx *gin.Context, staffUid int64, key string) bool {
	var ret MercuryGrayConfig
	err := mercury.GetConfigForJson(ctx, key, GRAY_TTL, &ret)
	if err != nil {
		zlog.Warnf(ctx, "HitGray GetConfigForJson fai,key=%v,err=%v", key, err)
		return false
	}

	if ret.Full == 1 {
		return true
	}

	if utils.InArrayInt64(staffUid, ret.NotStaff) {
		return false
	}
	if utils.InArrayInt64(staffUid, ret.StaffId) {
		return true
	}

	if len(ret.GroupId) > 0 {
		//获取老师的组织架构
		positions, err := mesh.GetStaffGroupPositions(ctx, staffUid)
		if err != nil {
			return false
		}

		for _, group := range positions {
			if utils.InArrayInt64(group.GroupId, ret.GroupId) {
				return true
			} else {
				groupDetail, err := mesh.GetGroupDetailById(ctx, group.GroupId)
				if err != nil {
					continue
				}

				for _, groupLevelId := range strings.Split(groupDetail.LevelStr, ",") {
					levelId, err := strconv.ParseInt(groupLevelId, 10, 0)
					if err != nil {
						continue
					}

					if utils.InArrayInt64(levelId, ret.GroupId) {
						return true
					}
				}
			}
		}
	}
	return false
}

type GrayUserGroupConfig struct {
	StaffId  []int64 `json:"staffId"`
	GroupId  []int64 `json:"groupId"`
	NotStaff []int64 `json:"notStaff"`
	Full     int64   `json:"full"`
}

func UserGroupGray(ctx *gin.Context, staffUid int64, key string, configSource string) bool {
	redisKey := fmt.Sprintf(RedisHitGray, defines.CONFIG_SOURCE_MERCURY, key, staffUid)
	redisV, _ := helpers.RedisClient.Get(ctx, redisKey)
	var redisHit RedisHitGrayDto
	_ = jsoniter.Unmarshal(redisV, &redisHit)
	zlog.Infof(ctx, "UserGroupGray FromRedis,key=%v,cache=%v,hit=%v", redisKey, redisHit.Cache, redisHit.Hit)
	if redisHit.Cache {
		return redisHit.Hit
	}
	hit := doUserGroupGray(ctx, staffUid, key, configSource)
	redisHit.Cache = true
	redisHit.Hit = hit
	redisSetVal, _ := jsoniter.Marshal(redisHit)
	helpers.RedisClient.Set(ctx, redisKey, string(redisSetVal), defines.SECOND_OF_MINUTE)
	return hit
}

// 按用户和用户组通用灰度接口，可兼用HitGray
func doUserGroupGray(ctx *gin.Context, staffUid int64, key string, configSource string) bool {
	var ret GrayUserGroupConfig
	if configSource == defines.CONFIG_SOURCE_DUXUESC {
		err := duxuescDao.ConfigDao.GetConfigByKey(ctx, key, &ret, nil)
		if err != nil {
			zlog.Warnf(ctx, "HitGray GetConfigByKey fai,key=%v,err=%v", key, err)
			return false
		}

	} else if configSource == defines.CONFIG_SOURCE_MERCURY {
		err := mercury.GetConfigForJson(ctx, key, GRAY_TTL, &ret)
		if err != nil {
			zlog.Warnf(ctx, "HitGray GetConfigForJson fai,key=%v,err=%v", key, err)
			return false
		}
	} else {
		return false
	}

	if ret.Full == 1 {
		return true
	}

	if utils.InArrayInt64(staffUid, ret.NotStaff) {
		return false
	}
	if utils.InArrayInt64(staffUid, ret.StaffId) {
		return true
	}

	if len(ret.GroupId) > 0 {
		//获取老师的组织架构
		positions, err := mesh.GetStaffGroupPositions(ctx, staffUid)
		if err != nil {
			return false
		}

		for _, group := range positions {
			if utils.InArrayInt64(group.GroupId, ret.GroupId) {
				return true
			} else {
				groupDetail, err := mesh.GetGroupDetailById(ctx, group.GroupId)
				if err != nil {
					continue
				}

				for _, groupLevelId := range strings.Split(groupDetail.LevelStr, ",") {
					levelId, err := strconv.ParseInt(groupLevelId, 10, 0)
					if err != nil {
						continue
					}

					if utils.InArrayInt64(levelId, ret.GroupId) {
						return true
					}
				}
			}
		}
	}

	return false
}
