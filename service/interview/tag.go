package interview

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	utils2 "gorm.io/gorm/utils"
	"strconv"
	"time"
)

func HideTag(ctx *gin.Context, callId, studentUid int64, callType int, tags []string) error {
	callIdStr := strconv.FormatInt(callId, 10)
	source := defines.AI_TOKEN
	token := defines.AI_TOKEN
	if callType == 3 {
		callIdStr = fmt.Sprintf("qw_%d_%d", studentUid, callId)
		source = defines.AiTokenQw
		token = defines.AiTokenQw
		//查询语音通话记录

	}
	var teacherId int64
	//处理自己存储的数据
	if callType == 2 {

		callRecordNew, err := models.AiCallRecordRef.GetByCallId(ctx, callId, studentUid)
		if err != nil {
			return err
		}
		if callRecordNew.CallId == callId {
			//可以查询到，走新的
			teacherId = callRecordNew.DeviceUid
			if err != nil {
				return err
			}
			abstractTags := callRecordNew.GetAbstractTags()
			newTags := make([]models.TagStruct, 0)
			if len(abstractTags) > 0 {
				for _, tagValue := range abstractTags {
					tmpTag := tagValue
					for _, tag := range tags {
						if tagValue.Label == tag || tagValue.TagKey == tag {
							tmpTag.HideFlag = "1"
							continue
						}
					}
					newTags = append(newTags, tmpTag)
				}
				//数据库更新
				newTagsStr, e := json.Marshal(newTags)
				if e != nil {
					return e
				}
				callRecordNew.Tags = string(newTagsStr)
				callRecordNew.UpdateTime = time.Now().Unix()
				er := models.AiCallRecordRef.Updates(ctx, []models.AiCallRecord{callRecordNew}, studentUid)
				if er != nil {
					return er
				}
			}
		} else {

			//查询电话外呼记录
			callTaskModel := models.AiCallRecordTask{}
			callTaskInfo, err := callTaskModel.GetByCallId(ctx, callIdStr)
			teacherId = callTaskInfo.DeviceUid
			if err != nil {
				return err
			}
			abstractTags := callTaskInfo.GetAbstractTags()
			newTags := make([]models.TagStruct, 0)
			if len(abstractTags) > 0 {
				for _, tagValue := range abstractTags {
					tmpTag := tagValue
					for _, tag := range tags {
						if tagValue.Label == tag || tagValue.TagKey == tag {
							tmpTag.HideFlag = "1"
							continue
						}
					}
					newTags = append(newTags, tmpTag)
				}
				//数据库更新
				newTagsStr, e := json.Marshal(newTags)
				if e != nil {
					return e
				}
				callTaskModel.CallId = callTaskInfo.CallId
				callTaskModel.Tags = string(newTagsStr)
				callTaskModel.UpdateTime = time.Now().Unix()
				er := callTaskModel.Updates(ctx, []models.AiCallRecordTask{callTaskModel})
				if er != nil {
					return er
				}
			}
		}
	} else {
		//查询电话外呼记录
		wxCallRecordModel := models.WxCallRecordNew{}
		wxCallRecord, err := wxCallRecordModel.GetByCallId(ctx, callId, studentUid)
		if err != nil {
			return err
		}
		if wxCallRecord.CallId <= 0 {
			return nil
		}
		teacherId = wxCallRecord.DeviceUid
	}

	//处理电话摘要
	hideReq := aiturbo.HideTagReq{
		CallId:     callIdStr,
		Source:     source,
		Token:      token,
		Hidetopics: tags,
		TeacherId:  utils2.ToString(teacherId),
	}
	//调用摘要接口处理
	result, err := aiturbo.HideTags(ctx, hideReq)
	if err != nil {
		zlog.Warnf(ctx, "ai turbo hide call tags failed req:%+v, err:%s", hideReq, err.Error())
	}
	if result.DelNo != "0" {
		return fmt.Errorf("删除标签失败，失败原因:%s", result.DelMessage)
	}
	return nil
}
