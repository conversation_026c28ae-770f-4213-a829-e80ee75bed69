package interview

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/ai"
	"assistantdeskgo/utils"
	"assistantdeskgo/utils/task"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"git.zuoyebang.cc/sp/godict/course/subject"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

// 实现 sort.Interface 接口
type SortInterview []InterviewRecord

func (a SortInterview) Len() int           { return len(a) }
func (a SortInterview) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a SortInterview) Less(i, j int) bool { return a[i].StartTime > a[j].StartTime } // 这里是 > 符号，表示从大到小排序

type InterviewRecord struct {
	Id                int64        `json:"id"`
	StartTime         int64        `json:"startTime"`
	Type              int64        `json:"type"`          //1 人填；2电话；3微信
	InterviewType     int          `json:"interviewType"` //1 人填；2 AI
	Status            int          `json:"status"`
	Result            int          `json:"result"`
	StudentUid        int64        `json:"studentUid"`
	CourseId          int64        `json:"courseId"`
	Topic             []string     `json:"topic"`
	RemindId          int64        `json:"remindId"`
	PersonUid         int64        `json:"personUid"`
	Duration          int64        `json:"duration"`
	CallAssistantUid  int64        `json:"callAssistantUid"`
	CallAssistantName string       `json:"callAssistantName"`
	Subject           string       `json:"subject"`
	AbstractInfo      AbstractInfo `json:"abstractInfo"`
}

type AllInterviewData struct {
	StuInterviewList    []InterviewRecord
	StuInterviewErr     error
	CallInterviewList   []InterviewRecord
	CallInterviewErr    error
	WxCallInterviewList []InterviewRecord
	WxCallInterviewErr  error
}

type InterviewDetail struct {
	BaseInfo      InterviewBaseInfo `json:"baseInfo"`
	AbstractInfo  AbstractInfo      `json:"abstractInfo"`
	InterviewInfo CallDetail        `json:"interviewInfo"`
}

type CallDetail struct {
	CallId    int64  `json:"callId"`
	Content   string `json:"content"`
	FileUrl   string `json:"fileUrl"`
	StartTime int64  `json:"startTime"`
}

type AbstractInfo struct {
	Content string                `json:"content"`
	Todo    muse.RemindInfo       `json:"todo"`
	Tags    []aiturbo.AbstractTag `json:"tags"`
	Topic   []string              `json:"topic"`
}

type InterviewBaseInfo struct {
	InterviewType        int64  `json:"interviewType"`
	InterviewTypeStr     string `json:"interviewTypeStr"`
	AssistantName        string `json:"assistantName"`
	AssistantUid         int64  `json:"assistantUid"`
	CourseId             int64  `json:"courseId"`
	CourseName           string `json:"courseName"`
	CustomDeviceId       string `json:"customDeviceId"`
	CustomDeviceBelonger string `json:"customDeviceBelonger"`
	CreateTime           int64  `json:"createTime"`
}

type AiAbstractParamItem struct {
	CallId       int64 `json:"callId" form:"callId"`
	StudentUid   int64 `json:"studentUid" form:"studentUid"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
}

func GetAllInterviewList(ctx *gin.Context, studentUid, courseId, assistantUid, lastTime int64, limit int) (list []InterviewRecord, err error) {
	if lastTime <= 0 {
		lastTime = time.Now().Unix() + 1
	}
	interviewData := AllInterviewData{}
	//t := task.NewAsyncTask()
	//查询手填维系记录
	//t.Add(func() {
	interviewData.StuInterviewList, interviewData.StuInterviewErr = GetWriteInterview(ctx, studentUid, assistantUid, courseId, lastTime, limit)
	//})
	//查询电话外呼维系记录
	//t.Add(func() {
	interviewData.CallInterviewList, interviewData.CallInterviewErr = GetCallInterview(ctx, studentUid, assistantUid, courseId, lastTime, limit)
	//})
	//查询微信外呼维系记录
	//t.Add(func() {
	interviewData.WxCallInterviewList, interviewData.WxCallInterviewErr = GetWxCallInterview(ctx, studentUid, assistantUid, lastTime, limit)
	//})
	//t.Wait()
	if interviewData.CallInterviewErr != nil {
		return
	}
	if interviewData.CallInterviewErr != nil {
		return
	}
	if interviewData.WxCallInterviewErr != nil {
		return
	}
	//格式化返回结果
	list, err = FormatInterviewList(ctx, interviewData, limit)
	if err != nil {
		return
	}
	return
}

func FormatInterviewList(ctx *gin.Context, interviewData AllInterviewData, limit int) (interviewList []InterviewRecord, err error) {
	//合并数据
	interviews := make([]InterviewRecord, 0)
	interviews = append(interviews, interviewData.StuInterviewList...)
	interviews = append(interviews, interviewData.CallInterviewList...)
	interviews = append(interviews, interviewData.WxCallInterviewList...)
	//组装公共数据，课程信息+待办信息
	courseIds := make([]int64, 0)
	remindReqs := make([]muse.GetRemindReq, 0)
	assistantUids := make([]int64, 0)
	for _, interview := range interviews {
		if interview.CourseId > 0 {
			courseIds = append(courseIds, interview.CourseId)
		}
		if interview.RemindId > 0 {
			tmpReq := muse.GetRemindReq{
				RemindId:  interview.RemindId,
				CreateUid: interview.PersonUid,
			}
			remindReqs = append(remindReqs, tmpReq)
		}
		if interview.CallAssistantUid > 0 && !utils.InArrayInt64(interview.CallAssistantUid, assistantUids) {
			assistantUids = append(assistantUids, interview.CallAssistantUid)
		}
	}

	//查询辅导老师信息
	deviceInfoMap := map[string]mesh.DeviceInfo{}
	if len(assistantUids) > 0 {
		assistantUids = utils.FilterInt64Duplicates(assistantUids)
		for _, subTeacherUidList := range utils.SplitInt64Array(assistantUids, 90) {
			subTeacherMap, err := mesh.GetDeviceInfoListByDeviceUidList(ctx, subTeacherUidList)
			if err != nil {
				return interviewList, err
			}
			for k, v := range subTeacherMap {
				deviceInfoMap[k] = v
			}
		}
	}

	//查询课程信息
	courseList := map[int64]dal.CourseLessonInfo{}
	if len(courseIds) > 0 {
		courseField := []string{"mainSubjectId", "courseId", "courseName"}
		lessonField := []string{"lessonId", "lessonName"}
		courseList, err = dal.GetCourseLessonInfoByCourseIds(ctx, courseIds, courseField, lessonField)
		if err != nil {
			return
		}
	}
	//查询待办信息
	remindList := map[int64]muse.RemindInfo{}
	if len(remindReqs) > 0 {
		remindList, err = muse.BatchGetRemindMapByIds(ctx, remindReqs)
		if err != nil {
			return
		}
	}

	//组装数据
	for _, interview := range interviews {
		tmpData := interview
		//组装学科
		if courseInfo, ok := courseList[interview.CourseId]; ok {
			subjectId := courseInfo.MainSubjectId
			if subjectStr, k := subject.Map[int(subjectId)]; k {
				tmpData.Subject = subjectStr
			}

		}
		//组装待办
		if remindInfo, ok := remindList[interview.RemindId]; ok {
			tmpData.AbstractInfo.Todo = remindInfo
		}
		//组装辅导老师名称
		if deviceInfo, ok := deviceInfoMap[strconv.FormatInt(interview.CallAssistantUid, 10)]; ok {
			tmpData.CallAssistantName = deviceInfo.NickName
		}
		if interview.InterviewType == models.TftcJSQValue {
			tmpData.CallAssistantName = "投放销售"
		}
		interviewList = append(interviewList, tmpData)
	}

	sort.Sort(SortInterview(interviewList))

	if len(interviewList) <= limit {
		return
	}

	return interviewList[:limit], nil
}

func GetWriteInterview(ctx *gin.Context, studentUid, assistantUid, courseId, lastTime int64, limit int) (list []InterviewRecord, err error) {
	stuInterviewModel := &models.StudentInterview{}
	stuInterviewList, err := stuInterviewModel.ListByToUidAndFromUidAndTimeRange(ctx, studentUid, assistantUid, courseId, lastTime, limit)
	if err != nil {
		return
	}
	if len(stuInterviewList) <= 0 {
		return
	}
	for _, stuInterview := range stuInterviewList {
		tmp := InterviewRecord{
			Id:               stuInterview.ID,
			Type:             int64(stuInterview.ChannelType + 1),
			StartTime:        stuInterview.CreateTime,
			InterviewType:    1,
			StudentUid:       stuInterview.StudentUID,
			PersonUid:        stuInterview.OperatorUID,
			CourseId:         stuInterview.CourseID,
			CallAssistantUid: stuInterview.OperatorUID,
			Topic:            stuInterview.GetInterViewTypeStr(),
			Result:           0,
			Status:           4,
		}
		tmp.AbstractInfo.Content = stuInterview.Content
		list = append(list, tmp)
	}
	return
}

func GetCallInterview(ctx *gin.Context, studentUid, assistantUid, courseId, lastTime int64, limit int) (list []InterviewRecord, err error) {
	//调用触达接口查询电话外呼记录
	req := touchmisgo.GetCallRecordHistoryReq{
		EndTime: lastTime - 1,
		Rn:      limit,
		ToUid:   []int64{studentUid},
	}
	if assistantUid > 0 {
		req.FromUid = assistantUid
	}
	callRecordHistory, err := touchmisgo.GetCallRecordHistory(ctx, req)
	if err != nil || len(callRecordHistory.CallRecordList) <= 0 {
		return
	}
	//获取所有的callId
	callIds := make([]string, 0)
	callIdList := make([]int64, 0)
	for _, callRecord := range callRecordHistory.CallRecordList {
		callIdList = append(callIdList, callRecord.CallId)
	}
	//查询电话通话维系记录
	callInterviewMap := make(map[string]models.AiCallRecord)
	if len(callIdList) > 0 {

		//先从新的查询
		newCallInterviewList, err := models.AiCallRecordRef.ListByCallIdIn(ctx, callIdList, studentUid)
		if err != nil {
			return nil, err
		}
		selectCallIdList := make([]int64, 0)
		if len(newCallInterviewList) > 0 {
			for _, newCallInterviewInfo := range newCallInterviewList {
				callInterviewMap[utils2.ToString(newCallInterviewInfo.CallId)] = newCallInterviewInfo
				selectCallIdList = append(selectCallIdList, newCallInterviewInfo.CallId)
			}
		}
		callIds = utils.ConvertArrayInt64ToArrayString(utils.RemoveInt64ArrayInt64Array(callIdList, selectCallIdList))
		if len(callIds) > 0 {
			callRecordModel := &models.AiCallRecordTask{}
			callInterviewList, err := callRecordModel.ListByCallIdList(ctx, callIds)
			if err != nil {
				return list, err
			}
			if len(callInterviewList) > 0 {
				for _, callInterviewInfo := range callInterviewList {
					callInterviewMap[callInterviewInfo.CallId] = models.AiCallRecordRef.From2(callInterviewInfo)
				}
			}

		}
	}
	for _, callRecord := range callRecordHistory.CallRecordList {
		tmp := InterviewRecord{
			Id:               callRecord.CallId,
			Type:             2,
			InterviewType:    2,
			StartTime:        callRecord.CreateTime,
			Topic:            ai.GetTopic(callRecord.SourceType),
			Duration:         callRecord.Duration,
			StudentUid:       callRecord.ToUid,
			CourseId:         callRecord.CourseId,
			CallAssistantUid: callRecord.DeviceUid,
			PersonUid:        callRecord.PersonUid,
			Result:           int(callRecord.CallResult),
			Status:           0,
		}
		callInterview, ok := callInterviewMap[strconv.FormatInt(callRecord.CallId, 10)]
		if ok {
			tmp.AbstractInfo.Content = callInterview.Abstract
			tmp.Status = int(callInterview.Status)
			tmp.Result = 2
			tmp.AbstractInfo.Tags = callInterview.GetCommonAbstractTags()
			tmp.RemindId = callInterview.RemindId
		}
		list = append(list, tmp)
	}
	return
}

func GetWxCallInterview(ctx *gin.Context, studentUid, assistantUid, lastTime int64, limit int) (list []InterviewRecord, err error) {
	wxCallRecordModel := &models.WxCallRecordNew{}
	wxCallRecordList, err := wxCallRecordModel.ListByTimeRange(ctx, studentUid, assistantUid, lastTime, limit)
	if err != nil {
		return
	}
	if len(wxCallRecordList) <= 0 {
		return
	}
	abstractReqList := getCallIdListByWxCallList(wxCallRecordList)
	//去AI摘要查询摘要信息
	abstractList, err := aiturbo.BatchPostSelectAbstract(ctx, abstractReqList)
	if err != nil {
		return
	}
	for _, wxCallRecord := range wxCallRecordList {
		tmp := InterviewRecord{
			Id:               wxCallRecord.CallId,
			Type:             3,
			InterviewType:    2,
			StartTime:        wxCallRecord.CreateTime,
			Duration:         wxCallRecord.Duration,
			StudentUid:       wxCallRecord.StudentUid,
			Status:           wxCallRecord.Status,
			CallAssistantUid: wxCallRecord.DeviceUid,
			PersonUid:        wxCallRecord.PersonUid,
			RemindId:         wxCallRecord.RemindId,
		}
		if wxCallRecord.CallResult == 2 || wxCallRecord.CallResult == 3 || wxCallRecord.CallResult == 4 {
			tmp.Result = 3
		} else if wxCallRecord.CallResult == 1 {
			tmp.Result = 2
		} else {
			tmp.Result = int(wxCallRecord.CallResult)
		}
		callStr := fmt.Sprintf("qw_%d_%d", wxCallRecord.StudentUid, wxCallRecord.CallId)
		if abstract, ok := abstractList[callStr]; ok {
			tmp.AbstractInfo.Content = abstract.AbstractContent.Abstract
			for _, tag := range abstract.AbstractContent.Tags {
				//只取未隐藏的标签
				if tag.HideFlag == "1" {
					continue
				}
				tmp.AbstractInfo.Tags = append(tmp.AbstractInfo.Tags, tag)
			}
		}
		list = append(list, tmp)
	}

	return
}

func getCallIdListByWxCallList(wxCallRecordList []models.WxCallRecordNew) []aiturbo.PostSelectAbstractReq {
	reqList := make([]aiturbo.PostSelectAbstractReq, 0)
	if len(wxCallRecordList) <= 0 {
		return reqList
	}
	for _, wxCallRecord := range wxCallRecordList {
		tmpReq := aiturbo.PostSelectAbstractReq{
			CallId:    fmt.Sprintf("qw_%d_%d", wxCallRecord.StudentUid, wxCallRecord.CallId),
			TeacherId: utils2.ToString(wxCallRecord.DeviceUid),
		}
		reqList = append(reqList, tmpReq)
	}
	return reqList
}

type InterViewDetailData struct {
	CallRecord    touchmisgo.GetCallRecordInfo
	CallInterview models.AiCallRecordTask
	CourseInfo    dal.CourseLessonInfo
	LpcInfo       mesh.DeviceInfo
	StuInfo       dau.StudentInfo
	WxCallRecord  models.WxCallRecordNew
	WxAbstract    aiturbo.PostSelectAbstractResult
	WxBelongInfo  kunpeng.GetBelongerRes
	TodoInfo      muse.RemindInfo
	Link          string
}

func GetCallInterviewDetail(ctx *gin.Context, studentUid, callId int64) (detail InterviewDetail, err error) {
	//调用touchmis_go接口查询电话外呼记录
	detailData := InterViewDetailData{}
	//查询电话通话维系记录
	req := touchmisgo.GetCallRecordInfoReq{
		CallIds: []int64{callId},
	}
	callRecordMap, err := touchmisgo.GetCallRecordByCallId(ctx, req)
	if err != nil {
		return
	}
	callRecord, ok := callRecordMap[callId]
	if !ok {
		return detail, fmt.Errorf("can not find the callRecord by callId:%d", callId)
	}
	detailData.CallRecord = callRecord
	if err != nil || detailData.CallRecord.CallId <= 0 {
		return
	}
	t := task.NewAsyncTask()
	//查询电话通话摘要记录
	t.Add(func() {
		callInterviewModel := &models.AiCallRecordTask{}
		callInterviewModelNew := &models.AiCallRecord{}
		newCall, _ := callInterviewModelNew.GetByCallId(ctx, callId, studentUid)
		if newCall.CallId == callId {
			detailData.CallInterview = toOldCall(ctx, newCall)
		} else {
			detailData.CallInterview, err = callInterviewModel.GetByCallId(ctx, strconv.FormatInt(callId, 10))
		}
		//查询待办信息
		if detailData.CallInterview.RemindId > 0 {
			remindReqList := make([]muse.GetRemindReq, 0)
			remindMap := make(map[int64]muse.RemindInfo)
			remindMap, err = muse.BatchGetRemindMapByIds(ctx, append(remindReqList, muse.GetRemindReq{
				RemindId:  detailData.CallInterview.RemindId,
				CreateUid: detailData.CallInterview.PersonUid,
			}))
			if remindInfo, ok := remindMap[detailData.CallInterview.RemindId]; ok {
				detailData.TodoInfo = remindInfo
			}
		}
	})
	//查询课程信息
	t.Add(func() {
		courseField := []string{"courseId", "courseName"}
		lessonField := []string{"lessonId", "lessonName"}
		if detailData.CallRecord.CourseId > 0 {
			detailData.CourseInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, detailData.CallRecord.CourseId, courseField, lessonField)
		}
	})
	//查询lpc/辅导老师信息
	t.Add(func() {
		detailData.LpcInfo, err = mesh.GetDeviceInfoListByDeviceUids(ctx, detailData.CallRecord.DeviceUid)
	})
	//查询学员信息
	t.Add(func() {
		stuList := map[int64]dau.StudentInfo{}
		stuList, err = dau.GetStudents(ctx, []int64{studentUid}, []string{})
		if StuInfo, ok := stuList[studentUid]; !ok {
			detailData.StuInfo = StuInfo
		}
	})
	t.Add(func() {
		//获取音频链接
		url := ""
		url, err = kunpeng.GetFileUrl(ctx, detailData.CallRecord.RecordFile)
		if err != nil {
			return
		}
		detailData.Link = url
	})
	t.Wait()
	//组装返回数据

	detail = InterviewDetail{
		BaseInfo: InterviewBaseInfo{
			InterviewType:        int64(detailData.CallRecord.CallMode),
			InterviewTypeStr:     "电话",
			AssistantUid:         detailData.CallRecord.DeviceUid,
			AssistantName:        detailData.LpcInfo.NickName,
			CourseId:             detailData.CourseInfo.CourseId,
			CourseName:           detailData.CourseInfo.CourseName,
			CustomDeviceId:       detailData.CallRecord.ToPhone,
			CustomDeviceBelonger: detailData.WxBelongInfo.BelongerStr,
			CreateTime:           detailData.CallRecord.CreateTime,
		},
		AbstractInfo: AbstractInfo{
			Todo:    detailData.TodoInfo,
			Content: detailData.CallInterview.Abstract,
			Tags:    detailData.CallInterview.GetCommonAbstractTags(),
			Topic:   ai.GetTopic(detailData.CallRecord.SourceType),
		},
		InterviewInfo: CallDetail{
			Content:   detailData.CallInterview.Content,
			FileUrl:   detailData.Link,
			StartTime: detailData.CallRecord.CreateTime,
			CallId:    callId,
		},
	}
	return
}

func GetWxCallInterviewDetail(ctx *gin.Context, studentUid, callId int64) (detail InterviewDetail, err error) {
	//查询微信外呼记录
	detailData := InterViewDetailData{}
	wxCallInterviewModel := models.WxCallRecordNew{}
	wxCallInterview, err := wxCallInterviewModel.GetByCallId(ctx, callId, studentUid)
	if err != nil || wxCallInterview.Id <= 0 {
		return
	}
	detailData.WxCallRecord = wxCallInterview
	t := task.NewAsyncTask()
	//查询用户微信信息
	relationList, err := kunpeng.GetWxInfoByUid(ctx, kunpeng.GetWxInfoByUidReq{
		StaffUid: wxCallInterview.DeviceUid,
		Uids:     []int64{studentUid},
		AppId:    2,
	})
	if err != nil {
		return
	}
	userWxInfo := kunpeng.GetWxInfoByUidRsp{}
	for _, relation := range relationList {
		if relation.StudentUid == wxCallInterview.StudentUid {
			userWxInfo = relation
		}
	}
	//查询摘要信息
	t.Add(func() {
		callIdStr := fmt.Sprintf("qw_%d_%d", wxCallInterview.StudentUid, wxCallInterview.CallId)
		detailData.WxAbstract, err = aiturbo.PostSelectAbstractByCallId(ctx, callIdStr, wxCallInterview.DeviceUid)
	})
	//查询课程信息
	t.Add(func() {
		courseField := []string{"courseId", "courseName"}
		lessonField := []string{"lessonId", "lessonName"}
		if wxCallInterview.CourseId > 0 {
			detailData.CourseInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, wxCallInterview.CourseId, courseField, lessonField)
		}
	})
	//查询lpc/辅导老师信息
	t.Add(func() {
		detailData.LpcInfo, err = mesh.GetDeviceInfoListByDeviceUids(ctx, wxCallInterview.DeviceUid)
	})
	//查询微信归属人
	t.Add(func() {
		wxBelongInfo := &kunpeng.GetBelongerRes{}
		wxBelongInfo, err = kunpeng.GetBelonger(ctx, strconv.FormatInt(detailData.WxCallRecord.DeviceUid, 10), strconv.FormatInt(studentUid, 10), wxCallInterview.ToRemoteId)
		detailData.WxBelongInfo = *wxBelongInfo
	})
	//查询待办信息
	t.Add(func() {
		if wxCallInterview.RemindId > 0 {
			remindReq := make([]muse.GetRemindReq, 0)
			remindMap, err1 := muse.BatchGetRemindMapByIds(ctx, append(remindReq, muse.GetRemindReq{
				RemindId:  wxCallInterview.RemindId,
				CreateUid: wxCallInterview.PersonUid,
			}))
			if err1 != nil {
				return
			}
			if remindInfo, ok := remindMap[wxCallInterview.RemindId]; ok {
				detailData.TodoInfo = remindInfo
			}
		}
	})
	//获取音频链接
	t.Add(func() {
		//获取音频链接
		url := ""
		url, err = kunpeng.GetFileUrl(ctx, wxCallInterview.RecordFile)
		if err != nil {
			return
		}
		detailData.Link = url
	})

	t.Wait()
	//组装返回数据
	detail = InterviewDetail{
		BaseInfo: InterviewBaseInfo{
			InterviewType:        detailData.WxCallRecord.MsgType,
			InterviewTypeStr:     defines.WxCallModeMap[detailData.WxCallRecord.MsgType],
			AssistantUid:         detailData.WxCallRecord.DeviceUid,
			AssistantName:        detailData.LpcInfo.NickName,
			CourseId:             detailData.CourseInfo.CourseId,
			CourseName:           detailData.CourseInfo.CourseName,
			CustomDeviceId:       userWxInfo.WeixinNickName,
			CustomDeviceBelonger: detailData.WxBelongInfo.BelongerStr,
			CreateTime:           detailData.WxCallRecord.CreateTime,
		},
		AbstractInfo: AbstractInfo{
			Todo:    detailData.TodoInfo,
			Content: detailData.WxAbstract.Content,
			Tags:    detailData.WxAbstract.Tags,
		},
		InterviewInfo: CallDetail{
			Content:   detailData.WxCallRecord.Content,
			FileUrl:   detailData.Link,
			StartTime: detailData.WxCallRecord.StartTime,
			CallId:    detailData.WxCallRecord.CallId,
		},
	}
	return
}

func toOldCall(ctx *gin.Context, callRecord models.AiCallRecord) models.AiCallRecordTask {
	task := models.AiCallRecordTask{}
	task.CallId = utils2.ToString(callRecord.CallId)
	task.SourceType = callRecord.SourceType
	task.CallMode = callRecord.CallMode
	task.FromUid = callRecord.DeviceUid
	task.DeviceUid = callRecord.DeviceUid
	task.ToUid = callRecord.StudentUid
	task.PersonUid = callRecord.PersonUid
	task.Content = callRecord.Content
	task.Status = callRecord.Status
	task.StartTime = callRecord.StartTime
	task.StopTime = callRecord.StopTime
	task.Duration = callRecord.Duration
	task.CallType = callRecord.CallType
	task.CourseId = callRecord.CourseId
	task.RecordFile = callRecord.RecordFile
	task.ResourseType = callRecord.ResourceType
	task.Abstract = callRecord.Abstract
	task.Tags = callRecord.Tags
	task.CreateTime = callRecord.CreateTime
	task.UpdateTime = callRecord.UpdateTime
	task.RemindId = callRecord.RemindId
	if strings.Trim(callRecord.Content, " ") != "" {
		content, _err := helpers.BaiduBucket2.DownloadContent(ctx, callRecord.Content)
		if _err != nil {
			zlog.Warnf(ctx, "toOldCall DownloadContent fail,callid=%v,content=%v", callRecord.CallId, callRecord.Content)
		}
		task.Content = string(content)

	}

	return task
}

func GetCallInterviewAiAbstract(ctx *gin.Context, abstractReqList []AiAbstractParamItem) (map[int64]string, error) {
	ch := make(chan models.AiCallRecord, len(abstractReqList))
	wg := &sync.WaitGroup{}
	for _, req := range abstractReqList {
		wg.Add(1)
		go func(req AiAbstractParamItem) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetCallInterviewAiAbstract panic, err:%s", r)
				}
			}()
			defer wg.Done()
			newCall, err := models.AiCallRecordRef.GetByCallId(ctx, req.CallId, req.StudentUid)
			if err != nil {
				zlog.Warnf(ctx, "GetCallInterviewAiAbstract GetByCallId failed, CallId:%s, err:%s", req.CallId, err)
				return
			}

			ch <- newCall
		}(req)
	}
	wg.Wait()
	close(ch)

	result := make(map[int64]string, len(abstractReqList))
	for abstractInfo := range ch {
		result[abstractInfo.CallId] = abstractInfo.Abstract
	}
	return result, nil
}
