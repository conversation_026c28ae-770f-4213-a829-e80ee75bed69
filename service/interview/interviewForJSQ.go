package interview

import (
	"assistantdeskgo/dto/dtodeskviewdetail"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

// AddJSQInterview 金丝雀用户透传信息写入维系详情表
func AddJSQInterview(ctx *gin.Context, studentUid int64, courseIds []int64, content string, operatorUid int64) (ids, cIds []int64, err error) {
	ids = make([]int64, 0)
	cIds = make([]int64, 0)

	insertData := make([]models.StudentInterview, 0)
	interviewTime := time.Now().Unix()
	interviewType := models.TftcJSQValue

	for _, courseId := range courseIds {
		insertData = append(insertData, models.StudentInterview{
			StudentUID:    studentUid,
			InterviewTime: interviewTime,
			Type:          interviewType,
			Content:       content,
			Deleted:       0,
			CreateTime:    interviewTime,
			UpdateTime:    interviewTime,
			OperatorUID:   operatorUid,
			Operator:      "",
			CourseID:      courseId,
		})
	}

	ids, cIds, err = models.StuInterviewRef.BatchCreate(ctx, insertData)
	if err != nil {
		return
	}
	return
}

// UpdateJSQInterview 金丝雀用户透传信息写入维系详情表更新
func UpdateJSQInterview(ctx *gin.Context, studentUid int64, content string, interviewIds []int64, uid int64) (ids, cIds []int64, err error) {
	ids = make([]int64, 0)
	cIds = make([]int64, 0)

	// 查记录
	res, err := models.StuInterviewRef.GetByIds(ctx, interviewIds)
	if err != nil {
		return
	}

	resMap := make(map[int64]models.StudentInterview, 0)
	for _, item := range res {
		resMap[item.ID] = item
	}

	updateIds := make([]int64, 0)
	for _, updateId := range interviewIds {
		old, ok := resMap[updateId]
		if !ok {
			zlog.Warnf(ctx, "UpdateJSQInterview update id err,%v", updateId)
			continue
		}

		if old.StudentUID != studentUid {
			zlog.Warnf(ctx, "UpdateJSQInterview update studentUid err,%v,%v", studentUid, updateId)
			continue
		}

		if old.Type != models.TftcJSQValue {
			zlog.Warnf(ctx, "UpdateJSQInterview type err,%v,%v", studentUid, updateId)
			continue
		}

		updateIds = append(updateIds, updateId)
		ids = append(ids, updateId)
		cIds = append(cIds, old.CourseID)
	}

	err = models.StuInterviewRef.UpdateByMap(ctx, updateIds, content)
	if err != nil {
		return
	}
	return
}

func GetJSQInterviewByIds(ctx *gin.Context, interviewIds []int64) (data []dtodeskviewdetail.GetInterViewItem, err error) {
	data = make([]dtodeskviewdetail.GetInterViewItem, 0)
	// 查记录
	res, err := models.StuInterviewRef.GetByIds(ctx, interviewIds)
	if err != nil {
		return
	}
	for _, item := range res {
		if item.Type != models.TftcJSQValue {
			zlog.Warnf(ctx, "GetJSQInterviewByIds type error:%v", item.ID)
			continue
		}

		data = append(data, dtodeskviewdetail.GetInterViewItem{
			Id:         item.ID,
			CourseId:   item.CourseID,
			StudentUid: item.StudentUID,
			Type:       int64(item.Type),
			Uid:        item.OperatorUID,
			Content:    item.Content,
			CreateTime: item.CreateTime,
			UpdateTime: item.UpdateTime,
		})
	}
	return
}
