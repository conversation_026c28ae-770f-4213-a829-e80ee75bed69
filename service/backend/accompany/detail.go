package accompany

import (
	dtoaccompany "assistantdeskgo/dto/accompany"
	"assistantdeskgo/models"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

func GetAccompanyDetail(ctx *gin.Context, req dtoaccompany.GetAccompanyDetailReq) (*dtoaccompany.GetAccompanyDetailRsp, error) {
	accompanyDetails, err := models.LessonAccompanyingDetailRef.GetAllLessonDetail(ctx, req.CourseId, req.LessonId)
	if err != nil {
		return nil, errors.WithMessagef(err, "[GetAccompanyDetail] GetAllLessonDetail failed")
	}

	detailInfos := make([]*dtoaccompany.AccompanyDetailInfo, 0)
	for _, detail := range accompanyDetails {
		tmpDetail := detail
		detailInfos = append(detailInfos, &dtoaccompany.AccompanyDetailInfo{
			CourseId:  tmpDetail.CourseId,
			LessonId:  tmpDetail.LessonId,
			StartTime: tmpDetail.StartTime,
			EndTime:   tmpDetail.EndTime,
			BanxueId:  tmpDetail.BanxueId,
		})
	}
	return &dtoaccompany.GetAccompanyDetailRsp{
		Details: detailInfos,
	}, nil
}
