package accompany

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/dal"
	dtoaccompany "assistantdeskgo/dto/accompany"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
)

func GetTagInfo(ctx *gin.Context, req dtoaccompany.GetTagInfoReq) (*dtoaccompany.GetTagInfoRsp, error) {
	courseId := req.CourseId

	var tagInfoConfList map[string]dtopreclass.TagInfoConf
	if err := assistantdesk.GetKeyForJsonMap(ctx, assistantdesk.CourseBanxueTagKey, &tagInfoConfList); err != nil {
		zlog.Warnf(ctx, "[GetTagInfo] GetKeyForJsonMap failed, key: %s, err: %+v", assistantdesk.CourseBanxueTagKey, err)
		return &dtoaccompany.GetTagInfoRsp{
			Tags: []*dtoaccompany.TagInfoDto{},
		}, nil
	} else if len(tagInfoConfList) == 0 {
		zlog.Warnf(ctx, "[GetTagInfo] 未获取到[%s]配置", assistantdesk.CourseBanxueTagKey)
		return &dtoaccompany.GetTagInfoRsp{
			Tags: []*dtoaccompany.TagInfoDto{},
		}, nil
	}

	// 不传课程 id，返回全量
	if courseId == 0 {
		tagInfos := make([]*dtoaccompany.TagInfoDto, 0)
		for key, conf := range tagInfoConfList {
			tmpKey := key
			tmpConf := conf
			tagInfos = append(tagInfos, &dtoaccompany.TagInfoDto{
				Label: tmpConf.Label,
				Value: tmpKey,
			})
		}
		sort.Slice(tagInfos, func(i, j int) bool {
			return tagInfos[i].Value < tagInfos[j].Value
		})
		return &dtoaccompany.GetTagInfoRsp{
			Tags: tagInfos,
		}, nil
	}

	// 现在先不支持权益包的课程，权益包的需要走额外的接口去判断和获取服务模式
	courseInfo, err := tower.GetCourseInfo(ctx, courseId)
	if err != nil {
		zlog.Warnf(ctx, "[GetTagInfo] GetCourseInfo failed, courseId: %d, err: %+v", courseId, err)
		return &dtoaccompany.GetTagInfoRsp{
			Tags: []*dtoaccompany.TagInfoDto{},
		}, nil
	}

	serviceType := 0
	priceTagInfo, err := tower.GetPriceTagInfo(ctx, tower.GetPriceTagInfoReq{
		Id: courseInfo.CoursePriceTag,
	})
	if err != nil {
		zlog.Warnf(ctx, "[GetTagInfo] GetPriceTagInfo failed, courseInfo: %s, err: %+v", fwyyutils.MarshalIgnoreError(courseInfo), err)
	} else {
		serviceType = priceTagInfo.Info.ServiceType
	}

	transGradeId := int64(0)
	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, courseId, []string{
		"courseId",
		"mainGradeId",
	}, []string{
		"lessonId",
	})
	if err != nil {
		zlog.Warnf(ctx, "[GetTagInfo] GetCourseLessonInfoByCourseId failed, courseId: %d, err: %+v", courseId, err)
	} else {
		transGradeId = consts.CourseTransMap[courseLessonInfo.MainGradeId]
	}

	tagInfos := make([]*dtoaccompany.TagInfoDto, 0)
	for key, conf := range tagInfoConfList {
		tmpKey := key
		tmpConf := conf
		if !tmpConf.Enable {
			continue
		}

		if len(tmpConf.Seasons) > 0 && !utils.InArrayInt64(courseInfo.Season, tmpConf.Seasons) {
			continue
		}

		if len(tmpConf.ServiceTypes) > 0 && !utils.InArrayInt(serviceType, tmpConf.ServiceTypes) {
			continue
		}

		if len(tmpConf.Grades) > 0 && !utils.InArrayInt64(transGradeId, tmpConf.Grades) {
			continue
		}

		tagInfos = append(tagInfos, &dtoaccompany.TagInfoDto{
			Label: tmpConf.Label,
			Value: tmpKey,
		})
	}

	sort.Slice(tagInfos, func(i, j int) bool {
		return tagInfos[i].Value < tagInfos[j].Value
	})
	return &dtoaccompany.GetTagInfoRsp{
		Tags: tagInfos,
	}, nil
}
