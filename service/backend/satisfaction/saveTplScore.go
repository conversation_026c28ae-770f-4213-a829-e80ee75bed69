package satisfaction

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/dto/dtoSatisfactionCollect"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func SaveTplScore(ctx *gin.Context, req dtoSatisfactionCollect.SaveTplScoreRequest) error {
	if req.TplId <= 0 {
		return errors.New("wrong TplId")
	}

	if req.Score <= 0 || req.Score > 5 {
		return errors.New("wrong score")
	}

	var userInfo *userprofile.UserInfo
	var assistantUID int64
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}
	if userInfo != nil {
		assistantUID = int64(userInfo.StaffUid)
	}
	// 获取模板更新时间
	var businessKey string
	ret, err := models.ArkTemplateRef.GetById(ctx, req.TplId)
	if err != nil {
		return err
	}

	tm := time.Unix(ret.UpdateTime, 0)
	timeStr := tm.Format(time.RFC3339)
	businessKey = fmt.Sprintf("tpl_%v_%v", req.TplId, timeStr)

	_, err = models.SatisfactionCollectRef.Create(ctx, &models.SatisfactionCollect{
		SourceType:   models.SatisfactionCollectSourceType,
		BusinessKey:  businessKey,
		ScoreType:    models.SatisfactionCollectScoreType,
		Score:        req.Score,
		UserComment:  req.UserComment,
		ExtraID:      req.TplId,
		ExtraInfo:    "",
		AssistantUID: assistantUID,
		IsDeleted:    false,
	})
	if err != nil {
		return err
	}

	// 记录老师填写记录
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	_, err = helpers.RedisClient.HSet(ctx, fmt.Sprintf("assistantdesk::tips_time_%v_%v_%v", assistantUID, req.TplId, ret.UpdateTime), dateStr, 2)
	if err != nil {
		zlog.Warnf(ctx, "SaveTplScore Hset err:%v", err)
		return nil
	}
	hLen, err := helpers.RedisClient.HLen(ctx, fmt.Sprintf("assistantdesk::tips_time_%v_%v_%v", assistantUID, req.TplId, ret.UpdateTime))
	if err != nil {
		zlog.Warnf(ctx, "SaveTplScore HLen err:%v", err)
		return nil
	}
	if hLen == 1 {
		_, err = helpers.RedisClient.Expire(ctx, fmt.Sprintf("assistantdesk::tips_time_%v_%v_%v", assistantUID, req.TplId, ret.UpdateTime), 60*60*24*6)
		if err != nil {
			zlog.Warnf(ctx, "SaveTplScore Expire err:%v", err)
			return nil
		}

	}
	return nil
}
