package public

const (
	CardScenePlayback = 2
)

type CardInfo struct {
	IsShow               bool                `json:"isShow"`
	CanHide              bool                `json:"canHide"`
	DefaultPic           []map[string]string `json:"defaultPic"`
	MaxPicCnt            int                 `json:"maxPicCnt"`
	DefaultTitle         []string            `json:"defaultTitle"`         //标题
	CardVariable         []string            `json:"cardVariable"`         // 卡片支持的变量名
	IntroductionVariable []string            `json:"introductionVariable"` //描述支持的变量
	DefaultIntroduction  []string            `json:"defaultIntroduction"`  //副标题
}

var SceneCardMap = map[int]CardInfo{
	CardScenePlayback: CardInfo{
		CanHide: true,
		DefaultPic: []map[string]string{
			{
				"url": "https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c",
			},
			{
				"url": "https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc",
			},
			{
				"url": "https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373",
			},
		},
		MaxPicCnt:            5,
		DefaultTitle:         []string{"同学快来看回放"},
		CardVariable:         []string{"学生名"},
		IntroductionVariable: []string{"学生名"},
		DefaultIntroduction:  []string{"点击这里快速进入课堂"},
	},
}

func FormatCardInfo(isShow bool) CardInfo {
	return CardInfo{
		IsShow:  isShow,
		CanHide: true,
		DefaultPic: []map[string]string{
			{
				"url": "https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c",
			},
			{
				"url": "https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc",
			},
			{
				"url": "https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373",
			},
		},
		MaxPicCnt:            5,
		DefaultTitle:         []string{"你报名的课程就要开始上课了"},
		CardVariable:         []string{"学生名"},
		IntroductionVariable: []string{"学生名"},
		DefaultIntroduction:  []string{"点击这里快速进入课堂"},
	}
}

func FormatCardInfoByScene(scene int, isShow bool) CardInfo {
	card, ok := SceneCardMap[scene]
	if !ok {
		return FormatCardInfo(isShow)
	}
	card.IsShow = isShow
	return card
}
