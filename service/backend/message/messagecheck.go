package message

import (
	"assistantdeskgo/api/muse/message"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"errors"
	"github.com/gin-gonic/gin"
)

func Check(ctx *gin.Context, req dtomessage.MessageCheckReq) (rsp *message.MultiCheckRsp, err error) {
	err = checkParam(req)
	if err != nil {
		return nil, err
	}

	userInfo := components.GetUserInfo(ctx)
	param := message.MultiCheckReq{
		AssistantUid:     userInfo.SelectedBusinessUid,
		PersonUid:        int64(userInfo.StaffUid),
		MessageCount:     req.MessageCount,
		IsSkipTaskCnt:    req.IsSkipTaskCnt,
		WxIdCount:        req.WxIdCount,
		GroupCount:       req.GroupCount,
		Ability:          req.SubType,
		TaskType:         req.TaskType,
		CourseId:         req.CourseId,
		StudentUid2WxIds: req.StudentUid2WxIds,
	}
	rsp, err = message.MultiCheck(ctx, param)
	return
}

func checkParam(req dtomessage.MessageCheckReq) error {
	sendType := defines.GetSendTypeBySubType(req.SubType)
	if sendType == 0 {
		return errors.New("不支持的群发类型")
	}
	return nil
}
