package message

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sort"
	"strings"
)

func checkConditionalParams(ctx *gin.Context, req *dtomessage.ListReq) (userInfo *userprofile.UserInfo, err error) {
	//if req.Type != dtomessage.IndividualCreation && req.Type != dtomessage.TeamSharing {
	//	err = components.DefaultError("类型参数错误")
	//	return nil, err
	//}

	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return nil, err
	}

	return userInfo, err
}

func List(ctx *gin.Context, req *dtomessage.ListReq) (rsp *dtomessage.ListRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return nil, err
	}
	_, err = checkConditionalParams(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "touchmessagegroup List checkConditionalParams failed, req: %+v", req)
		return nil, err
	}

	permissionGroupIdList, err := GetPermissionGroupIds(ctx, int64(userInfo.UserId), req.PermissionGroupId)
	if err != nil {
		return nil, err
	}

	tmpGroupIds := make([]int64, 0)
	if req.GroupId > 0 {
		tmpGroupIds = append(tmpGroupIds, req.GroupId)
	}
	messageGroups, total, err := models.MessageGroupRef.GetListByCond(ctx, req.FolderId, req.GroupName, tmpGroupIds,
		req.Authority, int64(userInfo.UserId), permissionGroupIdList, req.CreateName, req.StartTime, req.EndTime, req.Pn, req.Rn)
	if err != nil {
		zlog.Warnf(ctx, "touchmessagegroup List GetListByCond failed, req: %+v", req)
		return nil, err
	}

	var folderIds []int64

	for _, messageGroup := range messageGroups {
		folderIds = append(folderIds, messageGroup.FolderId)
	}

	sort.Slice(messageGroups, func(i, j int) bool {
		return messageGroups[i].CreateTime-messageGroups[j].CreateTime > 0
	})

	folders, err := models.MessageGroupFolderRef.GetFoldersByIds(ctx, folderIds)
	if err != nil {
		zlog.Warnf(ctx, "touchmessagegroup List GetFoldersByIds failed, req: %+v", req)
		return nil, err
	}

	var listFolderMessageGroups []dtomessage.ListFolderMessageGroup

	groupIdMap := make(map[int64]struct{})
	for _, messageGroup := range messageGroups {
		groupIdMap[messageGroup.PermissionGroupId] = struct{}{}
	}

	groupIds := make([]int64, 0)
	for groupId, _ := range groupIdMap {
		groupIds = append(groupIds, groupId)
	}

	permissionGroups, _ := mesh.GetGroupDetailByIds(ctx, groupIds)
	permissionGroupNames := make(map[int64]string)
	for permissionGroupId, permissionGroupDetail := range permissionGroups {
		groupNames := make([]string, 0)
		var node = permissionGroupDetail.ParentTree.Children
		groupNames = append(groupNames, permissionGroupDetail.ParentTree.Name)
		for node != nil {
			groupNames = append(groupNames, node.Name)
			node = node.Children
		}
		permissionGroupNames[cast.ToInt64(permissionGroupId)] = strings.Join(groupNames, "/")
	}

	for _, messageGroup := range messageGroups {
		var folderName string
		for _, folder := range folders {
			if folder.ID != messageGroup.FolderId {
				continue
			} else {
				folderName = folder.Name
			}
		}

		if folderName != "" {
			canDelete := 0
			if messageGroup.CreateUid == int64(userInfo.UserId) {
				canDelete = 1
			}
			listFolderMessageGroup := dtomessage.ListFolderMessageGroup{
				FolderId:         messageGroup.FolderId,
				FolderName:       folderName,
				GroupId:          messageGroup.ID,
				GroupName:        messageGroup.Name,
				AuthorityDesc:    defines.AuthorityTextMap[messageGroup.Authority],
				OrganizationDesc: permissionGroupNames[messageGroup.PermissionGroupId],
				Desc:             messageGroup.Desc,
				CanDelete:        canDelete,
				CreateName:       messageGroup.CreateName,
				CreateUid:        messageGroup.CreateUid,
				CreateTime:       messageGroup.CreateTime,
				CreateTimeStr:    utils.TimeStampFormat(messageGroup.CreateTime),
			}
			listFolderMessageGroups = append(listFolderMessageGroups, listFolderMessageGroup)
			continue
		}

	}

	pageInfo := dtomessage.PageInfo{
		Pn:    req.Pn,
		Rn:    req.Rn,
		Total: total,
	}

	rsp = &dtomessage.ListRsp{
		ListFolderMessageGroups: listFolderMessageGroups,
		PageInfo:                pageInfo,
	}

	return rsp, err
}
