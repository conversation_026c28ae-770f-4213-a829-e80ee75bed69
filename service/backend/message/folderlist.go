package message

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func FolderList(ctx *gin.Context, req *dtomessage.FolderListReq) (rsp *dtomessage.FolderListRsp, err error) {
	var userInfo *userprofile.UserInfo
	if userInfo, err = middleware.GetLoginUserInfo(ctx); err != nil {
		return
	}

	var folderList []models.MessageGroupFolder
	folderList, err = models.MessageGroupFolderRef.GetFoldersByUid(ctx, int64(userInfo.UserId))
	if err != nil {
		zlog.Warnf(ctx, "touchmessagegroup FolderList GetFoldersByUid failed, UserId: %+v", userInfo.UserId)
		return nil, err
	}
	folderIdList := []int64{}

	for _, folder := range folderList {
		folderIdList = append(folderIdList, folder.ID)
	}
	messageGroupMap := map[int64][]models.MessageGroup{}
	if len(folderList) > 0 {
		messageGroupList, err := models.MessageGroupRef.GetByFolderIds(ctx, folderIdList)
		if err != nil {
			return nil, err
		}
		for _, messageGroup := range messageGroupList {
			subGroupMessageList := messageGroupMap[messageGroup.FolderId]
			subGroupMessageList = append(subGroupMessageList, messageGroup)
			messageGroupMap[messageGroup.FolderId] = subGroupMessageList
		}

	}

	if req.ListType == defines.ListTypeForAll {
		folderIdMap := make(map[int64]struct{}, 0)
		for _, folderInfo := range folderList {
			folderIdMap[folderInfo.ID] = struct{}{}
		}

		var groupIds []int64
		groupIds, err = GetPermissionGroupIds(ctx, int64(userInfo.UserId), 0)
		if err != nil {
			return

		}

		var groupList []models.MessageGroup
		if groupList, err = models.MessageGroupRef.GetListByPermissionGroupIds(ctx, groupIds, []string{"id", "folder_id"}); err != nil {
			zlog.Warnf(ctx, "touchmessagegroup FolderList GetListByPermissionGroupIds failed, groupIds: %+v", groupIds)
			return nil, err
		}

		for _, groupInfo := range groupList {
			folderIdMap[groupInfo.FolderId] = struct{}{}
		}
		folderIds := make([]int64, 0)
		for folderId, _ := range folderIdMap {
			folderIds = append(folderIds, folderId)
		}

		if folderList, err = models.MessageGroupFolderRef.GetFoldersByIds(ctx, folderIds); err != nil {
			zlog.Warnf(ctx, "touchmessagegroup FolderList GetFoldersByIds failed, folderIds: %+v", folderIds)
			return nil, err
		}

	}

	folderInfoList := make([]dtomessage.FolderInfo, 0)

	for _, folder := range folderList {
		if len(messageGroupMap[folder.ID]) >= 50 {
			zlog.Warnf(ctx, "Folder has more than 50 message group,folderId=%v", folder.ID)
			continue
		}

		folderInfo := dtomessage.FolderInfo{
			Id:   folder.ID,
			Name: folder.Name,
		}
		folderInfoList = append(folderInfoList, folderInfo)
	}

	rsp = &dtomessage.FolderListRsp{
		ListFolderMessageGroups: folderInfoList,
	}

	return rsp, nil

}
