package message

import (
	"assistantdeskgo/api/lpcmsg"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"github.com/gin-gonic/gin"
)

func UpdateTaskStatus(ctx *gin.Context, req dtomessage.UpdateTaskStatusReq) (rsp *dtomessage.UpdateTaskStatusRsp, err error) {
	userInfo := components.GetUserInfo(ctx)
	updateRet, err := lpcmsg.UpdateTaskStatus(ctx, userInfo.SelectedBusinessUid, req.MainTaskId, req.Operate)
	if err != nil {
		err = components.DefaultError(err.Error())
		return
	}

	rsp = &dtomessage.UpdateTaskStatusRsp{
		MainTaskId: updateRet.MainTaskId,
	}
	if updateRet.IsSuccess {
		rsp.IsSuccess = 1
	}
	return
}
