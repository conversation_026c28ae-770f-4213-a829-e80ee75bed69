package message

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestPermission(t *testing.T) {
	env.SetRootPath("../../../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()
	ctx := gin.CreateNewContext(engine)

	//groupIds, err := GetParentPermissionGroupIds(ctx, 3000039349)
	//fmt.Println(groupIds)
	tree, err := GetOrganizationTree(ctx, 3000039349)
	fmt.Println(tree, err)
}
