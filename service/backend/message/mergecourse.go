package message

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	ServiceTypeIn     = 1
	ServiceTypeBefore = 2
	ServiceTypeAfter  = 3
)

func SameTplCourse(ctx *gin.Context, param dtomessage.SameTplCourseReq) (ret dtomessage.SameTplCourseRsp, err error) {
	// 获取课程信息
	userInfo := components.GetUserInfo(ctx)
	courseInfos, courseIds, err := getAssistantCourseInfo(ctx, param.Year, userInfo.SelectedBusinessUid, param.ServiceType)
	if err != nil {
		return
	}
	if len(courseIds) <= 0 {
		return
	}
	// 获取每个课程模板，相同模板下课程
	courseTpl, sameTplCourses, err := getSameTplCourse(ctx, courseIds)
	if err != nil {
		return
	}
	// 获取相同cpu课程
	sameCpuCourses := getSameCpuCourse(courseInfos)
	// 相同cpu并且相同模板的课程聚合
	sameCourse := getSameTplCpuCourse(courseIds, sameTplCourses, sameCpuCourses, courseTpl, courseInfos)
	// 格式化信息
	sameTplCourseCards := formatSameCourseCard(ctx, courseInfos, sameCourse)
	ret.SameTplCourse = sameTplCourseCards
	return ret, nil
}

func formatSameCourseCard(ctx *gin.Context, courseInfos map[int64]dal.CourseLessonInfo, sameCourse map[int64][]int64) map[int64][]dtomessage.SameTplCourseInfo {
	ret := map[int64][]dtomessage.SameTplCourseInfo{}
	teacherMap, err := getAllCourseTeacherInfos(ctx, courseInfos)
	if err != nil {
		// 主讲信息获取失败不影响主流程，告警但不返回err
		zlog.Warnf(ctx, "getAllCourseTeacherInfos_error")
	}
	for courseId, sameCourseIds := range sameCourse {
		sameCourseList := make([]dtomessage.SameTplCourseInfo, 0)
		for _, sameCourseId := range sameCourseIds {
			if sameCourseId == courseId {
				// 如果相同模板课程为当前课程，跳过
				continue
			}
			if len(courseInfos[sameCourseId].LessonList) != len(courseInfos[courseId].LessonList) {
				// 如果章节数量不一致，跳过
				continue
			}
			var teacherNames []string
			for _, tUid := range courseInfos[sameCourseId].TeacherUids {
				if _, ok := teacherMap[tUid]; ok && teacherMap[tUid] != "" {
					teacherNames = append(teacherNames, teacherMap[tUid])
				}
			}
			sameCourseInfo := dtomessage.SameTplCourseInfo{
				CourseId:        sameCourseId,
				CourseName:      courseInfos[sameCourseId].CourseName,
				CourseTime:      courseInfos[sameCourseId].OnlineFormatTimeAll,
				SameLessonIds:   getSameLessons(courseInfos[courseId].LessonList, courseInfos[sameCourseId].LessonList),
				TeacherNameList: teacherNames,
			}
			sameCourseList = append(sameCourseList, sameCourseInfo)
		}
		ret[courseId] = sameCourseList
	}
	return ret
}

func getAllCourseTeacherInfos(ctx *gin.Context, courseInfos map[int64]dal.CourseLessonInfo) (map[int64]string, error) {
	uidNameMap := make(map[int64]string)
	var teacherUids []int64
	for _, course := range courseInfos {
		teacherUids = append(teacherUids, course.TeacherUids...)
	}
	teacherFields := []string{"teacherUid", "teacherName", "teacherAvatar"}
	teacherMap, err := dau.GetTeachers(ctx, teacherUids, teacherFields)
	if err != nil {
		return uidNameMap, err
	}
	for teacherUid, info := range teacherMap {
		uidNameMap[teacherUid] = info.TeacherName
	}
	return uidNameMap, nil
}

func getSameLessons(curLessonList map[int]dal.LessonInfo, compareLessonList map[int]dal.LessonInfo) map[int64][]int64 {
	sameLessons := map[int64][]int64{}
	compareLessonMap := map[int][]dal.LessonInfo{}
	for _, lesson := range compareLessonList {
		compareLessonMap[lesson.OutlineId] = append(compareLessonMap[lesson.OutlineId], lesson)
	}
	for _, lesson := range curLessonList {
		if len(compareLessonMap[lesson.OutlineId]) <= 0 {
			continue
		}
		for _, sameLesson := range compareLessonMap[lesson.OutlineId] {
			if lesson.LessonName != sameLesson.LessonName {
				// 如果当前章节名称不等于同课程大纲下的章节名称，则跳过
				continue
			}
			sameLessons[int64(lesson.LessonId)] = append(sameLessons[int64(lesson.LessonId)], int64(sameLesson.LessonId))
		}

	}
	return sameLessons
}

func getSameTplCpuCourse(courseIds []int64, sameTplCourses map[int64][]int64, sameCpuCourses map[int64][]int64, courseTpl map[int64]int64, courseInfos map[int64]dal.CourseLessonInfo) map[int64][]int64 {
	sameCourse := map[int64][]int64{}
	for _, courseId := range courseIds {
		sameCourse[courseId] = utils.Int64SliceIntersect(sameTplCourses[courseTpl[courseId]], sameCpuCourses[courseInfos[courseId].CpuId])
	}
	return sameCourse
}

func getSameCpuCourse(courseInfos map[int64]dal.CourseLessonInfo) map[int64][]int64 {
	sameCouCourse := map[int64][]int64{}
	for courseId, courseInfo := range courseInfos {
		sameCouCourse[courseInfo.CpuId] = append(sameCouCourse[courseInfo.CpuId], courseId)
	}
	return sameCouCourse
}

func getSameTplCourse(ctx *gin.Context, courseIds []int64) (courseTpl map[int64]int64, sameTplCourses map[int64][]int64, err error) {
	courseTplBinds, err := models.CourseBind.GetTplIdByCourseIds(ctx, courseIds)
	if err != nil {
		return
	}
	courseTpl = make(map[int64]int64)
	sameTplCourses = make(map[int64][]int64)
	for _, bindInfo := range courseTplBinds {
		courseTpl[bindInfo.CourseId] = bindInfo.TplId
		sameTplCourses[bindInfo.TplId] = append(sameTplCourses[bindInfo.TplId], bindInfo.CourseId)
	}
	return
}

func getAssistantCourseInfo(ctx *gin.Context, year int64, assistantUid int64, serviceType int64) (courseInfos map[int64]dal.CourseLessonInfo, courseIds []int64, err error) {
	courseMap, err := tower.GetCourseBindByDeviceUid(ctx, []int{int(assistantUid)}, year)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseBindByDeviceUid err:%s", err.Error())
		return
	}

	for courseId := range courseMap.CourseBindData {
		courseIds = append(courseIds, courseId)
	}
	if len(courseIds) <= 0 {
		return
	}

	courseIds, err = GetServiceClassIds(ctx, courseIds, serviceType)
	if err != nil {
		zlog.Warnf(ctx, "GetServiceClassIds err:%s", err.Error())
		return
	}
	if len(courseIds) <= 0 {
		return
	}

	courseFields := []string{"courseId", "cpuId", "courseName", "onlineFormatTimeAll", "mainSubjectId", "learnSeason", "teacherUids"}
	lessonFields := []string{"lessonId", "outlineId", "lessonName"}
	courseInfos, err = dal.GetCourseLessonInfoByCourseIds(ctx, courseIds, courseFields, lessonFields)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseLessonInfoByCourseIds err:%s", err.Error())
		return
	}
	return courseInfos, courseIds, nil
}

func GetServiceClassIds(ctx *gin.Context, courseIds []int64, serviceType int64) ([]int64, error) {
	courseExpireTimeMap, err := tower.GetBatchExpireTimeByCourseIds(ctx, courseIds)
	if err != nil {
		zlog.Warnf(ctx, "GetBatchExpireTimeByCourseIds err:%s", err.Error())
		return nil, err
	}

	now := time.Now().Unix()
	serviceTypeCourseIds := map[int64][]int64{}
	for _, expire := range courseExpireTimeMap {
		if expire.ExpireTimeStart > now {
			serviceTypeCourseIds[ServiceTypeBefore] = append(serviceTypeCourseIds[ServiceTypeBefore], expire.CourseId)
		} else if expire.ExpireTime < now {
			serviceTypeCourseIds[ServiceTypeAfter] = append(serviceTypeCourseIds[ServiceTypeAfter], expire.CourseId)
		} else {
			serviceTypeCourseIds[ServiceTypeIn] = append(serviceTypeCourseIds[ServiceTypeIn], expire.CourseId)
		}
	}

	return serviceTypeCourseIds[serviceType], nil
}
