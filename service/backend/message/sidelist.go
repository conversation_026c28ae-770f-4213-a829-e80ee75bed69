package message

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"strings"
)

func checkParams(ctx *gin.Context, req *dtomessage.SideListReq) (userInfo *userprofile.UserInfo, err error) {
	if req.Type != defines.AuthorityForPersonal && req.Type != defines.AuthorityForGroup {
		err = components.DefaultError("类型参数错误")
		return nil, err
	}

	if len(req.AvailableRange) == 0 {
		req.AvailableRange = defines.AvailableRangeForStudentLevel
	}

	if req.AvailableRange != defines.AvailableRangeForStudentLevel && req.AvailableRange != defines.AvailableRangeForGeneral {
		err = components.DefaultError("使用范围参数错误")
		return nil, err
	}

	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return nil, err
	}

	return userInfo, err
}

func SideList(ctx *gin.Context, req *dtomessage.SideListReq) (rsp *dtomessage.SideListRsp, err error) {

	userInfo, err := checkParams(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "touchmessagegroup SideList checkParams failed, req: %+v, err: %+v", req, err)
		return nil, err
	}
	switch req.Type {
	case defines.AuthorityForPersonal:
		rsp, err = GetIndividualCreation(ctx, req, userInfo.UserId)
		if err != nil {
			err = components.DefaultError("获取个人创建列表失败")
			return nil, err
		}
	case defines.AuthorityForGroup:
		rsp, err = GetTeamSharing(ctx, req, userInfo.UserId)
		if err != nil {
			err = components.DefaultError("获取团队共享列表失败")
			return nil, err
		}
	default:
		err = components.DefaultError("类型参数错误")
		return nil, err
	}
	return rsp, err
}

func GetIndividualCreation(ctx *gin.Context, req *dtomessage.SideListReq, userId int) (rsp *dtomessage.SideListRsp, err error) {

	folderList, total, err := models.MessageGroupFolderRef.PageListByUser(ctx, int64(userId), req.Pn, req.Rn)
	if err != nil {
		return
	}
	var folderIds []int64
	var sideListFolders []dtomessage.SideListFolder
	for _, folder := range folderList {
		folderIds = append(folderIds, folder.ID)
	}
	messageGroups, err := models.MessageGroupRef.GetByFolderIds(ctx, folderIds)
	if err != nil {
		return nil, err
	}
	sort.Slice(messageGroups, func(i, j int) bool {
		return messageGroups[i].CreateTime-messageGroups[j].CreateTime > 0
	})

	var availableRange string
	if req.AvailableRange != defines.AvailableRangeForStudentLevel {
		availableRange = defines.AvailableRangeForGeneral
	}

	for _, folder := range folderList {
		var sideListMessageGroups []dtomessage.SideListMessageGroup
		for _, messageGroup := range messageGroups {
			if messageGroup.FolderId != folder.ID {
				continue
			}

			if len(availableRange) > 0 && messageGroup.AvailableRange != availableRange {
				continue
			}

			sideListMessageGroup := dtomessage.SideListMessageGroup{
				GroupId:   messageGroup.ID,
				GroupName: messageGroup.Name,
				Desc:      messageGroup.Desc,
			}
			sideListMessageGroups = append(sideListMessageGroups, sideListMessageGroup)
		}
		sideListFolder := dtomessage.SideListFolder{
			FolderId:      folder.ID,
			FolderName:    folder.Name,
			MessageGroups: sideListMessageGroups,
			Order:         folder.Order,
		}
		sideListFolders = append(sideListFolders, sideListFolder)
	}

	rsp = &dtomessage.SideListRsp{
		SideListFolders: sideListFolders,
		PageInfo: dtomessage.PageInfo{
			Pn:    req.Pn,
			Rn:    req.Rn,
			Total: total,
		},
	}

	return rsp, nil
}

func GetTeamSharing(ctx *gin.Context, req *dtomessage.SideListReq, userId int) (rsp *dtomessage.SideListRsp, err error) {

	folderIdList, messageGroups, err := getFolderAndGroupList(ctx, req, userId)

	if err != nil {
		return
	}
	if len(folderIdList) == 0 { //有条件，无权限则返回
		return
	}

	folderIdList = utils.FilterDuplicatesInt64(folderIdList)
	//获取文件夹列表
	folderList, total, err := models.MessageGroupFolderRef.PageListById(ctx, folderIdList, req.Pn, req.Rn)
	if err != nil {
		return
	}

	var availableRange string
	if req.AvailableRange != defines.AvailableRangeForStudentLevel {
		availableRange = defines.AvailableRangeForGeneral
	}
	sideListFolders := FormatMessageGroupFolder(messageGroups, folderList, availableRange)

	rsp = &dtomessage.SideListRsp{
		SideListFolders: sideListFolders,
		PageInfo: dtomessage.PageInfo{
			Pn:    req.Pn,
			Rn:    req.Rn,
			Total: total,
		},
	}
	return rsp, nil
}

// FormatMessageGroupFolder 格式化消息组文件夹信息
func FormatMessageGroupFolder(messageGroups []models.MessageGroup, folderList []models.MessageGroupFolder, availableRange string) []dtomessage.SideListFolder {
	sort.Slice(messageGroups, func(i, j int) bool {
		return messageGroups[i].CreateTime-messageGroups[j].CreateTime > 0
	})

	sideListFolders := make([]dtomessage.SideListFolder, 0)
	for _, folder := range folderList {
		var sideListMessageGroups []dtomessage.SideListMessageGroup
		for _, messageGroup := range messageGroups {
			if messageGroup.FolderId != folder.ID {
				continue
			}

			if len(availableRange) > 0 && messageGroup.AvailableRange != availableRange {
				continue
			}
			sideListMessageGroup := dtomessage.SideListMessageGroup{
				GroupId:   messageGroup.ID,
				GroupName: messageGroup.Name,
				Authority: messageGroup.Authority,
				Desc:      messageGroup.Desc,
			}
			sideListMessageGroups = append(sideListMessageGroups, sideListMessageGroup)
		}
		sideListFolder := dtomessage.SideListFolder{
			FolderId:      folder.ID,
			FolderName:    folder.Name,
			MessageGroups: sideListMessageGroups,
		}
		sideListFolders = append(sideListFolders, sideListFolder)
	}

	return sideListFolders
}

func getFolderAndGroupList(ctx *gin.Context, req *dtomessage.SideListReq, userId int) (folderIdList []int64, messageGroup []models.MessageGroup, err error) {
	organizationGroupIds, err := GetPermissionGroupIds(ctx, int64(userId), 0)
	if err != nil {
		return
	}

	if req.GroupId > 0 || strings.Trim(req.Name, " ") != "" {
		if req.GroupId > 0 {
			if len(organizationGroupIds) > 0 {
				if utils.InArrayInt64(req.GroupId, organizationGroupIds) {
					return nil, nil, errors.New("无该组织权限")
				}
			}
			messageGroup, err = models.MessageGroupRef.GetByIdAndTeam(ctx, req.GroupId)
		} else if strings.Trim(req.Name, " ") != "" {
			messageGroup, err = models.MessageGroupRef.ListByNameLike(ctx, req.Name, organizationGroupIds)
		}
		if err != nil {
			zlog.Warnf(ctx, "getFolderIdList fail,req=%v,err=%v", req, err)
			return
		}

		for _, groupInfo := range messageGroup {
			if len(organizationGroupIds) == 0 || utils.InArrayInt64(groupInfo.PermissionGroupId, organizationGroupIds) || (groupInfo.CreateUid == int64(userId) && groupInfo.Authority == models.AuthorityForPersonal) {
				folderIdList = append(folderIdList, groupInfo.FolderId)
			}
		}
	} else {
		folderIdList, err = models.MessageGroupRef.DistinctByGroupIdList(ctx, organizationGroupIds) //查团队
		if err != nil {
			zlog.Warnf(ctx, "getFolderIdList MessageGroupRef fail,organizationGroupIds=%v,err=%v", organizationGroupIds, err)
			return
		}

		messageGroup, err = models.MessageGroupRef.ListByFolderAndAuthority(ctx, folderIdList, organizationGroupIds)
		if err != nil {
			return
		}
		folderIdList = utils.FilterDuplicatesInt64(folderIdList)

	}
	return
}
