package message

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CheckMessageGroupPermission(ctx *gin.Context, req dtomessage.CheckMessageGroupPermissionReq) (*dtomessage.CheckMessageGroupPermissionRsp, error) {
	var messageGroups = make([]models.MessageGroup, 0)
	for _, authority := range req.Authorities {
		switch authority {
		case defines.AuthorityForGroup:
			permissionGroupIdList, err := GetPermissionGroupIds(ctx, req.CreatorId, req.PermissionGroupId)
			if err != nil {
				// 这里不返回，是还需要判断个人场景是否存在（err 存在无权限）
				zlog.Warnf(ctx, "[CheckMessageGroupPermission] GetPermissionGroupIds failed, authority: %d, req: %+v", authority, req)
			}

			tmpMessageGroups, _, err := models.MessageGroupRef.GetListByCond(ctx, 0, "", req.GroupIds,
				authority, req.CreatorId, permissionGroupIdList, "", 0, 0, 0, 0)
			if err != nil {
				zlog.Warnf(ctx, "[CheckMessageGroupPermission] List GetListByCond failed, authority: %d, req: %+v", authority, req)
				return nil, err
			}
			messageGroups = append(messageGroups, tmpMessageGroups...)
		case defines.AuthorityForPersonal:
			tmpMessageGroups, _, err := models.MessageGroupRef.GetListByCond(ctx, 0, "", req.GroupIds,
				authority, req.CreatorId, nil, "", 0, 0, 0, 0)
			if err != nil {
				zlog.Warnf(ctx, "[CheckMessageGroupPermission] List GetListByCond failed, authority: %d, req: %+v", authority, req)
				return nil, err
			}
			messageGroups = append(messageGroups, tmpMessageGroups...)
		}
	}

	groupIdSet := fwyyutils.NewInt64Set()
	groupIdSet.Adds(req.GroupIds)

	for _, mg := range messageGroups {
		groupIdSet.Remove(mg.ID)
	}
	return &dtomessage.CheckMessageGroupPermissionRsp{
		NoPermissionMessageGroupIds: groupIdSet.AsList(),
	}, nil
}
