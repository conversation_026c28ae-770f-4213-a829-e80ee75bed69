package message

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/service/servicebase"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
	"sync"
	"time"
)

func ListResource(ctx *gin.Context) (resp []dtochatword.ListResourceRsp, err error) {
	userInfo, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return
	}
	var organizationGroupIds []int64
	organizationGroupIds, err = GetPermissionGroupIds(ctx, int64(userInfo.UserId), 0)
	if err != nil {
		return
	}
	//查团队
	messageGroupList, err := models.MessageGroupRef.GetListByPermissionGroupIds(ctx, organizationGroupIds, []string{"id"})
	if err != nil {
		return nil, err
	}

	personMessageGroupList, _, err := models.MessageGroupRef.GetListByCond(ctx, 0, "", []int64{}, defines.AuthorityForPersonal, int64(userInfo.UserId), []int64{}, "", 0, 0, 0, 1000)
	if err != nil {
		return nil, err
	}
	messageGroupIds := make([][]int64, 10)
	if len(personMessageGroupList) > 0 {
		messageGroupList = append(messageGroupList, personMessageGroupList...)
	}
	for _, messageGroup := range messageGroupList {
		messageGroupIds[messageGroup.ID%10] = append(messageGroupIds[messageGroup.ID%10], messageGroup.ID)
	}
	//开携程查数据库
	items := getItems(ctx, messageGroupIds)
	if len(items) == 0 {
		return
	}
	wg := &sync.WaitGroup{}
	ch := make(chan []dtochatword.ListResourceRsp)
	for _, subItemList := range SplitItemArray(items, 20) {
		wg.Add(1)
		go func(chunkItemList []models.BaseMessage) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetGroupListByIds panic, err:%s", r)
				}
			}()
			resourceList := []dtochatword.ListResourceRsp{}
			defer wg.Done()
			for index := range chunkItemList {
				subResourceList := getResource(ctx, chunkItemList[index])
				if len(subResourceList) > 0 {
					resourceList = append(resourceList, subResourceList...)
				}

			}

			if len(resourceList) > 0 {
				ch <- resourceList
			}
		}(subItemList)
	}

	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseTeachersBatch panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			resp = append(resp, singleRet...)
		}
	}()
	wg.Wait()
	close(ch)
	colWg.Wait()

	return
}

func getResource(ctx *gin.Context, baseMessage models.BaseMessage) (resp []dtochatword.ListResourceRsp) {
	if baseMessage.GetMsgType() == defines.MessageTypePicture {
		for _, item := range baseMessage.GetItemList() {
			img := item.(models.ImgInfo)
			if len(strings.Split(img.Name, ".")) != 2 {
				continue
			}
			if img.Md5 == "" {
				md5, _ := servicebase.GetMd5(ctx, img.Url)
				img.Md5 = md5
			}
			resp = append(resp, dtochatword.ListResourceRsp{
				URL:    img.Url,
				Md5:    img.Md5,
				Suffix: strings.Split(img.Name, ".")[1],
				Name:   img.Name,
			})

		}
	}

	if baseMessage.GetMsgType() == defines.MessageTypeFile {
		for _, item := range baseMessage.GetItemList() {
			file := item.(models.FileInfo)
			if len(strings.Split(file.FileName, ".")) != 2 {
				continue
			}
			url, downloadErr := helpers.BaiduBucket2.GetUrlByFileName(ctx, file.FileName, 24*60*time.Second)
			if downloadErr != nil {
				continue
			}
			if file.Md5 == "" {
				md5, _ := servicebase.GetMd5(ctx, url)
				file.Md5 = md5
			}
			resp = append(resp, dtochatword.ListResourceRsp{
				URL:      url,
				Md5:      file.Md5,
				Suffix:   strings.Split(file.FileName, ".")[1],
				Name:     file.FileName,
				FileName: file.Ori,
			})
		}

	}

	if baseMessage.GetMsgType() == defines.MessageTypeVoice {
		for _, item := range baseMessage.GetItemList() {
			voice := item.(models.VoiceInfo)
			if len(strings.Split(voice.VoiceName, ".")) != 2 {
				continue
			}
			url, downloadErr := helpers.BaiduBucket2.GetUrlByFileName(ctx, voice.VoiceName, 24*60*time.Second)
			if downloadErr != nil {
				continue
			}

			if voice.Md5 == "" {
				md5, _ := servicebase.GetMd5(ctx, url)
				voice.Md5 = md5
			}
			resp = append(resp, dtochatword.ListResourceRsp{
				URL:    url,
				Md5:    voice.Md5,
				Suffix: strings.Split(voice.VoiceName, ".")[1],
				Name:   voice.VoiceName,
			})
		}

	}

	if baseMessage.GetMsgType() == defines.MessageTypeVideo {
		for _, item := range baseMessage.GetItemList() {
			video := item.(models.VideoInfo)
			if len(strings.Split(video.Name, ".")) != 2 {
				continue
			}
			url, downloadErr := helpers.BaiduBucket2.GetUrlByFileName(ctx, video.Name, 24*60*time.Second)
			if downloadErr != nil {
				continue
			}
			if video.Md5 == "" {
				md5, _ := servicebase.GetMd5(ctx, url)
				video.Md5 = md5
			}
			resp = append(resp, dtochatword.ListResourceRsp{
				URL:    url,
				Md5:    video.Md5,
				Suffix: strings.Split(video.Name, ".")[1],
				Name:   video.Name,
			})
		}

	}
	return
}

func SplitItemArray(arr []models.BaseMessage, num int) [][]models.BaseMessage {
	max := len(arr)
	var segmens = make([][]models.BaseMessage, 0)
	if max <= num {
		return append(segmens, arr)
	}
	quantity := max / num

	for i := 0; i <= quantity; i++ {
		minCur := i * num
		if minCur >= max {
			return segmens
		}
		maxCur := (i + 1) * num
		if maxCur > max {
			maxCur = max
		}
		segmens = append(segmens, arr[minCur:maxCur])
	}
	return segmens
}

func getItems(ctx *gin.Context, messageGroupIds [][]int64) (items []models.BaseMessage) {
	wg := &sync.WaitGroup{}
	ch := make(chan []models.BaseMessage)
	for _, chunk := range messageGroupIds {
		if len(chunk) == 0 {
			continue
		}
		wg.Add(1)
		go func(groupIds []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetGroupListByIds panic, err:%s", r)
				}
			}()
			defer wg.Done()
			if len(groupIds) == 0 {
				return
			}
			lastId := int64(0)
			resourceList := []models.BaseMessage{}
			for true {
				messageList, err := models.MessageRef.ListByGroupIdList(ctx, groupIds, lastId)
				if err != nil {
					return
				}
				for index := range messageList {
					if messageList[index].MessageType == defines.MessageTypeFile ||
						messageList[index].MessageType == defines.MessageTypeVoice ||
						messageList[index].MessageType == defines.MessageTypePicture ||
						messageList[index].MessageType == defines.MessageTypeVideo {
						msgContent, check := messageList[index].GetMessageContent()
						if check != nil {
							continue
						}
						if check = msgContent.Check(models.DefaultMsgCheckParams); check != nil {
							continue
						}
						resourceList = append(resourceList, msgContent)

					}
					lastId = messageList[index].ID
				}
				if len(messageList) != 1000 {
					ch <- resourceList
					return
				}

			}
		}(chunk)
	}
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseTeachersBatch panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			items = append(items, singleRet...)
		}
	}()
	wg.Wait()
	close(ch)
	colWg.Wait()
	return
}
