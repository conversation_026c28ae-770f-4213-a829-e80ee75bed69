package message

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/utils"
	"errors"
	"github.com/gin-gonic/gin"
)

func HtmlBaseInfo(ctx *gin.Context, req *dtomessage.HtmlBaseInfoReq) (rsp *dtomessage.HtmlBaseInfoRsp, err error) {
	rsp = &dtomessage.HtmlBaseInfoRsp{}
	if rsp.Title, rsp.Description, rsp.Icon, err = utils.GetHtmlBaseInfo(req.Url); err != nil && !errors.Is(err, components.ErrorUrlInvalid) {
		return nil, err
	}

	if errors.Is(err, components.ErrorUrlInvalid) {
		return rsp, nil
	}

	if len(rsp.Icon) > 0 {
		rsp.Icon, err = utils.DownloadImgToBos(ctx, rsp.Icon)
	}
	return
}
