package message

import (
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func MaterialList(ctx *gin.Context) (rsp *dtomessage.MaterialListRsp, err error) {
	var userInfo *userprofile.UserInfo
	if userInfo, err = middleware.GetLoginUserInfo(ctx); err != nil {
		return
	}

	var deviceInfo *mesh.GetDeviceListByDeviceUidsRsq
	if deviceInfo, err = mesh.GetDeviceListByDeviceUid(ctx, userInfo.SelectedBusinessUid); err != nil {
		return
	}

	if deviceInfo.DeviceUID == 0 {
		zlog.Warnf(ctx, "MaterialList_GetDeviceInfoListByDeviceUids_failed, deviceUid: %d, ret: %+v", userInfo.SelectedBusinessUid, deviceInfo)
		return nil, components.DefaultError("获取资产信息失败")
	}

	var staffUidStr string
	if staffUidStr, err = utils.EncodeUid(int(deviceInfo.DeviceUID)); err != nil {
		return
	}

	getMaterialReq := &kunpeng.GetMaterialListReq{
		CorpID:   deviceInfo.WecomCorpID,
		UserID:   deviceInfo.WecomUserID,
		StaffUid: staffUidStr,
		SndTime:  time.Now().Add(-1 * time.Hour * 24 * 30 * 3).Unix(), // 查最近三个月的视频号
		EndTime:  time.Now().Unix(),
	}

	var materialRet *kunpeng.GetMaterialListRsp
	if materialRet, err = kunpeng.GetMaterialList(ctx, getMaterialReq); err != nil {
		return
	}

	list := make([]dtomessage.MaterialInfo, 0, len(materialRet.List))
	for _, info := range materialRet.List {
		channelInfo := dtomessage.MaterialInfo{
			Avatar:       info.MsgContent.Avatar,
			CoverURL:     info.MsgContent.CoverURL,
			Desc:         info.MsgContent.Desc,
			Extras:       info.MsgContent.Extras,
			FeedType:     info.MsgContent.FeedType,
			Nickname:     info.MsgContent.Nickname,
			ThumbURL:     info.MsgContent.ThumbURL,
			URL:          info.MsgContent.URL,
			Eid:          info.MsgContent.Eid,
			ExpireTime:   info.MsgContent.ExpireTime,
			ThumbPid:     info.MsgContent.ThumbPid,
			ShowThumbURL: info.MsgContent.ShowThumbURL,
		}
		list = append(list, channelInfo)
	}

	rsp = &dtomessage.MaterialListRsp{
		List: list,
	}

	return
}
