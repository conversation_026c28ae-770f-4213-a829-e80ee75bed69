package message

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"sync"
	"time"
)

func checkMessageGroupParams(ctx *gin.Context, req dtomessage.SaveMessageGroupReq, userInfo *userprofile.UserInfo) (rsp dtomessage.SaveGroupRsp, err error, isValid bool) {
	if !defines.CanProcess(req.Type) {
		rsp.Success = 1
		rsp.Message = "type不正确，无法处理"
		return
	}

	var folderList []models.MessageGroupFolder
	folderList, err = models.MessageGroupFolderRef.GetById(ctx, req.FolderId)
	if err != nil {
		return
	}

	if len(folderList) == 0 {
		rsp.Success = 1
		rsp.Message = "文件夹不存在"
		return
	}

	if folderList[0].IsDeleted == 1 {
		rsp.Success = 1
		rsp.Message = "文件夹已被删除，无法新增消息组，请刷新页面"
		return
	}

	if len(req.GroupName) == 0 {
		rsp.Success = 1
		rsp.Message = "消息组名称不能为空"
		return
	}

	if len(req.GroupName) > 20*3 {
		rsp.Success = 1
		rsp.Message = "消息组名称不能超过20个文字"
		return
	}

	//if len(req.GroupDesc) == 0 {
	//	rsp.Success = 1
	//	rsp.Message = "消息组描述不能为空"
	//	return
	//}

	if len(req.GroupDesc) > 200*3 {
		rsp.Success = 1
		rsp.Message = "消息组描述不能超过200个文字"
		return
	}

	if len(req.MessageList) > 20 {
		rsp.Success = 1
		rsp.Message = "消息组内消息个数不能超过20"
		return
	}

	err = utils.ContainChineseOrDigit(req.GroupName)
	if err != nil {
		return
	}

	// 校验权限
	if req.GroupAuthority == defines.AuthorityForGroup {
		permissionList, _err := userprofile.GetEditPermissionList(ctx, userInfo.UserId)
		if _err != nil {
			err = _err
			return
		}
		hasAuthority := false
		for _, item := range permissionList {
			if item == "/assistantdesk/view/fwyy-module-page/message-group/team-message-group-permission" {
				hasAuthority = true
				break
			}
		}
		if !hasAuthority {
			rsp.Success = 1
			rsp.Message = "没有团队可见权限，只能保存为个人可见"
			return
		}

		groupTrees, _err := GetOrganizationTree(ctx, int64(userInfo.UserId))
		if _err != nil {
			return
		}

		hasPermission := CheckPermission(groupTrees, req.PermissionGroupId)
		if !hasPermission {
			rsp.Success = 1
			rsp.Message = "没有该团队权限，请刷新页面重新选择"
			return
		}
	}

	if req.Type == defines.UpdateMessageGroup {
		groupList, _err := models.MessageGroupRef.GetById(ctx, req.GroupId)
		if _err != nil {
			err = _err
			return
		}
		if len(groupList) == 0 {
			rsp.Success = 1
			rsp.Message = "该groupId不存在记录，无法更新"
			return
		}
		groupInfo := groupList[0]
		if groupInfo.IsDeleted == 1 {
			rsp.Success = 1
			rsp.Message = "该groupId已被删除，无法更新，请刷新页面"
			return
		}

		messageGroupList, _err := models.MessageGroupRef.ListByFolderId(ctx, req.FolderId)
		if _err != nil {
			err = _err
			return
		}

		for _, messageGroup := range messageGroupList {
			if messageGroup.ID != req.GroupId && messageGroup.Name == req.GroupName {
				rsp.Success = 1
				rsp.Message = "更新失败，消息组名称不可重复"
				return
			}
		}
	}

	isValid = true
	return
}

func SaveMessageGroup(ctx *gin.Context, req dtomessage.SaveMessageGroupReq) (rsp dtomessage.SaveGroupRsp, err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	var isValidParams bool
	if rsp, err, isValidParams = checkMessageGroupParams(ctx, req, userInfo); !isValidParams {
		return
	}

	switch req.Type {
	case defines.AddMessageGroup:
		return AddMessageGroup(ctx, req, userInfo)

	case defines.UpdateMessageGroup:
		return UpdateMessageGroup(ctx, req, userInfo)
	}
	return
}

func CheckPermission(trees []*dtomessage.GroupPermissionNode, permissionGroupId int64) bool {
	if len(trees) == 0 {
		return false
	}

	for _, item := range trees {
		if item.WithPermission == true {
			if item.ID == permissionGroupId {
				return true
			}
		}
		res := CheckPermission(item.Children, permissionGroupId)
		if res == true {
			return true
		}
	}

	return false
}

func AddMessageGroup(ctx *gin.Context, req dtomessage.SaveMessageGroupReq, userInfo *userprofile.UserInfo) (rsp dtomessage.SaveGroupRsp, err error) {
	messageGroupList, _err := models.MessageGroupRef.ListByFolderId(ctx, req.FolderId)
	if _err != nil {
		err = _err
		return
	}
	if len(messageGroupList) >= 20 {
		rsp.Success = 1
		rsp.Message = "该文件夹消息组超出上限，请先删除后新增"
		return
	}
	for _, tempMessageGroup := range messageGroupList {
		if tempMessageGroup.Name == req.GroupName {
			rsp.Success = 1
			rsp.Message = "新增失败，消息组名称不可重复"
			return
		}
	}

	now := time.Now().Unix()
	availableRange1v1 := false
	messageList := make([]*models.Message, 0)
	for index, message := range req.MessageList {
		// 初始化消息
		newMessage := &models.Message{
			Order:        int64(index + 1),
			MessageType:  message.Type,
			IntervalTime: message.IntervalTime,
			CreateUid:    int64(userInfo.UserId),
			CreateTime:   now,
		}

		// 解析消息内容
		var jsonContent, availableRange string
		if jsonContent, availableRange, err = GetJsonContentByMessageType(ctx, userInfo.UserName, message.Type, message.Content); err != nil {
			rsp.Success = 1
			rsp.Message = err.Error()
			return
		}

		// 记录消息组维度
		if availableRange == defines.AvailableRangeForStudentLevel {
			availableRange1v1 = true
		}
		newMessage.Content = jsonContent
		messageList = append(messageList, newMessage)
	}

	// 设置消息组维度
	availableRange := defines.AvailableRangeForGeneral
	if availableRange1v1 {
		availableRange = defines.AvailableRangeForStudentLevel
	}

	// 初始化消息组数据
	messageGroup := &models.MessageGroup{
		Name:              req.GroupName,
		Desc:              req.GroupDesc,
		FolderId:          req.FolderId,
		Authority:         req.GroupAuthority,
		PermissionGroupId: req.PermissionGroupId,
		AvailableRange:    availableRange,
		CreateTime:        now,
		CreateUid:         int64(userInfo.UserId),
		CreateName:        userInfo.UserName,
	}

	tx := models.MessageGroupRef.Tx(ctx)
	err = models.MessageGroupRef.BatchInsertWithTx(ctx, []*models.MessageGroup{messageGroup}, tx)
	if err != nil {
		tx.Rollback()
		return
	}

	for _, messageInfo := range messageList {
		messageInfo.MessageGroupId = messageGroup.ID
	}

	var preMessageId int64 = 0
	for _, messageInfo := range messageList {
		messageInfo.PreMessageId = preMessageId
		if err = models.MessageRef.BatchInsertWithTx(ctx, []*models.Message{messageInfo}, tx); err != nil {
			tx.Rollback()
			return
		}
		preMessageId = messageInfo.ID
	}

	tx.Commit()

	rsp.GroupId = messageGroup.ID
	return
}

func UpdateMessageGroup(ctx *gin.Context, req dtomessage.SaveMessageGroupReq, userInfo *userprofile.UserInfo) (rsp dtomessage.SaveGroupRsp, err error) {
	now := time.Now().Unix()
	messageList, _err := models.MessageRef.List(ctx, req.GroupId)
	if _err != nil {
		err = _err
		return
	}
	id2Message := make(map[int64]models.Message)
	validMessageIds := make([]int64, 0)
	for _, message := range messageList {
		id2Message[message.ID] = message
		validMessageIds = append(validMessageIds, message.ID)
	}
	newMessageIds := make([]int64, 0)
	for _, message := range req.MessageList {
		if message.MessageId != 0 {
			newMessageIds = append(newMessageIds, message.MessageId)
		}
	}
	/**
	当前：1、2、3
	情况1：页面A：1、3；页面B：1、2、3、4（0）；2会报错让刷新页面
	情况2：页面A：1、2、3、4；页面B：1、3、5（0）；删除2、4，新增5，更新1、3
	*/
	removeMessageIds, notExistMessageIds := utils.FindDifferenceForInt64(validMessageIds, newMessageIds)
	if len(notExistMessageIds) > 0 {
		rsp.Success = 1
		rsp.Message = "存在问题消息，可能被其它页面操作过，请刷新当前页面"
		return
	}

	var preMessageId int64 = 0
	availableRange1v1 := false
	var jsonContent, availableRange string
	jsonContentMap := make(map[int]string, 0)
	for index, message := range req.MessageList {
		if jsonContent, availableRange, err = GetJsonContentByMessageType(ctx, userInfo.UserName, message.Type, message.Content); err != nil {
			rsp.Success = 1
			rsp.Message = err.Error()
			return
		}
		if availableRange == defines.AvailableRangeForStudentLevel {
			availableRange1v1 = true
		}
		jsonContentMap[index] = jsonContent
	}

	tx := models.MessageRef.Tx(ctx)
	// 1.删除无效message
	if len(removeMessageIds) != 0 {
		messageValues := map[string]interface{}{
			"is_deleted":  1,
			"update_time": now,
			"update_uid":  userInfo.UserId,
		}
		err = models.MessageRef.UpdateByIds(ctx, req.GroupId, removeMessageIds, messageValues, tx)
		if err != nil {
			tx.Rollback()
			return
		}
	}

	for index, message := range req.MessageList {
		// 新增
		jsonContent = jsonContentMap[index]
		if message.MessageId == 0 {
			addMessage := &models.Message{
				MessageGroupId: req.GroupId,
				Order:          int64(index + 1),
				MessageType:    message.Type,
				IntervalTime:   message.IntervalTime,
				Content:        jsonContent,
				PreMessageId:   preMessageId,
				CreateTime:     now,
				CreateUid:      int64(userInfo.UserId),
			}

			if err = models.MessageRef.BatchInsertWithTx(ctx, []*models.Message{addMessage}, tx); err != nil {
				tx.Rollback()
				return
			}

			preMessageId = addMessage.ID
		} else { // 更新
			dbMessage, ok := id2Message[message.MessageId]
			if !ok {
				rsp.Success = 1
				rsp.Message = "存在问题消息，可能被其它页面操作过，请刷新当前页面"
				return
			}
			updateColumn := map[string]interface{}{
				"order":         int64(index + 1),
				"message_type":  message.Type,
				"content":       jsonContent,
				"interval_time": message.IntervalTime,
				"update_time":   now,
				"update_uid":    int64(userInfo.UserId),
			}
			if index != 0 {
				updateColumn["pre_message_id"] = preMessageId
			} else {
				updateColumn["pre_message_id"] = 0
			}

			if err = models.MessageRef.UpdateByIds(ctx, req.GroupId, []int64{dbMessage.ID}, updateColumn, tx); err != nil {
				tx.Rollback()
				return
			}

			preMessageId = dbMessage.ID
		}
	}

	// 3.更新消息组数据
	values := map[string]interface{}{
		"name":                req.GroupName,
		"desc":                req.GroupDesc,
		"authority":           req.GroupAuthority,
		"permission_group_id": req.PermissionGroupId,
		"available_range":     defines.AvailableRangeForGeneral,
		"update_time":         now,
		"update_uid":          userInfo.UserId,
		"update_name":         userInfo.UserName,
	}
	if availableRange1v1 {
		values["available_range"] = defines.AvailableRangeForStudentLevel
	}
	err = models.MessageGroupRef.UpdateById(ctx, req.GroupId, values, tx)
	if err != nil {
		tx.Rollback()
		return
	}

	tx.Commit()
	rsp.GroupId = req.GroupId
	return
}

func GetJsonContentByMessageType(ctx *gin.Context, userName string, messageType int64, content interface{}) (jsonContent string, availableRange string, err error) {
	var marshal []byte
	if marshal, err = json.Marshal(content); err != nil {
		return
	}

	jsonContent = string(marshal)
	messageInfo := models.Message{
		MessageType: messageType,
		Content:     jsonContent,
	}

	var msgContent models.BaseMessage
	if msgContent, err = messageInfo.GetMessageContent(); err != nil {
		return
	}

	availableRange = msgContent.GetAvailableRange()

	if err = msgContent.Check(models.DefaultMsgCheckParams); err != nil {
		return
	}

	if messageType == defines.MessageTypeVoice {
		voice := &models.Voice{}
		if err = models.DecodeContent(jsonContent, voice); err != nil {
			return
		}

		ret := models.Voice{
			VoiceList: make([]models.VoiceInfo, 0),
		}
		var silkName string
		for _, item := range voice.VoiceList {
			if silkName, err = utils.GetSilkName(ctx, item.VoiceName, item.VoiceType); err != nil {
				return
			}
			item.SilkVoiceLink = silkName

			ret.VoiceList = append(ret.VoiceList, item)
		}

		var data []byte
		if data, err = json.Marshal(ret); err != nil {
			return
		}
		jsonContent = string(data)
	}
	return
}

func DeleteMessageGroup(ctx *gin.Context, groupId int64) (rsp dtomessage.CommonRsp, err error) {
	groupList, err := models.MessageGroupRef.GetById(ctx, groupId)
	if err != nil {
		return
	}
	if len(groupList) == 0 {
		rsp.Success = 1
		rsp.Message = "该groupId不存在记录，无法删除"
		return
	}
	groupInfo := groupList[0]
	if groupInfo.IsDeleted == 1 {
		rsp.Success = 1
		rsp.Message = "该groupId已被删除，无法重复删除，请刷新页面"
		return
	}
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	tx := models.MessageRef.Tx(ctx)

	now := time.Now().Unix()
	values := map[string]interface{}{
		"is_deleted":  1,
		"update_time": now,
		"update_uid":  userInfo.UserId,
		"update_name": userInfo.UserName,
	}
	err = models.MessageGroupRef.UpdateById(ctx, groupId, values, tx)
	if err != nil {
		zlog.Warnf(ctx, "DeleteMessageGroup UpdateById fail, groupId:%+v, values:%+v, err:%+v", groupId, values, err)
		tx.Rollback()
		return
	}
	messageList, err := models.MessageRef.List(ctx, groupId)
	if err != nil {
		return
	}
	if len(messageList) > 0 {
		messageIds := make([]int64, 0)
		for _, message := range messageList {
			messageIds = append(messageIds, message.ID)
		}
		values = map[string]interface{}{
			"is_deleted":  1,
			"update_time": now,
			"update_uid":  userInfo.UserId,
		}
		err = models.MessageRef.UpdateByIds(ctx, groupId, messageIds, values, tx)
		if err != nil {
			tx.Rollback()
			return
		}
	}
	tx.Commit()
	return
}

func GetMessageGroupDetail(ctx *gin.Context, groupId int64) (rsp dtomessage.MessageGroupDetailRsp, err error) {
	messageGroups, err := models.MessageGroupRef.GetById(ctx, groupId)
	if err != nil {
		return
	}
	if len(messageGroups) == 0 {
		err = components.DefaultError("用户不存在该消息组，可以刷新重写查看数据")
		return
	}
	messageGroup := messageGroups[0]

	folderList, err := models.MessageGroupFolderRef.GetById(ctx, messageGroup.FolderId)
	if err != nil {
		return
	}
	if len(folderList) == 0 {
		err = components.DefaultError("该消息组不存在对应文件夹")
		zlog.Errorf(ctx, "该消息组不存在对应文件夹, groupId:%+v", groupId)
		return
	}
	folder := folderList[0]

	messageList, err := models.MessageRef.List(ctx, messageGroup.ID)
	if err != nil {
		return
	}

	var messageListRsp []dtomessage.MessageDetail

	for _, message := range messageList {

		contentStruct, _err := GetJsonStructByMessageType(ctx, message.MessageType, message.Content)
		if _err != nil {
			err = _err
			return
		}

		messageInfo := dtomessage.MessageDetail{
			Type:         message.MessageType,
			IntervalTime: message.IntervalTime,
			Content:      contentStruct,
			MessageId:    message.ID,
			Order:        message.Order,
		}
		messageListRsp = append(messageListRsp, messageInfo)
	}

	sort.Slice(messageListRsp, func(i, j int) bool {
		return messageListRsp[i].Order <= messageListRsp[j].Order
	})

	rsp = dtomessage.MessageGroupDetailRsp{
		FolderId:          messageGroup.FolderId,
		FolderName:        folder.Name,
		GroupId:           messageGroup.ID,
		GroupName:         messageGroup.Name,
		GroupDesc:         messageGroup.Desc,
		GroupAuthority:    messageGroup.Authority,
		PermissionGroupId: messageGroup.PermissionGroupId,
		MessageList:       messageListRsp,
	}

	return rsp, nil
}

func GetMessageGroupDetails(ctx *gin.Context, groupIds []int64) (*dtomessage.MessageGroupDetailsRsp, error) {
	ch := make(chan *dtomessage.MessageGroupDto, len(groupIds))
	errCh := make(chan error, len(groupIds))
	chunkedGroupIdList := utils.ChunkArrayInt64(groupIds, components.MaxGoroutineNum)

	for _, chunkedGroupIds := range chunkedGroupIdList {
		wg := &sync.WaitGroup{}
		for _, id := range chunkedGroupIds {
			wg.Add(1)
			tmpGroupId := id
			fwyyutils.GoWithRecoverAndReturnErr(ctx, func() error {
				defer wg.Done()
				messageModels, err := models.MessageRef.List(ctx, tmpGroupId)
				if err != nil {
					return err
				}

				var messageListRsp = make([]*dtomessage.MessageDetail, 0)
				for _, message := range messageModels {
					contentStruct, _err := GetJsonStructByMessageType(ctx, message.MessageType, message.Content)
					if _err != nil {
						return _err
					}

					messageInfo := &dtomessage.MessageDetail{
						Type:         message.MessageType,
						IntervalTime: message.IntervalTime,
						Content:      contentStruct,
						MessageId:    message.ID,
						Order:        message.Order,
					}
					messageListRsp = append(messageListRsp, messageInfo)
				}
				sort.Slice(messageListRsp, func(i, j int) bool {
					return messageListRsp[i].Order <= messageListRsp[j].Order
				})

				ch <- &dtomessage.MessageGroupDto{
					GroupID:     tmpGroupId,
					MessageList: messageListRsp,
				}
				return nil
			}, errCh)
			wg.Wait()
		}
	}

	close(ch)
	close(errCh)

	for err := range errCh {
		if err != nil {
			return nil, err
		}
	}

	groupDetails := make([]*dtomessage.MessageGroupDto, 0)
	for messageGroupDto := range ch {
		groupDetails = append(groupDetails, messageGroupDto)
	}
	return &dtomessage.MessageGroupDetailsRsp{
		GroupDetails: groupDetails,
	}, nil
}

func GetJsonStructByMessageType(ctx *gin.Context, messageType int64, content string) (contentStruct interface{}, err error) {
	str := []byte(content)

	switch messageType {
	case defines.MessageTypeWord:
		word := &models.Word{}
		if err = json.Unmarshal(str, word); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeWord, content, err)
			return
		}
		contentStruct = word

	case defines.MessageTypePicture:
		img := &models.Img{}
		if err = json.Unmarshal(str, img); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypePicture, content, err)
			return
		}
		contentStruct = img

	case defines.MessageTypeVideo:
		video := &models.Video{}
		if err = json.Unmarshal(str, video); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeVideo, content, err)
			return
		}
		videoList := make([]models.VideoInfo, 0)
		for _, videoInfo := range video.VideoList {
			videoInfo.Url, _ = helpers.BaiduBucket2.GetUrlByFileName(ctx, videoInfo.Name, 24*60*time.Second)
			videoList = append(videoList, videoInfo)
		}
		video.VideoList = videoList
		contentStruct = video

	case defines.MessageTypeVoice:
		voice := &models.Voice{}
		if err = json.Unmarshal(str, voice); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeVoice, content, err)
			return
		}
		url, _err := helpers.BaiduBucket2.GetUrlByFileName(ctx, voice.VoiceList[0].VoiceName, 24*60*time.Second)
		if _err != nil {
			err = _err
			return
		}
		voice.VoiceList[0].Url = url
		contentStruct = voice

	case defines.MessageTypeFile:
		file := &models.File{}
		if err = json.Unmarshal(str, file); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}
		url, _err := helpers.BaiduBucket2.GetUrlByFileName(ctx, file.FileList[0].FileName, 24*60*time.Second)
		if err != nil {
			err = _err
			return
		}
		file.FileList[0].Url = url
		contentStruct = file

	case defines.MessageTypeCardLink:
		contentStruct = &models.Card{}
		if err = json.Unmarshal(str, contentStruct); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}

	case defines.MessageTypeVideoMaterial:
		contentStruct = &models.VideoMaterial{}
		if err = json.Unmarshal(str, contentStruct); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}
	}
	return contentStruct, nil
}

func UpdateGroupAvailableRange(ctx *gin.Context, req dtomessage.MessageGroupUpdateAvailableRangeReq) (err error) {
	if len(req.StudentLevelSource) > 0 {
		if err = models.MessageGroupRef.UpdateAvailableRange(ctx, req.StudentLevelSource, defines.AvailableRangeForStudentLevel); err != nil {
			return err
		}
	}

	if len(req.GeneralLevelSource) > 0 {
		if err = models.MessageGroupRef.UpdateAvailableRange(ctx, req.GeneralLevelSource, defines.AvailableRangeForGeneral); err != nil {
			return err
		}
	}
	return nil
}
