package message

import (
	"assistantdeskgo/api/muse/message"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/infra/pkg/navigator"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"time"
)

func RouteToTouchmisSendMessage(ctx *gin.Context, param dtomessage.SendGroupMessageReq) (rsp dtomessage.SendGroupMessageRsp, err error) {
	touchmisReq, err := transToTouchmisgoSendParam(ctx, param)
	if err != nil {
		return
	}

	sendRsp, err := touchmisgo.SendGroupMessage(ctx, touchmisReq)
	if err != nil {
		return
	}
	rsp.TaskId = sendRsp.TaskId
	rsp.SendingTaskCnt = sendRsp.SendingTaskCnt
	rsp.HasCronTask = sendRsp.HasCronTask
	rsp.CronTimeRange = sendRsp.CronTimeRange
	return
}

func SendGroupMessage(ctx *gin.Context, param dtomessage.SendGroupMessageReq) (rsp dtomessage.SendGroupMessageRsp, err error) {
	messageParam, checkRet, err := formatGroupMessage(ctx, param)
	if err != nil {
		return rsp, err
	}
	ret, err := message.SendGroupMessage(ctx, messageParam)
	if err != nil {
		return rsp, err
	}
	rsp.TaskId = ret.TaskId
	rsp.SendingTaskCnt = checkRet.SendingTaskCnt
	rsp.HasCronTask = checkRet.HasCronTask
	rsp.CronTimeRange = checkRet.CronTimeRange
	return
}

func formatGroupMessage(ctx *gin.Context, param dtomessage.SendGroupMessageReq) (req message.SendGroupMessageReq, checkRet *touchmisgo.CheckSendParamRsp, err error) {
	userInfo := components.GetUserInfo(ctx)
	if userInfo.SelectedBusinessUid <= 0 {
		return req, nil, errors.New("资产id错误")
	}
	req.AssistantUID = userInfo.SelectedBusinessUid

	checkRet, err = checkSendMessage(ctx, param)
	if err != nil {
		return
	}

	if navigator.IsPressure(ctx) {
		for index, content := range param.Contents {
			receivers := make([]dtomessage.Receiver, 0, len(content.ReceiverList))
			for _, receiver := range content.ReceiverList {
				receiver.StudentUid = int64(navigator.RegressUid(uint64(receiver.StudentUid)))
				receiver.AtStudentUids = utils.RegressUidForArray(receiver.AtStudentUids)
				receivers = append(receivers, receiver)
			}

			content.ReceiverList = receivers
			param.Contents[index] = content
		}
	}

	if !utils.InArrayInt64(param.TaskType, []int64{defines.TaskTypeNow, defines.TaskTypeDelay}) {
		return req, checkRet, errors.New("不存在的任务类型")
	}

	if param.SubType == 0 {
		return req, checkRet, errors.New("类型错误")
	}
	req.SubType = param.SubType
	// 顺序执行
	req.GroupOrder = defines.GroupOrderTrue
	if param.SendType > 0 {
		req.SendType = param.SendType
	} else {
		req.SendType = defines.GetSendTypeBySubType(param.SubType)
	}
	if req.SendType == 0 {
		return req, checkRet, errors.New("发送类型错误")
	}

	// 定时发送为第0个group设置延迟时间
	req.TaskType = param.TaskType
	if param.TaskType == defines.TaskTypeDelay {
		now := time.Now().Unix()
		if now+defines.DefaultDelayTime >= param.SendTime {
			return req, checkRet, errors.New("定时任务发送时间不能小于当前时间")
		}
		req.SendTime = param.SendTime
	}

	messageGroups, err := formatMessageGroup(ctx, param.Contents, param.SubType, req.SendTime)
	if err != nil {
		return req, checkRet, err
	}

	req.MessageGroups = messageGroups
	return req, checkRet, nil
}

func checkSendMessage(ctx *gin.Context, param dtomessage.SendGroupMessageReq) (*touchmisgo.CheckSendParamRsp, error) {
	touchmisCheckReq, err := transToTouchmisgoCheckParam(ctx, param)
	if err != nil {
		return nil, err
	}

	checkRsp, err := touchmisgo.CheckSendParam(ctx, touchmisCheckReq)
	if err != nil {
		return nil, fmt.Errorf("[formatGroupMessage] check send param failed, err: %+v", err)
	} else if checkRsp.Status != 0 {
		return nil, fmt.Errorf("消息校验失败, 失败原因: %s", checkRsp.Msg)
	}
	return checkRsp, nil
}

func formatMessageGroup(ctx *gin.Context, contents []dtomessage.Content, subType, sendTime int64) (messageGroups []message.MessageGroup, err error) {
	messageGroups = []message.MessageGroup{}
	if len(contents) <= 0 {
		return messageGroups, errors.New("消息接收体不能为空")
	}
	for _, content := range contents {
		messageGroup := message.MessageGroup{
			Ability:  content.Ability,
			Strategy: content.Strategy,
		}
		messageGroup.SourceInfo = message.SourceInfo{
			CourseId: content.CourseId,
			LessonId: content.LessonId,
		}

		// LabelStudentList
		messageGroup.LabelStudentList = content.LabelStudentList

		// message
		var messageList []message.MessageInfo
		for _, msg := range content.MessageList {
			messageInfos, err := transMessage(msg).Format(ctx, msg.DelayTime, sendTime, msg.AtMembers)
			if err != nil {
				return messageGroups, err
			}
			messageList = append(messageList, messageInfos...)
		}
		messageGroup.MessageList = messageList

		// receiver
		receiver, err := formatReceiver(content.ReceiverList, subType)
		if err != nil {
			return messageGroups, err
		}
		messageGroup.Receivers = receiver
		// subOrder
		messageGroup.SubOrder = defines.SubOrderTrue
		messageGroups = append(messageGroups, messageGroup)
	}
	return
}

func transMessage(m message.MessageInfo) *models.Message {
	marshal, _ := json.Marshal(m.MsgContent)
	return &models.Message{
		MessageType: m.MsgType,
		Content:     string(marshal),
	}
}

func formatReceiver(receiverList []dtomessage.Receiver, subType int64) (retReceiver message.Receiver, err error) {
	retReceiver = message.Receiver{
		StudentUids: []int64{}, RemoteIds: []string{}, ChatId: "",
	}
	if len(receiverList) <= 0 {
		return retReceiver, errors.New("消息接收者不能为空")
	}

	for i, receiver := range receiverList {
		switch subType {
		case defines.MultiMessageToPerson, defines.MultiMessageToPersonByAssistant, defines.SingleMessageToPerson:
			// 发送到人
			if receiver.StudentUid <= 0 {
				return retReceiver, errors.New(fmt.Sprintf("发送到人时，第%d个学生uid不能为空", i+1))
			}
			retReceiver.StudentUids = append(retReceiver.StudentUids, receiver.StudentUid)
			retReceiver.RemoteIds = append(retReceiver.RemoteIds, receiver.RemoteIds...)
			break
		case defines.MultiMessageToGroup, defines.MultiMessageToGroupByAssistant, defines.SingleMessageToGroup:
			if len(receiverList) > 1 {
				return retReceiver, errors.New("群发到群时，一个消息组只能有一个群")
			}
			if receiver.ChatId == "" {
				return retReceiver, errors.New("发送到群时，群id不能为空")
			}
			retReceiver.ChatId = receiver.ChatId
			break
		default:
			return retReceiver, errors.New("不支持的群发类型")
		}
	}
	return
}

func transToTouchmisgoSendParam(ctx *gin.Context, param dtomessage.SendGroupMessageReq) (touchmisgo.SendGroupMessageReq, error) {
	touchmisReq := touchmisgo.SendGroupMessageReq{}
	userInfo := components.GetUserInfo(ctx)
	if userInfo.SelectedBusinessUid <= 0 {
		return touchmisReq, errors.New("资产id错误")
	}

	sendType := param.SendType
	if sendType == 0 {
		sendType = defines.GetSendTypeBySubType(param.SubType)
	}
	touchmisReq = touchmisgo.SendGroupMessageReq{
		AssistantUID: userInfo.SelectedBusinessUid,
		PersonUid:    int64(userInfo.StaffUid),
		SubType:      int(param.SubType),
		SceneType:    sendType,
		TaskType:     touchmisgo.TaskTypeSendMsg,
		SendTime:     param.SendTime,
		IsDelay:      getIsDelay(param.TaskType),
		Contents:     transBatchContent(param.Contents),
	}
	return touchmisReq, nil
}

func transToTouchmisgoCheckParam(ctx *gin.Context, param dtomessage.SendGroupMessageReq) (touchmisgo.CheckSendParamReq, error) {
	sendReq, err := transToTouchmisgoSendParam(ctx, param)
	if err != nil {
		return touchmisgo.CheckSendParamReq{}, err
	}

	return touchmisgo.CheckSendParamReq{
		AssistantUID: sendReq.AssistantUID,
		PersonUid:    sendReq.PersonUid,
		SubType:      sendReq.SubType,
		SceneType:    sendReq.SceneType,
		TaskType:     sendReq.TaskType,
		SendTime:     sendReq.SendTime,
		IsDelay:      sendReq.IsDelay,
		Contents:     sendReq.Contents,
	}, nil
}

func getIsDelay(taskType int64) int64 {
	switch int(taskType) {
	case defines.TaskTypeNow:
		return touchmisgo.TaskTypeNow
	case defines.TaskTypeDelay:
		return touchmisgo.TaskTypeDelay
	}
	return touchmisgo.TaskTypeNow
}

func transContent(c dtomessage.Content) touchmisgo.Content {
	labelList := make(map[string][]int64)
	for k, studList := range c.LabelStudentList {
		labelList[k] = fwyyutils.ConvertArrayIntToArrayInt64(studList)
	}

	receiverList := make([]touchmisgo.Receiver, 0)
	for _, receiver := range c.ReceiverList {
		receiverList = append(receiverList, touchmisgo.Receiver{
			StudentUid:    receiver.StudentUid,
			RemoteIds:     receiver.RemoteIds,
			ChatId:        receiver.ChatId,
			AtStudentUids: receiver.AtStudentUids,
		})
	}

	messageList := make([]touchmisgo.MessageInfo, 0)
	for _, msgInfo := range c.MessageList {
		messageList = append(messageList, touchmisgo.MessageInfo{
			DelayTime: msgInfo.DelayTime,
			AtMembers: touchmisgo.AtMembers{
				StudentUids: msgInfo.AtMembers.StudentUids,
				RemoteIds:   msgInfo.AtMembers.RemoteIds,
			},
			MsgType:    int(msgInfo.MsgType),
			MsgContent: msgInfo.MsgContent,
		})
	}

	return touchmisgo.Content{
		SourceInfo: []touchmisgo.Source{
			{
				Type: touchmisgo.SourceTypeCourse,
				Key:  cast.ToString(c.CourseId),
			},
			{
				Type: touchmisgo.SourceTypeLesson,
				Key:  cast.ToString(c.LessonId),
			},
		},
		LabelStudentList: labelList,
		ReceiverList:     receiverList,
		MessageList:      messageList,
		Ability:          int64(c.Ability),
		Strategy:         c.Strategy,
	}
}

func transBatchContent(cs []dtomessage.Content) []touchmisgo.Content {
	contents := make([]touchmisgo.Content, 0)
	for _, content := range cs {
		contents = append(contents, transContent(content))
	}
	return contents
}
