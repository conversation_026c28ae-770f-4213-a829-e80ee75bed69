package message

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"time"
)

func SaveFolder(ctx *gin.Context, req dtomessage.SaveFolderReq) (rsp dtomessage.SaveFolderRsp, err error) {
	if len(req.FolderName) == 0 {
		rsp.Success = 1
		rsp.Message = "文件名不能为空"
		return
	}

	if len(req.FolderName) > 20*3 {
		rsp.Success = 1
		rsp.Message = "文件名不能超过20个文字"
		return
	}

	err = utils.ContainChineseOrDigit(req.FolderName)
	if err != nil {
		return
	}

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	switch req.Type {
	case defines.AddFolder:
		folderList, _err := models.MessageGroupFolderRef.List(ctx, userInfo.UserId, nil)
		if _err != nil {
			err = _err
			return
		}

		sort.Slice(folderList, func(i, j int) bool {
			return folderList[i].Order >= folderList[j].Order
		})

		if len(folderList) >= 50 {
			rsp.Success = 1
			rsp.Message = "已超出文件夹上限，无法创建请先删除消息组和文件夹"
			return
		}

		for _, folder := range folderList {
			if folder.Name == req.FolderName {
				rsp.Success = 1
				rsp.Message = "该文件夹名称已存在，请修改"
				return
			}
		}

		var order int64 = 1
		if len(folderList) > 0 {
			order = folderList[0].Order + 1
		}
		now := time.Now().Unix()
		folder := &models.MessageGroupFolder{
			Name:       req.FolderName,
			Order:      order,
			CreateTime: now,
			CreateUid:  int64(userInfo.UserId),
			CreateName: userInfo.UserName,
		}
		err = models.MessageGroupFolderRef.BatchInsertWithTx(ctx, []*models.MessageGroupFolder{folder}, nil)
		if err != nil {
			return
		}
		rsp.FolderId = folder.ID

	case defines.UpdateFolder:
		folderList, _err := models.MessageGroupFolderRef.GetById(ctx, req.FolderId)
		if _err != nil {
			err = _err
			return
		}
		if len(folderList) == 0 {
			err = components.DefaultError("该文件ID不存在")
			return
		}
		if folderList[0].IsDeleted == 1 {
			rsp.Success = 1
			rsp.Message = "该文件已删除，禁止更新"
			return
		}

		folderList, err1 := models.MessageGroupFolderRef.List(ctx, userInfo.UserId, nil)
		if err1 != nil {
			err = err1
			return
		}

		for _, folder := range folderList {
			if req.FolderId != folder.ID && folder.Name == req.FolderName {
				rsp.Success = 1
				rsp.Message = "文件夹名称不可重复"
				return
			}
		}

		now := time.Now().Unix()
		folder := models.MessageGroupFolder{
			ID:         req.FolderId,
			Name:       req.FolderName,
			UpdateTime: now,
			UpdateUid:  int64(userInfo.UserId),
			UpdateName: userInfo.UserName,
		}
		err = models.MessageGroupFolderRef.BatchUpdateWithTx(ctx, []models.MessageGroupFolder{folder}, nil)
		if err != nil {
			return
		}
		rsp.FolderId = req.FolderId
	}

	return
}

func DeleteFolder(ctx *gin.Context, req dtomessage.DeleteFolderReq) (rsp dtomessage.CommonRsp, err error) {
	if req.FolderId == 0 {
		rsp.Success = 1
		rsp.Message = "文件ID不能为空"
		return
	}

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	folderList, err := models.MessageGroupFolderRef.GetById(ctx, req.FolderId)
	if err != nil {
		return
	}
	if len(folderList) == 0 {
		rsp.Success = 1
		rsp.Message = "该文件ID不存在"
		return
	}
	if folderList[0].IsDeleted == 1 {
		rsp.Success = 1
		rsp.Message = "该文件已删除，请勿重复删除"
		return
	}
	now := time.Now().Unix()

	folder := models.MessageGroupFolder{
		ID:         req.FolderId,
		IsDeleted:  1,
		UpdateTime: now,
		UpdateUid:  int64(userInfo.UserId),
		UpdateName: userInfo.UserName,
	}
	tx := models.MessageGroupFolderRef.Tx(ctx)
	// 1.删除
	// 删除文件夹
	err = models.MessageGroupFolderRef.BatchUpdateWithTx(ctx, []models.MessageGroupFolder{folder}, tx)
	if err != nil {
		tx.Rollback()
		return
	}

	// 删除消息组
	values := map[string]interface{}{
		"is_deleted":  1,
		"update_time": now,
		"update_uid":  userInfo.UserId,
		"update_name": userInfo.UserName,
	}

	messageGroupList, err := models.MessageGroupRef.ListByFolderId(ctx, req.FolderId)
	if err != nil {
		tx.Rollback()
		return
	}

	groupIds := make([]int64, 0)
	for _, messageGroup := range messageGroupList {
		groupIds = append(groupIds, messageGroup.ID)
	}
	err = models.MessageGroupRef.UpdateByFolderId(ctx, req.FolderId, values, tx)
	if err != nil {
		tx.Rollback()
		return
	}

	// 删除消息体
	values = map[string]interface{}{
		"is_deleted":  1,
		"update_time": now,
		"update_uid":  userInfo.UserId,
	}

	for _, groupId := range groupIds {
		err = models.MessageRef.UpdateByGroupId(ctx, groupId, values, tx)
		if err != nil {
			tx.Rollback()
			return
		}
	}

	// 2.更新剩余有效文件夹的顺序
	folderList, _err := models.MessageGroupFolderRef.List(ctx, userInfo.UserId, tx)
	if _err != nil {
		err = _err
		tx.Rollback()
		return
	}

	if len(folderList) == 0 {
		tx.Commit()
		return
	}
	sort.Slice(folderList, func(i, j int) bool {
		return folderList[i].Order <= folderList[j].Order
	})

	updateFolders := make([]models.MessageGroupFolder, 0)
	for index, folder := range folderList {
		updateFolders = append(updateFolders, models.MessageGroupFolder{
			ID:    folder.ID,
			Order: int64(index + 1),
		})
	}
	err = models.MessageGroupFolderRef.BatchUpdateWithTx(ctx, updateFolders, tx)
	if err != nil {
		tx.Rollback()
		return
	}
	tx.Commit()

	return
}

func FolderOrder(ctx *gin.Context, req dtomessage.FolderOrderReq) (rsp dtomessage.CommonRsp, err error) {
	if len(req.FolderIds) == 0 {
		rsp.Success = 1
		rsp.Message = "文件ID列表不能为空"
		return
	}

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	folderList, err := models.MessageGroupFolderRef.List(ctx, userInfo.UserId, nil)
	if err != nil {
		return
	}

	if len(folderList) == 0 {
		rsp.Success = 1
		rsp.Message = "目前不存在有效的文件夹，无法更新"
		return
	}

	validFolder := make(map[int64]models.MessageGroupFolder)
	validFolderIds := make([]int64, 0)
	for _, folder := range folderList {
		validFolder[folder.ID] = folder
		validFolderIds = append(validFolderIds, folder.ID)
	}

	redundant1, redundant2 := utils.FindDifferenceForInt64(req.FolderIds, validFolderIds)
	if len(redundant1) != 0 || len(redundant2) != 0 {
		zlog.Infof(ctx, "页面多余ID:%+v，数据库多余ID:%+v", redundant1, redundant2)
		rsp.Success = 1
		rsp.Message = "页面文件夹内容与数据库不一致，请刷新页面后再执行操作"
		return
	}

	updateFolderList := make([]models.MessageGroupFolder, 0)
	var order int64 = 1

	now := time.Now().Unix()

	// 页面是倒序展示的，所以保存顺序时需要先反转一下
	req.FolderIds = utils.ReverseSlice(req.FolderIds)

	for _, folderId := range req.FolderIds {
		folder, ok := validFolder[folderId]
		if !ok {
			continue
		}
		newFolder := models.MessageGroupFolder{
			ID:         folder.ID,
			Order:      order,
			UpdateTime: now,
			UpdateUid:  int64(userInfo.UserId),
			UpdateName: userInfo.UserName,
		}
		order++
		updateFolderList = append(updateFolderList, newFolder)
	}
	tx := helpers.MysqlClientFuDao.WithContext(ctx).Begin()
	err = models.MessageGroupFolderRef.BatchUpdateWithTx(ctx, updateFolderList, tx)
	if err != nil {
		tx.Rollback()
		return
	}
	tx.Commit()
	return
}
