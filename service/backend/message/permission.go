package message

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/conf"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/utils"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sort"
)

func GetParentPermissionGroupIds(ctx *gin.Context, staffUid int64) ([]int64, error) {
	permission, err := mesh.GetDataPermissionByStaffUid(ctx, staffUid)
	if err != nil {
		return nil, err
	}

	if permission.IsGlobal {
		return conf.Custom.Mesh.GroupId, nil
	}
	var groupDetail map[string]mesh.GroupDetail
	if groupDetail, err = mesh.GetGroupDetailByIds(ctx, permission.GroupIds); err != nil {
		return nil, err
	}

	groupIdMap := make(map[int64]struct{})
	for _, groupId := range permission.GroupIds {
		if _, ok := groupDetail[cast.ToString(groupId)]; !ok {
			continue
		}

		permissionDetail := groupDetail[cast.ToString(groupId)]
		currentItem := &permissionDetail.ParentTree
		for currentItem != nil {
			groupIdMap[currentItem.ID] = struct{}{}
			currentItem = currentItem.Children
		}
	}

	groupIds := make([]int64, 0)
	for groupId, _ := range groupIdMap {
		groupIds = append(groupIds, groupId)
	}
	return groupIds, nil
}

func BuildGroupPermissionTree(permission mesh.GroupTree, withPermission bool) *dtomessage.GroupPermissionNode {
	node := &dtomessage.GroupPermissionNode{
		ID:             int64(permission.Id),
		Name:           permission.Name,
		WithPermission: withPermission,
		Children:       make([]*dtomessage.GroupPermissionNode, 0),
	}

	if len(permission.Children) == 0 {
		return node
	}

	for _, child := range permission.Children {
		node.Children = append(node.Children, BuildGroupPermissionTree(child, withPermission))
	}
	return node
}

func FormatOrganizationTreeForGlobal(ctx *gin.Context) (groupTrees []*dtomessage.GroupPermissionNode, err error) {
	var item *mesh.GroupTree
	permissionTree := make([]mesh.GroupTree, 0)
	for _, groupId := range conf.Custom.Mesh.GroupId {
		if item, err = mesh.GetGroupAllChildren(ctx, groupId); err != nil {
			return nil, err
		}

		if item != nil {
			permissionTree = append(permissionTree, *item)
		}
	}

	groupTrees = make([]*dtomessage.GroupPermissionNode, 0)
	for _, permissionItem := range permissionTree {
		node := BuildGroupPermissionTree(permissionItem, true)
		groupTrees = append(groupTrees, node)
	}
	return
}

func GetOrganizationTree(ctx *gin.Context, staffUid int64) (groupTrees []*dtomessage.GroupPermissionNode, err error) {
	var permission mesh.Permission
	if permission, err = mesh.GetDataPermissionByStaffUid(ctx, staffUid); err != nil {
		return
	}

	if permission.IsGlobal {
		return FormatOrganizationTreeForGlobal(ctx)
	}

	// 向上取父节点
	var groupDetail map[string]mesh.GroupDetail
	if groupDetail, err = mesh.GetGroupDetailByIds(ctx, permission.GroupIds); err != nil {
		return nil, err
	}

	retMap := make(map[int64]struct{})
	nodeMap := make(map[int64]*dtomessage.GroupPermissionNode)
	// 标记链表中的节点是否被组装到树里
	childNodeMap := make(map[int64]map[int64]struct{})
	var item *mesh.GroupTree
	for _, groupId := range permission.GroupIds {
		if _, ok := groupDetail[cast.ToString(groupId)]; !ok {
			continue
		}

		permissionDetail := groupDetail[cast.ToString(groupId)]
		var permissionNode dtomessage.GroupPermissionNode
		if _, ok := nodeMap[permissionDetail.ParentTree.ID]; !ok {
			permissionNode = dtomessage.GroupPermissionNode{
				ID:       permissionDetail.ParentTree.ID,
				Name:     permissionDetail.ParentTree.Name,
				Children: make([]*dtomessage.GroupPermissionNode, 0),
			}
			nodeMap[permissionNode.ID] = &permissionNode // 通过map去重
		}
		permissionNode = *nodeMap[permissionDetail.ParentTree.ID]

		nodeMap, childNodeMap = BuildPermissionChildren(&permissionDetail.ParentTree, nodeMap, childNodeMap)

		// 每个节点向下取子节点
		if item, err = mesh.GetGroupAllChildren(ctx, groupId); err != nil {
			return nil, err
		}
		if item.Children == nil {
			continue
		}

		nodeMap = BuildPermission(item, nodeMap, childNodeMap)
		if _, ok := retMap[permissionNode.ID]; !ok {
			retMap[permissionNode.ID] = struct{}{}
		}
	}

	rootIds := make([]int, 0)
	for rootId, _ := range retMap {
		rootIds = append(rootIds, int(rootId))
	}
	sort.Ints(rootIds)

	ret := make([]*dtomessage.GroupPermissionNode, 0)
	for _, rootId := range rootIds {
		ret = append(ret, nodeMap[int64(rootId)])
	}

	return ret, err

}

func GetAllOrganizationTree(ctx *gin.Context) (groupTrees []mesh.GroupTree, err error) {
	productLineGroupList, err := mesh.GetAllProductLineGroup(ctx)
	if err != nil {
		return nil, err
	}
	permissionTree := make([]mesh.GroupTree, 0)
	var item *mesh.GroupTree
	for _, productLineGroup := range productLineGroupList {
		if productLineGroup.GroupId == 0 {
			continue
		}
		if item, err = mesh.GetGroupAllChildren(ctx, productLineGroup.GroupId); err != nil {
			return nil, err
		}

		if item != nil {
			permissionTree = append(permissionTree, *item)
		}
	}
	return permissionTree, nil

}

func BuildPermission(permission *mesh.GroupTree, nodeMap map[int64]*dtomessage.GroupPermissionNode, childNodeMap map[int64]map[int64]struct{}) map[int64]*dtomessage.GroupPermissionNode {
	if permission.Children == nil {
		return nodeMap
	}

	node := nodeMap[int64(permission.Id)]
	node.WithPermission = true
	for _, permissionDetail := range permission.Children {
		if _, ok := nodeMap[int64(permissionDetail.Id)]; !ok {
			permissionNode := dtomessage.GroupPermissionNode{
				ID:             int64(permissionDetail.Id),
				Name:           permissionDetail.Name,
				WithPermission: true,
				Children:       make([]*dtomessage.GroupPermissionNode, 0),
			}
			nodeMap[permissionNode.ID] = &permissionNode
			nodeMap = BuildPermission(&permissionDetail, nodeMap, childNodeMap)
		}

		if _, exist := childNodeMap[node.ID]; !exist {
			childNodeMap[node.ID] = make(map[int64]struct{})
		}

		if _, exist := childNodeMap[node.ID][int64(permissionDetail.Id)]; !exist {
			childNodeMap[node.ID][int64(permissionDetail.Id)] = struct{}{}
			node.Children = append(node.Children, nodeMap[int64(permissionDetail.Id)])
		}
	}
	return nodeMap
}

func BuildPermissionChildren(node *mesh.ParentTree, nodeMap map[int64]*dtomessage.GroupPermissionNode, childNodeMap map[int64]map[int64]struct{}) (map[int64]*dtomessage.GroupPermissionNode, map[int64]map[int64]struct{}) {
	if node.Children == nil {
		return nodeMap, childNodeMap
	}

	nodeInfo := nodeMap[node.ID]

	childNode := node.Children
	if _, ok := childNodeMap[node.ID][childNode.ID]; !ok {
		if _, exist := nodeMap[childNode.ID]; !exist {
			childNodeInfo := dtomessage.GroupPermissionNode{
				ID:       childNode.ID,
				Name:     childNode.Name,
				Children: make([]*dtomessage.GroupPermissionNode, 0),
			}
			nodeMap[childNode.ID] = &childNodeInfo
		}

		if _, exist := childNodeMap[node.ID]; !exist {
			childNodeMap[node.ID] = make(map[int64]struct{})
		}

		if _, exist := childNodeMap[node.ID][childNode.ID]; !exist {
			childNodeMap[node.ID][childNode.ID] = struct{}{}
			nodeInfo.Children = append(nodeInfo.Children, nodeMap[childNode.ID])
		}
	}

	return BuildPermissionChildren(node.Children, nodeMap, childNodeMap)
}

func GetPermissionGroupIds(ctx *gin.Context, staffUid int64, group int64) (groupIdList []int64, err error) {
	childGroupIdList, err := GetChildGroupId(ctx, staffUid, group)
	if err != nil {
		return
	}

	if len(childGroupIdList) == 0 {
		//说明是全量
		return nil, nil
	}

	if group == 0 {
		parentGroupIdList, err := GetParentPermissionGroupIds(ctx, staffUid)
		if err != nil {
			return nil, err
		}
		groupIdList = append(groupIdList, parentGroupIdList...)

	}

	groupIdList = append(groupIdList, childGroupIdList...)
	groupIdList = utils.FilterDuplicatesInt64(groupIdList)
	return
}

func GetOrganizationChildTree(ctx *gin.Context, staffUid int64) ([]mesh.GroupTree, bool, error) {
	permission, err := mesh.GetDataPermissionByStaffUid(ctx, staffUid)
	if err != nil {
		return nil, false, err
	}

	var groupTrees []mesh.GroupTree
	for _, groupId := range permission.GroupIds {
		gtree, _ := mesh.GetGroupAllChildren(ctx, groupId)
		groupTrees = append(groupTrees, *gtree)
	}
	return groupTrees, permission.IsGlobal, err

}

func GetChildGroupId(ctx *gin.Context, staffUid int64, groupId int64) (groupIdList []int64, err error) {
	trees, isGlobal, err := GetOrganizationChildTree(ctx, staffUid)
	if err != nil {
		return
	}

	if groupId > 0 {
		find := false
		var groupTree mesh.GroupTree
		for _, tree := range trees {
			groupNode := FindGroupTree(tree, groupId)
			if groupNode.Id > 0 {
				groupTree = groupNode
				find = true
				break
			}
		}
		if !find {
			return nil, errors.New("未找到权限组织")
		}
		trees = []mesh.GroupTree{groupTree}
	} else if isGlobal == true {
		//没传且全量数据
		return groupIdList, nil

	}

	for _, tree := range trees {
		subGroupIdList := FindChildGroupId(tree)
		if len(subGroupIdList) != 0 {
			groupIdList = append(groupIdList, subGroupIdList...)
		}
	}
	groupIdList = utils.FilterDuplicatesInt64(groupIdList)
	if len(groupIdList) == 0 {
		return nil, errors.New("无权限")
	}
	return groupIdList, nil
}

func FindChildGroupId(groupTree mesh.GroupTree) []int64 {
	var groupIdList = []int64{}
	if groupTree.Id <= 0 {
		return nil
	} else {
		groupIdList = append(groupIdList, int64(groupTree.Id))
	}
	for _, child := range groupTree.Children {
		childGroupId := FindChildGroupId(child)
		if childGroupId != nil {
			groupIdList = append(groupIdList, childGroupId...)
		}
	}

	return groupIdList

}

func FindGroupTree(groupTree mesh.GroupTree, groupId int64) mesh.GroupTree {
	if int64(groupTree.Id) == groupId {
		return groupTree
	}

	for _, childTree := range groupTree.Children {
		if int64(childTree.Id) == groupId {
			return childTree
		}
		tree := FindGroupTree(childTree, groupId)
		if tree.Id > 0 {
			return tree
		}
	}
	return mesh.GroupTree{}
}
