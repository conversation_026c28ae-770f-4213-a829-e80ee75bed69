package message

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGrayHit(t *testing.T) {
	env.SetRootPath("../../../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()
	helpers.InitApiClient()
	ctx := gin.CreateNewContext(engine)

	result, err := touchmisgo.GrayHit(ctx, touchmisgo.GrayHitRequest{
		PersonUid: 111,
		Key:       "muse_sensitive_log_gray_config",
	})
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(result)
}
