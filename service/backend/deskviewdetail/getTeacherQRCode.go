package deskviewdetail

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/dto/dtodeskviewdetail"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func GetTeacherQRCOde(ctx *gin.Context, req dtodeskviewdetail.GetTeacherQRCodeReq) (res dtodeskviewdetail.GetTeacherQRCodeResp, err error) {

	deviceUid := req.DeviceUid

	deviceInfoMap, err := mesh.GetDeviceInfoListByDeviceUidList(ctx, []int64{deviceUid})
	if err != nil {
		return
	}

	return dtodeskviewdetail.GetTeacherQRCodeResp{
		WxQRCode: deviceInfoMap[cast.ToString(deviceUid)].WxQRCodeUrl,
	}, nil
}
