package preclass

import (
	"assistantdeskgo/api/mesh"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/allocate"
	lessonconsts "git.zuoyebang.cc/fwyybase/fwyylibs/consts/lesson"
	"git.zuoyebang.cc/fwyybase/fwyylibs/defines"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"sync"
)

const (
	redisCacheKeyPrefix = "assistantdeskgo_ai_preclass_state_mq"
	defaultExpireTime   = 60 * 60 * 24
)

func AIAutoUpdatePreClassState(ctx *gin.Context, aiAutoTag dtopreclass.AIAutoTag) error {
	deviceInfo, err := mesh.GetDeviceInfoListByDeviceUids(ctx, aiAutoTag.AssistantUID)
	if err != nil {
		return errors.WithMessagef(err, "[AIAutoUpdatePreClassState] get device info by assistant uid fail")
	}

	leadsRsp, err := allocate.GetLeadsByCourseIdUid(ctx, allocate.GetLeadsByCourseIdUidReq{
		StudentUid: aiAutoTag.StudentUID,
		CourseId:   aiAutoTag.CourseID,
	})
	if err != nil {
		return errors.WithMessagef(err, "[AIAutoUpdatePreClassState] get leads fail")
	}

	ch := make(chan error, len(aiAutoTag.LessonPreClassTagInfos))
	wg := &sync.WaitGroup{}
	for _, lessonPreClassTagInfo := range aiAutoTag.LessonPreClassTagInfos {
		tmpLessonPreClassTagInfo := lessonPreClassTagInfo
		lessonId := tmpLessonPreClassTagInfo.LessonID
		if existCache(ctx, aiAutoTag.MsgID, lessonId) {
			zlog.Infof(ctx, "[AIAutoUpdatePreClassState] has been processed, msgID: %d, lessonId: %d", aiAutoTag.MsgID, lessonId)
			continue
		}

		wg.Add(1)
		fwyyutils.GoWithRecover(ctx, func() {
			defer wg.Done()
			preClassTagInfo := defines.PreClassTagInfo{
				Type:          lessonconsts.PreClassAdd,
				PreClassState: lessonconsts.PreClassState(tmpLessonPreClassTagInfo.TagType),
				LessonIds: []int64{
					lessonId,
				},
				OnlyUpdateState: false,
			}

			extInfo := tmpLessonPreClassTagInfo.ExtInfo
			if extInfo != nil {
				preClassTagInfo.LeaveSeason = extInfo.LeaveSeason
				preClassTagInfo.FirstLeaveReason = extInfo.FirstLeaveReason
				if extInfo.ContentTime > 0 {
					preClassTagInfo.ContentTime = cast.ToString(extInfo.ContentTime)
				}
				preClassTagInfo.AccompanyTag = extInfo.BanxueTag
			}

			_, err = UpdateMultiPreClassTag(ctx, dtopreclass.UpdateMultiPreClassTagReq{
				AssistantUid: aiAutoTag.AssistantUID,
				PersonUid:    int64(deviceInfo.StaffUid),
				CourseId:     aiAutoTag.CourseID,
				StudentInfos: []dtopreclass.StudentInfo{
					{
						LeadsId:    leadsRsp.LeadsId,
						StudentUid: aiAutoTag.StudentUID,
					},
				},
				PreClassTagInfo: preClassTagInfo,
				UpdateTime:      aiAutoTag.CreateTime,
				DataSource:      defines.PreClassDataFromAi,
			})
			if err != nil {
				ch <- err
				return
			}

			// 设置
			setCache(ctx, aiAutoTag.MsgID, lessonId)
		})
	}
	wg.Wait()
	close(ch)

	for err := range ch {
		if err != nil {
			return errors.WithMessagef(err, "[AIAutoUpdatePreClassState] update leads fail")
		}
	}
	return nil
}

func getCacheKey(msgID string, lessonId int64) string {
	return fmt.Sprintf("%s_%s_%d", redisCacheKeyPrefix, msgID, lessonId)
}

func existCache(ctx *gin.Context, msgID string, lessonId int64) bool {
	cacheKey := getCacheKey(msgID, lessonId)
	data, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err != nil {
		return false
	}

	if cast.ToString(data) == "1" {
		return true
	}
	return false
}

func setCache(ctx *gin.Context, msgID string, lessonId int64) {
	cacheKey := getCacheKey(msgID, lessonId)
	err := fwyyutils.RunWithRetry(func(idx int) error {
		return helpers.RedisClient.SetEx(ctx, cacheKey, "1", defaultExpireTime)
	})
	if err != nil {
		zlog.Warnf(ctx, "[AIAutoUpdatePreClassState] set cache fail, key: %s, err: %v", cacheKey, err)
	}
}
