package preclass

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	redisPreClassDetailCacheKeyPrefix = "assistantdeskgo_preclass_detail"
	tenMinuteExpireTime               = 10 * 60
)

func LessonStudentProxy(ctx *gin.Context, courseId, lessonId int64, studentUids []int64) ([]*models.LessonStudent, error) {
	studentSet := fwyyutils.NewInt64Set()
	studentSet.Adds(studentUids)
	// 先从 redis 获取
	redisLessonStudents := getPreClassDetailFromCache(ctx, courseId, lessonId, studentUids)
	for _, ls := range redisLessonStudents {
		studentSet.Remove(ls.StudentUid)
	}

	zlog.Infof(ctx, "[LessonStudentProxy] total student: %+v, notExistCacheStudentIds: %+v", studentUids, studentSet.AsList())
	if studentSet.Len() == 0 {
		return redisLessonStudents, nil
	}

	notExistCacheStudentIds := studentSet.AsList()
	lessonStudents, err := models.LessonStudentRef.GetByStudentIds(ctx, courseId, lessonId, notExistCacheStudentIds)
	if err != nil {
		return nil, err
	}

	// 设置缓存
	setPreClassDetailCache(ctx, lessonStudents)

	// 加上 redis 查出来的数据
	lessonStudents = append(lessonStudents, redisLessonStudents...)
	return lessonStudents, nil
}

func getPreClassDetailCacheKey(courseId, lessonId, studentId int64) string {
	return fmt.Sprintf("{%s_%d}_%d_%d", redisPreClassDetailCacheKeyPrefix, courseId, lessonId, studentId)
}

func getPreClassDetailFromCache(ctx *gin.Context, courseId, lessonId int64, studentIds []int64) []*models.LessonStudent {
	cacheKeys := make([]string, 0)
	for _, studentId := range studentIds {
		cacheKeys = append(cacheKeys, getPreClassDetailCacheKey(courseId, lessonId, studentId))
	}
	datas, err := helpers.RedisClient.MGET(ctx, cacheKeys...)
	if err != nil {
		return nil
	}

	if len(datas) == 0 {
		return nil
	}

	ls := make([]*models.LessonStudent, 0)
	for _, data := range datas {
		tmpVal := cast.ToString(data)
		if len(tmpVal) == 0 {
			continue
		}

		var tmpLessonStudent *models.LessonStudent
		if err := jsoniter.UnmarshalFromString(tmpVal, &tmpLessonStudent); err != nil {
			zlog.Warnf(ctx, "[getPreClassDetailFromCache] unmarshal lesson_student json err: %v", err)
			continue
		}
		ls = append(ls, tmpLessonStudent)
	}
	return ls
}

func setPreClassDetailCache(ctx *gin.Context, lessonStudents []*models.LessonStudent) {
	if len(lessonStudents) == 0 {
		return
	}
	scriptData := make([]interface{}, 0)
	cacheKeys, lessonStudentJsonValue := make([]interface{}, 0), make([]interface{}, 0)
	for _, lessonStudent := range lessonStudents {
		cacheKey := getPreClassDetailCacheKey(lessonStudent.CourseId, lessonStudent.LessonId, lessonStudent.StudentUid)

		lessonStudentJson, err := jsoniter.MarshalToString(lessonStudent)
		if err != nil {
			continue
		}

		cacheKeys = append(cacheKeys, cacheKey)
		lessonStudentJsonValue = append(lessonStudentJsonValue, lessonStudentJson)
	}

	scriptData = append(scriptData, cacheKeys...)
	scriptData = append(scriptData, lessonStudentJsonValue...)

	script := fmt.Sprintf(`
           for idx, key in ipairs(KEYS) do 
                redis.call("SETEX", key, %d, ARGV[idx])
           end`, tenMinuteExpireTime)
	_, err := helpers.RedisClient.Lua(ctx, script, len(cacheKeys), scriptData...)
	if err != nil {
		zlog.Warnf(ctx, "[setPreClassDetailCache] set cache fail, lessonStudent: %s, err: %v", fwyyutils.MarshalIgnoreError(lessonStudents), err)
	}
}

func delPreClassDetailCache(ctx *gin.Context, keys []string) {
	if len(keys) == 0 {
		return
	}

	tmpKeys := make([]interface{}, 0)
	for _, key := range keys {
		tmpKeys = append(tmpKeys, key)
	}
	if err := fwyyutils.RunWithRetry(func(idx int) error {
		_, err := helpers.RedisClient.Del(ctx, tmpKeys...)
		return err
	}); err != nil {
		zlog.Warnf(ctx, "[delPreClassDetailCache] del cache fail, keys: %v, err: %+v", keys, err)
		return
	}
	zlog.Infof(ctx, "[delPreClassDetailCache] del cache success, keys: %v", keys)
}
