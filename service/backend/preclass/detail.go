package preclass

import (
	"assistantdeskgo/components"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/models"
	lessonconsts "git.zuoyebang.cc/fwyybase/fwyylibs/consts/lesson"
	"git.zuoyebang.cc/fwyybase/fwyylibs/defines"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetPreClassDetail(ctx *gin.Context, req dtopreclass.GetPreClassDetailReq) (*dtopreclass.GetPreClassDetailRsp, error) {
	if err := checkGetDetailParam(ctx, req); err != nil {
		return nil, err
	}

	lessonStudents, err := LessonStudentProxy(ctx, req.CourseId, req.LessonId, req.StudentUids)
	if err != nil {
		return nil, err
	}

	lessonStudentsMap := make(map[int64]*models.LessonStudent)
	for _, lessStudent := range lessonStudents {
		tmpLessStudent := lessStudent
		lessonStudentsMap[tmpLessStudent.StudentUid] = tmpLessStudent
	}

	failedStudents := make([]int64, 0)
	preClassDetails := make([]*dtopreclass.PreClassDetail, 0)
	for _, studentId := range req.StudentUids {
		tmpStudentId := studentId
		tmpStud, exist := lessonStudentsMap[tmpStudentId]
		if exist {
			extraDataInfo, err := defines.ParseLessonStudentExtraData(ctx, tmpStud.ExtData)
			if err != nil {
				failedStudents = append(failedStudents, tmpStud.StudentUid)
				zlog.Warnf(ctx, "[GetPreClassDetail] parseLessonStudentExtraData failed, tmpStud: %s, err: %+v", fwyyutils.MarshalIgnoreError(tmpStud), err)
				continue
			}
			preClassDetails = append(preClassDetails, &dtopreclass.PreClassDetail{
				Id:               tmpStud.ID,
				StudentUid:       tmpStud.StudentUid,
				CourseId:         tmpStud.CourseId,
				LessonId:         tmpStud.LessonId,
				AssistantUid:     tmpStud.AssistantUid,
				PreAttend:        tmpStud.PreAttend,
				CreateTime:       tmpStud.CreateTime,
				UpdateTime:       tmpStud.UpdateTime,
				PreAttendTime:    extraDataInfo.GetPreAttendTime(),
				LeaveSeason:      extraDataInfo.GetLeaveSeason(),
				FirstLeaveReason: extraDataInfo.GetFirstLeaveReason(),
				ContentTime:      extraDataInfo.GetContentTime(),
				RemindTime:       extraDataInfo.GetRemindTime(),
				AccompanyTag:     extraDataInfo.GetAccompanyTag(),
				IsSyncRemind:     extraDataInfo.GetIsSyncRemind(),
				RemindId:         extraDataInfo.GetRemindId(),
			})
		} else {
			// 默认为未填
			preClassDetails = append(preClassDetails, &dtopreclass.PreClassDetail{
				StudentUid: studentId,
				CourseId:   req.CourseId,
				LessonId:   req.LessonId,
				PreAttend:  lessonconsts.PreClassStateNone.ToInt(),
			})
		}
	}

	return &dtopreclass.GetPreClassDetailRsp{
		PreClassDetails: preClassDetails,
		FailedStudents:  failedStudents,
	}, nil
}

func checkGetDetailParam(ctx *gin.Context, req dtopreclass.GetPreClassDetailReq) error {
	if req.CourseId <= 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[GetPreClassDetail] req.CourseId empty")
	}

	if req.LessonId <= 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[GetPreClassDetail] req.LessonId empty")
	}

	if len(req.StudentUids) == 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[GetPreClassDetail] req.StudentUids empty")
	}
	return nil
}
