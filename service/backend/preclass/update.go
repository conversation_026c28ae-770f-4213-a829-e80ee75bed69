package preclass

import (
	"assistantdeskgo/api/muse"
	"assistantdeskgo/components"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/duxuesc"
	lessonconsts "git.zuoyebang.cc/fwyybase/fwyylibs/consts/lesson"
	"git.zuoyebang.cc/fwyybase/fwyylibs/defines"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go.uber.org/ratelimit"
	"sync"
	"unicode/utf8"
)

func UpdateMultiPreClassTag(ctx *gin.Context, req dtopreclass.UpdateMultiPreClassTagReq) (*dtopreclass.UpdateMultiPreClassTagRsp, error) {
	if err := checkUpdateMultiTagParam(ctx, req); err != nil {
		return nil, err
	}

	limiter := ratelimit.New(components.MaxGoroutineNum)
	ch := make(chan dtopreclass.UpdatePreClassTagDto, len(req.StudentInfos))
	wg := &sync.WaitGroup{}

	for _, studentInfo := range req.StudentInfos {
		tmpStudentInfo := studentInfo
		wg.Add(1)
		fwyyutils.GoWithRecover(ctx, func() {
			defer wg.Done()
			limiter.Take()

			preClassTagDto := dtopreclass.UpdatePreClassTagDto{
				StudentUID: tmpStudentInfo.StudentUid,
			}

			tmpTagResult, err := UpdatePreClassTag(ctx, dtopreclass.UpdatePreClassTagReq{
				AssistantUid:    req.AssistantUid,
				PersonUid:       req.PersonUid,
				CourseId:        req.CourseId,
				LeadsId:         tmpStudentInfo.LeadsId,
				StudentUid:      tmpStudentInfo.StudentUid,
				PreClassTagInfo: req.PreClassTagInfo,
				UpdateTime:      req.UpdateTime,
				DataSource:      req.DataSource,
			})
			if err != nil {
				zlog.Warnf(ctx, "[UpdateMultiPreClassTag] UpdatePreClassTag failed, studentUid: %d, err: %v", tmpStudentInfo.StudentUid, err)
				preClassTagDto.Err = err
			} else {
				preClassTagDto.LessonResult = tmpTagResult.LessonStateResults
				preClassTagDto.UpdatedKeys = tmpTagResult.UpdatedKeys
			}
			ch <- preClassTagDto
		})
	}
	wg.Wait()
	close(ch)

	rsp := &dtopreclass.UpdateMultiPreClassTagRsp{
		FailedStudentIds:    []int64{},
		PreClassResultInfos: []*dtopreclass.PreClassResultInfo{},
	}
	updateAchillesKeySet := fwyyutils.NewStringSet()
	delRedisKeys := fwyyutils.NewStringSet()
	for resultDto := range ch {
		if resultDto.LessonResult != nil {
			rsp.PreClassResultInfos = append(rsp.PreClassResultInfos, &dtopreclass.PreClassResultInfo{
				StudentUid:         resultDto.StudentUID,
				LessonStateResults: resultDto.LessonResult,
			})

			for _, key := range resultDto.UpdatedKeys {
				tmpKey := key
				updateAchillesKeySet.Add(tmpKey.FormatAchillesKey())
				delRedisKeys.Add(getPreClassDetailCacheKey(tmpKey.CourseID, tmpKey.LessonID, tmpKey.StudentID))
			}
		} else {
			rsp.FailedStudentIds = append(rsp.FailedStudentIds, resultDto.StudentUID)
		}
	}

	delPreClassDetailCache(ctx, delRedisKeys.AsList())
	// 这里先返回报错，避免数据不一致而老师没有感知
	if err := sendToAchilles(ctx, updateAchillesKeySet.AsList()); err != nil {
		return nil, errors.WithMessage(err, "[UpdateMultiPreClassTag] send to achilles failed")
	}
	return rsp, nil
}

func UpdatePreClassTag(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq) (*dtopreclass.UpdatePreClassTagRsp, error) {
	tagInfo := req.PreClassTagInfo
	lessonStudentList, err := models.LessonStudentRef.GetByLessonsAndStudent(ctx, req.CourseId, req.StudentUid, tagInfo.LessonIds)
	if err != nil {
		return nil, errors.WithMessagef(err, "[UpdatePreClassTag] get lessonStudent info failed")
	}

	lessonStudentMap := make(map[int64]*models.LessonStudent)
	for _, student := range lessonStudentList {
		tmpStud := student
		lessonStudentMap[tmpStud.LessonId] = tmpStud
	}

	if req.LeadsId > 0 {
		if err := syncToDuXueSc(ctx, req, lessonStudentMap); err != nil {
			return nil, errors.WithMessagef(err, "[UpdatePreClassTag] syncToDuXueSc falied")
		}
	}

	updatedKeys := make([]defines.PreClassConfigAchillesKey, 0)
	result := make([]*dtopreclass.LessonStateResult, 0)
	for _, lessonId := range tagInfo.LessonIds {
		tmpLessonId := lessonId
		lessonStud, exist := lessonStudentMap[tmpLessonId]
		if !exist {
			err = processSingleLessonWithAdd(ctx, req, tmpLessonId)
		} else {
			err = processSingleLessonWithUpdate(ctx, req, tmpLessonId, lessonStud)
		}

		tmpResult := &dtopreclass.LessonStateResult{
			LessonId:      tmpLessonId,
			PreClassState: tagInfo.PreClassState,
			EditSuccess:   dtopreclass.EditSuccess,
		}
		if err != nil {
			tmpResult.EditSuccess = dtopreclass.EditFailed
			zlog.Warnf(ctx, "[UpdatePreClassTag] update lessonStudent info failed, lessonId: %d, req: %s, err: %+v",
				tmpLessonId, fwyyutils.MarshalIgnoreError(req), err)
		} else {
			tmpConfKey := defines.PreClassConfigAchillesKey{
				CourseID:  req.CourseId,
				LessonID:  tmpLessonId,
				StudentID: req.StudentUid,
			}
			updatedKeys = append(updatedKeys, tmpConfKey)
		}
		result = append(result, tmpResult)
	}
	return &dtopreclass.UpdatePreClassTagRsp{
		LessonStateResults: result,
		UpdatedKeys:        updatedKeys,
	}, nil
}

func processSingleLessonWithAdd(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq, lessonId int64) error {
	tagInfo := req.PreClassTagInfo
	if tagInfo.Type != lessonconsts.PreClassAdd {
		zlog.Warnf(ctx, "[ProcessSingleLessonWithAdd] invalid pre class tag type, type: %s", tagInfo.Type)
		return nil
	}

	lessonStud := &models.LessonStudent{
		StudentUid:   req.StudentUid,
		CourseId:     req.CourseId,
		LessonId:     lessonId,
		AssistantUid: req.AssistantUid,
		Status:       models.StatusOK,
		PreAttend:    tagInfo.PreClassState.ToInt(),
	}

	extraDataInfo := make(defines.ExtraDataInfo)
	extraDataInfo.SetPreAttendTime(req.UpdateTime)
	extraDataInfo.SetPreAttendDataSource(req.DataSource)
	switch tagInfo.PreClassState {
	case lessonconsts.PreClassStateLeave:
		extraDataInfo.SetLeaveTag(tagInfo)
	case lessonconsts.PreClassStatePlayback:
		extraDataInfo.SetPlayBackTag(tagInfo)
	case lessonconsts.PreClassStateAccompany:
		extraDataInfo.SetAccompanyTag(tagInfo)
	}

	if tagInfo.IsSyncRemind.IsSync() {
		// 同步待办
		remindId := syncRemind(ctx, req, 0)
		extraDataInfo.SetRemind(tagInfo, remindId)
	}

	extraDataStr, err := extraDataInfo.Marshal()
	if err != nil {
		return errors.WithMessage(err, "[ProcessSingleLessonWithAdd] marshal extraDataInfo failed")
	}
	lessonStud.ExtData = extraDataStr
	return models.LessonStudentRef.Create(ctx, req.CourseId, lessonStud)
}

func processSingleLessonWithUpdate(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq, lessonId int64, lessonStud *models.LessonStudent) error {
	tagInfo := req.PreClassTagInfo
	// 说明之前的数据作废，做一下初始化
	if lessonStud.Status != models.StatusOK {
		zlog.Infof(ctx, "[processSingleLessonWithUpdate] refresh old data")
		lessonStud = &models.LessonStudent{
			ClassId:   0,
			PreAttend: lessonconsts.PreClassStateNone.ToInt(),
			Status:    models.StatusOK,
			Focus:     models.FocusNot,
			ExtData:   "{}",
		}
	}

	// 添加分布式锁，防止
	lockKey := fmt.Sprintf("deskgo_update_preclass_%d_%d_%d", req.CourseId, lessonId, req.StudentUid)
	lockVal := uuid.New().String()
	lock, err := utils.LockRetry(ctx, lockKey, lockVal, 5, 3)
	if !lock {
		return errors.WithMessagef(err, "[processSingleLessonWithUpdate] get lock failed")
	}
	defer func() {
		if _, err = utils.ReleaseLockByValue(ctx, lockKey, lockVal); err != nil {
			zlog.Warnf(ctx, "[processSingleLessonWithUpdate] del lock failed")
		}
	}()

	// 重新查一遍数据，防止数据已经被更新
	if lessonStud, err = models.LessonStudentRef.GetByLessonAndStudent(ctx, req.CourseId, lessonId, req.StudentUid); err != nil {
		return errors.WithMessagef(err, "[processSingleLessonWithUpdate] GetByLessonAndStudent info failed")
	}

	extraDataInfo, err := defines.ParseLessonStudentExtraData(ctx, lessonStud.ExtData)
	if err != nil {
		return err
	}
	lastUpdateAttendTime := extraDataInfo.GetPreAttendTime()
	if lastUpdateAttendTime > req.UpdateTime {
		zlog.Warnf(ctx, "[processSingleLessonWithUpdate] update lessonStudent info failed, the current data is older, "+
			"extra: %s, req: %s", lessonStud.ExtData, fwyyutils.MarshalIgnoreError(req))
		return nil
	}

	switch tagInfo.Type {
	case lessonconsts.PreClassAdd:
		processSingleLessonWithAddUpdate(ctx, req, extraDataInfo, lessonStud)
	case lessonconsts.PreClassCancel:
		processSingleLessonWithCancelUpdate(ctx, req, extraDataInfo, lessonStud)
	default:
		return errors.Errorf("[processSingleLessonWithUpdate] invalid type: %s", tagInfo.Type)
	}

	// 更新 DB
	extraStr, err := extraDataInfo.Marshal()
	if err != nil {
		return err
	}
	lessonStud.ExtData = extraStr
	lessonStud.AssistantUid = req.AssistantUid
	return models.LessonStudentRef.UpdateByUniqueKey(ctx, req.CourseId, lessonId, req.StudentUid, lessonStud)
}

func processSingleLessonWithAddUpdate(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq, extraDataInfo defines.ExtraDataInfo, lessonStud *models.LessonStudent) {
	tagInfo := req.PreClassTagInfo
	playBackTag := false

	// 更新之前先清除数据(不一致的时候才需要清)
	if tagInfo.PreClassState.ToInt() != lessonStud.PreAttend {
		extraDataInfo.RefreshState()
	}

	// 设置标签
	lessonStud.PreAttend = tagInfo.PreClassState.ToInt()
	extraDataInfo.SetPreAttendTime(req.UpdateTime)
	extraDataInfo.SetPreAttendDataSource(req.DataSource)
	if !tagInfo.OnlyUpdateState {
		switch tagInfo.PreClassState {
		case lessonconsts.PreClassStateLeave:
			extraDataInfo.SetLeaveTag(tagInfo)
		case lessonconsts.PreClassStatePlayback:
			playBackTag = true
			extraDataInfo.SetPlayBackTag(tagInfo)
		case lessonconsts.PreClassStateAccompany:
			extraDataInfo.SetAccompanyTag(tagInfo)
		}
	}

	remindId := extraDataInfo.GetRemindId()
	if playBackTag && tagInfo.IsSyncRemind.IsSync() {
		// 同步待办
		remindId = syncRemind(ctx, req, remindId)
		extraDataInfo.SetRemind(tagInfo, remindId)
	} else {
		if remindId > 0 {
			// 删除待办
			if _, err := muse.DeleteRemind(ctx, &muse.DeleteRemindApiReq{
				Uid:      req.PersonUid,
				RemindId: remindId,
			}); err != nil {
				zlog.Warnf(ctx, "[processSingleLessonWithAddUpdate] delete remind failed, err: %+v", err)
			}
			extraDataInfo.RefreshRemind()
		}
	}
}

func processSingleLessonWithCancelUpdate(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq, extraDataInfo defines.ExtraDataInfo, lessonStud *models.LessonStudent) {
	tagInfo := req.PreClassTagInfo
	// 如果取消的和之前不一致，则不做操作
	if lessonStud.PreAttend != tagInfo.PreClassState.ToInt() {
		return
	}

	// 设置标签
	lessonStud.PreAttend = lessonconsts.PreClassStateNone.ToInt()
	extraDataInfo.SetPreAttendTime(req.UpdateTime)
	extraDataInfo.SetPreAttendDataSource(req.DataSource)
	extraDataInfo.RefreshExistState(tagInfo.PreClassState)

	// 删除待办
	remindId := extraDataInfo.GetRemindId()
	if remindId > 0 {
		// 删除待办
		if _, err := muse.DeleteRemind(ctx, &muse.DeleteRemindApiReq{
			Uid:      req.PersonUid,
			RemindId: remindId,
		}); err != nil {
			zlog.Warnf(ctx, "[processSingleLessonWithCancelUpdate] delete remind failed, err: %+v", err)
		}
		extraDataInfo.RefreshRemind()
	}
}

func checkUpdateMultiTagParam(ctx *gin.Context, req dtopreclass.UpdateMultiPreClassTagReq) error {
	if req.AssistantUid <= 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[checkUpdateMultiTagParam] req.AssistantUid empty")
	}

	if req.CourseId <= 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[checkUpdateMultiTagParam] req.CourseId empty")
	}

	if len(req.StudentInfos) == 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[checkUpdateMultiTagParam] req.StudentInfos empty")
	}

	for idx, info := range req.StudentInfos {
		if info.StudentUid == 0 {
			return components.ErrorParamInvalidFormat.Sprintf(fmt.Sprintf("[checkUpdateMultiTagParam] req.StudentInfos.StudentUid[index: %d] empty", idx))
		}
	}

	tagInfo := req.PreClassTagInfo
	if len(tagInfo.LessonIds) == 0 {
		return components.ErrorParamInvalidFormat.Sprintf("[checkUpdateMultiTagParam] req.PreClassTagInfo.LessonIds empty")
	}

	if _, exist := lessonconsts.PreClassStateMap[tagInfo.PreClassState]; !exist {
		return components.ErrorParamInvalidFormat.Sprintf(fmt.Sprintf("[checkUpdateMultiTagParam] req.PreClassTagInfo.PreClassState unknown: %v", tagInfo.PreClassState))
	}

	if utf8.RuneCountInString(tagInfo.LeaveSeason) > 50 {
		return components.ErrorParamInvalidFormat.Sprintf(fmt.Sprintf("[checkUpdateMultiTagParam] req.PreClassTagInfo.LeaveSeason "+
			"character length exceeds limit, leaveSeason: %s", tagInfo.LeaveSeason))
	}
	return nil
}

func syncToDuXueSc(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq, lessonStudentMap map[int64]*models.LessonStudent) error {
	tagInfo := req.PreClassTagInfo
	dayOffLessonIds := make([]int64, 0)
	removeDayOffLessonIds := make([]int64, 0)
	for _, lessonId := range tagInfo.LessonIds {
		lessonStud, exist := lessonStudentMap[lessonId]
		if !exist {
			zlog.Infof(ctx, "[syncToDuXueSc] lessonStudent not exist, lessonId: %d", lessonId)
			continue
		}

		if lessonStud.PreAttend == tagInfo.PreClassState.ToInt() {
			removeDayOffLessonIds = append(removeDayOffLessonIds, lessonId)
			continue
		}

		if tagInfo.PreClassState == lessonconsts.PreClassStateLeave {
			dayOffLessonIds = append(dayOffLessonIds, lessonId)
		} else {
			removeDayOffLessonIds = append(removeDayOffLessonIds, lessonId)
		}
	}

	if _, err := duxuesc.EditStudentDayOffCommonStatus(ctx, &duxuesc.EditStudentDayOffCommonStatusReq{
		LeadsId:               req.LeadsId,
		CourseId:              req.CourseId,
		LpcUid:                req.PersonUid,
		StudentUid:            req.StudentUid,
		RemoveDayOffLessonIds: removeDayOffLessonIds,
		DayOffLessonIds:       dayOffLessonIds,
	}); err != nil {
		return errors.WithMessagef(err, "[syncToDuXueSc] EditStudentDayOffCommonStatus failed")
	}
	return nil
}

func syncRemind(ctx *gin.Context, req dtopreclass.UpdatePreClassTagReq, remindId int64) int64 {
	tagInfo := req.PreClassTagInfo
	contentTime := carbon.CreateFromTimestamp(cast.ToInt64(tagInfo.ContentTime))
	remindTime := fmt.Sprintf("%s %s", contentTime.ToDateString(), tagInfo.RemindTime)
	content := fmt.Sprintf("学员请假，回放今日待完成，回放时间：%s", contentTime.ToDateTimeString())

	addReq := &muse.AddRemindReq{
		Uid:        req.PersonUid,
		AppType:    muse.RemindTypeAssistant,
		Content:    content,
		FromType:   1,
		RemindTime: remindTime,
		RemindId:   remindId,
		Students: []muse.StudentInfo{
			{
				CourseId:   req.CourseId,
				StudentUid: req.StudentUid,
			},
		},
	}
	addRsp, err := muse.AddRemind(ctx, addReq)
	if err != nil {
		zlog.Warnf(ctx, "[syncRemind] AddRemindErr: %+v, addReq: %s", err, fwyyutils.MarshalIgnoreError(addReq))
		return remindId
	}
	return addRsp.RemindId
}

func sendToAchilles(ctx *gin.Context, updateKeys []string) error {
	if len(updateKeys) == 0 {
		return nil
	}

	res := make(map[string]interface{})
	res["configKeys"] = updateKeys

	// achilles 那边收到的消息体与兜底接口的入参格式保持一致，那边收到通知会同步根据
	var nResp rmq.NmqResponse
	var err error
	err = fwyyutils.RunWithRetry(func(idx int) error {
		nResp, err = rmq.SendCmd(ctx, components.TouchConfigNotifyProducer, components.TouchConfigNotifyCommonNoEvent, components.Topic, components.Product, res, "")
		return err
	})
	zlog.Infof(ctx, "[sendToAchilles] send to mq[%d], transID: %v, err: %+v, updateKeys: %s", components.TouchConfigNotifyCommonNoEvent,
		nResp.TransID, err, fwyyutils.MarshalIgnoreError(res))
	if err != nil {
		return errors.New(fmt.Sprintf("[saveAIConfig] 更新 Achilles 配置失败"))
	}
	return nil
}
