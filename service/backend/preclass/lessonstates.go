package preclass

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/dal"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/models"
	"assistantdeskgo/service/common"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/defines"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"sort"
	"strings"
)

func GetAllLessonStates(ctx *gin.Context, req dtopreclass.GetAllLessonStatesReq) (*dtopreclass.GetAllLessonStatesRsp, error) {
	teacherNames, err := commonservice.GetTeacherNameByCourseIds(ctx, req.CourseId)
	if err != nil {
		return nil, errors.WithMessagef(err, "[GetAllLessonStates] GetTeacherNameByCourseIds failed")
	}
	teacherName := strings.Join(teacherNames, ",")

	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, req.CourseId,
		[]string{"courseId", "courseName"},
		[]string{"lessonId", "lessonName", "startTime", "stopTime", "lessonType", "status", "playType"})
	if err != nil {
		return nil, errors.WithMessagef(err, "[GetAllLessonStates] GetCourseLessonInfoByCourseId failed")
	}

	if len(courseLessonInfo.LessonList) == 0 {
		return &dtopreclass.GetAllLessonStatesRsp{
			CourseName:  courseLessonInfo.CourseName,
			TeacherName: teacherName,
		}, nil
	}

	lessonIds := make([]int64, 0)
	for _, info := range courseLessonInfo.LessonList {
		tmpInfo := info
		lessonIds = append(lessonIds, int64(tmpInfo.LessonId))
	}

	lessonStudents, err := models.LessonStudentRef.GetByLessonsAndStudent(ctx, req.CourseId, req.StudentUid, lessonIds)
	if err != nil {
		return nil, errors.WithMessagef(err, "[GetAllLessonStates] GetByLessonStudent failed")
	}
	lessonStudentsMap := make(map[int64]*models.LessonStudent)
	for _, student := range lessonStudents {
		lessonStudentsMap[student.LessonId] = student
	}

	var lessonPlayTypeMap map[string]dtopreclass.LessonPlayTypeDto
	if err = assistantdesk.GetKeyForJsonMap(ctx, assistantdesk.LessonPlayTypeKey, &lessonPlayTypeMap); err != nil {
		return nil, errors.WithMessagef(err, "[GetAllLessonStates] GetKeyForJsonMap failed")
	}

	lessonStateInfos := make([]*dtopreclass.LessonStateInfo, 0)
	for _, info := range courseLessonInfo.LessonList {
		tmpInfo := info
		tmpLessonStateInfo := &dtopreclass.LessonStateInfo{
			LessonId:        tmpInfo.LessonId,
			LessonName:      tmpInfo.LessonName,
			LessonTime:      lessonTime(tmpInfo.StartTime, tmpInfo.StopTime),
			LessonStartTime: tmpInfo.StartTime,
			LessonStopTime:  tmpInfo.StopTime,
		}

		if lessonPlayTypeDto, exist := lessonPlayTypeMap[cast.ToString(tmpInfo.PlayType)]; exist {
			tmpLessonStateInfo.PlayTypeText = lessonPlayTypeDto.Name
		}

		if lessonStud, exist := lessonStudentsMap[int64(tmpInfo.LessonId)]; exist {
			tmpLessonStateInfo.PreAttend = lessonStud.PreAttend
			extraDataInfo, err := defines.ParseLessonStudentExtraData(ctx, lessonStud.ExtData)
			if err != nil {
				zlog.Warnf(ctx, "[GetAllLessonStates] parseLessonStudentExtraData failed, lessonId: %d, extra: %s, err: %+v",
					tmpInfo.LessonId, lessonStud.ExtData, err)
				return nil, errors.WithMessagef(err, "[GetAllLessonStates] parseLessonStudentExtraData failed, lessonId: %d", tmpInfo.LessonId)
			}
			tmpLessonStateInfo.LeaveSeason = extraDataInfo.GetLeaveSeason()
			tmpLessonStateInfo.FirstLeaveReason = extraDataInfo.GetFirstLeaveReason()
			tmpLessonStateInfo.ContentTime = extraDataInfo.GetContentTime()
			tmpLessonStateInfo.IsSyncRemind = extraDataInfo.GetIsSyncRemind()
			tmpLessonStateInfo.RemindTime = extraDataInfo.GetRemindTime()
			tmpLessonStateInfo.AccompanyTag = extraDataInfo.GetAccompanyTag()
			tmpLessonStateInfo.PreAttendTime = extraDataInfo.GetPreAttendTime()
			tmpLessonStateInfo.RemindId = extraDataInfo.GetRemindId()
		}
		lessonStateInfos = append(lessonStateInfos, tmpLessonStateInfo)
	}

	sort.Slice(lessonStateInfos, func(i, j int) bool {
		return lessonStateInfos[i].LessonId < lessonStateInfos[j].LessonId
	})

	return &dtopreclass.GetAllLessonStatesRsp{
		CourseName:       courseLessonInfo.CourseName,
		TeacherName:      teacherName,
		LessonStateInfos: lessonStateInfos,
	}, nil
}

func lessonTime(startTime, stopTime int) string {
	startCar := carbon.CreateFromTimestamp(int64(startTime))
	stopCar := carbon.CreateFromTimestamp(int64(stopTime))
	return fmt.Sprintf("%s-%s", startCar.StdTime().Format("01月02日 15:04"),
		stopCar.StdTime().Format("15:04"))
}
