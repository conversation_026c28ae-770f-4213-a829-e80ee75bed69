package preclass

import (
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"testing"
)

func TestAIAutoUpdatePreClassState(t *testing.T) {
	env.SetRootPath("../../../")
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	helpers.PreInit()

	helpers.InitRedis()
	helpers.InitMysql()
	helpers.InitApiClient()
	ctx := gin.CreateNewContext(engine)

	val := `{"sceneType":97,"courseId":537807,"studentUid":2135423798,"assistantUid":4300412528,"createTime":1724318874,"lessonPreClassTagInfos":[{"lessonId":510511,"tagType":5,"extInfo":{"banXueTag":"","contentTime":0,"firstLeaveReason":"","leaveSeason":""}},{"lessonId":510512,"tagType":1,"extInfo":{"banXueTag":"","contentTime":0,"firstLeaveReason":"","leaveSeason":"我不想去上课"}},{"lessonId":510513,"tagType":3,"extInfo":{"banXueTag":"","contentTime":1724865316,"firstLeaveReason":"","leaveSeason":""}}]}`

	var aiAutoTag dtopreclass.AIAutoTag
	if err := jsoniter.Unmarshal([]byte(val), &aiAutoTag); err != nil {
		return
	}

	aiAutoTag.MsgID = "111111"
	err := AIAutoUpdatePreClassState(ctx, aiAutoTag)
	t.Log(err)
}
