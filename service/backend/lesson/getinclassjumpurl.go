package lesson

import (
	"assistantdeskgo/api/classme"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolesson"
	"github.com/gin-gonic/gin"
)

// GetInClassJumpUrl 获取章节的直播间链接
func GetInClassJumpUrl(ctx *gin.Context, req *dtolesson.GetInClassJumpUrlReq) (rsp *dtolesson.GetInClassJumpUrlRsp, err error) {
	userInfo := components.GetUserInfo(ctx)
	if userInfo.SelectedBusinessUid <= 0 {
		return nil, components.ErrorAssistantUidInvalid
	}

	params := classme.GetInClassJumpUrlReq{
		LessonId:     req.LessonId,
		CourseId:     req.CourseId,
		AssistantUid: userInfo.SelectedBusinessUid,
		AppId:        req.AppId,
	}

	var ret *classme.GetInClassJumpUrlRsp
	if ret, err = classme.GetInClassJumpUrl(ctx, params); err != nil {
		return
	}

	rsp = &dtolesson.GetInClassJumpUrlRsp{
		JumpUrl: ret.JumpUrl,
	}
	return
}
