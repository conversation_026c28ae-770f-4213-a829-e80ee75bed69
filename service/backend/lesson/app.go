package lesson

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/dto/dtolesson"
	"errors"
	"github.com/gin-gonic/gin"
	"sort"
)

func GetAppList(ctx *gin.Context) (rsp *dtolesson.GetAppListRsp, err error) {
	var data map[string]map[string]assistantdesk.AppInfo
	if err = assistantdesk.GetKeyForJson(ctx, assistantdesk.AppKey, &data); err != nil {
		return nil, err
	}

	appList := data[assistantdesk.AppKey]
	if len(appList) == 0 {
		return nil, errors.New("未获取到app配置")
	}

	appInfoList := make([]dtolesson.AppInfo, 0)
	for appId, appInfo := range appList {
		appDetail := dtolesson.AppInfo{
			AppId:    appId,
			AppName:  appInfo.Name,
			IconUrl:  appInfo.IconURL,
			IconType: appInfo.IconType,
		}
		appInfoList = append(appInfoList, appDetail)
	}

	sort.SliceStable(appInfoList, func(i, j int) bool {
		return appInfoList[i].AppId < appInfoList[j].AppId
	})

	rsp = &dtolesson.GetAppListRsp{
		AppList: appInfoList,
	}
	return
}
