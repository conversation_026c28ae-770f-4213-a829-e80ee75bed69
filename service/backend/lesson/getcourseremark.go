package lesson

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolesson"
	"assistantdeskgo/models"
	"errors"
	"github.com/gin-gonic/gin"
)

func GetCourseRemark(ctx *gin.Context, req dtolesson.GetCourseRemarkReq) (ret map[int64]string, err error) {
	userInfo := components.GetUserInfo(ctx)
	assistantUid := userInfo.SelectedBusinessUid
	if assistantUid == 0 {
		return ret, errors.New("资产id异常，请稍后重试")
	}
	list, err := models.ACS.GetList(ctx, req.CourseIds, assistantUid, req.StudentUids)
	if err != nil {
		return
	}
	ret = map[int64]string{}
	for _, item := range list {
		remark := item.GetExtData().ScRemark
		if remark != "" {
			ret[item.StudentUid] = remark
		}
	}
	return
}
