package sailorscore

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/api/fwyyevaluate"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtosailor"
	"assistantdeskgo/middleware"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

func Export(ctx *gin.Context, req *dtosailor.ListReq) (rsp *dtosailor.ListRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return nil, err
	}

	if err = checkExportParams(req); err != nil {
		return
	}
	passport, _ := ctx.Cookie("ZYBUSS")
	ips, _ := ctx.Cookie("ZYBIPSCAS")
	skip, _ := ctx.Cookie("SKIP")
	dockerIps, _ := ctx.Cookie("DOCKERIPS")

	cookies := map[string]string{
		"ZYBIPSCAS": ips,
		"ZYBUSS":    passport,
		"SKIP":      skip,
		"DOCKERIPS": dockerIps,
	}

	req.Cookies = cookies
	inputParam, err := json.Marshal(req)
	if err != nil {
		zlog.Warnf(ctx, "Export json marshal err:%+v", err)
		return
	}

	taskName := fmt.Sprintf("%+v_%+v_%+v", "水手分导出", time.Now().Unix(), userInfo.UserId) // 下划线分割，导出处理时需要使用

	addTaskReq := &fwyyevaluate.AddDownloadTaskReq{
		DownloadType: fwyyevaluate.TblDownloadTaskTypeSailorScore,
		InputParam:   inputParam,
		TaskName:     taskName,
	}
	addTaskRsp := &fwyyevaluate.AddDownloadTaskRsp{}

	err = apis.Do(ctx, addTaskReq, addTaskRsp, apis.WithCookies(cookies))
	if err != nil {
		zlog.Warnf(ctx, "添加下载任务失败，req：%+v， err：%+v", addTaskReq, err)
		return
	}

	return
}

func checkExportParams(req *dtosailor.ListReq) error {
	if len(req.UserStatus) == 0 {
		return errors.New("请选择筛选项后导出（人员状态）")
	}
	if req.UserStatus == defines.IsAllStr {
		return errors.New("请选择人员状态归属后再进行导出（非全部）")
	}

	if len(req.PersonType) == 0 {
		return errors.New("请选择筛选项后导出（人员类型）")
	}
	if len(req.HrPost) == 0 {
		return errors.New("请选择筛选项后导出（人员岗位）")
	}
	if len(req.ProductLine) == 0 {
		return errors.New("请选择筛选项后导出（人力归属）")
	}
	if len(req.Grade) == 0 {
		return errors.New("请选择筛选项后导出（学部）")
	}
	if len(req.GradeLevel) == 0 {
		return errors.New("请选择筛选项后导出（年级）")
	}
	if len(req.Subject) == 0 {
		return errors.New("请选择筛选项后导出（学科）")
	}

	if len(req.ProductLine) == 0 {
		req.ProductLine = strconv.Itoa(defines.AscriptionAssistant)
	}
	productLine, _ := strconv.ParseInt(req.ProductLine, 10, 64)
	if _, ok := defines.ValidPersonBelong[productLine]; !ok {
		return errors.New("人力归属超出搜索范围")
	}

	if len(req.Grade) == 0 {
		req.Grade = strconv.Itoa(defines.IsAll)
	}
	grade, _ := strconv.ParseInt(req.Grade, 10, 64)
	if _, ok := defines.ValidGrade[grade]; !ok {
		return errors.New("学部超出搜索范围")
	}

	if len(req.Subject) == 0 {
		req.Subject = strconv.Itoa(defines.IsAll)
	}
	subject, _ := strconv.ParseInt(req.Subject, 10, 64)
	if _, ok := defines.SubjectTextMap[subject]; !ok && subject != defines.IsAll {
		return errors.New("学科超出搜索范围")
	}

	if len(req.HrPost) == 0 {
		req.HrPost = strconv.Itoa(defines.HrPostTutor)
	}
	hrPost, _ := strconv.ParseInt(req.HrPost, 10, 64)
	if _, ok := defines.HrPostMap[hrPost]; !ok {
		return errors.New("人力归属超出搜索范围")
	}
	// 测试环境不过滤人力归属
	//if env.GetRunEnv() == env.RunEnvTest {
	//	req.HrPost = strconv.Itoa(defines.IsAll)
	//}

	return nil
}
