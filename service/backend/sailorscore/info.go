package sailorscore

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtosailor"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
)

const (
	IsShowSailorScore  = 1
	NotShowSailorScore = 0
)

func Info(ctx *gin.Context) (rsp *dtosailor.InfoRsp, err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return nil, err
	}

	var userList userprofile.GetUserListRsp
	req := &dtosailor.ListReq{
		StaffUid: strconv.Itoa(userInfo.StaffUid),
	}
	userList, err = userprofile.GetUserList(ctx, req)
	if err != nil {
		return nil, err
	}

	rsp = &dtosailor.InfoRsp{
		StaffUid:  int64(userInfo.StaffUid),
		ScoreInfo: dtosailor.ScoreInfo{},
	}
	// 未搜索到符合真人ID, 不展示水手分模块
	if len(userList.List) == 0 {
		zlog.Warnf(ctx, "sailorScoreInfo get staffInfo failed, staffUid: %s", userInfo.StaffUid)
		return rsp, nil
	}

	staffInfo := userList.List[0]
	rsp.IsShow = isShowSailorScore(staffInfo)

	if rsp.IsShow == IsShowSailorScore {
		var sailorScoreList []models.SailorScore
		sailorScoreList, err = models.SailorScoreRef.GetByStaffUids(ctx, []int64{staffInfo.StaffUID})
		if err != nil {
			return nil, err
		}
		if len(sailorScoreList) == 0 {
			return
		}

		sailorScoreInfo := sailorScoreList[0]
		total := GetSailorScoreTotal(sailorScoreInfo)

		rsp.ScoreInfo = dtosailor.ScoreInfo{
			Total:         utils.FormatFloat(total, utils.DefaultDecimal),
			TotalLevel:    GetSailorScoreLevel(total),
			Qualification: utils.FormatFloat(sailorScoreInfo.GetQualificationScore(), utils.DefaultDecimal),
			Contribute:    utils.FormatFloat(sailorScoreInfo.GetContributeScore(), utils.DefaultDecimal),
			Level:         utils.FormatFloat(sailorScoreInfo.Scale, utils.DefaultDecimal),
			Other:         utils.FormatFloat(sailorScoreInfo.Other, utils.DefaultDecimal),
			CostLevel:     sailorScoreInfo.CostLevel,
			Quality:       utils.FormatFloat(sailorScoreInfo.GetQualityScore(), utils.DefaultDecimal),
			Performance:   utils.FormatFloat(sailorScoreInfo.GetPerformanceScore(), utils.DefaultDecimal),
		}
	}

	return
}

func isShowSailorScore(staffInfo userprofile.StaffInfo) int64 {
	if !defines.ContainPrimaryAndJunior(staffInfo.Grade) {
		return NotShowSailorScore
	}

	if !defines.ContainSubject(staffInfo.Subject) {
		return NotShowSailorScore
	}

	if !defines.ContainActiveAndPreResignation(staffInfo.UserStatus) {
		return NotShowSailorScore
	}

	if _, ok := defines.ValidPersonBelong[staffInfo.ProductLine]; !ok {
		return NotShowSailorScore
	}

	if _, ok := defines.HrPostMap[staffInfo.HrPost]; !ok {
		return NotShowSailorScore
	}

	return IsShowSailorScore
}
