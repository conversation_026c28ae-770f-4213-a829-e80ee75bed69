package sailorscore

import (
	"assistantdeskgo/dto/dtocsv"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"github.com/gin-gonic/gin"
	"time"
)

func UploadSailorScore(ctx *gin.Context, req dtocsv.UploadSailorScoreReq) (rsp dtocsv.UploadSailorScoreRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	excelTask := &models.ExcelTask{
		FileName:      req.FileName,
		FilePath:      req.FilePath,
		RetFile:       "",
		Status:        models.ExcelTaskStatusInit,
		FailReason:    "",
		Type:          req.Type,
		OperatorUid:   int64(userInfo.UserId),
		OperatorUname: userInfo.UserName,
		CreateTime:    time.Now().Unix(),
		StartTime:     0,
		EndTime:       0,
	}
	err = excelTask.Insert(ctx)
	return
}

func CanUploadSailorScore(ctx *gin.Context) (rsp dtocsv.CanUploadSailorScoreRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		rsp.CanUpload = 1
		return
	}
	taskList, err := models.ExcelTaskRef.GetByUid(ctx, userInfo.UserId)
	if err != nil {
		rsp.CanUpload = 1
		return
	}
	for _, task := range taskList {
		if task.Status == models.ExcelTaskStatusInit || task.Status == models.ExcelTaskStatusExecuting {
			rsp.CanUpload = 1
			return
		}
	}
	return
}
