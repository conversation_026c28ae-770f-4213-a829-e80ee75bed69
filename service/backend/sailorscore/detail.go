package sailorscore

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtosailor"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
)

func Detail(ctx *gin.Context, req dtosailor.DetailReq) (rsp *dtosailor.ScoreDetailRsp, err error) {
	if req.StaffUid == 0 {
		var userInfo *userprofile.UserInfo
		userInfo, err = middleware.GetLoginUserInfo(ctx)
		if err != nil {
			return nil, err
		}

		req.StaffUid = int64(userInfo.StaffUid)
	}
	if req.StaffUid == 0 {
		return nil, errors.New("获取真人Uid失败")
	}

	var userList userprofile.GetUserListRsp
	params := &dtosailor.ListReq{
		StaffUid: strconv.Itoa(int(req.StaffUid)),
	}
	userList, err = userprofile.GetUserList(ctx, params)
	if err != nil {
		return nil, err
	}

	detailList := make([]dtosailor.ScoreDetail, 0)
	if len(userList.List) == 0 {
		zlog.Warnf(ctx, "sailorScoreInfo get staffInfo failed, staffUid: %s", req.StaffUid)
		return
	}

	rsp = &dtosailor.ScoreDetailRsp{}
	staffInfo := userList.List[0]
	for _, grade := range staffInfo.Grade {
		if grade == defines.PrimarySchool {
			rsp.Grade = grade
			break
		} else if grade == defines.JuniorSchool {
			rsp.Grade = grade
			break
		}
	}

	var scoreLogs []models.SailorScoreOperateLog
	scoreLogs, err = models.SailorScoreOperateLogRef.GetByStaffUid(ctx, req.StaffUid)
	if err != nil {
		return nil, err
	}
	if len(scoreLogs) == 0 {
		return
	}

	for _, log := range scoreLogs {
		item := dtosailor.ScoreDetail{
			TeachingQualification: utils.FormatFloat(log.TeachingQualification, utils.DefaultDecimal),
			WorkingAge:            utils.FormatFloat(log.WorkingAge, utils.DefaultDecimal),
			Qualification:         utils.FormatFloat(log.Qualification, utils.DefaultDecimal),
			UserPraise:            utils.FormatFloat(log.UserPraise, utils.DefaultDecimal),
			Praise:                utils.FormatFloat(log.Praise, utils.DefaultDecimal),
			Scale:                 utils.FormatFloat(log.Scale, utils.DefaultDecimal),
			Other:                 utils.FormatFloat(log.Other, utils.DefaultDecimal),
			CostLevel:             log.CostLevel,
			Satisfaction:          utils.FormatFloat(log.Satisfaction, utils.DefaultDecimal),
			Punishment:            utils.FormatFloat(log.Punishment, utils.DefaultDecimal),
			Performance:           utils.FormatFloat(log.Performance, utils.DefaultDecimal),
			Exam:                  utils.FormatFloat(log.Exam, utils.DefaultDecimal),
			DataType:              log.DataType,
			OperateTime:           utils.ChangeStampToYmdHm(log.OperateTime),
			OperateName:           log.OperateName,
		}
		detailList = append(detailList, item)
	}
	rsp.ScoreList = detailList
	return
}
