package sailorscore

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtosailor"
	"assistantdeskgo/models"
	"errors"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
)

func checkParams(req *dtosailor.ListReq) error {
	if len(req.ProductLine) == 0 {
		req.ProductLine = strconv.Itoa(defines.AscriptionAssistant)
	}
	productLine, _ := strconv.ParseInt(req.ProductLine, 10, 64)
	if _, ok := defines.ValidPersonBelong[productLine]; !ok {
		return errors.New("人力归属超出搜索范围")
	}

	if len(req.Grade) == 0 {
		req.Grade = strconv.Itoa(defines.IsAll)
	}
	grade, _ := strconv.ParseInt(req.Grade, 10, 64)
	if _, ok := defines.ValidGrade[grade]; !ok {
		return errors.New("学部超出搜索范围")
	}

	if len(req.Subject) == 0 {
		req.Subject = strconv.Itoa(defines.IsAll)
	}
	subject, _ := strconv.ParseInt(req.Subject, 10, 64)
	if _, ok := defines.SubjectTextMap[subject]; !ok && subject != defines.IsAll {
		return errors.New("学科超出搜索范围")
	}

	if len(req.HrPost) == 0 {
		req.HrPost = strconv.Itoa(defines.HrPostTutor)
	}
	hrPost, _ := strconv.ParseInt(req.HrPost, 10, 64)
	if _, ok := defines.HrPostMap[hrPost]; !ok {
		return errors.New("人力归属超出搜索范围")
	}
	// 测试环境不过滤人力归属
	//if env.GetRunEnv() == env.RunEnvTest {
	//	req.HrPost = strconv.Itoa(defines.IsAll)
	//}

	return nil
}

func List(ctx *gin.Context, req *dtosailor.ListReq) (rsp *dtosailor.ListRsp, err error) {
	pageInfo := models.NormalPage{
		No:   req.Pn,
		Size: req.Rn,
	}

	if err = checkParams(req); err != nil {
		return nil, err
	}

	userRsp := userprofile.GetUserListRsp{}
	userRsp, err = userprofile.GetUserList(ctx, req)
	if err != nil {
		return nil, err
	}

	rsp = &dtosailor.ListRsp{
		List: make([]dtosailor.SailorScoreItem, 0),
		PageInfo: dtosailor.PageInfo{
			Pn:    pageInfo.No,
			Rn:    pageInfo.Size,
			Total: userRsp.Total,
		},
	}

	if len(userRsp.List) == 0 {
		return
	}

	uIds := make([]int64, 0)
	for _, item := range userRsp.List {
		uIds = append(uIds, item.StaffUID)
	}

	sailorScoreMap := make(map[int64]models.SailorScore, 0)
	sailorScoreMap, err = GetSailorScoreMap(ctx, uIds)
	if err != nil {
		return nil, err
	}

	var confInfo *userprofile.GeneralConfRsp
	confInfo, err = userprofile.GetGeneralConf(ctx)
	if err != nil {
		return nil, err
	}

	rsp.List = make([]dtosailor.SailorScoreItem, 0)
	for _, item := range userRsp.List {
		scoreInfo := sailorScoreMap[item.StaffUID]
		total := GetSailorScoreTotal(scoreInfo)
		detail := dtosailor.ScoreDetail{
			TeachingQualification: scoreInfo.TeachingQualification,
			WorkingAge:            scoreInfo.WorkingAge,
			Qualification:         scoreInfo.Qualification,
			UserPraise:            scoreInfo.UserPraise,
			Praise:                scoreInfo.Praise,
			Scale:                 scoreInfo.Scale,
			Other:                 scoreInfo.Other,
			CostLevel:             scoreInfo.CostLevel,
			Satisfaction:          scoreInfo.Satisfaction,
			Punishment:            scoreInfo.Punishment,
			Performance:           scoreInfo.Performance,
			Exam:                  scoreInfo.Exam,
		}
		info := dtosailor.SailorScoreItem{
			UserName:      item.UserName,
			UserPhone:     item.UserPhone,
			StaffUID:      item.StaffUID,
			GradeStr:      formatConfItem(item.Grade, confInfo.User.Grade),
			SubjectStr:    formatConfItem(item.Subject, confInfo.User.SubjectMap),
			GradeLevelStr: formatConfItemMap(item.GradeLevel, confInfo.User.GradeLevelMap),
			ProductLine:   defines.ValidPersonBelong[item.ProductLine],
			HrPost:        defines.HrPostMap[item.HrPost],
			SailorScore:   total,
			SailorLevel:   GetSailorScoreLevel(total),
			ScoreDetail:   detail,
		}

		rsp.List = append(rsp.List, info)
	}

	return
}

func formatConfItem(items []int64, confInfo []userprofile.ConfItem) string {
	names := make(map[int64]string)
	for _, item := range confInfo {
		names[item.Value] = item.Name
	}

	var itemStr string
	for _, item := range items {
		if name, ok := names[item]; ok {
			itemStr += name + ","
		}
	}

	return strings.Trim(itemStr, ",")
}

func formatConfItemMap(items []int64, confInfoMap map[string][]userprofile.ConfItem) string {
	names := make(map[int64]string)
	for _, confInfo := range confInfoMap {

		for _, item := range confInfo {
			names[item.Value] = item.Name
		}
	}
	var itemStr string
	for _, item := range items {
		if name, ok := names[item]; ok {
			itemStr += name + ","
		}
	}
	return strings.Trim(itemStr, ",")
}

func GetSailorScoreTotal(score models.SailorScore) float64 {
	// 水手分 =资质分 +贡献分 +等级分 + 其他分
	return score.GetQualificationScore() + score.GetContributeScore() + score.Scale + score.Other
}

func GetSailorScoreLevel(total float64) string {
	switch {
	case total <= 0:
		return ""
	case total < 30:
		return "V0"
	case total < 60:
		return "V1"
	case total < 90:
		return "V2"
	case total < 120:
		return "V3"
	case total < 135:
		return "V4"
	case total < 150:
		return "V5"
	case total < 160:
		return "V6"
	case total < 170:
		return "V7"
	default:
		return "V8"
	}
}

func GetCostLevelScore(score models.SailorScore, staffInfo userprofile.StaffInfo) float64 {
	var isPrimary, isJunior bool
	for _, grade := range staffInfo.Grade {
		if grade == defines.PrimarySchool {
			isPrimary = true
		} else if grade == defines.JuniorSchool {
			isJunior = true
		}
	}

	if !isPrimary || isJunior {
		return 0
	}

	scoreMap := defines.CostLevelScoreMapForJunior
	if isPrimary {
		scoreMap = defines.CostLevelScoreMapForPrimary
	}
	return scoreMap[score.CostLevel]
}

func GetSailorScoreMap(ctx *gin.Context, uIds []int64) (map[int64]models.SailorScore, error) {
	sailorScoreList, err := models.SailorScoreRef.GetByStaffUids(ctx, uIds)
	if err != nil {
		return nil, err
	}

	ret := make(map[int64]models.SailorScore)
	for _, sailorScore := range sailorScoreList {
		ret[sailorScore.StaffUid] = sailorScore
	}

	return ret, nil
}
