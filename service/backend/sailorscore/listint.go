package sailorscore

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtosailor"
	"github.com/gin-gonic/gin"
	"strconv"
)

func ListInit(ctx *gin.Context) ([]dtosailor.ListInitRsp, error) {
	confInfo, err := userprofile.GetGeneralConf(ctx)
	if err != nil {
		return nil, err
	}

	ret := make([]dtosailor.ListInitRsp, 0)
	//人员状态
	userStatus := dtosailor.ListInitRsp{
		FieldKey: "userStatus",
		FieldMap: formatUserStatus(),
	}
	ret = append(ret, userStatus)

	//人员类型
	personType := dtosailor.ListInitRsp{
		FieldKey: "personType",
		FieldMap: formatGeneralConfItem(confInfo.User.PersonType, true, nil),
	}
	ret = append(ret, personType)

	//人力岗位
	HrPost := dtosailor.ListInitRsp{
		FieldKey: "hrPost",
		FieldMap: formatHrPost(),
	}
	ret = append(ret, HrPost)

	// 人员归属
	productLine := dtosailor.ListInitRsp{
		FieldKey: "productLine",
		FieldMap: formatGeneralConfItem(confInfo.System.PersonBelongMap, false, defines.ValidPersonBelong),
	}
	ret = append(ret, productLine)

	// 学部
	grade := dtosailor.ListInitRsp{
		FieldKey: "grade",
		FieldMap: formatGeneralConfItem(confInfo.User.Grade, true, defines.ValidGrade),
	}
	ret = append(ret, grade)

	// 年级
	gradeLevelItem := formatGradeLevel(confInfo.User.GradeLevelMap)
	gradeLevel := dtosailor.ListInitRsp{
		FieldKey: "gradeLevel",
		FieldMap: gradeLevelItem,
	}
	ret = append(ret, gradeLevel)

	// 学科
	subject := dtosailor.ListInitRsp{
		FieldKey: "subject",
		FieldMap: formatGeneralConfItem(confInfo.User.SubjectMap, true, defines.SubjectTextMap),
	}
	ret = append(ret, subject)

	return ret, nil
}

func formatGradeLevel(gradeLevelMap map[string][]userprofile.ConfItem) []dtosailor.ListInitItem {
	ret := make([]dtosailor.ListInitItem, 0)
	ret = append(ret, dtosailor.ListInitItem{
		Label: "全部",
		Value: strconv.Itoa(defines.IsAll),
	})

	grades := []string{"1", "20"} // 仅支持小学、初中
	for _, grade := range grades {
		if _, ok := gradeLevelMap[grade]; !ok {
			continue
		}

		for _, item := range gradeLevelMap[grade] {
			ret = append(ret, dtosailor.ListInitItem{
				Label: item.Name,
				Value: item.GetValueStr(),
			})
		}
	}

	return ret
}

func formatGeneralConfItem(items []userprofile.ConfItem, needAllItem bool, validMap map[int64]string) []dtosailor.ListInitItem {
	ret := make([]dtosailor.ListInitItem, 0)

	if needAllItem {
		ret = append(ret, dtosailor.ListInitItem{
			Label: "全部",
			Value: strconv.Itoa(defines.IsAll),
		})
	}

	for _, item := range items {
		if _, ok := validMap[item.Value]; len(validMap) > 0 && !ok {
			continue
		}
		ret = append(ret, dtosailor.ListInitItem{
			Label: item.Name,
			Value: item.GetValueStr(),
		})
	}
	return ret
}

func formatHrPost() []dtosailor.ListInitItem {
	statusList := []int64{defines.HrPostTutor, defines.HrPostUnTutor}

	ret := make([]dtosailor.ListInitItem, 0)

	return formatItem(statusList, defines.HrPostMap, ret)
}

func formatUserStatus() []dtosailor.ListInitItem {
	statusList := []int64{defines.UserStatusOnJob, defines.UserStatusReadyLeave, defines.UserStatusLeaveNotUnbind, defines.UserStatusLeaveUnbind}
	ret := []dtosailor.ListInitItem{
		dtosailor.ListInitItem{
			Label: "全部",
			Value: strconv.Itoa(defines.IsAll),
		},
	}

	return formatItem(statusList, defines.UserStatusMap, ret)
}

func formatItem(values []int64, names map[int64]string, ret []dtosailor.ListInitItem) []dtosailor.ListInitItem {
	for _, value := range values {
		if name, ok := names[value]; ok {
			item := dtosailor.ListInitItem{
				Label: name,
				Value: strconv.Itoa(int(value)),
			}
			ret = append(ret, item)
		}
	}

	return ret
}
