package groupposter

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models/grouppostertemplate"
	"assistantdeskgo/service/backend/message"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func Add(ctx *gin.Context, req dtogroupposter.AddGroupPosterReq) (resp dtogroupposter.AddGroupPosterResp, err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add GetLoginUserInfo failed, err: %+v", err)
		return
	}
	if err = checkSecondGroupIds(ctx, req.TplGroupIds); err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add checkSecondGroupIds failed, err: %+v", err)
		return
	}

	gpt := grouppostertemplate.GroupPosterTemplate{}
	err = grouppostertemplate.ApplyFields(&gpt, req.TplName, req.TplType, req.TplStyle, req.TplGroupIds, req.TplDesc, req.TplContent)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add ApplyFields failed, err: %+v, req: %+v", err, req)
		return
	}

	var id int64
	id, err = gpt.Save(ctx, userInfo.UserId, userInfo.UserName)
	resp.Id = id
	return
}

func Update(ctx *gin.Context, req dtogroupposter.UpdateGroupPosterReq) (err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Update GetLoginUserInfo failed, err: %+v", err)
		return
	}
	if err = checkSecondGroupIds(ctx, req.TplGroupIds); err != nil {
		zlog.Warnf(ctx, "GroupPoster.Update checkSecondGroupIds failed, err: %+v", err)
		return
	}

	var gpt *grouppostertemplate.GroupPosterTemplate
	gpt, err = grouppostertemplate.GetById(ctx, req.Id)
	if err != nil || gpt.Id == 0 {
		zlog.Warnf(ctx, "GroupPoster.Update GetById failed, err: %+v, req: %+v", err, req)
		if gpt.Id == 0 {
			err = errors.New(fmt.Sprintf("数据已失效，请刷新后重试"))
		}
		return
	}

	err = grouppostertemplate.ApplyFields(gpt, req.TplName, gpt.TplType, req.TplStyle, req.TplGroupIds, req.TplDesc, req.TplContent)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add ApplyFields failed, err: %+v, req: %+v", err, req)
		return
	}

	_, err = gpt.Save(ctx, userInfo.UserId, userInfo.UserName)
	return
}

func Delete(ctx *gin.Context, req dtogroupposter.DeleteGroupPosterReq) (err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Delete GetLoginUserInfo failed, err: %+v", err)
		return
	}

	var gpt *grouppostertemplate.GroupPosterTemplate
	gpt, err = grouppostertemplate.GetById(ctx, req.Id)
	if gpt.Id == 0 {
		// 没有查到数据，直接置为成功
		err = nil
		return
	}
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Delete GetById failed, err: %+v, req: %+v", err, req)
		return
	}

	err = gpt.Delete(ctx, userInfo.UserId, userInfo.UserName)
	return
}

func GetSecondGroupIdNameMap(ctx *gin.Context) (dataMap map[int64]string, err error) {
	dataMap = make(map[int64]string)
	groupTrees, fErr := message.FormatOrganizationTreeForGlobal(ctx)
	if fErr != nil {
		err = fErr
		return
	}

	// 仅保留二级团队
	for _, groupTree := range groupTrees {
		if groupTree == nil || groupTree.Children == nil || len(groupTree.Children) == 0 {
			continue
		}

		for _, child := range groupTree.Children {
			dataMap[child.ID] = child.Name
		}
	}
	return
}

func checkSecondGroupIds(ctx *gin.Context, groupIds []int64) (err error) {
	var idNameMap map[int64]string
	idNameMap, err = GetSecondGroupIdNameMap(ctx)
	if err != nil {
		return err
	}

	for _, id := range groupIds {
		if _, ok := idNameMap[id]; !ok {
			err = errors.New(fmt.Sprintf("未知的团队id[%d]，请确认二级团队id存在", id))
			return
		}
	}
	return
}
