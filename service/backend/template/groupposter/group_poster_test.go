package groupposter

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/dto/dtoapitemplate"
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models/grouppostertemplate"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/smartystreets/assertions"
	"testing"
)

func getCtx() *gin.Context {
	env.SetRootPath("../../../../")
	engine := gin.New()
	gin.SetMode(gin.TestMode)
	helpers.Init(engine)
	ctx := gin.CreateNewContext(engine)
	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   1234567890,
		UserName: "测试用户",
	})
	return ctx
}

func newItem(tplName string, tplType, tplStyle int64, tplGroupIds []int64, tplDesc, tplContent string) dtogroupposter.AddGroupPosterReq {
	return dtogroupposter.AddGroupPosterReq{
		GroupPosterReq: dtogroupposter.GroupPosterReq{
			TplName:     tplName,
			TplStyle:    tplStyle,
			TplGroupIds: tplGroupIds,
			TplDesc:     tplDesc,
			TplContent:  tplContent,
		},
		TplType: tplType,
	}
}

func before(ctx *gin.Context, tplName1, tplName2 string, tplGroupIds1, tplGroupIds2 []int64) (addIds []int64) {
	addIds = make([]int64, 0)

	item := newItem(tplName1, grouppostertemplate.TplTypePayQrCode, grouppostertemplate.TplStyleHorizon, tplGroupIds1, "测试描述1", "{}")
	add1, err := Add(ctx, item)
	if err != nil {
		return
	}
	addIds = append(addIds, add1.Id)

	item.TplName = tplName2
	item.TplGroupIds = tplGroupIds2
	item.TplDesc = "测试描述2"

	add2, err := Add(ctx, item)
	if err != nil {
		return
	}
	addIds = append(addIds, add2.Id)
	return
}

func after(ctx *gin.Context, ids []int64) {
	for _, id := range ids {
		err := Delete(ctx, dtogroupposter.DeleteGroupPosterReq{Id: id})
		if err != nil {
			fmt.Println(fmt.Sprintf("test.after delete failed id: %d", id))
			continue
		}
	}
}

func TestAdd(t *testing.T) {
	ctx := getCtx()
	item := newItem("", grouppostertemplate.TplTypePayQrCode, grouppostertemplate.TplStyleHorizon, []int64{1, 2}, "测试描述1", "{}")
	_, err := Add(ctx, item)
	assertions.ShouldContainSubstring(err.Error(), "tplName empty")

	item.TplName = "一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十"
	_, err = Add(ctx, item)
	assertions.ShouldContainSubstring(err.Error(), fmt.Sprintf("tplName[%s] len > 50 charactors", item.TplName))

	item.TplName = "测试模板"
	item.TplType = 0
	_, err = Add(ctx, item)
	assertions.ShouldContainSubstring(err.Error(), fmt.Sprintf("tplType[%d] invalid", item.TplType))

	item.TplType = grouppostertemplate.TplTypePayQrCode
	item.TplStyle = 0
	_, err = Add(ctx, item)
	assertions.ShouldContainSubstring(err.Error(), fmt.Sprintf("tplStyle[%d] invalid", item.TplStyle))

	item.TplStyle = grouppostertemplate.TplStyleHorizon
	item.TplGroupIds = []int64{}
	_, err = Add(ctx, item)
	assertions.ShouldContainSubstring(err.Error(), "tplGroupIds empty")
}

func TestDelete(t *testing.T) {
	ctx := getCtx()

	addIds := before(ctx, "测试删除模板1", "测试删除模板2", []int64{1, 2}, []int64{3, 4})
	assertions.ShouldNotBeEmpty(addIds)

	after(ctx, addIds)
}

func TestUpdate(t *testing.T) {
	ctx := getCtx()

	addIds := before(ctx, "测试更新模板1", "测试更新模板2", []int64{5, 6}, []int64{7})
	assertions.ShouldNotBeEmpty(addIds, 2)

	// update
	up := dtogroupposter.UpdateGroupPosterReq{
		GroupPosterReq: dtogroupposter.GroupPosterReq{
			TplName:     "测试更新模板3",
			TplGroupIds: []int64{6},
			TplDesc:     "测试描述3",
		},
		Id: addIds[0],
	}
	err := Update(ctx, up)
	assertions.ShouldBeNil(err)

	up = dtogroupposter.UpdateGroupPosterReq{
		GroupPosterReq: dtogroupposter.GroupPosterReq{
			TplName: "测试更新模板4测试更新模板4测试更新模板4测试更新模板4测试更新模板4测试更新模板4测试更新模板4测试更新模板4测试更新模板4测试更新模板4",
			TplDesc: "测试描述4",
		},
		Id: addIds[1],
	}
	err = Update(ctx, up)
	assertions.ShouldNotBeNil(err)
	assertions.ShouldContainSubstring(err.Error(), fmt.Sprintf("tplName[%s] len > 50 charactors", up.TplName))

	after(ctx, addIds)
}

func TestGetById(t *testing.T) {
	ctx := getCtx()

	addIds := before(ctx, "测试查询模板1", "测试查询模板2", []int64{8}, []int64{9})
	assertions.ShouldNotBeEmpty(addIds, 2)

	// get
	resp, err := GetById(ctx, dtogroupposter.GetByIdReq{Id: addIds[0]})
	assertions.ShouldBeNil(err)

	assertions.ShouldNotBeEmpty(resp)
	assertions.ShouldEqual(resp.TplName, "测试查询模板1")
	assertions.ShouldEqual(len(resp.TplGroupIds), len([]int64{8}))

	resp, err = GetById(ctx, dtogroupposter.GetByIdReq{Id: addIds[1]})
	assertions.ShouldBeNil(err)

	assertions.ShouldNotBeEmpty(resp)
	assertions.ShouldEqual(resp.TplName, "测试查询模板2")
	assertions.ShouldEqual(len(resp.TplGroupIds), len([]int64{9}))

	after(ctx, addIds)
}

func TestFindByTypeGroupIds(t *testing.T) {
	ctx := getCtx()

	addIds := before(ctx, "测试查询模板1", "测试查询模板2", []int64{10, 11}, []int64{11, 12})
	assertions.ShouldNotBeEmpty(addIds)

	// find
	find := dtoapitemplate.GetPosterTemplateByGroupIdsReq{
		TplType:     grouppostertemplate.TplTypePayQrCode,
		TplGroupIds: []int64{10},
	}
	respList, err := FindByTypeGroupIds(ctx, find)
	assertions.ShouldBeNil(err)
	assertions.ShouldEqual(len(respList), 1)

	find.TplGroupIds = []int64{11}
	respList, err = FindByTypeGroupIds(ctx, find)
	assertions.ShouldBeNil(err)
	assertions.ShouldEqual(len(respList), 2)

	find.TplGroupIds = []int64{10, 12}
	respList, err = FindByTypeGroupIds(ctx, find)
	assertions.ShouldBeNil(err)
	assertions.ShouldEqual(len(respList), 2)

	after(ctx, addIds)
}

func TestFindByPaged(t *testing.T) {
	ctx := getCtx()

	addIds := before(ctx, "测试查询模板1", "测试查询模板2", []int64{8}, []int64{9})
	assertions.ShouldNotBeEmpty(addIds)

	// find

	after(ctx, addIds)
}
