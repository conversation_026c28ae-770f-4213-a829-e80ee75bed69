package groupposter

import (
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/models/grouppostertemplate"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetById(ctx *gin.Context, req dtogroupposter.GetByIdReq) (resp dtogroupposter.GetByIdResp, err error) {
	var gpt *grouppostertemplate.GroupPosterTemplate
	gpt, err = grouppostertemplate.GetById(ctx, req.Id)
	if err != nil || gpt.Id == 0 {
		zlog.Warnf(ctx, "GroupPoster.GetById GetById error, id: %d, err: %+v", req.Id, err)
		if gpt.Id == 0 {
			err = errors.New(fmt.Sprintf("数据已失效，请刷新后重试"))
		}
		return
	}

	resp = dtogroupposter.GetByIdResp{
		Id:          gpt.Id,
		TplName:     gpt.TplName,
		TplType:     gpt.TplType,
		TplStyle:    gpt.TplStyle,
		TplGroupIds: gpt.GetGroupIds(ctx),
		TplDesc:     gpt.TplDesc,
		TplContent:  gpt.TplContent,
	}
	return
}
