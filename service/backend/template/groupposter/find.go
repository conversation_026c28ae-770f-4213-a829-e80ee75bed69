package groupposter

import (
	"assistantdeskgo/dto/dtoapitemplate"
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/models/grouppostertemplate"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

func FindByPaged(ctx *gin.Context, req dtogroupposter.FindByPagedReq) (resp dtogroupposter.FindByPagedResp, err error) {
	findByPaged, total, dbErr := grouppostertemplate.FindByPaged(ctx, req.Id, req.TplType, req.TplStyle, req.TplGroupIds, req.TplName, req.Pn, req.Rn)
	if dbErr != nil {
		err = dbErr
		zlog.Warnf(ctx, "GroupPoster.FindByPaged FindByPaged error, req: %+v, err: %+v", req, dbErr)
		return
	}
	resp.Total = total
	if total == 0 {
		return
	}

	list, formatErr := formatPagedItem(ctx, findByPaged)
	if formatErr != nil {
		err = formatErr
		zlog.Warnf(ctx, "GroupPoster.FindByPaged formatPagedItem error, findByPaged: %+v, err: %+v", findByPaged, formatErr)
		return
	}
	resp.List = list
	return
}

func formatPagedItem(ctx *gin.Context, dataList []*grouppostertemplate.GroupPosterTemplate) (list []dtogroupposter.FindByPagedItem, err error) {
	list = make([]dtogroupposter.FindByPagedItem, 0, len(dataList))
	if len(dataList) == 0 {
		return
	}

	groupIdNameMap, gErr := GetSecondGroupIdNameMap(ctx)
	if gErr != nil {
		err = gErr
		return
	}

	for _, data := range dataList {
		_, updaterName := data.GetUpdaterInfo()

		item := dtogroupposter.FindByPagedItem{
			Id:                data.Id,
			TplName:           data.TplName,
			TplStyleDesc:      grouppostertemplate.GetTplStyleOptions()[data.TplStyle],
			TplGroupIdsDesc:   formatGroupIdsToNameConcat(ctx, data, groupIdNameMap),
			TplDesc:           data.TplDesc,
			TplContent:        data.TplContent,
			TplOperatorName:   updaterName,
			TplUpdateTimeDesc: time.Unix(data.UpdateTime, 0).Format("2006-01-02 15:04:05"),
		}

		list = append(list, item)
	}
	return
}

func formatGroupIdsToNameConcat(ctx *gin.Context, data *grouppostertemplate.GroupPosterTemplate, idNameMap map[int64]string) string {
	groupIds := data.GetGroupIds(ctx)
	groupNames := make([]string, 0, len(groupIds))
	for _, id := range groupIds {
		groupNames = append(groupNames, idNameMap[id])
	}

	return strings.Join(groupNames, "/")
}

func FindByTypeGroupIds(ctx *gin.Context, req dtoapitemplate.GetPosterTemplateByGroupIdsReq) (resp []dtoapitemplate.GetPosterTemplateByGroupIdsResp, err error) {
	var templates []*grouppostertemplate.GroupPosterTemplate
	templates, err = grouppostertemplate.FindByTypeGroupIds(ctx, req.TplType, req.TplGroupIds)
	if err != nil {
		return
	}
	if len(templates) == 0 {
		return
	}

	resp = make([]dtoapitemplate.GetPosterTemplateByGroupIdsResp, 0, len(templates))
	for _, template := range templates {
		resp = append(resp, dtoapitemplate.GetPosterTemplateByGroupIdsResp{
			Id:          template.Id,
			TplName:     template.TplName,
			TplType:     template.TplType,
			TplStyle:    template.TplStyle,
			TplDesc:     template.TplDesc,
			TplContent:  template.TplContent,
			TplGroupIds: template.GetGroupIds(ctx),
		})
	}

	return
}
