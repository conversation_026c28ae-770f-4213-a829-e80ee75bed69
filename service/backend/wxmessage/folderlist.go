package wxmessage

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/service/backend/message"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

func checkParams(ctx *gin.Context, req *dtomessage.WxMessageFolderListReq) (userInfo *userprofile.UserInfo, err error) {

	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return nil, err
	}

	return userInfo, err
}

func FolderList(ctx *gin.Context, req *dtomessage.WxMessageFolderListReq) (rsp *dtomessage.SideListRsp, err error) {
	var userInfo *userprofile.UserInfo
	if userInfo, err = checkParams(ctx, req); err != nil {
		zlog.Warnf(ctx, "wxmessage folderList checkParams failed, req: %+v, err: %+v", req, err)
		return nil, err
	}

	// 获取有权限的组织ID
	var organizationGroupIds []int64
	organizationGroupIds, err = message.GetPermissionGroupIds(ctx, int64(userInfo.UserId), 0)
	if err != nil {
		return
	}

	// 获取可使用的范围
	var availableRange string
	availableRanges := make([]string, 0)
	if req.Ability != defines.MultiMessageToPerson {
		availableRange = defines.AvailableRangeForGeneral
		availableRanges = append(availableRanges, availableRange)
	}

	// 搜索有权限的文件夹ID
	req.Name = strings.TrimSpace(req.Name)
	var messageGroups []models.MessageGroup
	messageGroups, err = models.MessageGroupRef.SearchAllList(ctx, availableRanges, req.Name, organizationGroupIds, []int64{}, int64(userInfo.UserId), []string{"distinct folder_id"}) //查团队
	if err != nil {
		zlog.Warnf(ctx, "searchAllGroups failed, organizationGroupIds: %+v, req: %,err=%+v", organizationGroupIds, err)
		return
	}

	folderIds := make([]int64, 0)
	for _, messageGroupInfo := range messageGroups {
		folderIds = append(folderIds, messageGroupInfo.FolderId)
	}
	folderIds = utils.FilterDuplicatesInt64(folderIds)

	//获取文件夹列表
	folderList, total, err := models.MessageGroupFolderRef.PageListById(ctx, folderIds, req.Pn, req.Rn)
	curFolderIds := make([]int64, 0)
	for _, folder := range folderList {
		curFolderIds = append(curFolderIds, folder.ID)
	}
	messageGroups, err = models.MessageGroupRef.SearchAllList(ctx, availableRanges, req.Name, organizationGroupIds, curFolderIds, int64(userInfo.UserId), []string{})
	if err != nil {
		return
	}

	sideListFolders := message.FormatMessageGroupFolder(messageGroups, folderList, availableRange)
	rsp = &dtomessage.SideListRsp{
		SideListFolders: sideListFolders,
		PageInfo: dtomessage.PageInfo{
			Pn:    req.Pn,
			Rn:    req.Rn,
			Total: total,
		},
	}
	return rsp, nil
}
