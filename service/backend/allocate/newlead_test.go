package allocate

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestNewLeadAdd(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/Desktop/zyb/assistantdeskgo")
	helpers.Init(engine)
	data, _ := mesh.GetDeviceInfoListByDeviceUids(ctx, 2000212884)
	fmt.Println(data)


}


func TestNewLeadMessageNotice(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/Desktop/zyb/assistantdeskgo")
	helpers.Init(engine)

	NewLeadMessageNotice(ctx)
}



