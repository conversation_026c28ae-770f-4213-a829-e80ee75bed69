package allocate

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/infomsg"
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strings"
	"time"
)

const NewLeadMercucyConfigKey = "fwyy_new_lead_notice"

type SendConfig struct {
	SendTime            []string `json:"send_time"`
	SecondDay           bool     `json:"second_day"`
	SendType            int      `json:"send_type"`
	MergeSendType       int      `json:"merge_send_type"`
	WindowTime          int      `json:"window_time"`
	ShowStudentNameNums int      `json:"show_student_name_nums"`
	Content             string   `json:"content"`
}

// NewLeadAdd 新例子过来创建或者更新提醒消息
func NewLeadAdd(ctx *gin.Context, courseId, studentUid, kpUid int64) error {
	key := fmt.Sprintf("%d-%d-newleadadd", courseId, kpUid)
	lock, err2 := utils.LockRetry(ctx, key, "1", 5, 3)
	if !lock {
		return err2
	}
	defer func() {
		_, _ = utils.ReleaseLockByValue(ctx, key, "1")
	}()
	notice, err := models.TblNewLeadDingDingNoticeRef.GetNoticeByCourseIdBusinessUid(ctx, courseId, kpUid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			sendConfig, sendTimeArr, sendTime, personUid, err := getConfig(ctx, kpUid)
			if err != nil {
				zlog.Warnf(ctx, "NewLeadAdd getConfig, err: %+v", err)
				return err
			}
			if sendConfig == nil { // 未找到配置，不执行
				return nil
			}
			var newNotice = &models.TblNewLeadDingDingNotice{
				CourseId:            courseId,
				BusinessUid:         kpUid,
				Status:              models.DingDingNoticeTaskInit,
				PersonUid:           personUid,
				StudentUids:         fmt.Sprint(studentUid),
				WindowTime:          sendConfig.WindowTime,
				ShowStudentNameNums: sendConfig.ShowStudentNameNums,
				SendTime:            sendTime,
				SendTimeArr:         strings.Join(sendTimeArr, ","),
				CreateTime:          time.Now().Unix(),
				UpdateTime:          time.Now().Unix(),
				Content:             sendConfig.Content,
			}
			return models.TblNewLeadDingDingNoticeRef.AddNotice(ctx, newNotice)
		}
	} else {
		studentUids, _ := utils.StringToInt64Slice(ctx, notice.StudentUids)
		studentUids = append(studentUids, studentUid)
		updates := map[string]interface{}{
			"student_uids": utils.Int64ArrayToString(studentUids),
			"update_time":  time.Now().Unix(),
		}
		return models.TblNewLeadDingDingNoticeRef.UpdateById(ctx, notice.Id, updates)
	}
	return nil
}

// NewLeadMessageNotice 新例子消息发送钉钉提醒
func NewLeadMessageNotice(ctx *gin.Context) error {
	unSendNotice, err := models.TblNewLeadDingDingNoticeRef.GetUnSendMessage(ctx)
	if err != nil {
		zlog.Warnf(ctx, "NewLeadMessageNotice GetUnSendMessage, err: %+v", err)
		return err
	}
	token, err := infomsg.GetToken(ctx)
	if err != nil {
		return err
	}
	for _, notice := range unSendNotice {
		key := fmt.Sprintf("%d-%d-newleadadd", notice.CourseId, notice.BusinessUid)
		lock, _ := utils.LockRetry(ctx, key, "1", 5, 3)
		if !lock {
			continue
		}
		err = NewLeadMessageNoticeLogic(ctx, &notice, token)
		var status int
		var failMessage string
		if err != nil {
			zlog.Errorf(ctx, "NewLeadMessageNotice NewLeadMessageNoticeLogic, id:%d, err: %+v", notice.Id, err)
			status = models.DingDingNoticeTaskSendFail
			failMessage = err.Error()
		} else {
			status = models.DingDingNoticeTaskSendSuccess
		}
		updates := map[string]interface{}{
			"content":      notice.Content,
			"status":       status,
			"fail_message": failMessage,
			"update_time":  time.Now().Unix(),
		}
		err = models.TblNewLeadDingDingNoticeRef.UpdateById(ctx, notice.Id, updates)
		if err != nil {
			zlog.Warnf(ctx, "NewLeadMessageNotice UpdateById, id:%d, err: %+v", notice.Id, err)
			_, _ = utils.ReleaseLockByValue(ctx, key, "1")
			return err
		}
		_, _ = utils.ReleaseLockByValue(ctx, key, "1")
	}

	return nil

}

func NewLeadMessageNoticeLogic(ctx *gin.Context, notice *models.TblNewLeadDingDingNotice, token string) (err error) {
	// 获取课程信息
	studentUids, _ := utils.StringToInt64Slice(ctx, notice.StudentUids)
	if len(studentUids) == 0 {
		return nil
	}
	courseFields := []string{"courseId", "courseName", "learnSeason"}
	var courseInfo dal.CourseLessonInfo
	if courseInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, int64(notice.CourseId), courseFields, nil); err != nil {
		zlog.Warnf(ctx, "GetTeacherInfoReq 获取课程信息失败, courseId: %d, err: %+v", notice.CourseId, err)
		return components.ErrorAPIGetCourseInfo
	}
	content := notice.Content
	content = strings.ReplaceAll(content, "{course}", courseInfo.CourseName)
	content = strings.ReplaceAll(content, "{season}", defines.LearnSeasonNameMap[courseInfo.LearnSeason])
	content = strings.ReplaceAll(content, "{send_time}", fmt.Sprintf("[%s]", notice.SendTimeArr))
	content = strings.ReplaceAll(content, "{n}", fmt.Sprintf("%d", len(studentUids)))

	if len(studentUids) <= notice.ShowStudentNameNums {
		studentInfoMap, err := dau.GetStudents(ctx, studentUids, []string{"studentUid", "studentName", "uname", "registerPhone"})
		if err != nil {
			return err
		}
		var studentInfo []string
		for _, uid := range studentUids {
			if _, ok := studentInfoMap[uid]; !ok {
				continue
			}
			studentInfo = append(studentInfo, fmt.Sprintf("%s:%s  ", studentInfoMap[uid].StudentName, utils.GetPhoneLast4(studentInfoMap[uid].RegisterPhone)))
		}
		content = strings.ReplaceAll(content, "{studentInfo}", fmt.Sprintf("  学生信息:%s", strings.Join(studentInfo, "，")))
	} else {
		content = strings.ReplaceAll(content, "{studentInfo}", "")
	}
	notice.Content = content
	_, tplId := infomsg.GetSecret()
	params := infomsg.SendMsgParams{
		TplId:        tplId,
		Level:        2,
		Receivers:    fmt.Sprint(notice.PersonUid),
		TplArgs:      fmt.Sprintf("{\"content\":\"%s\"}", content),
		ReceiverType: "ipsUid",
	}

	return infomsg.SendMsg(ctx, params, token)
}

const (
	KpAscriptionListFuDao = 1
	KpAscriptionListLpc   = 4

	MergeSendTypeEndTimeSend    = 1 // 结束时间发送
	MergeSendTypeWindowTimeSend = 2 // 窗口时间发送
)

func getDeviceNewLeadAddConfig(ctx *gin.Context, businessUid int64) (string, int64, error) {
	var configKey string
	deviceInfo, err := mesh.GetDeviceInfoListByDeviceUids(ctx, businessUid)
	if err != nil {
		return configKey, 0, err
	}

	if len(deviceInfo.KpAscriptionList) > 0 {
		switch deviceInfo.KpAscriptionList[0] {
		case KpAscriptionListLpc:
			configKey = fmt.Sprintf("lpc-%d", deviceInfo.Grade)
		case KpAscriptionListFuDao:
			configKey = fmt.Sprintf("fudao-%d", deviceInfo.Grade)
		}

	}
	return configKey, int64(deviceInfo.StaffUid), nil
}

// getConfig 返回配置表, 发送时间范围，发送时间戳, 真人uid,  error
func getConfig(ctx *gin.Context, kpUid int64) (*SendConfig, []string, int64, int64, error) {
	configKey, personUid, err := getDeviceNewLeadAddConfig(ctx, kpUid)
	if configKey == "" || err != nil {
		return nil, nil, 0, personUid, err
	}
	var sendConfig = make(map[string][]SendConfig)
	err = mercury.GetConfigForJson(ctx, NewLeadMercucyConfigKey, 60, &sendConfig)
	if err != nil {
		return nil, nil, 0, personUid, err
	}
	// 获取不到配置直接return
	configs, ok := sendConfig[configKey]
	if !ok {
		return nil, nil, 0, personUid, nil
	}

	getSendTime := func(one SendConfig, now time.Time, nowStr string) ([]string, int64) {
		var sendTimeObj, zeroTimeObj time.Time
		var sendTime []string
		if one.MergeSendType == MergeSendTypeWindowTimeSend {
			sendTimeObj = now.Add(time.Second * time.Duration(one.WindowTime))
			sendTime = []string{nowStr, sendTimeObj.Format("15:04:05")}
		} else if one.MergeSendType == MergeSendTypeEndTimeSend {
			if nowStr < one.SendTime[1] { // 当前时间与结束时间未跨天
				zeroTimeObj, _ = time.ParseInLocation("2006-01-02", now.Format("2006-01-02"), now.Location())
			} else { // 当前时间与结束时间已跨天
				zeroTimeObj, _ = time.ParseInLocation("2006-01-02", now.Format("2006-01-02"), now.Location())
				zeroTimeObj = zeroTimeObj.AddDate(0, 0, 1)
			}

			sendTime = one.SendTime
			sendTimeObj, _ = time.ParseInLocation("2006-01-02 15:04:05", zeroTimeObj.Format("2006-01-02")+" "+one.SendTime[1], now.Location())
		}
		return sendTime, sendTimeObj.Unix()
	}

	now := time.Now()
	nowStr := now.Format("15:04:05")
	for _, one := range configs {
		if !one.SecondDay { // 时间未跨天
			if one.SendTime[0] <= nowStr && nowStr < one.SendTime[1] {
				sendTime, sendTimeUnix := getSendTime(one, now, nowStr)
				return &one, sendTime, sendTimeUnix, personUid, nil
			}
		} else {
			if one.SendTime[0] <= nowStr || nowStr < one.SendTime[1] {
				sendTime, sendTimeUnix := getSendTime(one, now, nowStr)
				return &one, sendTime, sendTimeUnix, personUid, nil
			}
		}
	}
	return nil, nil, 0, personUid, nil
}
