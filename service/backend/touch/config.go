package touch

import (
	"assistantdeskgo/api/sms"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"strconv"
	"strings"
)

type TemplateInfo struct {
	Id        int64    `json:"id"`
	SceneId   int64    `json:"sceneId"`
	SceneName string   `json:"SceneName"`
	VarList   []string `json:"varList"`
	Content   string   `json:"content"`
}

func GetTouchTemplateList(ctx *gin.Context, action, source, gradeId, subjectId, priceTag int64) (templateList []TemplateInfo, err error) {
	var (
		touchConfigList []models.TouchConf
		configValues    map[string][]string
		templateInfo    TemplateInfo
	)

	if touchConfigList, err = models.TouchConfigRef.GetTouchConfigBySourceAndAction(ctx, action, source); err != nil {
		return nil, err
	}

	templates := make(map[string]TemplateInfo)
	ids := make([]string, 0, len(configValues))
	for _, touchConfig := range touchConfigList {
		if !isMatchTemplate(ctx, touchConfig, gradeId, subjectId, priceTag) {
			continue
		}

		if configValues, err = touchConfig.GetConfigValue(); err != nil {
			zlog.Warnf(ctx, "get_touch_config_confValue_failed, configInfo: %+v", touchConfig)
			continue
		}
		for templateId, varList := range configValues {
			if templateInfo, err = formatTemplateInfo(ctx, touchConfig, templateId, varList); err != nil {
				continue
			}

			templates[templateId] = templateInfo
			ids = append(ids, templateId)
		}
	}

	// 按顺序输出
	sort.Strings(ids)
	templateList = make([]TemplateInfo, 0, len(ids))
	for _, id := range ids {
		if tplInfo, ok := templates[id]; ok {
			templateList = append(templateList, tplInfo)
		}
	}
	return templateList, nil
}

func formatTemplateInfo(ctx *gin.Context, touchConfig models.TouchConf, templateId string, varList []string) (templateInfo TemplateInfo, err error) {
	var (
		tplId     int
		tplDetail *sms.GetTplInfoRsp
	)

	if tplId, err = strconv.Atoi(templateId); err != nil {
		zlog.Warnf(ctx, "get_touch_config_tplId_failed, configInfo: %+v", touchConfig)
		return
	}

	templateInfo = TemplateInfo{
		Id:        int64(tplId),
		SceneId:   touchConfig.SceneId,
		SceneName: defines.TouchConfSceneMap[touchConfig.SceneId],
		VarList:   make([]string, 0),
		Content:   "",
	}

	if touchConfig.Action != defines.TouchConfActionIvr {
		// 过滤空的变量key
		for _, varKey := range varList {
			if len(varKey) == 0 {
				continue
			}
			templateInfo.VarList = append(templateInfo.VarList, varKey)
		}

		// 校验短信模板及重组content
		if tplDetail, err = GetSmsTplInfo(ctx, templateInfo.Id, templateInfo.VarList); err != nil {
			zlog.Warnf(ctx, "get_touch_config_templateInfo_failed, configInfo: %+v, templateId: %d", touchConfig, templateInfo.Id)
			return
		}
		if tplDetail.TplId == 0 {
			zlog.Warnf(ctx, "get_touch_config_templateInfo_failed, 短信模板已失效, templateId: %d", touchConfig, templateInfo.Id)
			return templateInfo, errors.New("短信模板已失效")
		}
		templateInfo.Content = tplDetail.Content
	}
	return templateInfo, nil
}

func isMatchTemplate(ctx *gin.Context, touchConfig models.TouchConf, gradeId, subjectId, priceTag int64) bool {
	var (
		err        error
		gradeIds   []string
		subjectIds []string
		priceTags  []string
	)
	if gradeIds, err = touchConfig.GetGradeIds(); err != nil {
		zlog.Warnf(ctx, "get_touch_config_graceIds_failed, configInfo: %+v", touchConfig)
		return false
	}
	if len(gradeIds) > 0 && !utils.InArrayString(strconv.Itoa(int(gradeId)), gradeIds) {
		return false
	}

	if subjectIds, err = touchConfig.GetSubjectIds(); err != nil {
		zlog.Warnf(ctx, "get_touch_config_subjectIds_failed, configInfo: %+v", touchConfig)
		return false
	}
	if len(subjectIds) > 0 && !utils.InArrayString(strconv.Itoa(int(subjectId)), subjectIds) {
		return false
	}

	if priceTags, err = touchConfig.GetPriceTags(); err != nil {
		zlog.Warnf(ctx, "get_touch_config_priceTags_failed, configInfo: %+v", touchConfig)
		return false
	}
	if len(priceTags) > 0 && !utils.InArrayString(strconv.Itoa(int(priceTag)), priceTags) {
		return false
	}

	return true
}

func GetSmsTplInfo(ctx *gin.Context, tplId int64, varList []string) (*sms.GetTplInfoRsp, error) {
	ret, err := sms.GetTplInfo(ctx, tplId)
	if err != nil {
		return nil, err
	}

	if len(varList) > 0 {
		for index, varKey := range varList {
			varIndexStr := fmt.Sprintf("{%d}", index+1)
			varStr := fmt.Sprintf("{%s}", varKey)
			ret.Content = strings.ReplaceAll(ret.Content, varIndexStr, varStr)
		}
	}
	return ret, nil
}
