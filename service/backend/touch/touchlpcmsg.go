package touch

import (
	"assistantdeskgo/api/agg"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/delayer"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
	"time"
)

const (
	CallResultUnAccess = 3 //1: 正在呼叫, 2:呼叫成功, 3:呼叫失败
	CallResultAccess   = 2 //1: 正在呼叫, 2:呼叫成功, 3:呼叫失败
	CallResultCalling  = 1 //1: 正在呼叫, 2:呼叫成功, 3:呼叫失败
)

type TouchCallStatusData struct {
	StaffUid     int64 `json:"staffUid"`
	CallId       int64 `json:"callId"`
	Status       int64 `json:"status"`
	ModeType     int64 `json:"modeType"`
	BusinessLine int64 `json:"businessLine"`
}

type TouchCallTimeData struct {
	CallId       int64  `json:"call_id" mapstructure:"call_id"`
	CallType     int64  `json:"call_type" mapstructure:"call_type"`
	SourceType   int64  `json:"source_type" mapstructure:"source_type"`
	CourseId     int64  `json:"course_id" mapstructure:"course_id"`
	ToUid        int64  `json:"to_uid" mapstructure:"to_uid"`
	DeviceId     int64  `json:"device_uid" mapstructure:"device_uid"`
	PersonUid    int64  `json:"person_uid" mapstructure:"person_uid"`
	FromUid      int64  `json:"from_uid" mapstructure:"from_uid"`
	Timestamp    int64  `json:"timestamp" mapstructure:"timestamp"`
	CallResult   int64  `json:"call_result" mapstructure:"call_result"`
	Duration     int64  `json:"duration" mapstructure:"duration"`
	StartTime    int64  `json:"start_time" mapstructure:"start_time"`
	PressMark    int    `json:"_press_mark" mapstructure:"_press_mark"`
	BusinessType string `json:"business_type" mapstructure:"business_type"`
	BusinessKey  string `json:"business_key" mapstructure:"business_key"`
}

func TouchCallTime(ctx *gin.Context, params TouchCallTimeData) error {
	zlog.Infof(ctx, "touchLpcMessage.params:%+v", params)
	if params.BusinessType != touchmis.BusinessTypeForPublicSea {
		zlog.Infof(ctx, "skip publicSea call record,call_id=%v", params.CallId)
		return nil
	}

	if params.DeviceId <= 0 || params.CallId <= 0 {
		paramsStr, err := json.Marshal(params)
		if err != nil {
			zlog.Warnf(ctx, "参数错误, 触达实时电话状态回调: 将参数转换为JSON失败: %w", err)
			return nil
		}
		zlog.Warnf(ctx, "参数错误,触达实时电话状态回调 params %s", paramsStr)
		return nil
	}
	//查询电话通话维系记录
	//req := touchmisgo.GetCallRecordInfoReq{
	//	CallIds: []int64{params.CallId},
	//}
	//callRecordMap, err := touchmisgo.GetCallRecordByCallId(ctx, req)
	//if err != nil {
	//	return errors.New(fmt.Sprintf("根据callId查询通话记录失败, callId:%d, params:%+v, err: %v", req.CallIds, params, err))
	//}
	//callRecord, ok := callRecordMap[params.CallId]
	//if !ok {
	//	return fmt.Errorf("can not find the callRecord by callId:%d", params.CallId)
	//}
	//判断 BusinessType 是否不是 privateSea 或 publicSea
	if params.PressMark == 1 {
		zlog.Infof(ctx, "skip preessMark data,call_id=%v", params.CallId)
		return nil
	}

	studentUid, courseId, err := utils.SplitBusinessKey(params.BusinessKey)
	if err != nil || courseId == 0 || studentUid == 0 {
		return err
	}
	params.StartTime = params.StartTime / 1000 // 毫秒转换为秒
	err = updateOpLogCallTime(ctx, params.DeviceId, studentUid, courseId, params.StartTime, params.CallResult)
	if err != nil {
		zlog.Warnf(ctx, "[TouchCallTime] updateOpLogCallTime failed, params:%+v, err: %+v", params, err)
		return err
	}

	if params.CallResult == CallResultAccess {
		err = updateLockStatus(ctx, studentUid, courseId)
		if err != nil {
			zlog.Warnf(ctx, "[TouchCallTime] updateLockStatus failed, params:%+v, err: %+v", params, err)
			return err
		}
	}

	//添加延时任务
	callbackInfo := make(map[string]interface{})
	callbackInfo["callId"] = params.CallId
	callbackInfo["startTime"] = params.StartTime
	callbackInfo["deviceUid"] = params.DeviceId
	callbackInfo["courseId"] = courseId
	callbackInfo["studentUid"] = studentUid
	callbackInfo["clueId"] = params.BusinessKey
	err = AddDelayerTaskForTouchCallTime(ctx, callbackInfo)
	if err != nil {
		zlog.Warnf(ctx, "[AddDelayerTaskForTouchCallTime] set delay message,, err: %+v", err)
		return errors.New(fmt.Sprintf("添加延时任务失败, err: %v", err))
	}
	return nil

}

func updateLockStatus(ctx *gin.Context, studentUid, courseId int64) (err error) {
	// 释放线索锁定状态
	req := agg.RefreshLockStatusReq{
		StudentUid: studentUid,
		CourseId:   courseId,
		LockStatus: agg.LockStatusOn,
	}
	resp, err := agg.RefreshLockStatus(ctx, req)
	if err != nil {
		return
	}
	if !resp.Result {
		err = errors.New(fmt.Sprintf("数据更新失败,req:%+v", req))
		return
	}
	return
}

func updateOpLogCallTime(ctx *gin.Context, assistantUid, studentuid, courseId, startTime, callResult int64) error {
	key := fmt.Sprintf("%d-%d-%d-newleadadd", courseId, studentuid, assistantUid)
	lock, err2 := utils.LockRetry(ctx, key, "1", 5, 3)
	if !lock {
		return err2
	}
	defer func() {
		_, _ = utils.ReleaseLockByValue(ctx, key, "1")
	}()
	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: assistantUid,
	}
	cluId := utils.GetClueId(courseId, studentuid)
	publicSeaOpLog, err := tblPublicSeaOpLog.GetListFirstByCond(ctx, map[string]interface{}{"clue_id": cluId, "device_uid": assistantUid}, 0, 1, "")
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tblPublicSeaOpLog = models.TblPublicSeaOpLog{
				CourseId:   courseId,
				StudentUid: studentuid,
				DeviceUid:  assistantUid,
				ClueId:     cluId,
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			}
			if callResult == CallResultAccess {
				tblPublicSeaOpLog.LastAccessTime = startTime
			} else {
				tblPublicSeaOpLog.LastCallTime = startTime
			}

			err = tblPublicSeaOpLog.Insert(ctx, tblPublicSeaOpLog)
			if err != nil {
				zlog.Warnf(ctx, "[mqconsumer touchcalltime updateOpLogCallTime] insert publicseaoplog failed, err: %+v", err)
			}
			return err
		} else {
			return err
		}
	} else if len(publicSeaOpLog) > 0 {
		existingLog := publicSeaOpLog[0]
		if startTime > existingLog.LastAccessTime {
			clueId := utils.GetClueId(courseId, studentuid)
			cond := map[string]interface{}{"clue_id": clueId, "device_uid": assistantUid}
			udpateData := map[string]interface{}{
				"update_time": time.Now().Unix(),
			}
			if callResult == CallResultAccess {
				udpateData["last_access_time"] = startTime
			} else {
				udpateData["last_call_time"] = startTime
			}
			err = tblPublicSeaOpLog.UpdateByCondition(ctx, cond, udpateData)
			if err != nil {
				zlog.Warnf(ctx, "[mqconsumer touchcalltime updateOpLogCallTime] update publicseaoplog failed, err: %+v", err)
				return err
			}
		}
	}
	return nil
}

func AddDelayerTaskForTouchCallTime(ctx *gin.Context, touchCallInfo map[string]interface{}) error {
	contentStr, err := jsoniter.MarshalToString(touchCallInfo)
	if err != nil {
		return err
	}

	params := &delayer.SetDelayMessageReq{
		AppKey:        components.DelayerAppKey,
		CallbackTime:  time.Now().Add(defines.PublicSeaTouchExpireTime * time.Second).Unix(),
		ContentType:   components.DelayerContentTypeForTouchCallTime,
		Content:       contentStr,
		RetryLimit:    components.DelayerRetryLimit,
		OvertimeLimit: components.DelayerDefaultOverTime,
	}

	ret, err := delayer.SetDelayMessage(ctx, params)
	zlog.Infof(ctx, "[AddDelayerTaskForTouchCallTime] set delay message, ret: %+v, err: %+v", ret, err)

	return err
}
