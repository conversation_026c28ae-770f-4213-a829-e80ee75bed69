package laxintagtool

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type LaxinTagFixDataManager interface {
	BatchCreateFixData(ctx *gin.Context, data []*models.LaxinTagDataFix, tx *gorm.DB) error
	BatchDeleteFixData(ctx *gin.Context, ids []int64, tx *gorm.DB) error
	InitFixDataFromContent(ctx *gin.Context, content [][]string, task *models.LaxinTagDataFixTask) ([]*models.LaxinTagDataFix, map[int][]string, error)
	GetExistList(ctx *gin.Context, preData []*models.LaxinTagDataFix) ([]*models.LaxinTagDataFix, []int, error)
	GetNotExistList(ctx *gin.Context, preData []*models.LaxinTagDataFix) ([]int64, []*models.LaxinTagDataFix, []int, error)
	GetListByPage(ctx *gin.Context, pageIndex, pageSize int, courseID []int, email, phone string) ([]*models.LaxinTagDataFix, map[string]mesh.StaffInfo, int64, error)
}

func GetLaxinTagFixDataManager(ctx *gin.Context) LaxinTagFixDataManager {
	return &dataImpl{}
}
