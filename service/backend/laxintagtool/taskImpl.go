package laxintagtool

import (
	"assistantdeskgo/dto/dtolaxintagfixtool"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

type taskImpl struct{}

func (t taskImpl) GetList(ctx *gin.Context, lastID int64) (res []*models.LaxinTagDataFixTask, err error) {
	return models.LaxinTagDataFixTaskDao.GetList(ctx, lastID)
}

func (t taskImpl) GetTaskById(ctx *gin.Context, taskId int64) (*models.LaxinTagDataFixTask, error) {
	return models.LaxinTagDataFixTaskDao.GetByTaskID(ctx, taskId)
}

func (t taskImpl) UpdateTask(ctx *gin.Context, status int, taskId int64, resultFile, failReason string) error {
	task, err := models.LaxinTagDataFixTaskDao.GetByTaskID(ctx, taskId)
	if err != nil {
		return err
	}

	if task.ID == 0 {
		zlog.Warnf(ctx, "task not exit %v", taskId)
		return nil
	}

	updateMap := make(map[string]interface{})

	if task.Status != 0 && task.Status != status {
		updateMap["status"] = status
	}

	if resultFile != "" && resultFile != task.RetFile {
		updateMap["ret_file"] = resultFile
	}

	if failReason != "" && failReason != task.FailReason {
		updateMap["fail_reason"] = failReason
	}

	err = models.LaxinTagDataFixTaskDao.UpdateByMap(ctx, taskId, updateMap)
	if err != nil {
		return err
	}
	return nil
}

func (t taskImpl) BuildTaskResp(ctx *gin.Context, total int64, taskList []*models.LaxinTagDataFixTask) dtolaxintagfixtool.GetLaxinTagFileTaskResponse {
	var err error
	taskRes := make([]*dtolaxintagfixtool.LaxinTagTaskItem, 0)
	for _, task := range taskList {
		var remoteFile string

		retId := task.FilePath
		if task.RetFile != "" {
			retId = task.RetFile
		}

		remoteFile, err = helpers.BaiduBucket2.GetUrlByFileName(ctx, retId, 24*60*time.Second)
		if err != nil {
			zlog.Errorf(ctx, "BuildTaskResp GetUrlByFileName err:%v", err)
			remoteFile = ""
		}

		taskRes = append(taskRes, &dtolaxintagfixtool.LaxinTagTaskItem{
			ID:             task.ID,
			OriginFileName: task.OriginFileName,
			Comment:        task.CommentInfo,
			Status:         task.Status,
			OperatorType:   task.OperatorType,
			OperatorName:   task.OperatorName,
			UpdateTime:     task.UpdateTime,
			RetFileURL:     remoteFile,
		})
	}
	return dtolaxintagfixtool.GetLaxinTagFileTaskResponse{
		List:  taskRes,
		Total: total,
	}
}

func (t taskImpl) GetTaskListByStatusAndOperator(ctx *gin.Context, status int, operator string, page, size int) ([]*models.LaxinTagDataFixTask, int64, error) {
	return models.LaxinTagDataFixTaskDao.GetByStatusAndOperator(ctx, status, page, size, operator)
}

func (t taskImpl) WriteErrorStatus(ctx *gin.Context, errStr string, taskId int64) error {
	updateMap := map[string]interface{}{
		"fail_reason": errStr,
		"status":      models.LaxinTagDataFixTaskStatusPartialFail,
	}
	err := models.LaxinTagDataFixTaskDao.UpdateByMap(ctx, taskId, updateMap)
	if err != nil {
		return err
	}
	return nil
}

func (t taskImpl) BuildDaoTask(ctx *gin.Context, taskReq *dtolaxintagfixtool.UploadLaxinTagFileRequest) (*models.LaxinTagDataFixTask, error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return nil, err
	}
	return &models.LaxinTagDataFixTask{
		OriginFileName: taskReq.OriginFileName,
		CommentInfo:    taskReq.Comment,
		FilePath:       taskReq.RemoteFileName,
		RetFile:        "", // 后续处理完再更新结果文件
		Status:         models.LaxinTagDataFixTaskStatusPending,
		FailReason:     "",
		OperatorType:   taskReq.Type,
		OperatorUid:    int64(userInfo.UserId),
		OperatorName:   userInfo.UserName,
		CreateTime:     time.Now().Unix(),
		UpdateTime:     time.Now().Unix(),
		IsDeleted:      false,
	}, nil
}

func (t taskImpl) CreateTask(ctx *gin.Context, task *models.LaxinTagDataFixTask) (int64, error) {
	taskId, err := models.LaxinTagDataFixTaskDao.Create(ctx, task)
	if err != nil {
		return 0, err
	}
	return taskId, nil
}
