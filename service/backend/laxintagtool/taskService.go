package laxintagtool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolaxintagfixtool"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"reflect"
	"strconv"
	"strings"
)

func UploadLaxinTagFile(ctx *gin.Context, req dtolaxintagfixtool.UploadLaxinTagFileRequest) (err error) {
	// 校验
	if !utils.InArrayInt(req.Type, []int{models.LaxinTagDataFixTaskOperaTypeCreate, models.LaxinTagDataFixTaskOperaTypeDelete}) {
		return errors.New("type is not valid:" + strconv.Itoa(req.Type))
	}

	if req.RemoteFileName == "" || req.OriginFileName == "" {
		return errors.New("RemoteFileName or OriginFileName is empty")
	}

	_, content, err := utils.GetFileContent(ctx, req.RemoteFileName)
	if err != nil {
		return errors.New("get file content error:" + err.Error())
	}

	// 至少有一行数据
	if len(content) <= 2 {
		return errors.New("file has no content")
	}

	if len(content[0]) != components.LaxinTagFixTemplateColumn || !reflect.DeepEqual(content[0], components.GetLaxinTagFixTemplateHead()) {
		return errors.New("template is not valid")
	}

	// build dao
	taskDao, err := GetLaxinTagFixTaskManager(ctx).BuildDaoTask(ctx, &req)
	if err != nil {
		return err
	}

	// create task
	taskId, err := GetLaxinTagFixTaskManager(ctx).CreateTask(ctx, taskDao)
	if err != nil {
		return err
	}

	// send msg
	data := map[string]interface{}{
		"taskId": taskId,
	}

	zlog.Infof(ctx, "sendLaXinTagFixTaskMQ, param:%+v", data)
	res, err := rmq.SendCmd(ctx, "self_product", 214202, "core", "zb", data, "")
	if err != nil {
		errNew := GetLaxinTagFixTaskManager(ctx).WriteErrorStatus(ctx, err.Error(), taskId)
		if errNew != nil {
			return err
		}
		zlog.Warnf(ctx, "sendLaXinTagFixTaskMQ err, taskId:%+v,msgId:%+v,err:%v", taskId, res, err)
	}

	return
}

func GetLaxinTagFileTask(ctx *gin.Context, req dtolaxintagfixtool.GetLaxinTagFileTaskRequest) (resp dtolaxintagfixtool.GetLaxinTagFileTaskResponse, err error) {
	// 校验
	if req.Status != 0 && !utils.InArrayInt(req.Status, []int{models.LaxinTagDataFixTaskStatusPending, models.LaxinTagDataFixTaskStatusInProgress,
		models.LaxinTagDataFixTaskStatusSuccess, models.LaxinTagDataFixTaskStatusPartialFail}) {
		return resp, errors.New("status is not valid:" + strconv.Itoa(req.Status))
	}

	var status int
	page := components.DefaultPageNum
	size := components.DefaultSizeNum

	if req.Status != 0 {
		status = req.Status
	}

	if req.Page != 0 {
		page = req.Page
	}

	if req.Size != 0 {
		size = req.Size
	}

	// filter
	taskList, total, err := GetLaxinTagFixTaskManager(ctx).GetTaskListByStatusAndOperator(ctx, status, req.OperatorName, page, size)
	if err != nil {
		return resp, err
	}

	if req.FileName == "" {
		return GetLaxinTagFixTaskManager(ctx).BuildTaskResp(ctx, total, taskList), nil
	}

	// search
	var lastID int64
	batchSize := 500
	result := make([]*models.LaxinTagDataFixTask, 0)

	for {
		var batch []*models.LaxinTagDataFixTask
		batch, err = GetLaxinTagFixTaskManager(ctx).GetList(ctx, lastID)
		if err != nil {
			return
		}
		for _, task := range batch {
			if strings.Contains(strings.ToLower(task.OriginFileName), strings.ToLower(req.FileName)) {
				result = append(result, task)
			}
		}

		if len(batch) < batchSize {
			break
		}

		lastID = batch[len(batch)-1].ID
	}

	// 分页
	total = int64(len(result))
	start := (page - 1) * size
	end := page * size
	if len(result) > end {
		result = result[start:end]
	}

	return GetLaxinTagFixTaskManager(ctx).BuildTaskResp(ctx, total, result), nil
}
