package laxintagtool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"os"
	"strconv"
	"strings"
	"time"
)

func HandleLaxinTagFixTask(ctx *gin.Context, p mcpack.V2Map) error {
	var err error
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "HandleLaxinTagFixTask error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "HandleLaxinTagFixTask panic, err:%v", errPanic)
		}
	}()

	taskId := cast.ToInt(p["taskId"])
	zlog.Infof(ctx, "214202_HandleLaxinTagFixTask_Param: %+v", taskId)

	// get task
	task, err := GetLaxinTagFixTaskManager(ctx).GetTaskById(ctx, int64(taskId))
	if err != nil {
		return err
	}

	if task.ID == 0 || task.Status != models.LaxinTagDataFixTaskStatusPending {
		zlog.Warnf(ctx, "Laxin tag fix task not need handle: status is %v,id is %v", task.Status, task.ID)
		return nil
	}

	// 解析
	_, content, err := utils.GetFileContent(ctx, task.FilePath)
	if err != nil {
		errNew := GetLaxinTagFixTaskManager(ctx).UpdateTask(ctx, models.LaxinTagDataFixTaskStatusPartialFail, int64(taskId), "", err.Error())
		if errNew != nil {
			zlog.Warnf(ctx, "Laxin tag fix task write err  UpdateTask err %v,ori err:%v,id is %v", err.Error(), errNew.Error(), task.ID)
			return err
		}
		return errors.New("get file content error:" + err.Error())
	}

	// 更新状态
	err = GetLaxinTagFixTaskManager(ctx).UpdateTask(ctx, models.LaxinTagDataFixTaskStatusInProgress, int64(taskId), "", "")
	if err != nil {
		errNew := GetLaxinTagFixTaskManager(ctx).UpdateTask(ctx, models.LaxinTagDataFixTaskStatusPartialFail, int64(taskId), "", err.Error())
		if errNew != nil {
			zlog.Warnf(ctx, "Laxin tag fix task UpdateTask to in progress err %v,ori err:%v,id is %v", err.Error(), errNew.Error(), task.ID)
			return err
		}
		return err
	}

	// 执行数据导入
	err = doLaxinTagFixImport(ctx, task, content)
	if err != nil {
		errNew := GetLaxinTagFixTaskManager(ctx).UpdateTask(ctx, models.LaxinTagDataFixTaskStatusPartialFail, int64(taskId), "", err.Error())
		if errNew != nil {
			zlog.Warnf(ctx, "Laxin tag fix task doLaxinTagFixImport err %v,ori err:%v,id is %v", err.Error(), errNew.Error(), task.ID)
			return err
		}
		return err
	}

	return nil
}

func doLaxinTagFixImport(ctx *gin.Context, task *models.LaxinTagDataFixTask, content [][]string) error {
	var hasValidateErr bool
	ins, err := GetLaxinTagFixImportInstance(ctx, task.OperatorType)
	if err != nil {
		return err
	}

	resContent := make([][]string, 0)
	resContent = append(resContent, content[0:1]...)

	// 第三行开始有数据,写入成功的即可
	for i := 2; i < len(content); i += 500 {
		end := i + 500
		if end > len(content) {
			end = len(content)
		}
		group := content[i:end]
		// 提取数据+基本的校验
		data, validateErr, err := ins.InitData(ctx, group, task)
		if err != nil {
			// 获取资产信息失败
			return err
		}
		// 可写入的数据
		data, indexMap := getInsertData(ctx, data, validateErr)

		validateErr, err = ins.InsertData(ctx, task, data, indexMap, validateErr)
		if err != nil {
			return err
		}

		if len(validateErr) > 0 {
			hasValidateErr = true
		}

		// 写入结果文件
		resContent = buildResContent(ctx, resContent, group, validateErr)
	}

	// 生成 csv 文件
	csvFileName := ins.GetCosFileName(task)
	csvFilePath := "./" + csvFileName + ".csv"
	if err := utils.WriteToCsv(csvFilePath, resContent, true); err != nil {
		zlog.Warnf(ctx, "laxinTagFixImportResult,WriteToCsv error,err:%s,return", err)
		return nil
	}
	defer func() {
		os.Remove(csvFilePath)
	}()

	// 上传 bos
	remoteFilePath, _, err := utils.UploadFile2Bos(ctx, csvFilePath, csvFileName, "csv", "laxinTagFixImportResult")
	if err != nil {
		zlog.Warnf(ctx, "laxinTagFixImportResult,UploadFile2Bos error,err:%s,return", err)
		return err
	}

	status := models.LaxinTagDataFixTaskStatusSuccess
	fileReason := ""

	if hasValidateErr {
		status = models.LaxinTagDataFixTaskStatusPartialFail
		fileReason = "some uploads failed due to some abnormal data"
	}

	// 更新状态
	err = GetLaxinTagFixTaskManager(ctx).UpdateTask(ctx, status, task.ID, remoteFilePath, fileReason)
	if err != nil {
		return err
	}

	return nil
}

func buildResContent(ctx *gin.Context, res [][]string, newGroup [][]string, validateErr map[int][]string) [][]string {
	for i, _ := range newGroup {
		e, ok := validateErr[i]
		if ok {
			newGroup[i][5] = strings.Join(e, " ")
			continue
		}
		newGroup[i][5] = "处理成功"
	}

	res = append(res, newGroup...)
	return res
}

func getInsertData(ctx *gin.Context, data []*models.LaxinTagDataFix, validateErr map[int][]string) ([]*models.LaxinTagDataFix, map[int]int) {
	var i int
	indexMap := make(map[int]int, 0) // res:origin
	res := make([]*models.LaxinTagDataFix, 0)
	for index, info := range data {
		if _, ok := validateErr[index]; !ok {
			res = append(res, info)
			indexMap[i] = index
			i++
		}
	}

	return res, indexMap
}

func buildLaxinTagFixDataLog(task *models.LaxinTagDataFixTask, data []*models.LaxinTagDataFix) []*models.LaxinTagDataFixLog {
	result := make([]*models.LaxinTagDataFixLog, 0)
	for _, item := range data {
		result = append(result, &models.LaxinTagDataFixLog{
			RecordID:          item.ID,
			OperatorType:      task.OperatorType,
			CourseID:          item.CourseID,
			AssistantPhone:    item.AssistantPhone,
			AssistantUID:      item.AssistantUID,
			PersonEmailPrefix: item.PersonEmailPrefix,
			PersonUID:         item.PersonUID,
			OperatorUID:       task.OperatorUid,
			Operator:          task.OperatorName,
			IsDeleted:         false,
			CreateTime:        time.Now().Unix(),
			UpdateTime:        time.Now().Unix(),
		})
	}
	return result
}

type LaxinTagFixImport interface {
	InitData(ctx *gin.Context, content [][]string, task *models.LaxinTagDataFixTask) ([]*models.LaxinTagDataFix, map[int][]string, error)
	InsertData(ctx *gin.Context, task *models.LaxinTagDataFixTask, data []*models.LaxinTagDataFix, indexMap map[int]int, validateErr map[int][]string) (map[int][]string, error)
	GetCosFileName(task *models.LaxinTagDataFixTask) string
}

func GetLaxinTagFixImportInstance(ctx *gin.Context, t int) (LaxinTagFixImport, error) {
	switch t {
	case models.LaxinTagDataFixTaskOperaTypeCreate:
		return &laxinTagDataFixTaskCreate{}, nil
	case models.LaxinTagDataFixTaskOperaTypeDelete:
		return &laxinTagDataFixTaskDelete{}, nil
	default:
		return nil, components.ErrorParamInvalid
	}
}

type laxinTagDataFixTaskCreate struct{}

func (l laxinTagDataFixTaskCreate) GetCosFileName(task *models.LaxinTagDataFixTask) string {
	return components.ResultFileNameCreate + "_" + strconv.Itoa(int(task.ID)) + "_" + time.Now().Format("2006-01-02_15:04:05")
}

func (l laxinTagDataFixTaskCreate) InsertData(ctx *gin.Context, task *models.LaxinTagDataFixTask, data []*models.LaxinTagDataFix, indexMap map[int]int, validateErr map[int][]string) (map[int][]string, error) {
	if len(data) == 0 {
		return validateErr, nil
	}

	var err error
	// 先查数据是否已存在
	legalData, errIndex, err := GetLaxinTagFixDataManager(ctx).GetExistList(ctx, data)
	if err != nil {
		return nil, err
	}

	for _, index := range errIndex {
		validateErr[indexMap[index]] = append(validateErr[indexMap[index]], components.ValidateErrDataIsExist)
	}

	// 创建数据
	err = helpers.MysqlClientFuDao.Transaction(func(tx *gorm.DB) error {
		err = GetLaxinTagFixDataManager(ctx).BatchCreateFixData(ctx, legalData, tx)
		if err != nil {
			return err
		}

		// 写 log
		err = models.LaxinTagDataFixLogDao.BatchCreate(ctx, buildLaxinTagFixDataLog(task, legalData), tx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return validateErr, nil
}

func (l laxinTagDataFixTaskCreate) InitData(ctx *gin.Context, content [][]string, task *models.LaxinTagDataFixTask) ([]*models.LaxinTagDataFix, map[int][]string, error) {
	return GetLaxinTagFixDataManager(ctx).InitFixDataFromContent(ctx, content, task)
}

type laxinTagDataFixTaskDelete struct{}

func (l laxinTagDataFixTaskDelete) GetCosFileName(task *models.LaxinTagDataFixTask) string {
	return components.ResultFileNameDelete + "_" + strconv.Itoa(int(task.ID)) + "_" + time.Now().Format("2006-01-02_15:04:05")
}

func (l laxinTagDataFixTaskDelete) InsertData(ctx *gin.Context, task *models.LaxinTagDataFixTask, data []*models.LaxinTagDataFix, indexMap map[int]int, validateErr map[int][]string) (map[int][]string, error) {
	if len(data) == 0 {
		return validateErr, nil
	}

	var err error
	// 先查要删除的数据是否不存在
	ids, beforeData, errIndex, err := GetLaxinTagFixDataManager(ctx).GetNotExistList(ctx, data)
	if err != nil {
		return nil, err
	}

	for _, index := range errIndex {
		validateErr[indexMap[index]] = append(validateErr[indexMap[index]], components.ValidateErrDataNotExist)
	}

	// 删除数据
	err = helpers.MysqlClientFuDao.Transaction(func(tx *gorm.DB) error {
		err = GetLaxinTagFixDataManager(ctx).BatchDeleteFixData(ctx, ids, tx)
		if err != nil {
			return err
		}

		// 写 log
		err = models.LaxinTagDataFixLogDao.BatchCreate(ctx, buildLaxinTagFixDataLog(task, beforeData), tx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return validateErr, nil
}

func (l laxinTagDataFixTaskDelete) InitData(ctx *gin.Context, content [][]string, task *models.LaxinTagDataFixTask) ([]*models.LaxinTagDataFix, map[int][]string, error) {
	return GetLaxinTagFixDataManager(ctx).InitFixDataFromContent(ctx, content, task)
}
