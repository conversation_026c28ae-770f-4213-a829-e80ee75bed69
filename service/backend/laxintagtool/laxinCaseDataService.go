package laxintagtool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolaxintagfixtool"
	"assistantdeskgo/models"
	"errors"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
	"time"
)

func GetLaxinTagCase(ctx *gin.Context, req dtolaxintagfixtool.GetLaxinTagCaseRequest) (dtolaxintagfixtool.GetLaxinTagCaseResponse, error) {
	var resp dtolaxintagfixtool.GetLaxinTagCaseResponse
	page := components.DefaultPageNum
	size := components.DefaultSizeNum

	if req.Page != 0 {
		page = req.Page
	}

	if req.Size != 0 {
		size = req.Size
	}

	var courseId []int
	if req.CourseID != "" {
		strArr := strings.Split(req.CourseID, ",")
		for _, str := range strArr {
			num, err := strconv.Atoi(str)
			if err != nil || num <= 0 {
				return resp, errors.New("course id params is validate")
			}
			courseId = append(courseId, num)
		}
	}

	if len(courseId) == 0 && req.AssistantPhone == "" && req.PersonEmailPrefix == "" {
		return resp, errors.New("please add case query conditions")
	}

	dataList, staffInfo, total, err := GetLaxinTagFixDataManager(ctx).GetListByPage(ctx, page, size, courseId, req.PersonEmailPrefix, req.AssistantPhone)
	if err != nil {
		return resp, err
	}

	taskRes := make([]*dtolaxintagfixtool.LaxinTagCaseItem, 0)
	for _, data := range dataList {
		first, err := models.GetLaxinTagDataFixFirstLineStr(data.FirstLineTeam)
		if err != nil {
			return resp, err
		}

		second, err := models.GetLaxinTagDataFixSecondLineStr(data.SecondLineTeam)
		if err != nil {
			return resp, err
		}

		taskRes = append(taskRes, &dtolaxintagfixtool.LaxinTagCaseItem{
			Name:           staffInfo[data.PersonEmailPrefix+components.Emailsuffix].UserName,
			CourseID:       data.CourseID,
			AssistantPhone: data.AssistantPhone,
			PersonEmail:    data.PersonEmailPrefix,
			FirstLineTeam:  first,
			SecondLineTeam: second,
			Operator:       data.Operator,
			IsDeleted:      data.IsDeleted,
			UpdateTime:     data.UpdateTime,
		})
	}
	return dtolaxintagfixtool.GetLaxinTagCaseResponse{
		List:  taskRes,
		Total: total,
	}, nil
}

func GetCaseChangeLog(ctx *gin.Context, req dtolaxintagfixtool.GetCaseChangeLogRequest) (resp dtolaxintagfixtool.GetCaseChangeLogResponse, err error) {
	var courseId []int
	var email, phone []string

	if req.CourseID != "" {
		strArr := strings.Split(req.CourseID, ",")
		for _, str := range strArr {
			num, err := strconv.Atoi(str)
			if err != nil || num <= 0 {
				return resp, errors.New("course id params is validate")
			}
			courseId = append(courseId, num)
		}
	}

	if req.PersonEmailPrefix != "" {
		strArr := strings.Split(req.PersonEmailPrefix, ",")
		for _, str := range strArr {
			if str == "" {
				return resp, errors.New("email params is validate")
			}
			email = append(email, str)
		}
	}

	if req.AssistantPhone != "" {
		strArr := strings.Split(req.AssistantPhone, ",")
		for _, str := range strArr {
			if str == "" {
				return resp, errors.New("phone params is validate")
			}
			phone = append(phone, str)
		}
	}

	res, err := models.LaxinTagDataFixLogDao.GetByCond(ctx, courseId, email, phone)
	if err != nil {
		return
	}

	item := make([]*dtolaxintagfixtool.GetCaseChangeLogItem, 0)
	for _, data := range res {
		item = append(item, &dtolaxintagfixtool.GetCaseChangeLogItem{
			RecordID:          data.RecordID,
			OperatorType:      data.OperatorType,
			CourseID:          data.CourseID,
			AssistantPhone:    data.AssistantPhone,
			AssistantUID:      data.AssistantUID,
			PersonEmailPrefix: data.PersonEmailPrefix,
			Operator:          data.Operator,
			CreateTime:        time.Unix(data.CreateTime, 0).Format("2006-01-02 15:04:05"),
		})
	}
	return dtolaxintagfixtool.GetCaseChangeLogResponse{
		List: item,
	}, nil

}
