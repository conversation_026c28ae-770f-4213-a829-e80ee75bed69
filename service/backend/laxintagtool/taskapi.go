package laxintagtool

import (
	"assistantdeskgo/dto/dtolaxintagfixtool"
	"assistantdeskgo/models"
	"github.com/gin-gonic/gin"
)

type LaxinTagFixTaskManager interface {
	CreateTask(ctx *gin.Context, task *models.LaxinTagDataFixTask) (int64, error)
	WriteErrorStatus(ctx *gin.Context, err string, taskId int64) error
	GetTaskListByStatusAndOperator(ctx *gin.Context, status int, operator string, page, size int) ([]*models.LaxinTagDataFixTask, int64, error)
	GetTaskById(ctx *gin.Context, taskId int64) (*models.LaxinTagDataFixTask, error)
	GetList(ctx *gin.Context, lastID int64) (res []*models.LaxinTagDataFixTask, err error)
	UpdateTask(ctx *gin.Context, status int, taskId int64, resultFile, failReason string) error
	BuildDaoTask(ctx *gin.Context, task *dtolaxintagfixtool.UploadLaxinTagFileRequest) (*models.LaxinTagDataFixTask, error)
	BuildTaskResp(ctx *gin.Context, total int64, taskList []*models.LaxinTagDataFixTask) dtolaxintagfixtool.GetLaxinTagFileTaskResponse
}

func GetLaxinTagFixTaskManager(ctx *gin.Context) LaxinTagFixTaskManager {
	return &taskImpl{}
}
