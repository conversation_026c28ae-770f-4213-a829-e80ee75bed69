package laxintagtool

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/components"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type dataImpl struct {
}

func (d dataImpl) GetListByPage(ctx *gin.Context, pageIndex, pageSize int, courseID []int, email, phone string) ([]*models.LaxinTagDataFix, map[string]mesh.StaffInfo, int64, error) {
	dataList, total, err := models.LaxinTagDataFixDao.GetListByPage(ctx, pageIndex, pageSize, courseID, email, phone)
	if err != nil {
		return nil, nil, 0, err
	}

	emailArray := make([]string, 0)
	// get name
	for _, item := range dataList {
		emailArray = append(emailArray, item.PersonEmailPrefix)
	}

	staffInfo, err := mesh.GetStaffInfoListByEmails(ctx, utils.SliceUniq(emailArray))
	if err != nil {
		return nil, nil, 0, err
	}
	return dataList, staffInfo, total, nil
}

func (d dataImpl) GetNotExistList(ctx *gin.Context, preData []*models.LaxinTagDataFix) ([]int64, []*models.LaxinTagDataFix, []int, error) {
	errIndex := make([]int, 0)
	result := make([]int64, 0)
	beforeData := make([]*models.LaxinTagDataFix, 0)

	cond := generateWhereClause(preData)

	getData, err := models.LaxinTagDataFixDao.GetListByCond(ctx, cond)
	if err != nil {
		return nil, nil, nil, err
	}

	keyMap := make(map[string]*models.LaxinTagDataFix)
	for _, item := range getData {
		keyMap[strconv.Itoa(item.CourseID)+item.AssistantPhone+item.PersonEmailPrefix] = item
	}

	for index, item := range preData {
		if oldData, ok := keyMap[strconv.Itoa(item.CourseID)+item.AssistantPhone+item.PersonEmailPrefix]; !ok {
			errIndex = append(errIndex, index)
			continue
		} else {
			result = append(result, oldData.ID)
			beforeData = append(beforeData, oldData)
		}
	}

	return result, beforeData, errIndex, nil
}

func (d dataImpl) GetExistList(ctx *gin.Context, preData []*models.LaxinTagDataFix) ([]*models.LaxinTagDataFix, []int, error) {
	errIndex := make([]int, 0)
	result := make([]*models.LaxinTagDataFix, 0)
	cond := generateWhereClause(preData)

	getData, err := models.LaxinTagDataFixDao.GetListByCond(ctx, cond)
	if err != nil {
		return nil, nil, err
	}

	keyMap := make(map[string]*models.LaxinTagDataFix)
	for _, item := range getData {
		keyMap[strconv.Itoa(item.CourseID)+item.AssistantPhone+item.PersonEmailPrefix] = item
	}

	for index, item := range preData {
		if _, ok := keyMap[strconv.Itoa(item.CourseID)+item.AssistantPhone+item.PersonEmailPrefix]; ok {
			errIndex = append(errIndex, index)
			continue
		}
		result = append(result, item)
	}

	return result, errIndex, nil
}

func generateWhereClause(data []*models.LaxinTagDataFix) string {
	var conditions []string

	for _, item := range data {
		condition := fmt.Sprintf(
			"(course_id = %d AND assistant_phone = '%s' AND person_email_prefix = '%s')",
			item.CourseID, item.AssistantPhone, item.PersonEmailPrefix,
		)
		conditions = append(conditions, condition)
	}

	whereClause := strings.Join(conditions, " OR ")

	return "(" + whereClause + ")" + " AND is_deleted = 0"
}

func (d dataImpl) BatchCreateFixData(ctx *gin.Context, data []*models.LaxinTagDataFix, tx *gorm.DB) error {
	return models.LaxinTagDataFixDao.BatchCreate(ctx, data, tx)
}

func (d dataImpl) BatchDeleteFixData(ctx *gin.Context, ids []int64, tx *gorm.DB) error {
	return models.LaxinTagDataFixDao.BatchDelete(ctx, ids, tx)
}

func (d dataImpl) InitFixDataFromContent(ctx *gin.Context, content [][]string, task *models.LaxinTagDataFixTask) ([]*models.LaxinTagDataFix, map[int][]string, error) {
	result := make([]*models.LaxinTagDataFix, 0)
	validateErr := make(map[int][]string, 0)
	uniqueMap := make(map[string]struct{})

	var assistantPhone, email []string
	var courseIDs []int64

	//[]string{"课程ID", "资产手机号", "真人邮箱前缀", "一级服务团队", "二级服务团队", "处理结果-此处无需填写"}
	for i, rows := range content {
		courseID, err := strconv.Atoi(rows[0])
		if err != nil || courseID <= 0 {
			validateErr[i] = append(validateErr[i], components.ValidateErrCourseId)
		}

		firstLine, errLine1 := models.GetLaxinTagDataFixFirstLine(rows[3])
		secondLine, errLine2 := models.GetLaxinTagDataFixSecondLine(rows[4])
		if errLine1 != nil || errLine2 != nil {
			validateErr[i] = append(validateErr[i], components.ValidateErrFirstOrSecondLine)
		}

		// 一批数据里有无重复数据
		if _, ok := uniqueMap[strconv.Itoa(courseID)+rows[1]+rows[2]]; ok {
			validateErr[i] = append(validateErr[i], components.ValidateErrDuplicateData)
		}

		uniqueMap[strconv.Itoa(courseID)+rows[1]+rows[2]] = struct{}{}

		result = append(result, &models.LaxinTagDataFix{
			CourseID:          courseID,
			AssistantPhone:    rows[1],
			AssistantUID:      0,
			PersonEmailPrefix: rows[2],
			PersonUID:         0,
			FirstLineTeam:     firstLine,
			SecondLineTeam:    secondLine,
			OperatorUID:       task.OperatorUid,
			Operator:          task.OperatorName,
			IsDeleted:         false,
			DeletedID:         0,
			CreateTime:        time.Now().Unix(),
			UpdateTime:        time.Now().Unix(),
		})

		assistantPhone = append(assistantPhone, rows[1])
		email = append(email, rows[2])
		courseIDs = append(courseIDs, int64(courseID))
	}

	// 资产信息
	deviceInfo, err := mesh.GetDeviceInfoListByDevicePhones(ctx, utils.SliceUniq(assistantPhone))
	if err != nil {
		return nil, nil, err
	}

	// 真人信息
	staffInfo, err := mesh.GetStaffInfoListByEmails(ctx, utils.SliceUniq(email))
	if err != nil {
		return nil, nil, err
	}

	// 课程 id 校验
	courseInfo, err := dal.GetCourseLessonInfoByCourseIds(ctx, utils.ArrayUnique(courseIDs), []string{"courseId"}, nil)
	if err != nil {
		return nil, nil, err
	}

	// 资产-真人-匹配/课程 id 校验
	for i, data := range result {
		info, ok1 := deviceInfo[data.AssistantPhone]
		staff, ok2 := staffInfo[data.PersonEmailPrefix+components.Emailsuffix]
		if !ok1 || !ok2 {
			validateErr[i] = append(validateErr[i], components.ValidateErrAssistantOrPersonNotExist)
			continue
		}

		if !utils.InArrayInt64(info.DeviceId, staff.DeviceUidList) {
			validateErr[i] = append(validateErr[i], components.ValidateErrAssistantPersonNoMatch)
			continue
		}

		// 课程信息
		if _, ok := courseInfo[int64(data.CourseID)]; !ok {
			validateErr[i] = append(validateErr[i], components.ValidateErrCourseId)
			continue
		}

		result[i].AssistantUID = info.DeviceId
		result[i].PersonUID = staff.StaffUid
	}

	return result, validateErr, nil
}
