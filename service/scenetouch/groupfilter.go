package scenetouch

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/components"
	"assistantdeskgo/data/scenetpl"
	"assistantdeskgo/dto/scenetouch"
	"math"
	"sort"
	"strings"

	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func GroupFilterGroupBind(ctx *gin.Context, req scenetouch.GroupFilterGroupBindReq) (rsp scenetouch.GroupFilterGroupBindRsp, err error) {
	switch req.SendType {
	case touchmis.SendTypePersonalLearnFeedback:
		return GroupFilterGroupBindForPersonalLearnFeedback(ctx, req)
	default:
		err = components.ErrorParamInvalid
		return
	}
}

func GroupFilterGroupBindForPersonalLearnFeedback(ctx *gin.Context, req scenetouch.GroupFilterGroupBindReq) (rsp scenetouch.GroupFilterGroupBindRsp, err error) {
	// ==================== 分组配置 ====================
	type GroupConf struct {
		GroupTitle               string   `json:"groupTitle"`
		LabelList                []string `json:"labelList"`
		StageTestScoreRange      string   `json:"stageTestScoreRange"`
		InclassRightPercentRange string   `json:"inclassRightRateRange"`
		VariableNames            []string `json:"variableNames"`
	}

	groupConf := []GroupConf{
		{
			GroupTitle:               "分组1",
			LabelList:                []string{"阶段测≥85分", "课中正确率≥80%"},
			StageTestScoreRange:      "[85,)",
			InclassRightPercentRange: "[80,)",
			VariableNames:            []string{"阶段测优秀知识目标", "优点表扬", "课中知识目标数量", "课中优秀知识目标"},
		},
		{
			GroupTitle:               "分组2",
			LabelList:                []string{"阶段测≥85分", "50%≤课中正确率<80%"},
			StageTestScoreRange:      "[85,)",
			InclassRightPercentRange: "[50,80)",
			VariableNames:            []string{"阶段测优秀知识目标", "优点表扬", "课中知识目标数量", "课中良好知识目标"},
		},
		{
			GroupTitle:               "分组3",
			LabelList:                []string{"阶段测≥85分", "课中正确率<50%"},
			StageTestScoreRange:      "[85,)",
			InclassRightPercentRange: "[0,50)",
			VariableNames:            []string{"阶段测优秀知识目标", "优点表扬"},
		},
		{
			GroupTitle:               "分组4",
			LabelList:                []string{"75分≤阶段测<85分", "课中正确率≥80%"},
			StageTestScoreRange:      "[75,85)",
			InclassRightPercentRange: "[80,)",
			VariableNames:            []string{"阶段测优秀知识目标", "阶段测薄弱知识目标", "优点表扬", "课中知识目标数量", "课中优秀知识目标"},
		},
		{
			GroupTitle:               "分组5",
			LabelList:                []string{"75分≤阶段测<85分", "50%≤课中正确率<80%"},
			StageTestScoreRange:      "[75,85)",
			InclassRightPercentRange: "[50,80)",
			VariableNames:            []string{"阶段测优秀知识目标", "阶段测薄弱知识目标", "优点表扬", "课中知识目标数量", "课中良好知识目标"},
		},
		{
			GroupTitle:               "分组6",
			LabelList:                []string{"75分≤阶段测<85分", "课中正确率<50%"},
			StageTestScoreRange:      "[75,85)",
			InclassRightPercentRange: "[0,50)",
			VariableNames:            []string{"阶段测优秀知识目标", "阶段测薄弱知识目标", "优点表扬"},
		},
		{
			GroupTitle:               "分组7",
			LabelList:                []string{"60分≤阶段测<75分", "课中正确率≥80%"},
			StageTestScoreRange:      "[60,75)",
			InclassRightPercentRange: "[80,)",
			VariableNames:            []string{"阶段测优秀知识目标", "阶段测薄弱知识目标", "优点表扬", "课中知识目标数量", "课中优秀知识目标"},
		},
		{
			GroupTitle:               "分组8",
			LabelList:                []string{"60分≤阶段测<75分", "50%≤课中正确率<80%"},
			StageTestScoreRange:      "[60,75)",
			InclassRightPercentRange: "[50,80)",
			VariableNames:            []string{"阶段测优秀知识目标", "阶段测薄弱知识目标", "优点表扬", "课中知识目标数量", "课中良好知识目标"},
		},
		{
			GroupTitle:               "分组9",
			LabelList:                []string{"60分≤阶段测<75分", "课中正确率<50%"},
			StageTestScoreRange:      "[60,75)",
			InclassRightPercentRange: "[0,50)",
			VariableNames:            []string{"阶段测优秀知识目标", "阶段测薄弱知识目标", "优点表扬"},
		},
		{
			GroupTitle:               "分组10",
			LabelList:                []string{"阶段测<60分", "课中正确率≥80%"},
			StageTestScoreRange:      "(0,60)",
			InclassRightPercentRange: "[80,)",
			VariableNames:            []string{"阶段测优秀知识目标", "优点表扬", "课中知识目标数量", "课中优秀知识目标"},
		},
		{
			GroupTitle:               "分组11",
			LabelList:                []string{"阶段测<60分", "50%≤课中正确率<80%"},
			StageTestScoreRange:      "(0,60)",
			InclassRightPercentRange: "[50,80)",
			VariableNames:            []string{"阶段测优秀知识目标", "优点表扬", "课中知识目标数量", "课中良好知识目标"},
		},
		{
			GroupTitle:               "分组12",
			LabelList:                []string{"阶段测<60分", "课中正确率<50%"},
			StageTestScoreRange:      "(0,60)",
			InclassRightPercentRange: "[0,50)",
			VariableNames:            []string{"阶段测优秀知识目标", "优点表扬"},
		},
	}

	// ==================== 参数检查 ====================
	if req.CourseId == 0 || req.AssistantUid == 0 || req.SceneContext == "" {
		return rsp, components.ErrorParamInvalid
	}

	parsedSc, err := parseSceneContext(ctx, int(req.SendType), req.SceneContext)
	if err != nil {
		zlog.Infof(ctx, "parseSceneContextForPersonalFeedback error! sceneContext:%+v, err:%+v", req.SceneContext, err)
		return rsp, err
	}
	sceneContext := parsedSc.PersonalFeedbackContext
	if sceneContext.ExamId == 0 || sceneContext.ExamLessonId == 0 || len(sceneContext.LessonIds) == 0 {
		return rsp, components.ErrorParamInvalid
	}

	// ==================== 数据获取 ====================
	luActionReq := dataproxy.LuCourseLessonIdsStudentUidsAssistantUidReq{ // idl_assistant_lesson_student_action
		CourseId:     req.CourseId,
		LessonIds:    fwyyutils.JoinArrayInt64ToString([]int64{sceneContext.ExamLessonId}, ","),
		StudentUids:  fwyyutils.JoinArrayInt64ToString(req.ArrBindWxStudentUids, ","),
		AssistantUid: req.AssistantUid,
		Fields:       strings.Join([]string{"course_id", "lesson_id", "student_uid", "assistant_uid", "trade_status", "exam_answer"}, ","),
	}
	luActionRsp := dataproxy.LessonStudentActionRsp{}
	luActionRsp, err = dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid(ctx, luActionReq)
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid error! req:%+v, err:%+v", luActionReq, err)
		return rsp, err
	}

	commonLuRsp := dataproxy.CommonLuRsp{}
	lessonIdChunks := fwyyutils.ChunkArrayInt64(sceneContext.LessonIds, 10)
	for _, lessonIds := range lessonIdChunks {
		commonLuReq := dataproxy.LuLessonsStudentsReq{ // dataware_idl_common_lesson_student
			StudentUids: fwyyutils.JoinArrayInt64ToString(req.ArrBindWxStudentUids, ","),
			LessonIds:   fwyyutils.JoinArrayInt64ToString(lessonIds, ","),
			Fields:      strings.Join([]string{"course_id", "lesson_id", "student_uid", "inclass_participate_cnt", "playback_participate_cnt", "inclass_right_cnt", "playback_right_cnt"}, ","),
		}

		commonLuRspSub, err := dataproxy.GetCommonListByLessonsStudents(ctx, commonLuReq)
		if err != nil {
			zlog.Infof(ctx, "dataproxy.GetCommonListByLessonsStudents error! req:%+v, err:%+v", commonLuReq, err)
			return rsp, err
		}
		commonLuRsp.Total += commonLuRspSub.Total
		commonLuRsp.List = append(commonLuRsp.List, commonLuRspSub.List...)
	}

	// ==================== 指标计算 ====================
	studentStageTestScore := make(map[int64]float64)
	for _, lu := range luActionRsp.List {
		if lu.ExamAnswer.Exam9 != nil && lu.ExamAnswer.Exam9.AnswerScore != nil && *lu.ExamAnswer.Exam9.AnswerScore != "" {
			studentStageTestScore[lu.StudentUid] = cast.ToFloat64(*lu.ExamAnswer.Exam9.AnswerScore) / 10
		} else {
			studentStageTestScore[lu.StudentUid] = 0
		}
	}

	studentInclassRightCnt := make(map[int64]int64)
	studentInclassParticipateCnt := make(map[int64]int64)
	studentInclassRightPercent := make(map[int64]float64)
	for _, lu := range commonLuRsp.List {
		studentInclassRightCnt[lu.StudentUid] += lu.InclassRightCnt + lu.PlaybackRightCnt
		studentInclassParticipateCnt[lu.StudentUid] += lu.InclassParticipateCnt + lu.PlaybackParticipateCnt
	}
	for studentUid, inclassRightCnt := range studentInclassRightCnt {
		if studentInclassParticipateCnt[studentUid] == 0 {
			studentInclassRightPercent[studentUid] = 0
			continue
		}
		studentInclassRightPercent[studentUid] = 100 * cast.ToFloat64(inclassRightCnt) / cast.ToFloat64(studentInclassParticipateCnt[studentUid])
	}
	zlog.Debugf(ctx, "GroupFilterGroupBindForPersonalLearnFeedback studentStageTestScore:%+v, studentInclassRightPercent:%+v", studentStageTestScore, studentInclassRightPercent)

	// ==================== 分组计算 ====================
	rsp.ResultList = make([]scenetouch.GroupFilterGroupBindItem, len(groupConf))
	assignedStudents := make(map[int64]bool)

	for i, conf := range groupConf {
		rsp.ResultList[i] = scenetouch.GroupFilterGroupBindItem{
			Title:          conf.GroupTitle,
			LabelList:      conf.LabelList,
			StudentUidList: make([]int64, 0),
			VariableNames:  conf.VariableNames,
		}

		tplList := scenetpl.FeedbackGroupTpl[conf.GroupTitle]
		if len(tplList) != 4 {
			zlog.Infof(ctx, "tplList len error! tplList:%+v", tplList)
			return rsp, components.ErrorSystemError
		}
		tpl := make([]string, 0)
		if tplList[0] != "" {
			tpl = append(tpl, scenetpl.StageTestTpl[tplList[0]]...)
		}
		if tplList[1] != "" {
			if len(tpl) > 0 {
				tpl = append(tpl, "\n")
			}
			tpl = append(tpl, scenetpl.DuringCourseTpl[tplList[1]]...)
		}
		if tplList[2] != "" {
			if len(tpl) > 0 {
				tpl = append(tpl, "\n")
			}
			tpl = append(tpl, scenetpl.InClassTpl[tplList[2]]...)
		}
		if tplList[3] != "" {
			if len(tpl) > 0 {
				tpl = append(tpl, "\n")
			}
			tpl = append(tpl, scenetpl.LearnSuggestTpl[tplList[3]]...)
		}
		rsp.ResultList[i].DefaultTpl = tpl
	}

	for _, studentUid := range req.ArrBindWxStudentUids {
		stageTestScore, ok1 := studentStageTestScore[studentUid]
		inclassRightPercent, ok2 := studentInclassRightPercent[studentUid]
		if !ok1 || !ok2 {
			continue
		}

		for i, conf := range groupConf {
			if assignedStudents[studentUid] {
				break
			}
			stageTestMatch := isInRange(stageTestScore, conf.StageTestScoreRange)
			inclassRightMatch := isInRange(inclassRightPercent, conf.InclassRightPercentRange)
			if stageTestMatch && inclassRightMatch {
				rsp.ResultList[i].StudentUidList = append(rsp.ResultList[i].StudentUidList, studentUid)
				assignedStudents[studentUid] = true
			}
		}
	}

	for i := range rsp.ResultList {
		sort.Slice(rsp.ResultList[i].StudentUidList, func(j, k int) bool {
			return rsp.ResultList[i].StudentUidList[j] < rsp.ResultList[i].StudentUidList[k]
		})
	}

	return
}

func parseRange(rangeStr string) (min, max float64, err error) {
	if len(rangeStr) < 3 {
		return 0, 0, components.ErrorParamInvalid
	}
	rangeStr = rangeStr[1 : len(rangeStr)-1]
	parts := strings.Split(rangeStr, ",")
	if len(parts) != 2 {
		return 0, 0, components.ErrorParamInvalid
	}
	if parts[0] == "" {
		min = 0
	} else {
		min, err = cast.ToFloat64E(parts[0])
		if err != nil {
			return 0, 0, err
		}
	}
	if parts[1] == "" {
		max = math.MaxFloat64
	} else {
		max, err = cast.ToFloat64E(parts[1])
		if err != nil {
			return 0, 0, err
		}
	}
	return min, max, nil
}

func isInRange(value float64, rangeStr string) bool {
	_min, _max, err := parseRange(rangeStr)
	if err != nil {
		return false
	}
	leftInclusive := rangeStr[0] == '['
	rightInclusive := rangeStr[len(rangeStr)-1] == ']'
	if leftInclusive && rightInclusive {
		return value >= _min && value <= _max
	} else if leftInclusive {
		return value >= _min && value < _max
	} else if rightInclusive {
		return value > _min && value <= _max
	} else {
		return value > _min && value < _max
	}
}
