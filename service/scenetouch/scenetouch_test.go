package scenetouch

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"math"
	"math/rand"
	"strconv"
	"testing"
)

func TestGetTaskResultsForPersonalFeedback(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/fudao_bzr/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	helpers.InitApiClient()
	defer helpers.Release()

	p := GetTaskResultsForPersonalFeedbackPrams{
		AssistantUid: 4310483295,
		CourseId:     547966,
		GroupName:    "分组7",
		SceneContext: "[{\"key\":\"exam9\",\"value\":{\"label\":\"阶段测1\",\"value\":200559965,\"canSelect\":true,\"defaultSelect\":false,\"extra\":{\"lessonId\":534532,\"lessonStartTime\":1746844237,\"lessonStopTime\":1746846048}}},{\"key\":\"lesson\",\"value\":[{\"label\":\"沉海洄天溯，绒雪凝屏息\",\"value\":534531,\"canSelect\":true,\"defaultSelect\":false,\"extra\":{\"lessonType\":1,\"mode\":0,\"startTime\":1746757837,\"stopTime\":1746759648}}]}]",
		SceneType:    6005,
		StudentUids:  []int64{2135369107},
	}
	ret, _ := GetTaskResultsForPersonalFeedback(ctx, p)
	retJson, _ := jsoniter.MarshalIndent(ret, "", "  ")
	fmt.Printf("%+v\n", string(retJson))
}

// TestEsDataGen 生成es测试数据  generated by deepseek-r1
func TestEsDataGen(t *testing.T) {
	var (
		randSeed             int64 = 1000
		assistantUID               = 4310483295
		courseID                   = 547966
		studentUIDList             = []int64{2135369107, 2135414071}
		lessonIDList               = []int{534531, 534532, 534533}
		pointIDList                = []int{72, 73, 75, 76, 78, 79, 81, 82, 84, 85, 87, 88, 90} // 知识目标
		exam9LessonList            = [][2]int{{200559965, 534532}, {200559973, 534533}}        // 阶段测
		exam7LessonList            = [][2]int{}                                                // 巩固练习
		participateRateRange       = []float64{0.6, 1.0}
		rightRateRange             = []float64{0.5, 1.0}
	)

	_json := jsoniter.ConfigCompatibleWithStandardLibrary
	datawareIDLCommonLessonStudent := func() {
		fmt.Println("POST /dataware_idl_common_lesson_student/_bulk?filter_path=items.*.error")
		for _, studentUID := range studentUIDList {
			for _, lessonID := range lessonIDList {
				isInclass30 := rand.Intn(2)
				isInclassFinish := rand.Intn(2)
				inclassQuestionCnt := rand.Intn(16) + 5 // 5-20

				// Calculate participate counts
				partMin := int(math.Round(participateRateRange[0] * float64(inclassQuestionCnt)))
				partMax := int(math.Round(participateRateRange[1] * float64(inclassQuestionCnt)))
				inclassPartCnt := rand.Intn(partMax-partMin+1) + partMin

				remaining := inclassQuestionCnt - inclassPartCnt
				playbackPartMin := int(math.Round(participateRateRange[0] * float64(remaining)))
				playbackPartMax := int(math.Round(participateRateRange[1] * float64(remaining)))
				playbackPartCnt := rand.Intn(playbackPartMax-playbackPartMin+1) + playbackPartMin

				// Calculate right counts
				rightMin := int(math.Round(rightRateRange[0] * float64(inclassPartCnt)))
				rightMax := int(math.Round(rightRateRange[1] * float64(inclassPartCnt)))
				inclassRightCnt := rand.Intn(rightMax-rightMin+1) + rightMin

				playbackRightMin := int(math.Round(rightRateRange[0] * float64(playbackPartCnt)))
				playbackRightMax := int(math.Round(rightRateRange[1] * float64(playbackPartCnt)))
				playbackRightCnt := rand.Intn(playbackRightMax-playbackRightMin+1) + playbackRightMin

				attendDuration := rand.Intn(3601)
				totalPlayback := rand.Intn(3601)

				// Build exam1
				exam1 := map[string]int{
					"is_submit":       1,
					"total_num":       inclassQuestionCnt,
					"participate_num": inclassPartCnt,
					"right_num":       inclassRightCnt,
				}
				exam1Bytes, _ := _json.Marshal(exam1)
				exam1Str := string(exam1Bytes)

				// Build exam7
				var exam7 map[string]interface{}
				for _, entry := range exam7LessonList {
					if entry[1] == lessonID {
						exam7 = map[string]interface{}{
							"is_submit": 1,
							"exam_id":   entry[0],
							"exam_type": 7,
						}
						break
					}
				}
				if exam7 == nil {
					exam7 = make(map[string]interface{})
				}
				exam7Bytes, _ := _json.Marshal(exam7)
				exam7Str := string(exam7Bytes)

				// Build data
				data := map[string]interface{}{
					"course_id":   courseID,
					"lesson_id":   lessonID,
					"student_uid": studentUID,
					"is_inclass_teacher_room_attend_30minute":     isInclass30,
					"is_inclass_teacher_room_attend_finish":       isInclassFinish,
					"playback_participate_cnt":                    playbackPartCnt,
					"inclass_participate_cnt":                     inclassPartCnt,
					"playback_right_cnt":                          playbackRightCnt,
					"inclass_right_cnt":                           inclassRightCnt,
					"inclass_question_cnt":                        inclassQuestionCnt,
					"inclass_teacher_room_attend_duration":        attendDuration,
					"inclass_teacher_room_total_playback_time_v1": totalPlayback,
					"exam1": exam1Str,
					"exam7": exam7Str,
				}

				fmt.Println(`{ "index": {} }`)
				dataBytes, _ := _json.Marshal(data)
				fmt.Println(string(dataBytes))
			}
		}
	}

	idlAssistantLessonStudentAction := func() {
		fmt.Println("POST /idl_assistant_lesson_student_action/_bulk?filter_path=items.*.error")
		for _, studentUID := range studentUIDList {
			for _, lessonID := range lessonIDList {
				playbackTime := rand.Intn(3601)
				isViewFinished := rand.Intn(2)
				hwCorrect := rand.Intn(16) + 5 // 5-20
				hwRight := int(math.Round(rightRateRange[0]*float64(hwCorrect))) + rand.Intn(int(math.Round((rightRateRange[1]-rightRateRange[0])*float64(hwCorrect)))+1)

				// Build exam9
				var exam9 map[string]string
				for _, entry := range exam9LessonList {
					if entry[1] == lessonID {
						total := rand.Intn(16) + 5
						partMin := int(math.Round(participateRateRange[0] * float64(total)))
						partMax := int(math.Round(participateRateRange[1] * float64(total)))
						participate := rand.Intn(partMax-partMin+1) + partMin
						rightMin := int(math.Round(rightRateRange[0] * float64(participate)))
						rightMax := int(math.Round(rightRateRange[1] * float64(participate)))
						right := rand.Intn(rightMax-rightMin+1) + rightMin
						score := rand.Intn(101) * 10

						exam9 = map[string]string{
							"answer_score":    strconv.Itoa(score),
							"participate_num": strconv.Itoa(participate),
							"right_num":       strconv.Itoa(right),
							"total_num":       strconv.Itoa(total),
							"exam_id":         strconv.Itoa(entry[0]),
						}
						break
					}
				}
				if exam9 == nil {
					exam9 = make(map[string]string)
				}

				data := map[string]interface{}{
					"course_id":                  courseID,
					"lesson_id":                  lessonID,
					"student_uid":                studentUID,
					"assistant_uid":              assistantUID,
					"trade_status":               1,
					"playback_time_in_7d":        playbackTime,
					"is_view_finished":           isViewFinished,
					"homework_first_right_cnt":   hwRight,
					"homework_first_correct_cnt": hwCorrect,
					"exam_answer": map[string]interface{}{
						"exam9": exam9,
					},
				}

				fmt.Println(`{ "index": {} }`)
				dataBytes, _ := _json.Marshal(data)
				fmt.Println(string(dataBytes))
			}
		}
	}

	idlLessonStudentPointAction := func() {
		fmt.Println("POST /idl_lesson_student_point_action/_bulk?filter_path=items.*.error")
		for _, studentUID := range studentUIDList {
			for _, lessonID := range lessonIDList {
				for _, pointID := range pointIDList {
					questionCnt := rand.Intn(16) + 5
					partMin := int(math.Round(participateRateRange[0] * float64(questionCnt)))
					partMax := int(math.Round(participateRateRange[1] * float64(questionCnt)))
					inclassPart := rand.Intn(partMax-partMin+1) + partMin

					remaining := questionCnt - inclassPart
					playbackPartMin := int(math.Round(participateRateRange[0] * float64(remaining)))
					playbackPartMax := int(math.Round(participateRateRange[1] * float64(remaining)))
					playbackPart := rand.Intn(playbackPartMax-playbackPartMin+1) + playbackPartMin

					rightMin := int(math.Round(rightRateRange[0] * float64(inclassPart)))
					rightMax := int(math.Round(rightRateRange[1] * float64(inclassPart)))
					inclassRight := rand.Intn(rightMax-rightMin+1) + rightMin

					playbackRightMin := int(math.Round(rightRateRange[0] * float64(playbackPart)))
					playbackRightMax := int(math.Round(rightRateRange[1] * float64(playbackPart)))
					playbackRight := rand.Intn(playbackRightMax-playbackRightMin+1) + playbackRightMin

					data := map[string]interface{}{
						"lesson_id":                lessonID,
						"student_uid":              studentUID,
						"point_id":                 pointID,
						"inclass_question_cnt":     questionCnt,
						"inclass_right_cnt":        inclassRight,
						"inclass_participate_cnt":  inclassPart,
						"playback_right_cnt":       playbackRight,
						"playback_participate_cnt": playbackPart,
					}

					fmt.Println(`{ "index": {} }`)
					dataBytes, _ := _json.Marshal(data)
					fmt.Println(string(dataBytes))
				}
			}
		}
	}

	idlLessonPointCommonAction := func() {
		fmt.Println("POST /idl_lesson_point_common_action/_bulk?filter_path=items.*.error")
		for _, lessonID := range lessonIDList {
			for _, pointID := range pointIDList {
				data := map[string]interface{}{
					"lesson_id":            lessonID,
					"point_id":             pointID,
					"inclass_question_cnt": rand.Intn(16) + 5,
				}
				fmt.Println(`{ "index": {} }`)
				dataBytes, _ := _json.Marshal(data)
				fmt.Println(string(dataBytes))
			}
		}
	}

	idlLessonStudentPointExamAction := func() {
		fmt.Println("POST /idl_lesson_student_point_exam_action/_bulk?filter_path=items.*.error")
		for _, studentUID := range studentUIDList {
			for _, entry := range exam9LessonList {
				examID, lessonID := entry[0], entry[1]
				for _, pointID := range pointIDList {
					submitCnt := rand.Intn(21)
					rightCnt := rand.Intn(submitCnt + 1)
					data := map[string]interface{}{
						"lesson_id":        lessonID,
						"student_uid":      studentUID,
						"point_id":         pointID,
						"exam_type":        9,
						"exam_id":          examID,
						"first_submit_cnt": submitCnt,
						"first_right_cnt":  rightCnt,
					}
					fmt.Println(`{ "index": {} }`)
					dataBytes, _ := _json.Marshal(data)
					fmt.Println(string(dataBytes))
				}
			}
		}
	}

	genData := func() {
		fmt.Println()
		datawareIDLCommonLessonStudent()
		fmt.Println()
		idlAssistantLessonStudentAction()
		fmt.Println()
		idlLessonStudentPointAction()
		fmt.Println()
		idlLessonPointCommonAction()
		fmt.Println()
		idlLessonStudentPointExamAction()
		fmt.Println()
	}

	genDeleteQuery := func() {
		query := map[string]interface{}{
			"query": map[string]interface{}{
				"bool": map[string]interface{}{
					"must": []map[string]interface{}{
						{
							"terms": map[string]interface{}{
								"lesson_id": lessonIDList,
							},
						},
					},
				},
			},
		}

		indexes := []string{
			"dataware_idl_common_lesson_student",
			"idl_assistant_lesson_student_action",
			"idl_lesson_student_point_action",
			"idl_lesson_point_common_action",
			"idl_lesson_student_point_exam_action",
		}

		for _, index := range indexes {
			fmt.Printf("POST /%s/_delete_by_query?filter_path=items.*.error\n", index)
			queryBytes, _ := _json.Marshal(query)
			fmt.Println(string(queryBytes))
		}
	}
	_, _ = genDeleteQuery, genData

	// ----------------
	rand.Seed(randSeed)
	fmt.Println()
	genDeleteQuery()
	genData()
}
