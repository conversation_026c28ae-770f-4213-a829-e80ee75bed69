package stuaimessage

import (
	"assistantdeskgo/models"
	"github.com/gin-gonic/gin"
)

type ListResult struct {
	Count int64 `json:"count"`
	List  []models.StuAiMessage
}

func List(ctx *gin.Context, uid, assistantUid int64, page, pageSize int) (result ListResult, err error) {
	model := &models.StuAiMessage{
		StudentUid:   uid,
		AssistantUid: assistantUid,
	}
	list, err := model.List(ctx, uid, page, pageSize)
	if err != nil {
		return
	}
	count, err := model.Count(ctx, uid)
	if err != nil {
		return
	}
	result.Count = count
	result.List = list
	return
}
