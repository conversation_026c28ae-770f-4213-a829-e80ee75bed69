package genke

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtogenke"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	utils2 "assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
	"time"
)

func WxCallRecord(ctx *gin.Context, studentUid int64) (response []dtogenke.WxCallRecordRsp, err error) {

	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}
	callList, err := models.WxCallRecordRef.ListByToUidAndFromUidAndTimeRange(ctx, studentUid, loginDeviceInfo.DeviceUid, time.Now().Unix()-defines.SECOND_OF_7DAY)
	if err != nil {
		zlog.Warnf(ctx, "WxCallRecord QueryDB fail,studentUid=%v,err=%v", studentUid, err)
		return
	}
	if len(callList) == 0 {
		return
	}

	relationList, err := kunpeng.GetWxInfoByUid(ctx, GetUidByWxIdReq(loginDeviceInfo.DeviceUid, studentUid))
	if err != nil {
		return
	}
	relationMap := map[string]kunpeng.GetWxInfoByUidRsp{}
	for _, relation := range relationList {
		relationMap[utils.ToString(relation.RemoteId)] = relation
	}

	studentMap, err := dau.GetStudents(ctx, []int64{studentUid}, []string{"studentUid", "studentName", "phone"})
	if err != nil {
		return
	}
	studentInfo := studentMap[studentUid]
	if int64(studentInfo.StudentUid) != studentUid {
		err = errors.New("学生未找到")
		return
	}
	for _, callInfo := range callList {
		relation := relationMap[callInfo.StudentRemoteId]
		callResult := "接通"
		callResultType := 1
		if callInfo.CallResult != 1 {
			callResult = "未接通"
			callResultType = 0
		}

		response = append(response, dtogenke.WxCallRecordRsp{
			CallId:         callInfo.CallId,
			StudentUid:     studentUid,
			StudentName:    studentInfo.StudentName,
			Remark:         relation.Remark,
			WeixinName:     relation.WeixinName,
			WeixinNickName: relation.WeixinNickName,
			StartTime:      utils2.TimeStampFormat(callInfo.StartTime),
			SortTime:       callInfo.StartTime,
			Duration:       callInfo.Duration,
			CallResult:     callResult,
			CallResultType: int64(callResultType),
			MsgType:        callInfo.MsgType,
			DeviceUid:      loginDeviceInfo.DeviceUid,
		})

	}
	return
}

func GetUidByWxIdReq(deviceUid int64, studentUid int64) kunpeng.GetWxInfoByUidReq {
	return kunpeng.GetWxInfoByUidReq{
		StaffUid: deviceUid,
		Uids:     []int64{studentUid},
		AppId:    2,
	}
}
