package csv

import (
	"assistantdeskgo/dto/dtocsv"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"time"
)

func List(ctx *gin.Context, param dtocsv.CsvTaskListParam) (*dtocsv.CsvTaskListRet, error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	conds := map[string]interface{}{
		"operator_uid": userInfo.UserId,
	}
	if param.Type > 0 {
		conds["type"] = param.Type
	}
	page, size := 1, 10
	if param.Page > 0 {
		page = param.Page
	}
	if param.Size > 0 && param.Size < 100 {
		size = param.Size
	}
	taskList, total, err := models.ExcelTaskRef.GetList(ctx, conds, page, size)
	if err != nil {
		return nil, err
	}
	if len(taskList) == 0 {
		return &dtocsv.CsvTaskListRet{
			Total: total,
			List:  nil,
		}, nil
	}
	taskIds := make([]int, 0)
	for _, item := range taskList {
		taskIds = append(taskIds, item.ID)
	}

	list := make([]*dtocsv.ExcelTaskItem, 0)
	for _, _item := range taskList {
		_status := _item.Status
		_retFile := ""
		if (_status == models.ExcelTaskStatusSuccess || _status == models.ExcelTaskStatusPartSuccess) && (len(_item.RetFile) > 0) {
			_remoteFile, err := helpers.BaiduBucket2.GetUrlByFileName(ctx, _item.RetFile, 24*60*time.Second)
			if err == nil {
				_retFile = _remoteFile
			}
		} else if _status == models.ExcelTaskStatusFail {
			_remoteFile, err := helpers.BaiduBucket2.GetUrlByFileName(ctx, _item.FilePath, 24*60*time.Second)
			if err == nil {
				_retFile = _remoteFile
			}
		}
		_status = models.ExcelTaskStatusFEDisplayMap[_item.Status]
		_statusStr := models.ExcelTaskStatusFEDisplayStrMap[_item.Status]
		extraInfo := &models.SailorScoreExtraInfo{}
		json.Unmarshal([]byte(_item.ExtraInfo), extraInfo)
		if extraInfo.FailCount == 0 {
			_retFile = ""
		}
		_retItem := &dtocsv.ExcelTaskItem{
			Id:            _item.ID,
			OperatorUname: _item.OperatorUname,
			OperatorTime:  utils.ChangeStampToYmdHm(_item.CreateTime),
			FileName:      _item.FileName,
			Status:        _status,
			StatusStr:     _statusStr,
			RetFile:       _retFile,
			SuccessCount:  extraInfo.SuccessCount,
			FailCount:     extraInfo.FailCount,
		}
		list = append(list, _retItem)
	}
	return &dtocsv.CsvTaskListRet{
		Total: total,
		List:  list,
	}, nil
}
