package impl

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

type SailorScore struct {
}

func (s *SailorScore) GetTitle(ctx *gin.Context) []string {
	return []string{"教师ID", "教资分", "司龄分", "学历分", "用户表扬分", "评优分", "等级分", "其他分", "辅导费等级", "用户满意度", "品质惩处", "业绩达成", "功底考试", "失败原因"}
}

func (s *SailorScore) GetTemplateFileName(ctx *gin.Context) string {
	return "水手分导入结果.csv"
}

func (s *SailorScore) GetCosFileName(ctx *gin.Context, taskInfo *models.ExcelTask) string {
	return "excelsailorscore-" + strconv.Itoa(taskInfo.ID)
}

func (s *SailorScore) Exec(ctx *gin.Context, content [][]string, taskInfo *models.ExcelTask) (bool, [][]string, error) {
	defer func() {
		if err := recover(); err != nil {
			models.ExcelTaskRef.UpdateTaskFail(ctx, taskInfo.ID, err.(error).Error())
			zlog.Errorf(ctx, "doExcelTask Exec panic err : %+v", err)
		}
	}()
	allSuccess := true
	if len(content) < 8 {
		return allSuccess, content, nil
	}
	successCount := 0
	failCount := 0
	processedStaffUid := make(map[int64]int64)

	// 不兼容旧文件
	if len(content[7]) == 14 {
		for i, temp := range content {
			if i >= 7 {
				temp[13] = "不兼容历史模版文件，请下载含有【数据类型】字段的新模版"
			}
		}
		allSuccess = false
		if len(content)-7-successCount >= 0 {
			failCount = len(content) - 7 - successCount
		}
		sailorScoreExtraInfo := models.SailorScoreExtraInfo{
			SuccessCount: int64(successCount),
			FailCount:    int64(failCount),
		}
		bytes, _ := json.Marshal(sailorScoreExtraInfo)
		models.ExcelTaskRef.UpdateTaskExtraInfo(ctx, taskInfo.ID, string(bytes))
		return allSuccess, content, nil
	}

	// csv文件第7行开始才有数据
	errorIndex := 14
	for i := 7; i < len(content); i += 100 {
		end := i + 100
		if end > len(content) {
			end = len(content)
		}
		groupOf100 := content[i:end]
		staffUidList := make([]int64, 0, len(content))
		staffUid2ImportSailorScore := make(map[int64]models.SailorScore)
		staffUid2ContentRow := make(map[int64][][]string)
		for index, row := range groupOf100 {
			sailorScore, err := s.getRowData(ctx, row)
			if err != nil {
				allSuccess = false
				row = append(row, err.Error())
				content[i+index] = row
				zlog.Infof(ctx, "SailorScore exec, getRowdata error, data:%s, row:%d, err:%s, return", row, i, err)
				continue
			}
			staffUidList = append(staffUidList, sailorScore.StaffUid)
			staffUid2ImportSailorScore[sailorScore.StaffUid] = sailorScore
			if _, ok := staffUid2ContentRow[sailorScore.StaffUid]; !ok {
				staffUid2ContentRow[sailorScore.StaffUid] = make([][]string, 0)
			}
			staffUid2ContentRow[sailorScore.StaffUid] = append(staffUid2ContentRow[sailorScore.StaffUid], row) // 同一个uid可能存在多条记录
		}
		staffUidList = utils.ArrayUnique(staffUidList)
		staffUid2Info, err := mesh.GetStaffInfoList(ctx, staffUidList)
		/*staffUid2HrPost := make(map[int64]int64)
		if len(staffUidList) != 0 {
			for _, uid := range staffUidList {
				var userList userprofile.GetUserListRsp
				params := &dtosailor.ListReq{
					StaffUid: strconv.Itoa(int(uid)),
				}
				userList, err = userprofile.GetUserList(ctx, params)
				if err != nil || userList.List == nil || len(userList.List) == 0 {
					staffUid2HrPost[uid] = -1
				} else {
					staffUid2HrPost[uid] = userList.List[0].HrPost
				}
			}
		}*/
		if err != nil {
			zlog.Warnf(ctx, "SailorScore exec, 获取真人信息失败, err:%+v", err)
			for _, staffUid := range staffUidList {
				for _, temp := range staffUid2ContentRow[staffUid] {
					temp[errorIndex] = "获取真人信息失败"
				}
			}
		} else {
			validStaffUids := make([]int64, 0, len(staffUidList))
			for _, staffUid := range staffUidList {
				/*hrPost, ok := staffUid2HrPost[staffUid]
				if !ok || hrPost == -1 {
					staffUid2ContentRow[staffUid][13] = "未查询到该uid人力岗位信息"
					continue
				} else if hrPost != defines.HrPostTutor {
					staffUid2ContentRow[staffUid][13] = "非辅导老师人力岗位"
					continue
				}*/
				for _, temp := range staffUid2ContentRow[staffUid] {

					info, ok := staffUid2Info[strconv.FormatInt(staffUid, 10)]
					if !ok {
						temp[errorIndex] = "未查询到该uid信息，该员工可能已离职已解绑"
						continue
					}
					if !defines.ContainPrimaryAndJunior(info.Grade) {
						temp[errorIndex] = "非小学或初中老师"
						continue
					}
					if !defines.ContainActiveAndPreResignation(info.EmpStatus) {
						temp[errorIndex] = "非在职老师"
						continue
					}
					if !defines.ContainSubject(info.Subject) {
						temp[errorIndex] = "非学科列表内老师"
						continue
					}
					if info.UserAscription != defines.AscriptionAssistant {
						temp[errorIndex] = "非辅导老师人员归属"
						continue
					}
					_, ok = processedStaffUid[staffUid]
					if ok {
						temp[errorIndex] = "该教师ID已处理过，存在重复教师ID"
						continue
					} else {
						processedStaffUid[staffUid] = 1
					}
					validStaffUids = append(validStaffUids, staffUid)
				}
			}
			validStaffUids = utils.ArrayUnique(validStaffUids)
			if len(validStaffUids) == 0 {
				continue
			}
			sailorScoreList, _err := models.SailorScoreRef.GetByStaffUids(ctx, validStaffUids)
			if _err != nil {
				zlog.Warnf(ctx, "SailorScore GetByStaffUids error, err: %+v", _err)
				for _, validStaffUid := range validStaffUids {
					staffUid2ContentRow[validStaffUid][0][errorIndex] = "获取数据库数据异常，执行失败"
				}
			} else {
				staffUid2DbSailorScore := make(map[int64]models.SailorScore)
				for _, sailorScore := range sailorScoreList {
					staffUid2DbSailorScore[sailorScore.StaffUid] = sailorScore
				}
				updateSailorScore := make([]models.SailorScore, 0)
				insertSailorScore := make([]models.SailorScore, 0)
				insertSailorScoreLogs := make([]models.SailorScoreOperateLog, 0)
				for _, validStaffUid := range validStaffUids {
					sailorScore, ok := staffUid2DbSailorScore[validStaffUid]
					importSailorScore := staffUid2ImportSailorScore[validStaffUid]
					if ok {
						// 存在的话就更新
						sailorScore.TeachingQualification += importSailorScore.TeachingQualification
						sailorScore.WorkingAge += importSailorScore.WorkingAge
						sailorScore.Qualification += importSailorScore.Qualification
						sailorScore.UserPraise += importSailorScore.UserPraise
						sailorScore.Praise += importSailorScore.Praise
						sailorScore.Scale += importSailorScore.Scale
						sailorScore.Other += importSailorScore.Other
						sailorScore.CostLevel = importSailorScore.CostLevel
						sailorScore.Satisfaction += importSailorScore.Satisfaction
						sailorScore.Punishment += importSailorScore.Punishment
						sailorScore.Performance += importSailorScore.Performance
						sailorScore.Exam += importSailorScore.Exam
						sailorScore.DataType = importSailorScore.DataType
						sailorScore.UpdateTime = time.Now().Unix()
						sailorScore.UpdateUid = taskInfo.OperatorUid
						sailorScore.UpdateName = taskInfo.OperatorUname
						updateSailorScore = append(updateSailorScore, sailorScore)

						importSailorScore.UpdateTime = time.Now().Unix()
						importSailorScore.UpdateUid = taskInfo.OperatorUid
						importSailorScore.UpdateName = taskInfo.OperatorUname
						sailorScoreLog := models.SailorScoreOperateLog{
							StaffUid:              importSailorScore.StaffUid,
							TeachingQualification: importSailorScore.TeachingQualification,
							WorkingAge:            importSailorScore.WorkingAge,
							Qualification:         importSailorScore.Qualification,
							UserPraise:            importSailorScore.UserPraise,
							Praise:                importSailorScore.Praise,
							Scale:                 importSailorScore.Scale,
							Other:                 importSailorScore.Other,
							CostLevel:             importSailorScore.CostLevel,
							Satisfaction:          importSailorScore.Satisfaction,
							Punishment:            importSailorScore.Punishment,
							Performance:           importSailorScore.Performance,
							Exam:                  importSailorScore.Exam,
							DataType:              importSailorScore.DataType,
							OperateTime:           importSailorScore.UpdateTime,
							OperateUid:            importSailorScore.UpdateUid,
							OperateName:           importSailorScore.UpdateName,
						}
						insertSailorScoreLogs = append(insertSailorScoreLogs, sailorScoreLog)
					} else {
						// 不存在就插入
						importSailorScore.CreateTime = time.Now().Unix()
						importSailorScore.CreateUid = taskInfo.OperatorUid
						importSailorScore.CreateName = taskInfo.OperatorUname
						importSailorScore.UpdateTime = time.Now().Unix()
						importSailorScore.UpdateUid = taskInfo.OperatorUid
						importSailorScore.UpdateName = taskInfo.OperatorUname
						insertSailorScore = append(insertSailorScore, importSailorScore)
						sailorScoreLog := models.SailorScoreOperateLog{
							StaffUid:              importSailorScore.StaffUid,
							TeachingQualification: importSailorScore.TeachingQualification,
							WorkingAge:            importSailorScore.WorkingAge,
							Qualification:         importSailorScore.Qualification,
							UserPraise:            importSailorScore.UserPraise,
							Praise:                importSailorScore.Praise,
							Scale:                 importSailorScore.Scale,
							Other:                 importSailorScore.Other,
							CostLevel:             importSailorScore.CostLevel,
							Satisfaction:          importSailorScore.Satisfaction,
							Punishment:            importSailorScore.Punishment,
							Performance:           importSailorScore.Performance,
							Exam:                  importSailorScore.Exam,
							DataType:              importSailorScore.DataType,
							OperateTime:           importSailorScore.UpdateTime,
							OperateUid:            importSailorScore.UpdateUid,
							OperateName:           importSailorScore.UpdateName,
						}
						insertSailorScoreLogs = append(insertSailorScoreLogs, sailorScoreLog)
					}
				}
				tx := helpers.MysqlClientFuDao.WithContext(ctx).Begin()
				_err = models.SailorScoreRef.BatchUpdateWithTx(ctx, updateSailorScore, tx)
				if _err != nil {
					for _, validStaffUid := range validStaffUids {
						staffUid2ContentRow[validStaffUid][0][13] = "更新数据库SailorScore数据异常，执行失败"
					}
					tx.Rollback()
					continue
				}
				_err = models.SailorScoreRef.BatchInsertWithTx(ctx, insertSailorScore, tx)
				if _err != nil {
					for _, validStaffUid := range validStaffUids {
						staffUid2ContentRow[validStaffUid][0][13] = "插入数据库SailorScore数据异常，执行失败"
					}
					tx.Rollback()
					continue
				}
				_err = models.SailorScoreOperateLogRef.BatchInsertWithTx(ctx, insertSailorScoreLogs, tx)
				if _err != nil {
					for _, validStaffUid := range validStaffUids {
						staffUid2ContentRow[validStaffUid][0][13] = "插入数据库SailorScoreOperateLog数据异常，执行失败"
					}
					tx.Rollback()
					continue
				}
				successCount += len(insertSailorScoreLogs)
				tx.Commit()
			}
		}
	}
	// 理论上len(content)>7，然后总条数-成功数=失败数
	if len(content)-7-successCount >= 0 {
		failCount = len(content) - 7 - successCount
	}
	sailorScoreExtraInfo := models.SailorScoreExtraInfo{
		SuccessCount: int64(successCount),
		FailCount:    int64(failCount),
	}
	bytes, _ := json.Marshal(sailorScoreExtraInfo)
	models.ExcelTaskRef.UpdateTaskExtraInfo(ctx, taskInfo.ID, string(bytes))
	return allSuccess, content, nil
}

func (s *SailorScore) getRowData(ctx *gin.Context, row []string) (sailorScore models.SailorScore, err error) {
	if len(row) != 15 {
		err = components.ErrorExcelRowParamCountError
		return
	}

	//转码防止乱码
	for i := 0; i < len(row); i++ {
		row[i], err = fwyyutils.TransCsvCoding(ctx, row[i])
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(i+1, "字段编码错误")
			return
		}
	}

	var staffUid int64
	if row[0] != "" {
		staffUid, err = strconv.ParseInt(row[0], 10, 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(1, "真人uid格式不正确，请输入数字")
			return
		}
	}

	var teachingQualification float64
	if row[1] != "" {
		teachingQualification, err = strconv.ParseFloat(row[1], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(2, "教资分格式不正确，请输入数字")
			return
		}
	}

	var workingAge float64
	if row[2] != "" {
		workingAge, err = strconv.ParseFloat(row[2], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(3, "司龄分格式不正确，请输入数字")
			return
		}
	}

	var qualification float64
	if row[3] != "" {
		qualification, err = strconv.ParseFloat(row[3], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(4, "学历分格式不正确，请输入数字")
			return
		}
	}

	var userPraise float64
	if row[4] != "" {
		userPraise, err = strconv.ParseFloat(row[4], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(5, "用户表扬分格式不正确，请输入数字")
			return
		}
	}

	var praise float64
	if row[5] != "" {
		praise, err = strconv.ParseFloat(row[5], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(6, "评优分格式不正确，请输入数字")
			return
		}
	}

	var scale float64
	if row[6] != "" {
		scale, err = strconv.ParseFloat(row[6], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(7, "等级分格式不正确，请输入数字")
			return
		}
	}

	var other float64
	if row[7] != "" {
		other, err = strconv.ParseFloat(row[7], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(8, "其他分格式不正确，请输入数字")
			return
		}
	}

	costLevel := row[8]
	if len(costLevel) == 0 {
		err = components.ErrorExcelColError.Sprintf(9, "辅导费等级不能为空")
		return
	}

	var satisfaction float64
	if row[9] != "" {
		satisfaction, err = strconv.ParseFloat(row[9], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(10, "用户满意度格式不正确，请输入数字")
			return
		}
	}

	var punishment float64
	if row[10] != "" {
		punishment, err = strconv.ParseFloat(row[10], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(11, "品质惩处格式不正确，请输入数字")
			return
		}
	}

	var performance float64
	if row[11] != "" {
		performance, err = strconv.ParseFloat(row[11], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(12, "业绩达成格式不正确，请输入数字")
			return
		}
	}

	var exam float64
	if row[12] != "" {
		exam, err = strconv.ParseFloat(row[12], 64)
		if err != nil {
			err = components.ErrorExcelColError.Sprintf(13, "功底考试格式不正确，请输入数字")
			return
		}
	}

	//dataType限制30个字符
	dataType := fwyyutils.Substr(row[13], 0, 30)

	sailorScore = models.SailorScore{
		StaffUid:              staffUid,
		TeachingQualification: teachingQualification,
		WorkingAge:            workingAge,
		Qualification:         qualification,
		UserPraise:            userPraise,
		Praise:                praise,
		Scale:                 scale,
		Other:                 other,
		CostLevel:             costLevel,
		Satisfaction:          satisfaction,
		Punishment:            punishment,
		Performance:           performance,
		Exam:                  exam,
		DataType:              dataType,
	}
	return
}
