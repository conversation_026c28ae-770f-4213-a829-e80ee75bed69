package impl

import (
	"assistantdeskgo/components"
	"assistantdeskgo/models"
	"github.com/gin-gonic/gin"
)

type ExcelType struct {
	Value  int    `json:"value"`
	<PERSON><PERSON>  string `json:"label"`
	Remark string `json:"remark"`
}

const (
	ExcelTypeUploadSailorScore = 1
)

var TypeMap = map[int]ExcelType{
	ExcelTypeUploadSailorScore: {
		Value:  ExcelTypeUploadSailorScore,
		Lable:  "上传水手分",
		Remark: "",
	},
}

func TypeList(ctx *gin.Context) []ExcelType {
	ret := make([]ExcelType, 0)
	for _, v := range TypeMap {
		ret = append(ret, v)
	}
	return ret
}

type IExcelTask interface {
	GetTitle(ctx *gin.Context) []string
	GetTemplateFileName(ctx *gin.Context) string
	Exec(ctx *gin.Context, content [][]string, taskInfo *models.ExcelTask) (bool, [][]string, error)
	GetCosFileName(ctx *gin.Context, taskInfo *models.ExcelTask) string
}

func GetIExcelInstance(ctx *gin.Context, t int) (IExcelTask, error) {
	switch t {
	case ExcelTypeUploadSailorScore:
		return &SailorScore{}, nil
	default:
		return nil, components.ErrorParamInvalid
	}
}

func getRowColumnStr(ctx *gin.Context, row []string, column int) string {
	if len(row) > column {
		return row[column]
	}
	return ""
}
