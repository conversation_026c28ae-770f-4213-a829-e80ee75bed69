package impl

import (
	"fmt"
	"testing"

	"encoding/csv"
	"os"
)

func TestFile(t *testing.T) {
	s := &SailorScore{}

	dstFileName := "excelsailorscore.csv"
	// 打开CSV文件
	file, err := os.Open(dstFileName)
	if err != nil {
		t.Fatalf("Failed to open CSV file: %v", err)
	}
	defer file.Close()

	// 创建一个CSV Reader
	reader := csv.NewReader(file)

	// 读取CSV文件中的所有行数据
	records, err := reader.ReadAll()
	if err != nil {
		t.Fatalf("Failed to read CSV records: %v", err)
	}
	i := 0
	// 遍历每一行数据进行测试
	for _, record := range records {
		if i < 7 {
			i++
			continue
		}
		// 调用getRowData函数
		sailorScore, err := s.getRowData(nil, record)
		if err != nil {
			t.Errorf("Failed to parse row data: %v", err)
			continue
		}
		fmt.Printf("sailorScore.DataType:%+v \n", sailorScore.DataType)
	}
}
