package csv

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestHandleSailorScore(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/GolandProjects/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	err := ExcelTask(ctx)
	zlog.Infof(ctx, "err :%+v", err)
}
