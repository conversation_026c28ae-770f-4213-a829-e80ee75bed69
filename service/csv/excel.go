package csv

import (
	"assistantdeskgo/models"
	"assistantdeskgo/service/csv/impl"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"os"
)

func ExcelTask(ctx *gin.Context) error {
	initExcelTaskList, err := models.ExcelTaskRef.GetInitTaskList(ctx, 10)
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,GetInitTaskList,err:%s,return", err)
		return err
	}
	if len(initExcelTaskList) == 0 {
		zlog.Infof(ctx, "ExcelTask,GetInitTaskList,empty,return")
		return nil
	}
	zlog.Infof(ctx, "ExcelTask,GetInitTaskList,data:%s,start exec", initExcelTaskList)
	for _, excelTask := range initExcelTaskList {
		err = doExcelTask(ctx, excelTask)
		if err != nil {
			zlog.Warnf(ctx, "ExcelTask,doExcelTask,error, err:%s", err)
			return nil
		}
	}
	zlog.Infof(ctx, "ExcelTask,end exec")
	return nil
}

func DoExcelByTaskId(ctx *gin.Context, taskId int64) (rsp models.ExcelTask, err error) {
	taskInfo, err := models.ExcelTaskRef.GetById(ctx, taskId)
	if err != nil {
		return
	}
	if taskInfo.ID == 0 {
		return
	}
	zlog.Infof(ctx, "DoExcelByTaskId 原taskinfo：%+v", taskInfo)
	_, _err := models.ExcelTaskRef.UpdateTaskToInit(ctx, taskId)
	if _err != nil {
		zlog.Warnf(ctx, "DoExcelByTaskId, UpdateTaskToInit error, err:%s", err)
		return
	}
	taskInfo, err = models.ExcelTaskRef.GetById(ctx, taskId)
	if err != nil {
		return
	}
	err = doExcelTask(ctx, &taskInfo)
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,DoExcelByTaskId,error, err:%s", err)
		return
	}
	zlog.Infof(ctx, "DoExcelByTaskId success")
	rsp, err = models.ExcelTaskRef.GetById(ctx, taskId)
	return
}

func UpdateExtraInfo(ctx *gin.Context, taskId int64, extraInfo string) (err error) {
	err = models.ExcelTaskRef.UpdateExtraInfo(ctx, taskId, extraInfo)
	return
}

func doExcelTask(ctx *gin.Context, task *models.ExcelTask) error {
	defer func() {
		if err := recover(); err != nil {
			models.ExcelTaskRef.UpdateTaskFail(ctx, task.ID, err.(error).Error())
			zlog.Errorf(ctx, "doExcelTask panic err : %+v", err)
		}
	}()
	zlog.Infof(ctx, "ExcelTask,doExcelTask,task:%s,start exec", task)
	success, err := models.ExcelTaskRef.UpdateInitToExecuting(ctx, task.ID)
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,UpdateInitToExecuting,err:%s,return", err)
		_, _err := models.ExcelTaskRef.UpdateTaskFail(ctx, task.ID, err.Error())
		if _err != nil {
			zlog.Warnf(ctx, "ExcelTask,UpdateTaskFail,err:%s,return", _err)
		}
		return err
	}
	if !success {
		zlog.Infof(ctx, "ExcelTask,doExcelTask,UpdateInitToExecuting false,return")
		return nil
	}
	utfWithBOM, content, err := utils.GetFileContent(ctx, task.FilePath)
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,getFileContent,filePath:%s,err:%s,return", task.FilePath, err)
		_, _err := models.ExcelTaskRef.UpdateTaskFail(ctx, task.ID, err.Error())
		if _err != nil {
			zlog.Warnf(ctx, "ExcelTask,UpdateTaskFail,err:%s,return", _err)
		}
		return err
	}
	IExcel, err := impl.GetIExcelInstance(ctx, task.Type)
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,GetIExcelInstance,type:%d,err:%s,return", task.Type, err)
		_, _err := models.ExcelTaskRef.UpdateTaskFail(ctx, task.ID, err.Error())
		if _err != nil {
			zlog.Warnf(ctx, "ExcelTask,UpdateTaskFail,err:%s,return", _err)
		}
		return err
	}
	allSucc, csvData, err := IExcel.Exec(ctx, content, task)
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,Exec error, err:%s,return", err)
		_, _err := models.ExcelTaskRef.UpdateTaskFail(ctx, task.ID, err.Error())
		if _err != nil {
			zlog.Warnf(ctx, "ExcelTask,UpdateTaskFail,err:%s,return", _err)
		}
		return err
	}
	csvFileName := IExcel.GetCosFileName(ctx, task)
	csvFilePath := "./" + csvFileName + ".csv"
	if err := utils.WriteToCsv(csvFilePath, csvData, utfWithBOM); err != nil {
		zlog.Warnf(ctx, "ExcelTask,WriteToCsv error,err:%s,return", err)
		return nil
	}
	defer func() {
		os.Remove(csvFilePath)
	}()
	remoteFilePath, _, err := utils.UploadFile2Bos(ctx, csvFilePath, csvFileName, "csv", "export")
	if err != nil {
		zlog.Warnf(ctx, "ExcelTask,UploadFile2Bos error,err:%s,return", err)
		return nil
	}
	if err := models.ExcelTaskRef.UpdateRetFile(ctx, task.ID, remoteFilePath, allSucc); err != nil {
		zlog.Warnf(ctx, "ExcelTask,UpdateRetFile error,err:%s,return", err)
		return nil
	}
	return nil
}
