package assistant

import (
	"assistantdeskgo/dto/dtoassistant"
	"assistantdeskgo/models/notice"
	"github.com/gin-gonic/gin"
)

func AddAssistantNotice(ctx *gin.Context, req dtoassistant.AddAssistantNoticeReq) (resp dtoassistant.AddAssistantNoticeResp, err error) {
	an := notice.AssistantNotice{}
	err = notice.ApplyFields(&an, req.AssistantUid, req.PersonUid, req.Type, req.DetailType, req.SourceId, req.Content, req.ExtData)
	if err != nil {
		return
	}

	resp.Id, err = an.Add(ctx)
	if err != nil {
		return
	}
	return
}
