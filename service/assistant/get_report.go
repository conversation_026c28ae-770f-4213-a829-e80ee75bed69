package assistant

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/examcore"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/exercise"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/ratelimit"
	"strconv"
	"strings"
	"sync"
)

const defaultMaxGoroutineNum = 6

func getPronunciationReport(ctx *gin.Context, courseId int64, lessonId int64, studentUidList []int64) (result map[int64]string, err error) {
	if len(studentUidList) == 0 {
		return
	}
	goroutineNum := defaultMaxGoroutineNum
	limit := ratelimit.New(goroutineNum)
	wg := sync.WaitGroup{}
	studentChunk := utils.SplitInt64Array(studentUidList, 50)
	ch := make(chan map[int64]*exercise.PronunciationUrlInfo)
	for _, chunk := range studentChunk {
		wg.Add(1)
		go func(courseId int64, lessonId int64, chunk []int64) {
			defer wg.Done()
			limit.Take()
			reportUrlList, err := exercise.GetPronunciationUrl(ctx, exercise.GetPronunciationUrlReq{
				CourseId:    courseId,
				LessonId:    lessonId,
				StudentUids: chunk,
			})
			if err != nil {
				return
			}
			ch <- reportUrlList
		}(courseId, lessonId, chunk)
	}

	result = make(map[int64]string)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v.GeneratePicUrl
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()
	return
}

func getVoiceReport(ctx *gin.Context, courseId int64, lessonId int64, studentUidList []int64) (result map[int64]string, err error) {
	if len(studentUidList) == 0 {
		return
	}
	goroutineNum := defaultMaxGoroutineNum
	limit := ratelimit.New(goroutineNum)
	wg := sync.WaitGroup{}
	studentChunk := utils.SplitInt64Array(studentUidList, 50)
	ch := make(chan map[int64]*exercise.DubReportUrlInfo)
	for _, chunk := range studentChunk {
		wg.Add(1)
		go func(courseId int64, lessonId int64, chunk []int64) {
			defer wg.Done()
			limit.Take()
			reportUrlList, err := exercise.GetDubUrl(ctx, exercise.GetDubReportUrlReq{
				CourseId:    courseId,
				LessonId:    lessonId,
				StudentUids: chunk,
			})
			if err != nil {
				return
			}
			ch <- reportUrlList
		}(courseId, lessonId, chunk)
	}

	result = make(map[int64]string)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v.GeneratePicUrl
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()
	return
}

func GetBottomTestReportFromBridge(ctx *gin.Context, courseId int64, lessonId int64, studentUids []int64) (*muse.MuseBridgeRsp, error) {
	var req muse.MuseBridgeReq
	req.Function = "getBottomTestUrlList"
	req.LessonId = lessonId
	req.CourseId = courseId
	stuStrs := make([]string, 0)
	for _, stuId := range studentUids {
		stuStrs = append(stuStrs, strconv.FormatInt(stuId, 10))
	}
	req.StudentUids = strings.Join(stuStrs, ",")
	rsp, err := muse.MuseBridgeQuery(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetBottomTestReportFromBridge error! err:%+v", err)
		return nil, err
	}
	return rsp, nil
}

type GetBottomTestReportRet struct {
	ReportMap        map[string]examcore.GetReportUrlRspItem
	ExamIdSubjectMap map[string]int
}

func GetLessonReportFromBridge(ctx *gin.Context, courseId int64, lessonId int64, studentUids []int64) (*assistantdesk.DeskBridgeRsp, error) {
	var req assistantdesk.DeskBridgeReq
	req.Function = "getJxReportUrlData"
	req.LessonId = lessonId
	req.CourseId = courseId
	stuStrs := make([]string, 0)
	for _, stuId := range studentUids {
		stuStrs = append(stuStrs, strconv.FormatInt(stuId, 10))
	}
	req.StudentUids = strings.Join(stuStrs, ",")
	rsp, err := assistantdesk.DeskBridgeQuery(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonReportFromBridge error! err:%+v", err)
		return nil, err
	}
	return rsp, nil
}

func GetStageTestReportFromBridge(ctx *gin.Context, courseId int64, lessonId int64, studentUids []int64) (*assistantdesk.DeskBridgeRsp, error) {
	var req assistantdesk.DeskBridgeReq
	req.Function = "getStageTestList"
	req.LessonId = lessonId
	req.CourseId = courseId
	stuStrs := make([]string, 0)
	for _, stuId := range studentUids {
		stuStrs = append(stuStrs, strconv.FormatInt(stuId, 10))
	}
	req.StudentUids = strings.Join(stuStrs, ",")
	rsp, err := assistantdesk.DeskBridgeQuery(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetStageTestReportFromBridge error! err:%+v", err)
		return nil, err
	}
	return rsp, nil
}
