package assistant

import (
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/api/muse/message"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtoassistant"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/infra/pkg/navigator"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type MessageNode struct {
	MessageInfo     models.Message
	NextMessageInfo *MessageNode
}

func SendGroupMessage(ctx *gin.Context, req dtoassistant.SendGroupMessageReq) (rsp *dtoassistant.SendGroupMessageRsp, err error) {
	if req.AssistantUid == 0 || len(req.MessageList) == 0 {
		return nil, components.ErrorParamInvalid
	}

	if navigator.IsPressure(ctx) {
		req.AssistantUid = int64(navigator.RegressUid(uint64(req.AssistantUid)))
		req.StudentUid = int64(navigator.RegressUid(uint64(req.StudentUid)))
	}

	var config mercury.SendWxMessage
	if _err := mercury.GetConfigForJson(ctx, mercury.ConfigKeyForWxSend, mercury.DefaultExpireTime, &config); _err != nil {
		zlog.Warnf(ctx, "[SendGroupMessage] get mercury config failed, err: %+v", _err)
		return nil, _err
	}

	groupInfo := message.MessageGroup{
		SourceInfo: message.SourceInfo{},
		SubOrder:   message.NeedOrderSend,
		Receivers: message.Receiver{
			StudentUids: []int64{req.StudentUid},
			RemoteIds:   []string{req.RemoteId},
		},
	}
	subType := defines.SingleMessageToPerson
	sendType := defines.SendTypeSignalSop
	if req.ChatType == defines.ChatTypeForGroup { //是否是群聊
		//群聊的话remoteId必传
		if len(req.RemoteId) <= 0 {
			return nil, components.ErrorParamInvalid
		}
		subType = defines.SingleMessageToGroup
		sendType = defines.SendTypeSignalGroupSop
		receiver := message.Receiver{
			ChatId: req.RemoteId,
		}
		groupInfo.Receivers = receiver
	} else if req.StudentUid == 0 { //不是群聊的话学生ID必传
		return nil, components.ErrorParamInvalid
	}

	messageIds := make([]int64, 0, len(req.MessageList))
	messageMap := make(map[int64][]string)
	for _, messageItem := range req.MessageList {
		messageIds = append(messageIds, messageItem.MessageId)
		if len(messageItem.MessageContent) > 0 {
			messageMap[messageItem.MessageId] = messageItem.MessageContent
		}
	}

	groupMessageList, err := getGroupMessageList(ctx, req.MessageGroupId, messageIds)
	if err != nil {
		return nil, err
	}

	var (
		msgContent models.BaseMessage
	)
	messageInfoList := make([]message.MessageInfo, 0)
	for _, msgItem := range groupMessageList {
		if msgContent, err = msgItem.GetMessageContent(); err != nil {
			zlog.Warnf(ctx, "get_message_content_failed, messageInfo: %+v, err: %+v", msgContent, err)
			return nil, components.ErrorDbSelect.Wrap(err)
		}

		// 图片需要每张延迟1s
		if msgItem.MessageType == defines.MessageTypeWord {
			messageInfo := message.MessageInfo{
				DelayTime:  msgItem.IntervalTime,
				MsgType:    msgItem.MessageType,
				MsgContent: msgContent.GetMessageInfo(),
			}

			// 文本消息用户可以自定义
			if content, ok := messageMap[msgItem.ID]; ok && msgItem.MessageType == defines.MessageTypeWord {
				messageInfo.MsgContent = content
			}
			messageInfoList = append(messageInfoList, messageInfo)
		} else {
			itemList := msgContent.GetItemList()
			if itemList == nil {
				zlog.Warnf(ctx, "get_img_message_content_failed, messageInfo: %+v", msgContent)
				return nil, components.ErrorDbSelect
			}

			for index, item := range itemList {
				var delayTime int64
				delayTime = 1 // 默认延迟1s
				if config.DefaultDelayTime > 0 {
					delayTime = config.DefaultDelayTime // 取配置的默认延迟时间
				}
				if index == 0 {
					delayTime = msgItem.IntervalTime
				}
				messageInfo := message.MessageInfo{
					DelayTime:  delayTime,
					MsgType:    msgItem.MessageType,
					MsgContent: item,
				}
				messageInfoList = append(messageInfoList, messageInfo)
			}
		}
	}

	if len(messageInfoList) == 0 {
		zlog.Warnf(ctx, "messageInfoList is empty, req: %+v", req)
		return nil, components.ErrorParamInvalid
	}

	groupInfo.MessageList = messageInfoList

	sendGroupMessageReq := message.SendGroupMessageReq{
		AssistantUID:  req.AssistantUid,
		SubType:       int64(subType),
		SendType:      int64(sendType),
		TaskType:      defines.TaskTypeNow,
		GroupOrder:    message.NeedOrderSend,
		MessageGroups: []message.MessageGroup{groupInfo},
	}

	var ret *message.SendGroupMessageRsp
	if ret, err = message.SendGroupMessage(ctx, sendGroupMessageReq); err != nil {
		zlog.Warnf(ctx, "创建任务失败, req: %+v, err: %s", sendGroupMessageReq, err.Error())
		return nil, err
	}

	rsp = &dtoassistant.SendGroupMessageRsp{TaskId: ret.TaskId}
	return
}

func getGroupMessageList(ctx *gin.Context, messageGroupId int64, messageIds []int64) ([]models.Message, error) {
	messageList, err := models.MessageRef.List(ctx, messageGroupId)
	if err != nil {
		return nil, err
	}

	var headerMessage models.Message
	nexMessageMap := make(map[int64]models.Message)
	ids := make([]int64, 0)
	for _, messageItem := range messageList {
		if messageItem.PreMessageId == 0 {
			headerMessage = messageItem
		}
		ids = append(ids, messageItem.ID)

		nexMessageMap[messageItem.PreMessageId] = messageItem
	}

	diffInfo, _ := utils.FindDifferenceForInt64(messageIds, ids)

	if len(diffInfo) > 0 {
		return nil, components.ErrorParamInvalid.Sprintf("消息组和模板ID不一致")
	}

	if headerMessage.ID == 0 {
		return nil, components.ErrorParamInvalid.Sprintf("消息组配置错误")
	}
	msgList := buildMessageList(headerMessage, nexMessageMap)

	ret := make([]models.Message, 0, len(messageIds))
	for _, item := range msgList {
		if utils.InArrayInt64(item.ID, messageIds) {
			ret = append(ret, item)
		}
	}

	return ret, nil
}

func buildMessageList(node models.Message, nexMessageMap map[int64]models.Message) []models.Message {
	ret := []models.Message{node}

	if _, ok := nexMessageMap[node.ID]; !ok {
		return ret
	}

	return append(ret, buildMessageList(nexMessageMap[node.ID], nexMessageMap)...)
}
