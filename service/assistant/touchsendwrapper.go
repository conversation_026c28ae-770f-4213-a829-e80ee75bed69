package assistant

import (
	"assistantdeskgo/api/coursebase"
	"assistantdeskgo/api/jxreport"
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/scenetouch"
	scenetouchmodel "assistantdeskgo/models/scenetouch"
	"reflect"

	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"gorm.io/gorm"

	"assistantdeskgo/dto/dtoassistant"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"strings"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"

	"errors"
	"fmt"
	"strconv"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type ReportUrlMapInfo struct {
	BottomTestReportUrlMap    map[int64]map[int]string
	SubjectIdNameMap          map[int]string
	SubjectNameIdMap          map[string]int
	LessonReportUrlMap        map[int64]string
	StageTestReportUrlMap     map[int64]string
	PronunciationReportUrlMap map[int64]string
	VoiceReportUrlMap         map[int64]string
}

func TouchSendWrapper(ctx *gin.Context, req dtomessage.TouchSendWrapperReq) (rsp *dtoassistant.TouchSendWrapperResp, err error) {
	rsp = &dtoassistant.TouchSendWrapperResp{}
	info, err := middleware.GetLoginUserInfo(ctx)

	assistantUid := info.SelectedBusinessUid
	personUid := int64(info.StaffUid)
	sceneType := req.SceneType

	noReportStudentList := make(map[int64]bool)
	mapInfo := ReportUrlMapInfo{}
	for _, content := range req.Contents {
		var courseId int64
		var lessonId int64
		courseId, err = getCourseIdFromContent(content)
		if err != nil {
			zlog.Warnf(ctx, "getCourseIdFromContent error! err:%+v", err)
			return nil, err
		}
		lessonId, err = getLessonIdFromContent(content)
		if err != nil {
			zlog.Warnf(ctx, "getLessonIdFromContent error! err:%+v", err)
			return nil, err
		}
		studentUids := getStudentUidsFromContent(content)
		if len(studentUids) == 0 {
			err = errors.New("存在空的发送列表！")
			return nil, err
		}
		err = handleReportUrlMap(ctx, sceneType, courseId, lessonId, studentUids, &mapInfo)
		if err != nil {
			zlog.Warnf(ctx, "handleReportUrlMap error! err:%+v", err)
			return nil, err
		}
		handleNoReportStudentList(ctx, sceneType, content, studentUids, noReportStudentList, &mapInfo)
	}

	if len(noReportStudentList) > 0 {
		rsp.NoReportStudentList = make([]int64, 0)
		for stuId := range noReportStudentList {
			rsp.NoReportStudentList = append(rsp.NoReportStudentList, stuId)
		}
		rsp.Status = 16
		rsp.Reason = fmt.Sprintf("有%+v个学员未生成报告，已自动删除，是否开始发送", len(noReportStudentList))
		return rsp, nil
	}

	for _, content := range req.Contents {
		for _, msg := range content.MessageList {
			if msg.MsgType == components.MessageTypeCreatePicture {
				handleCreatePictureMsgContent(ctx, sceneType, &msg, &mapInfo)
			}
		}
	}

	request := touchmisgo.SendGroupMsgReq{}
	request.AssistantUID = assistantUid
	request.PersonUid = personUid
	request.SubType = req.SubType
	request.SceneType = req.SceneType
	request.SendTime = 0
	request.IsDelay = req.IsDelay
	request.TaskType = components.TaskTypeSendMsg

	request.Contents = make([]touchmisgo.Content, 0)
	for _, content := range req.Contents {
		var ctt touchmisgo.Content
		ctt.Ability = 1
		ctt.Strategy = 1
		ctt.SourceInfo = make([]touchmisgo.Source, 0)
		for _, source := range content.SourceInfo {
			keyVal, _ := fwyyutils.InterfaceToInt64(source.Key)
			ctt.SourceInfo = append(ctt.SourceInfo, touchmisgo.Source{
				Type: source.Type,
				Key:  strconv.FormatInt(keyVal, 10),
			})
		}
		ctt.LabelStudentList = content.LabelStudentList
		ctt.ReceiverList = make([]touchmisgo.Receiver, 0)
		for _, recv := range content.ReceiverList {
			ctt.ReceiverList = append(ctt.ReceiverList, touchmisgo.Receiver{
				StudentUid:    recv.StudentUid,
				ChatId:        recv.ChatId,
				RemoteIds:     recv.RemoteIds,
				AtStudentUids: recv.AtStudentUids,
			})
		}
		ctt.MessageList = make([]touchmisgo.MessageInfo, 0)
		for _, message := range content.MessageList {
			ctt.MessageList = append(ctt.MessageList, touchmisgo.MessageInfo{
				DelayTime: 5,
				AtMembers: touchmisgo.AtMembers{
					StudentUids: message.AtMembers.StudentUids,
					RemoteIds:   message.AtMembers.RemoteIds,
				},
				MsgType:    message.MsgType,
				MsgContent: message.MsgContent,
			})
			if message.MsgType == components.MessageTypeCreatePicture {
				request.TaskType = components.TaskTypeCreatePicture
			}
		}
		ctt.Ext = &touchmisgo.ExtDetail{
			ExtType: content.Ext.ExtType,
			ExtInfo: content.Ext.ExtInfo,
		}

		ctt.VariableValues, err = handlePrepareJobVariableValues(ctx, req.SceneType, req.PrepareJobId, content)
		if err != nil {
			zlog.Warnf(ctx, "handlePrepareJobVariableValues error! req:%+v, err:%+v", req, err)
			return nil, err
		}
		request.Contents = append(request.Contents, ctt)
	}

	requestStr, _ := jsoniter.MarshalToString(request)
	zlog.Infof(ctx, "touchmisgo.SendGroupMsgReq request: %s", requestStr)
	sendRet, sendErr := touchmisgo.SendGroupMsg(ctx, request)
	if sendErr != nil {
		zlog.Warnf(ctx, "touchmisgo.SendGroupMsg error! err:%+v", sendErr)
		err = components.ErrorParamInvalid.Wrap(sendErr)
		return nil, err
	}
	zlog.Infof(ctx, "touchmisgo.SendGroupMsg send done! sendRet:%+v", sendRet)
	rsp.TaskId = sendRet.TaskId

	return rsp, nil
}

func getCourseIdFromContent(content dtomessage.ContentEx) (courseId int64, err error) {
	for _, source := range content.SourceInfo {
		if source.Type == "course" {
			courseId, err = fwyyutils.InterfaceToInt64(source.Key)
			return
		}
	}
	return 0, errors.New("param invalid")
}

func getLessonIdFromContent(content dtomessage.ContentEx) (lessonId int64, err error) {
	for _, source := range content.SourceInfo {
		if source.Type == "lesson" {
			lessonId, err = fwyyutils.InterfaceToInt64(source.Key)
			return
		}
	}
	return 0, errors.New("param invalid")
}

func getStudentUidsFromContent(content dtomessage.ContentEx) (studentUids []int64) {
	studentUids = make([]int64, 0)
	for _, recv := range content.ReceiverList {
		studentUids = append(studentUids, recv.StudentUid)
	}
	return
}

func GetSubjectMap(ctx *gin.Context) (map[int]string, map[string]int) {
	idNameMap := make(map[int]string)
	nameIdMap := make(map[string]int)
	rsp, err := coursebase.GetSubjectIds(ctx)
	if err != nil {
		return idNameMap, nameIdMap
	}
	for _, subject := range rsp.Subject {
		idNameMap[cast.ToInt(subject.Value)] = subject.Name
		nameIdMap[subject.Name] = cast.ToInt(subject.Value)
	}
	return idNameMap, nameIdMap
}

func handleReportUrlMap(ctx *gin.Context, sceneType int64, courseId int64, lessonId int64, studentUids []int64, mapInfo *ReportUrlMapInfo) (err error) {
	getRetMap := func(res interface{}) map[string]interface{} {
		resVal := reflect.ValueOf(res)
		switch resVal.Kind() {
		case reflect.Map:
			ret := make(map[string]interface{})
			for _, key := range resVal.MapKeys() {
				ret[key.String()] = resVal.MapIndex(key).Interface()
			}
			return ret
		}
		return make(map[string]interface{})
	}
	switch sceneType {
	case touchmis.SendTypeBottomTestReport, touchmis.SendTypeBottomTestReportFd: // 群发摸底测报告
		mapInfo.SubjectIdNameMap, mapInfo.SubjectNameIdMap = GetSubjectMap(ctx)
		reportUrlMap := make(map[int64]map[int]string)
		bridgeRet, _err := GetBottomTestReportFromBridge(ctx, courseId, lessonId, studentUids)
		zlog.Infof(ctx, "GetBottomTestReportFromBridge ret: %+v", bridgeRet)
		if _err != nil {
			return _err
		}

		retMap := getRetMap(bridgeRet.Res)
		for subjectIdStr, reportUrlsInf := range retMap {
			subjectId, _err2 := strconv.Atoi(subjectIdStr)
			if _err2 != nil {
				return _err2
			}
			reportUrls := reportUrlsInf.(map[string]interface{})
			for studentUidStr, reportUrlStrInf := range reportUrls {
				reportUrlStr := reportUrlStrInf.(string)
				curStudentUid, _err3 := strconv.ParseInt(studentUidStr, 10, 64)
				if _err3 != nil {
					return _err3
				}
				reportUrlStr = strings.Split(reportUrlStr, " , ")[0]
				if _, _ok := reportUrlMap[curStudentUid]; !_ok {
					reportUrlMap[curStudentUid] = make(map[int]string)
				}
				reportUrlMap[curStudentUid][subjectId] = reportUrlStr
			}
		}
		mapInfo.BottomTestReportUrlMap = reportUrlMap
	case touchmis.SendTypeClassReport, touchmis.SendTypeLearnReportFd, int64(touchmis.SendTypeLearnReportCambridgeEnglish): // 群发课堂报告
		reportUrlMap := make(map[int64]string)

		bridgeRet, _err := GetLessonReportFromBridge(ctx, courseId, lessonId, studentUids)
		zlog.Infof(ctx, "GetLessonReportFromBridge ret: %+v", bridgeRet)
		if _err != nil {
			return _err
		}

		retMap := getRetMap(bridgeRet.Res)
		for studentUidStr, reportUrlsInf := range retMap {
			curStudentUid, _err3 := strconv.ParseInt(studentUidStr, 10, 64)
			if _err3 != nil {
				return _err3
			}
			reportUrls := reportUrlsInf.(map[string]interface{})
			reportUrl := ""
			for fieldKey, reportUrlStrInf := range reportUrls {
				if fieldKey == "lookurl" { // 班主任预览链接，不打点
					reportUrl = reportUrlStrInf.(string)
					break
				}
			}
			reportUrlMap[curStudentUid] = reportUrl
		}
		mapInfo.LessonReportUrlMap = reportUrlMap
	case touchmis.SendTypeStageResultFeedbackCard: // 阶段测结果反馈
		reportUrlMap := make(map[int64]string)

		bridgeRet, _err := GetStageTestReportFromBridge(ctx, courseId, lessonId, studentUids)
		zlog.Infof(ctx, "GetStageTestReportFromBridge ret: %+v", bridgeRet)
		if _err != nil {
			return _err
		}

		retMap := getRetMap(bridgeRet.Res)
		for studentUidStr, reportUrlsInf := range retMap {
			curStudentUid, _err3 := strconv.ParseInt(studentUidStr, 10, 64)
			if _err3 != nil {
				return _err3
			}
			reportUrlsWrap := reportUrlsInf.(map[string]interface{})
			reportUrls := make(map[string]interface{})
			for _, mapInf := range reportUrlsWrap {
				reportUrls = mapInf.(map[string]interface{})
				break
			}
			reportUrl := ""
			for fieldKey, reportUrlStrInf := range reportUrls {
				if fieldKey == "lookurl" {
					reportUrl = reportUrlStrInf.(string)
					break
				}
			}
			reportUrlMap[curStudentUid] = reportUrl
		}
		mapInfo.StageTestReportUrlMap = reportUrlMap
	case touchmis.SendTypePronunciationReport:
	case touchmis.SendTypePronunciationReportGroup:
		pronunciationReportUrlMap, _err := getPronunciationReport(ctx, courseId, lessonId, studentUids)
		if _err != nil {
			return _err
		}
		if len(mapInfo.PronunciationReportUrlMap) == 0 {
			mapInfo.PronunciationReportUrlMap = map[int64]string{}
		}
		for key, value := range pronunciationReportUrlMap {
			mapInfo.PronunciationReportUrlMap[key] = value
		}
	case touchmis.SendTypeVoiceReportGroup:
		voiceReportUrlMap, _err := getVoiceReport(ctx, courseId, lessonId, studentUids)
		if _err != nil {
			return _err
		}
		if len(mapInfo.VoiceReportUrlMap) == 0 {
			mapInfo.VoiceReportUrlMap = map[int64]string{}
		}

		for key, value := range voiceReportUrlMap {
			mapInfo.VoiceReportUrlMap[key] = value
		}

	case touchmis.SendTypeUnitReportDYD:
		reportUrlMap := make(map[int64]string)

		reportMap, _err := jxreport.NewClient().GetLessonReportUrlLpc(ctx, lessonId, studentUids)
		zlog.Infof(ctx, "GetLessonReportUrlLpc ret: %+v", reportMap)
		if _err != nil {
			return _err
		}

		for _, studentUID := range studentUids {
			if _, ok := reportMap[studentUID]; ok {
				reportUrlMap[studentUID] = reportMap[studentUID].ScreenShotUrl
			}
		}
		mapInfo.LessonReportUrlMap = reportUrlMap
	default:

	} // end of switch clause
	return nil
}

func getTemplVars(content dtomessage.ContentEx) map[string]bool {
	templVars := make(map[string]bool)
	for _, msg := range content.MessageList {
		msgContent := msg.MsgContent.(map[string]interface{})
		switch msg.MsgType {
		case components.MessageTypeWord:
			for _, wordInf := range msgContent["word"].([]interface{}) {
				word := wordInf.(string)
				if strings.HasPrefix(word, "#") && strings.HasSuffix(word, "#") {
					templVars[word] = true
				}
			}
		case components.MessageTypeCreatePicture:
			if picMeta, ok := msgContent["picUrlListMeta"].(map[string]interface{}); ok {
				picMetaUrls := picMeta["picUrl"].([]interface{})
				for _, picMetaUrlInf := range picMetaUrls {
					picMetaUrl := picMetaUrlInf.(string)
					templVars[picMetaUrl] = true
				}
			}
		case components.MessageTypeCardLink:
			for _, cardInf := range msgContent["cardList"].([]interface{}) {
				card := cardInf.(map[string]interface{})
				link := card["link"].(string)
				templVars[link] = true
				for _, wordInf := range card["title"].([]interface{}) {
					word := wordInf.(string)
					if strings.HasPrefix(word, "#") && strings.HasSuffix(word, "#") {
						templVars[word] = true
					}
				}
				for _, wordInf := range card["introduction"].([]interface{}) {
					word := wordInf.(string)
					if strings.HasPrefix(word, "#") && strings.HasSuffix(word, "#") {
						templVars[word] = true
					}
				}
			}
		}
	}
	return templVars
}
func handleNoReportStudentList(ctx *gin.Context, sceneType int64, content dtomessage.ContentEx, studentUids []int64, noReportStudentList map[int64]bool, mapInfo *ReportUrlMapInfo) {
	defer func() {
		if r := recover(); r != nil {
			zlog.Errorf(ctx, "handleNoReportStudentList panic, err:%s", r)
		}
	}()

	templVars := getTemplVars(content)

	switch sceneType {
	case touchmis.SendTypeBottomTestReport, touchmis.SendTypeBottomTestReportFd:
		needSubjects := make(map[int]bool)
		needSubjectNames := make(map[string]bool)
		for word := range templVars {
			if strings.Contains(word, "摸底测报告链接") {
				subjectName := strings.Replace(strings.Replace(word, "#", "", -1), "摸底测报告链接", "", -1)
				needSubjectNames[subjectName] = true
			} else if strings.Contains(word, "摸底测图片") {
				subjectName := strings.Replace(strings.Replace(word, "#", "", -1), "摸底测图片", "", -1)
				needSubjectNames[subjectName] = true
			}
		}
		for subjectName := range needSubjectNames {
			if targetSubjectId, _ok := mapInfo.SubjectNameIdMap[subjectName]; _ok {
				needSubjects[targetSubjectId] = true
			} else {
				switch subjectName {
				default:

				}
			}
		}
		for _, stuId := range studentUids {
			for needSubId := range needSubjects {
				if mapInfo.BottomTestReportUrlMap[stuId][needSubId] == "" {
					noReportStudentList[stuId] = true
				}
			}
		}
	case touchmis.SendTypeClassReport, touchmis.SendTypeLearnReportFd, int64(touchmis.SendTypeLearnReportCambridgeEnglish): // 群发课堂报告
		hasReportVar := false
		for word := range templVars {
			if word == "#课堂报告链接#" || word == "#课堂报告图片#" {
				hasReportVar = true
				break
			}
		}
		if hasReportVar {
			for _, stuId := range studentUids {
				if mapInfo.LessonReportUrlMap[stuId] == "" {
					noReportStudentList[stuId] = true
				}
			}
		}
	case touchmis.SendTypeStageResultFeedbackCard: // 阶段测结果反馈
		hasReportVar := false
		for word := range templVars {
			if word == "#阶段测报告链接#" || word == "#阶段测报告图片#" {
				hasReportVar = true
				break
			}
		}
		if hasReportVar {
			for _, stuId := range studentUids {
				if mapInfo.StageTestReportUrlMap[stuId] == "" {
					noReportStudentList[stuId] = true
				}
			}
		}
	case touchmis.SendTypePronunciationReport:
	case touchmis.SendTypePronunciationReportGroup:
		hasReportVar := false
		for word := range templVars {
			if strings.Index(word, "纠音报告") > 0 {
				hasReportVar = true
				break
			}
		}
		if hasReportVar {
			for _, stuId := range studentUids {
				if mapInfo.PronunciationReportUrlMap[stuId] == "" {
					noReportStudentList[stuId] = true
				}
			}
		}
	case touchmis.SendTypeVoiceReportGroup:
		hasReportVar := false
		for word := range templVars {
			if strings.Index(word, "配音小达人报告") > 0 {
				hasReportVar = true
				break
			}
		}
		if hasReportVar {
			for _, stuId := range studentUids {
				if mapInfo.VoiceReportUrlMap[stuId] == "" {
					noReportStudentList[stuId] = true
				}
			}
		}
	case touchmis.SendTypeUnitReportDYD:
		hasReportVar := false
		for word := range templVars {
			if word == "#单元报告链接#" {
				hasReportVar = true
				break
			}
		}
		if hasReportVar {
			for _, stuId := range studentUids {
				if mapInfo.LessonReportUrlMap[stuId] == "" {
					noReportStudentList[stuId] = true
				}
			}
		}
	default:

	}
}

func handleCreatePictureMsgContent(ctx *gin.Context, sceneType int64, msg *dtomessage.MessageInfo, mapInfo *ReportUrlMapInfo) {
	msgContent := msg.MsgContent.(map[string]interface{})
	picMeta := msgContent["picUrlListMeta"].(map[string]interface{})
	picUrlList := make([]map[string]interface{}, 0)
	switch sceneType {
	case touchmis.SendTypeBottomTestReport, touchmis.SendTypeBottomTestReportFd: // 群发摸底测报告
		picMetaUrlsInf := picMeta["picUrl"].([]interface{})
		cardSubjects := make([]int, 0)
		for _, picMetaUrlInf := range picMetaUrlsInf {
			picMetaUrl := picMetaUrlInf.(string)
			subjectName := strings.Replace(strings.Replace(strings.Replace(picMetaUrl, "#", "", -1), "摸底测图片", "", -1), "摸底测报告链接", "", -1)
			if subjectId, ok := mapInfo.SubjectNameIdMap[subjectName]; ok {
				cardSubjects = append(cardSubjects, subjectId)
			}
		}
		for stuId, subMap := range mapInfo.BottomTestReportUrlMap {
			for _, subjectId := range cardSubjects {
				picItem := map[string]interface{}{
					"picUrl":     []string{subMap[subjectId]},
					"studentUid": stuId,
					"groupId":    "",
					"width":      picMeta["width"],
					"height":     0,
				}
				picUrlList = append(picUrlList, picItem)
			}
		}
	case touchmis.SendTypeClassReport, touchmis.SendTypeLearnReportFd, int64(touchmis.SendTypeLearnReportCambridgeEnglish): // 群发课堂报告
		for stuId, url := range mapInfo.LessonReportUrlMap {
			picItem := map[string]interface{}{
				"picUrl":     []string{url},
				"studentUid": stuId,
				"groupId":    "",
				"width":      picMeta["width"],
				"height":     0,
			}
			picUrlList = append(picUrlList, picItem)
		}
	case touchmis.SendTypeStageResultFeedbackCard: // 阶段测结果反馈
		for stuId, url := range mapInfo.StageTestReportUrlMap {
			picItem := map[string]interface{}{
				"picUrl":     []string{url},
				"studentUid": stuId,
				"groupId":    "",
				"width":      picMeta["width"],
				"height":     0,
			}
			picUrlList = append(picUrlList, picItem)
		}
	case touchmis.SendTypePronunciationReport:
	case touchmis.SendTypePronunciationReportGroup:
		for stuId, url := range mapInfo.PronunciationReportUrlMap {
			picItem := map[string]interface{}{
				"picUrl":     []string{url},
				"studentUid": stuId,
				"groupId":    "",
				"width":      picMeta["width"],
				"height":     0,
			}
			picUrlList = append(picUrlList, picItem)
		}
	case touchmis.SendTypeVoiceReportGroup:
		for stuId, url := range mapInfo.VoiceReportUrlMap {
			picItem := map[string]interface{}{
				"picUrl":     []string{url},
				"studentUid": stuId,
				"groupId":    "",
				"width":      picMeta["width"],
				"height":     0,
			}
			picUrlList = append(picUrlList, picItem)
		}
	case touchmis.SendTypeUnitReportDYD:
		for stuId, url := range mapInfo.LessonReportUrlMap {
			picItem := map[string]interface{}{
				"picUrl":     []string{url},
				"studentUid": stuId,
				"groupId":    "",
				"width":      picMeta["width"],
				"height":     0,
			}
			picUrlList = append(picUrlList, picItem)
		}
	}

	msgContent["picUrlList"] = picUrlList
	msgContent["templateUrl"] = ""
	delete(msgContent, "picUrlListMeta")
}

// 检查数据准备作业ID必传
var requirePrepareJobSendTypes = map[int64]bool{
	touchmis.SendTypePersonalLearnFeedback: true,
}

func handlePrepareJobVariableValues(ctx *gin.Context, sceneType int64, jobId int64, content dtomessage.ContentEx) ([]touchmisgo.VariableValueItem, error) {
	variableValues := make([]touchmisgo.VariableValueItem, 0)
	if jobId == 0 {
		if requirePrepareJobSendTypes[sceneType] {
			return variableValues, components.DefaultError("生成任务ID为空")
		}
		return variableValues, nil
	}

	templVars := getTemplVars(content)
	receiverUids := make([]int64, 0)
	for _, rec := range content.ReceiverList {
		receiverUids = append(receiverUids, rec.StudentUid)
	}
	taskList, err := scenetouchmodel.TblSceneTouchPrepareTaskRef.GetSuccessTaskListByJobIdStudentUids(ctx, jobId, receiverUids)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zlog.Infof(ctx, "handlePrepareJobVariableValues taskList not found! jobId:%d", jobId)
			return variableValues, err
		}
		return variableValues, err
	}
	if len(taskList) == 0 {
		zlog.Infof(ctx, "handlePrepareJobVariableValues taskList not found! jobId:%d, studentUids:%+v", jobId, receiverUids)
		return variableValues, nil
	}
	studentTaskMap := make(map[int64]scenetouch.TaskResultsItem)
	for _, task := range taskList {
		if task.Status != scenetouchmodel.TaskStatusSuccess {
			continue
		}
		vars := make(map[string]interface{})
		if task.Variables != "" {
			err = jsoniter.UnmarshalFromString(task.Variables, &vars)
			if err != nil {
				return variableValues, err
			}
		}
		extra := scenetouch.PrepareTaskExtra{}
		if task.Extra != "" {
			err = jsoniter.UnmarshalFromString(task.Extra, &extra)
			if err != nil {
				return variableValues, err
			}
		}
		taskResult := scenetouch.TaskResultsItem{
			GroupName:   task.GroupName,
			StudentUid:  task.StudentUid,
			Variables:   vars,
			Status:      task.Status,
			FailReasons: extra.FailReasons,
		}
		studentTaskMap[task.StudentUid] = taskResult
	}
	variableValueMap := make(map[string]touchmisgo.VariableValueItem)

	for uid, task := range studentTaskMap {
		vars := task.Variables
		if vars == nil {
			continue
		}
		for word, val := range vars {
			if _, ok := variableValueMap[word]; !ok {
				variableValueMap[word] = touchmisgo.VariableValueItem{
					VariableName:  word,
					StudentValues: make(map[int64]string),
				}
			}
			if _, ok := variableValueMap[word].StudentValues[uid]; !ok {
				variableValueMap[word].StudentValues[uid], err = cast.ToStringE(val)
				if err != nil {
					zlog.Infof(ctx, "student prepare task variables to string fail! studentUid:%+v, jobId:%+v, word:%+v", uid, jobId, word)
					return variableValues, err
				}
			}
		}
	}
	// 如果变量存在，则应在每个学生上都存在
	for word := range templVars {
		if _, ok := variableValueMap[word]; !ok {
			continue
		}
		for _, uid := range receiverUids {
			if _, ok := variableValueMap[word].StudentValues[int64(uid)]; !ok {
				zlog.Infof(ctx, "student prepare task variables not exist! studentUid:%+v, jobId:%+v, word:%+v", uid, jobId, word)
				err = fmt.Errorf("学员%d变量%s不存在", uid, word)
				return variableValues, err
			}
		}
	}

	for _, val := range variableValueMap {
		variableValues = append(variableValues, touchmisgo.VariableValueItem{
			VariableName:  val.VariableName,
			SingleValues:  val.SingleValues,
			StudentValues: val.StudentValues,
		})
	}

	return variableValues, nil
}
