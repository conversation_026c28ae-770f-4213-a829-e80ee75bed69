package assistant

import (
	"assistantdeskgo/api/muse/message"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtoassistant"
	"github.com/gin-gonic/gin"
)

func MessageCheck(ctx *gin.Context, req dtoassistant.MessageCheckReq) (rsp *dtoassistant.MessageCheckRes, err error) {
	if req.AssistantUid == 0 {
		return nil, components.ErrorParamInvalid
	}
	if req.ChatType == defines.ChatTypeForGroup {
		if len(req.GroupRemoteId) == 0 {
			return nil, components.ErrorParamInvalid
		}
	} else if len(req.StudentUid) == 0 {
		return nil, components.ErrorParamInvalid
	}

	response, err := message.SendGroupMessageCheck(ctx, message.SendGroupMessageCheckReq{
		AssistantUid:  req.AssistantUid,
		StudentUids:   req.StudentUid,
		ChatType:      req.ChatType,
		GroupRemoteId: req.GroupRemoteId,
	})
	if err != nil {
		return nil, err
	}
	return &dtoassistant.MessageCheckRes{
		Status:      response.Status,
		Reason:      response.Reason,
		NoticeLimit: response.NoticeLimit,
		UsedLimit:   response.UsedLimit,
	}, nil

}
