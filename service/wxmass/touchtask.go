package wxmass

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/lpcmsg"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtowxmass"
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"strconv"
	"time"
)

const (
	StatusCodeNone           = 0  // 待发送（可取消）
	StatusCodeProcess        = 1  // 发送中
	StatusCodeSuccess        = 2  // 全部成功
	StatusCodePartSuccessRe  = 3  // 部分成功（可重试)
	StatusCodeFailure        = 4  // 全部失败（可重试）
	StatusCodeCanceled       = 5  // 全部取消
	StatusCodePartSuccess    = 6  // 部分成功（不可重试）
	StatusCodeExpire         = 7  // 任务过期
	StatusCodeStop           = 8  // 已中止（不可重试）
	StatusCodeTaskInit       = 9  // 任务待拆分
	StatusCodeTaskFailed     = 10 // 任务生成失败
	StatusCodeTaskInCancel   = 11 // 取消中
	StatusCodeTaskInStop     = 12 // 中止中
	StatusCodeTaskPartStop   = 13 // 部分中止
	StatusCodeTaskStopFailed = 14 // 中止失败
	StatusCodeTaskRetryNone  = 15 // 重试待发送
)

func MessageHistory(ctx *gin.Context, req dtowxmass.MessageHistoryReq) (resp dtowxmass.MessageHistoryResp, err error) {
	params := lpcmsg.FindPagedReq{
		AssistantUid: req.AssistantUid,
		BeginTime:    req.BeginTime,
		EndTime:      req.EndTime,
		TaskType:     req.TaskType,
		PageNum:      req.PageNum,
		PageSize:     req.PageSize,
	}

	if req.SceneTypes != nil && len(req.SceneTypes) > 0 {
		params.SceneTypes = req.SceneTypes
	} else if req.SceneId > 0 {
		sceneTypes := make([]int64, 0)
		sceneTypeOptions := components.GetSceneTypeBySceneId(req.SceneId)
		for sceneType, _ := range sceneTypeOptions {
			sceneTypes = append(sceneTypes, sceneType)
		}
		if len(sceneTypes) > 0 {
			params.SceneTypes = sceneTypes
		}
	}

	var paged lpcmsg.FindPagedResp
	paged, err = lpcmsg.FindPaged(ctx, params)
	if err != nil {
		return
	}
	studentList, err := getStudentList(ctx, paged.List)

	resp.Total = paged.Total
	resp.List = buildMainItem(paged.List, studentList)
	return
}

func buildMainItem(mainList []lpcmsg.MainTaskPagedItem, studentList map[int64]dau.StudentInfo) (list []dtowxmass.MainTaskPagedItem) {
	list = make([]dtowxmass.MainTaskPagedItem, 0, len(mainList))
	if len(mainList) == 0 {
		return
	}

	for _, main := range mainList {
		groupItems, statusCode, sendBeginTime, sendEndTime := buildGroupItem(main, studentList)

		sendBeginTimeDesc := "-"
		if sendBeginTime > 0 {
			sendBeginTimeDesc = time.Unix(sendBeginTime, 0).Format("2006-01-02 15:04")
		} else if main.SendTime > 0 {
			sendBeginTimeDesc = time.Unix(main.SendTime, 0).Format("2006-01-02 15:04")
		}
		sendEndTimeDesc := "-"
		if sendEndTime > 0 {
			sendEndTimeDesc = time.Unix(sendEndTime, 0).Format("2006-01-02 15:04")
		}
		taskTypeOptions := components.GetTaskTypeOptions()
		var taskTypeDesc interface{}
		taskTypeDesc = "其他任务"
		if desc, ok := taskTypeOptions[main.TaskType]; ok {
			taskTypeDesc = desc
		}

		statusCode = getMainTaskStatusCode(main, statusCode)
		list = append(list, dtowxmass.MainTaskPagedItem{
			MainTaskId:        main.MainTaskId,
			StatusCode:        statusCode,
			StatusText:        getStatusText(statusCode),
			SendBeginTimeDesc: sendBeginTimeDesc,
			SendEndTimeDesc:   sendEndTimeDesc,
			TaskType:          main.TaskType,
			TaskTypeDesc:      taskTypeDesc,
			SceneType:         main.SceneType,
			SceneTypeDesc:     components.GetSceneTypeDescBySceneTypeId(main.SceneType),
			CreateTime:        main.CreateTime,
			GroupTasks:        groupItems,
		})

	}
	return
}

func buildGroupItem(mainTask lpcmsg.MainTaskPagedItem, studentList map[int64]dau.StudentInfo) (groupItems []dtowxmass.GroupTaskPagedItem, statusCode, sendBeginTime, sendEndTime int64) {
	// 先判断主任务状态
	if exist, _ := fwyyutils.InArrayInt64(mainTask.Status, []int64{
		lpcmsg.MainTaskStatusNone,
		lpcmsg.MainTaskStatusSplitFailed,
	}); exist {
		statusCode = transferMainTaskStatusCode(mainTask.Status)
		return
	}

	groupList := mainTask.GroupTasks
	groupItems = make([]dtowxmass.GroupTaskPagedItem, 0, len(groupList))
	if len(groupList) == 0 {
		return
	}

	status := 0
	for _, group := range groupList {
		subItems, groupStatusCode, groupSendBeginTime, groupSendEndTime := buildSubItem(group.SubTasks, studentList)

		sendBeginTimeDesc := "-"
		if groupSendBeginTime > 0 {
			sendBeginTimeDesc = time.Unix(groupSendBeginTime, 0).Format("2006-01-02 15:04")
		}
		sendEndTimeDesc := "-"
		if groupSendEndTime > 0 {
			sendEndTimeDesc = time.Unix(groupSendEndTime, 0).Format("2006-01-02 15:04")
		}
		groupItems = append(groupItems, dtowxmass.GroupTaskPagedItem{
			GroupTaskId:       group.GroupTaskId,
			StatusCode:        groupStatusCode,
			StatusText:        getStatusText(groupStatusCode),
			SendBeginTimeDesc: sendBeginTimeDesc,
			SendEndTimeDesc:   sendEndTimeDesc,
			SubTasks:          subItems,
		})

		// status
		status = status | 1<<groupStatusCode

		// min
		if sendBeginTime == 0 || groupSendBeginTime < sendBeginTime {
			sendBeginTime = groupSendBeginTime
		}

		// max
		if groupSendEndTime > sendEndTime {
			sendEndTime = groupSendEndTime
		}
	}

	statusCode = getOffsetStatus(status)
	return
}

func buildSubItem(subList []lpcmsg.SubTaskPagedItem, studentList map[int64]dau.StudentInfo) (subItems []dtowxmass.SubTaskPagedItem, statusCode, sendBeginTime, sendEndTime int64) {
	subItems = make([]dtowxmass.SubTaskPagedItem, 0, len(subList))
	if len(subList) == 0 {
		return
	}

	status := 0
	for _, sub := range subList {
		sub.StatusCode = transferSubStatusCode(sub.StatusCode)

		//处理内容中的部分数据
		var msgContent interface{}
		if sub.Content.MsgType == defines.MessageTypeVoice {
			//语音内容需要将文件名转为mp3链接
			msgContent = buildVoiceContent(&gin.Context{}, sub.Content)
		}
		msgContent = sub.Content.MsgContent

		tmpSubTaskItem := dtowxmass.SubTaskPagedItem{
			SubTaskId:    sub.SubTaskId,
			StatusCode:   sub.StatusCode,
			ParentTaskId: sub.RefDataId,
			StatusText:   getStatusText(sub.StatusCode),
			SubType:      sub.SubType,
			AtMembers:    sub.AtMembers,
			Content: dtowxmass.SubMessagePagedItem{
				TplType:        sub.Content.TplType,
				MsgType:        sub.Content.MsgType,
				MsgContent:     msgContent,
				MsgContents:    sub.Content.MsgContents,
				DefaultContent: sub.Content.DefaultContent,
				AtMembers:      buildAtMembers(sub.AtMembers, studentList),
			},
		}

		if sub.KpSubTaskItem != nil {
			kpTaskStatusCode := transferSubStatusCode(sub.KpSubTaskItem.StatusCode)
			tmpSubTaskItem.KpSubTaskItem = &dtowxmass.KpSubTaskItem{
				ParentTaskId: sub.KpSubTaskItem.ParentTaskId,
				StatusCode:   kpTaskStatusCode,
				StatusText:   getStatusText(kpTaskStatusCode),
				SubType:      sub.KpSubTaskItem.SubType,
				Content: dtowxmass.SubMessagePagedItem{
					TplType:        sub.KpSubTaskItem.Content.TplType,
					MsgType:        sub.KpSubTaskItem.Content.MsgType,
					MsgContent:     sub.KpSubTaskItem.Content.MsgContent,
					MsgContents:    sub.KpSubTaskItem.Content.MsgContents,
					DefaultContent: sub.KpSubTaskItem.Content.DefaultContent,
				},
				ReceiverIds: sub.KpSubTaskItem.ReceiverIds,
			}
		}
		subItems = append(subItems, tmpSubTaskItem)
		// status
		status = status | 1<<sub.StatusCode

		// min
		if sendBeginTime == 0 || sub.SendBeginTime < sendBeginTime {
			sendBeginTime = sub.SendBeginTime
		}

		// max
		if sub.SendEndTime > sendEndTime {
			sendEndTime = sub.SendEndTime
		}
	}

	// deal status
	statusCode = getOffsetStatus(status)
	return
}

func getOffsetStatus(status int) int64 {
	// 所有子任务都是待执行时, 当前任务为待执行
	if status|(1<<StatusCodeNone) == (1 << StatusCodeNone) {
		return StatusCodeNone
	}

	// 子任务取消时, 当前任务为取消
	if status&(1<<StatusCodeCanceled) > 0 {
		return StatusCodeCanceled
	}

	// 子任务中止时, 当前任务中止
	if status&(1<<StatusCodeStop) > 0 {
		return StatusCodeStop
	}

	// 存在待执行/执行中的任务时, 当前任务为执行中(时间间隔空隙只存在 待执行任务+ 执行完成的任务)
	if status&(1<<StatusCodeProcess|1<<StatusCodeNone) > 0 {
		return StatusCodeProcess
	}

	// 子任务全部成功时, 当前任务为成功
	if status|(1<<StatusCodeSuccess) == (1 << StatusCodeSuccess) {
		return StatusCodeSuccess
	}

	// 子任务全部失败时, 当前任务为失败
	if status|(1<<StatusCodeFailure) == (1 << StatusCodeFailure) {
		return StatusCodeFailure
	}

	// 子任务全部失效时, 当前任务为失效
	if status|(1<<StatusCodeExpire) == (1 << StatusCodeExpire) {
		return StatusCodeExpire
	}

	// 同时存在成功和失败 或者是有部分成功时, 部分成功
	if (status&(1<<StatusCodeSuccess) > 0 && status&(1<<StatusCodeFailure) > 0) || status&(1<<StatusCodePartSuccessRe) > 0 {
		return StatusCodePartSuccessRe
	}

	return StatusCodeCanceled
}

func transferSubStatusCode(status int64) int64 {
	switch status {
	case lpcmsg.TaskStatusNone:
		return StatusCodeNone
	case lpcmsg.TaskStatusProcess:
		return StatusCodeProcess
	case lpcmsg.TaskStatusSuccess:
		return StatusCodeSuccess
	case lpcmsg.TaskStatusExpired:
		return StatusCodeExpire
	case lpcmsg.TaskStatusCanceled:
		return StatusCodeCanceled
	case lpcmsg.TaskStatusFailure:
		return StatusCodeFailure
	case lpcmsg.TaskStatusPartSuccess:
		return StatusCodePartSuccessRe
	case lpcmsg.TaskStatusStop:
		return StatusCodeStop
	}
	return status
}

func getMainTaskStatusCode(mainTask lpcmsg.MainTaskPagedItem, status int64) int64 {
	if exist, _ := fwyyutils.InArrayInt64(mainTask.Status, []int64{
		lpcmsg.MainTaskStatusCanceled,
		lpcmsg.MainTaskStatusInCancel,
		lpcmsg.MainTaskStatusInStop,
		lpcmsg.MainTaskStatusPartStop,
		lpcmsg.MainTaskStatusStop,
		lpcmsg.MainTaskStatusRetry,
		lpcmsg.MainTaskStatusStopFailed,
	}); exist {
		return transferMainTaskStatusCode(mainTask.Status)
	}
	return status
}

func transferMainTaskStatusCode(status int64) int64 {
	switch status {
	case lpcmsg.MainTaskStatusNone:
		return StatusCodeTaskInit
	case lpcmsg.MainTaskStatusSplitFailed:
		return StatusCodeTaskFailed
	case lpcmsg.MainTaskStatusCanceled:
		return StatusCodeCanceled
	case lpcmsg.MainTaskStatusInCancel:
		return StatusCodeTaskInCancel
	case lpcmsg.MainTaskStatusInStop:
		return StatusCodeTaskInStop
	case lpcmsg.MainTaskStatusPartStop:
		return StatusCodeTaskPartStop
	case lpcmsg.MainTaskStatusStop:
		return StatusCodeStop
	case lpcmsg.MainTaskStatusRetry:
		return StatusCodeTaskRetryNone
	case lpcmsg.MainTaskStatusStopFailed:
		return StatusCodeTaskStopFailed
	}
	return status
}

func getStatusText(statusCode int64) string {
	switch statusCode {
	case StatusCodeNone:
		return "待发送"
	case StatusCodeProcess:
		return "发送中"
	case StatusCodeSuccess:
		return "全部成功"
	case StatusCodePartSuccessRe:
		return "部分成功" // 可重试
	case StatusCodeFailure:
		return "全部失败" // 可重试
	case StatusCodeCanceled:
		return "已取消" // 不可重试
	case StatusCodeStop:
		return "已中止" // 不可重试
	case StatusCodeExpire:
		return "已失效" //不可重试
	case StatusCodeTaskInit:
		return "任务创建中" //不可重试
	case StatusCodeTaskFailed:
		return "任务生成失败" //不可重试
	case StatusCodeTaskInCancel:
		return "取消中" //不可重试
	case StatusCodeTaskInStop:
		return "中止中" //不可重试
	case StatusCodeTaskPartStop:
		return "部分中止" //不可重试
	case StatusCodeTaskStopFailed:
		return "中止失败" //不可重试
	case StatusCodeTaskRetryNone:
		return "重试待发送" //不可重试
	}
	return ""
}

func GetGroupReceivers(ctx *gin.Context, req dtowxmass.GetGroupReceiversReq) (list []interface{}, err error) {
	params := lpcmsg.GetGroupReceiversReq{
		MainTaskId:  req.MainTaskId,
		GroupTaskId: req.GroupTaskId,
	}

	var receivers []lpcmsg.GetGroupReceiversResp
	receivers, err = lpcmsg.GetGroupReceivers(ctx, params)
	if err != nil {
		return
	}

	ReceiverIds := make([]int64, 0, len(receivers))
	for _, receiver := range receivers {
		ReceiverIds = append(ReceiverIds, receiver.ReceiverId)
	}
	// get info
	if req.SendType == defines.ChatTypeForPersonal {
		list, err = getPersonReceiverList(ctx, ReceiverIds, receivers)
	} else if req.SendType == defines.ChatTypeForGroup {
		//查询用户设备信息
		deviceInfo, er := mesh.GetDeviceListByDeviceUid(ctx, req.AssistantUid)
		if er != nil {
			return
		}
		list, err = getGroupReceiverList(ctx, ReceiverIds, receivers, *deviceInfo)
	}

	return
}

func getGroupReceiverList(ctx *gin.Context, groupIdList []int64, receivers []lpcmsg.GetGroupReceiversResp, deviceInfo mesh.GetDeviceListByDeviceUidsRsq) (list []interface{}, err error) {
	//批量查询群信息
	var groupList map[string]*kunpeng.GetGroupInfoRsp
	groupList, err = kunpeng.BatchGetGroupInfoByIds(ctx, groupIdList, &deviceInfo)
	if err != nil {
		return
	}
	//组装数据
	list = make([]interface{}, 0, len(receivers))
	for _, receiver := range receivers {
		groupIdStr := strconv.FormatInt(receiver.ReceiverId, 10)
		group, ok := groupList[groupIdStr]
		if !ok {
			continue
		}

		list = append(list, dtowxmass.GetGroupReceiversResp{
			GroupName: group.GroupName,
		})
	}
	return
}

func getPersonReceiverList(ctx *gin.Context, uidList []int64, receivers []lpcmsg.GetGroupReceiversResp) (list []interface{}, err error) {
	var students map[int64]dau.StudentInfo
	students, err = dau.GetStudents(ctx, uidList, []string{"studentUid", "studentName", "phone"})
	if err != nil {
		return
	}

	list = make([]interface{}, 0, len(receivers))
	for _, receiver := range receivers {
		student, ok := students[receiver.ReceiverId]
		if !ok {
			continue
		}

		list = append(list, dtowxmass.GetPersonReceiversResp{
			StudentUid:  receiver.ReceiverId,
			StudentName: student.StudentName,
			Phone:       utils.MaskPhone11(student.Phone),
		})
	}
	return
}

func GetSubTaskDetail(ctx *gin.Context, req dtowxmass.GetSubTaskDetailReq) (resp interface{}, err error) {
	params := lpcmsg.GetSubTaskDetailReq{
		AssistantUid: req.AssistantUid,
		GroupTaskId:  req.GroupTaskId,
		SubTaskId:    req.SubTaskId,
		ParentTaskId: req.ParentTaskId,
	}

	var subTaskDetailResp lpcmsg.GetSubTaskDetailResp
	subTaskDetailResp, err = lpcmsg.GetSubTaskDetail(ctx, params)
	if err != nil {
		return
	}

	// 获取错误信息map
	var statusCodeList []lpcmsg.GetStatusCodeMapResp
	statusCodeList, err = lpcmsg.GetStatusCodeMap(ctx, req.AssistantUid, []int64{})
	if err != nil {
		return
	}
	statusCodeInfoMap := make(map[int64]lpcmsg.GetStatusCodeMapResp)
	for _, statusCodeInfo := range statusCodeList {
		statusCodeInfoMap[statusCodeInfo.StatusCode] = statusCodeInfo
	}

	// get info
	if req.SendType == defines.ChatTypeForPersonal {
		return getPersonSubTaskDetail(ctx, req.AssistantUid, subTaskDetailResp, statusCodeInfoMap)
	} else if req.SendType == defines.ChatTypeForGroup {
		return getGroupSubTaskDetail(ctx, req.AssistantUid, subTaskDetailResp, statusCodeInfoMap)
	}
	return
}

func getGroupSubTaskDetail(ctx *gin.Context, assistantUid int64, subTaskDetailResp lpcmsg.GetSubTaskDetailResp, statusCodeInfoMap map[int64]lpcmsg.GetStatusCodeMapResp) (resp dtowxmass.GetGroupSubTaskDetailResp, err error) {
	//查询用户设备信息
	deviceInfo, er := mesh.GetDeviceListByDeviceUid(ctx, assistantUid)
	if er != nil {
		return
	}
	//获取群IDlist
	groupIdList := make([]int64, 0)
	for _, detailResp := range subTaskDetailResp.List {
		for _, item := range detailResp.ReceiverList {
			groupIdList = append(groupIdList, item.ReceiverId)
		}
	}
	//批量查询群信息
	var groupList map[string]*kunpeng.GetGroupInfoRsp
	groupList, err = kunpeng.BatchGetGroupInfoByIds(ctx, groupIdList, deviceInfo)

	resp, err = formatGroupSubTaskDetail(ctx, subTaskDetailResp.List, groupList, statusCodeInfoMap)
	resp.TotalUids = len(subTaskDetailResp.ReceiverIds)
	return
}

func getPersonSubTaskDetail(ctx *gin.Context, assistantUid int64, subTaskDetailResp lpcmsg.GetSubTaskDetailResp, statusCodeInfoMap map[int64]lpcmsg.GetStatusCodeMapResp) (resp dtowxmass.GetPersonSubTaskDetailResp, err error) {
	// 获取学生信息
	uidWxIdMap := make(map[int64]string)
	for _, detailResp := range subTaskDetailResp.List {
		for _, item := range detailResp.ReceiverList {
			uidWxIdMap[item.ReceiverId] = item.RemoteId
		}
	}
	uids := make([]int64, 0, len(uidWxIdMap))
	for k, _ := range uidWxIdMap {
		uids = append(uids, k)
	}
	var students map[int64]dau.StudentInfo
	students, err = dau.GetStudents(ctx, subTaskDetailResp.ReceiverIds, []string{"studentUid", "studentName", "phone"})
	if err != nil {
		return
	}

	// 获取微信名备注
	var wxInfoByUids map[int64]map[int64]muse.GetWxInfoByUidsResp
	wxInfoByUids, err = muse.GetWxInfoByUids(ctx, assistantUid, uids)
	if err != nil {
		return
	}

	resp, err = formatPersonSubTaskDetail(ctx, subTaskDetailResp.List, students, statusCodeInfoMap, wxInfoByUids)
	resp.TotalUids = len(subTaskDetailResp.ReceiverIds)
	return
}

func formatGroupSubTaskDetail(ctx *gin.Context, subTaskDetailList []lpcmsg.GetSubTaskDetailData, groupData map[string]*kunpeng.GetGroupInfoRsp, statusCodeInfoMap map[int64]lpcmsg.GetStatusCodeMapResp) (resp dtowxmass.GetGroupSubTaskDetailResp, err error) {
	resp.List = make([]dtowxmass.GetGroupSubTaskDetailData, 0, len(subTaskDetailList))

	execUids := make(map[int64]int64)
	for _, detailResp := range subTaskDetailList {
		respItem := dtowxmass.GetGroupSubTaskDetailData{
			StatusCode: detailResp.StatusCode,
			StatusText: detailResp.StatusText,
		}

		groupList := make([]dtowxmass.GetGroupSubTaskDetailItem, 0, len(detailResp.ReceiverList))
		for _, receiver := range detailResp.ReceiverList {
			execUids[receiver.ReceiverId] = receiver.ReceiverId
			group, ok := groupData[strconv.FormatInt(receiver.ReceiverId, 10)]
			if !ok {
				zlog.Warnf(ctx, "GetSubTaskDetail.formatPersonSubTaskDetail dau not find student[%d]", receiver.ReceiverId)
				groupList = append(groupList, dtowxmass.GetGroupSubTaskDetailItem{
					GroupId:   receiver.ReceiverId,
					GroupName: cast.ToString(receiver.ReceiverId),
				})
				continue
			}

			// 成功
			if detailResp.StatusCode == lpcmsg.TaskStatusSuccess {
				groupList = append(groupList, dtowxmass.GetGroupSubTaskDetailItem{
					GroupId:   receiver.ReceiverId,
					GroupName: group.GroupName,
				})
				continue
			}

			// 非成功
			var statusCodeInfo lpcmsg.GetStatusCodeMapResp
			statusCodeInfo, ok = statusCodeInfoMap[receiver.ErrCode]
			if ok {
				groupList = append(groupList, dtowxmass.GetGroupSubTaskDetailItem{
					GroupId:   receiver.ReceiverId,
					GroupName: group.GroupName,
					ErrMsg:    statusCodeInfo.StatusText,
					Solution:  statusCodeInfo.Solution,
				})
			} else {
				zlog.Warnf(ctx, "GetSubTaskDetail.formatPersonSubTaskDetail unknown status[%d]", receiver.ErrCode)
				groupList = append(groupList, dtowxmass.GetGroupSubTaskDetailItem{
					GroupId:   receiver.ReceiverId,
					GroupName: group.GroupName,
					ErrMsg:    "未知状态",
					Solution:  "",
				})
			}
		}
		respItem.GroupList = groupList

		resp.List = append(resp.List, respItem)
	}
	resp.ExecUids = len(execUids)
	return
}

func formatPersonSubTaskDetail(ctx *gin.Context, subTaskDetailList []lpcmsg.GetSubTaskDetailData, students map[int64]dau.StudentInfo, statusCodeInfoMap map[int64]lpcmsg.GetStatusCodeMapResp, wxInfoByUids map[int64]map[int64]muse.GetWxInfoByUidsResp) (resp dtowxmass.GetPersonSubTaskDetailResp, err error) {
	resp.List = make([]dtowxmass.GetPersonSubTaskDetailData, 0, len(subTaskDetailList))

	execUids := make(map[int64]int64)
	for _, detailResp := range subTaskDetailList {
		respItem := dtowxmass.GetPersonSubTaskDetailData{
			StatusCode: detailResp.StatusCode,
			StatusText: detailResp.StatusText,
		}

		studentList := make([]dtowxmass.GetPersonSubTaskDetailItem, 0, len(detailResp.ReceiverList))
		for _, receiver := range detailResp.ReceiverList {
			execUids[receiver.ReceiverId] = receiver.ReceiverId

			student, ok := students[receiver.ReceiverId]
			wxName := receiver.RemoteId
			if wxIdInfoMap, stuOk := wxInfoByUids[receiver.ReceiverId]; stuOk {
				for wxId, uidsResp := range wxIdInfoMap {
					if len(receiver.RemoteId) == 0 {
						wxName = uidsResp.Remark
						break
					}

					if receiver.RemoteId == cast.ToString(wxId) {
						wxName = uidsResp.Remark
						break
					}
				}
			}
			if !ok {
				zlog.Warnf(ctx, "GetSubTaskDetail.formatPersonSubTaskDetail dau not find student[%d]", receiver.ReceiverId)
				studentList = append(studentList, dtowxmass.GetPersonSubTaskDetailItem{
					StudentUid:  receiver.ReceiverId,
					StudentName: cast.ToString(receiver.ReceiverId),
					WxName:      wxName,
				})
				continue
			}

			// 成功
			if detailResp.StatusCode == lpcmsg.TaskStatusSuccess {
				studentList = append(studentList, dtowxmass.GetPersonSubTaskDetailItem{
					StudentUid:  receiver.ReceiverId,
					StudentName: student.StudentName,
					WxName:      wxName,
				})
				continue
			}

			// 非成功
			var statusCodeInfo lpcmsg.GetStatusCodeMapResp
			statusCodeInfo, ok = statusCodeInfoMap[receiver.ErrCode]
			if ok {
				tmpDetailItem := dtowxmass.GetPersonSubTaskDetailItem{
					StudentUid:  receiver.ReceiverId,
					StudentName: student.StudentName,
					WxName:      wxName,
					ErrMsg:      statusCodeInfo.StatusText,
					Solution:    statusCodeInfo.Solution,
				}
				if receiver.FailedInfo != nil {
					tmpDetailItem.ErrMsg = fmt.Sprintf("%s\n【%s】", tmpDetailItem.ErrMsg, utils.MarshalIgnoreError(receiver.FailedInfo.Msg))
					if len(receiver.FailedInfo.Solution) > 0 {
						tmpDetailItem.Solution = receiver.FailedInfo.Solution
					}
				}
				studentList = append(studentList, tmpDetailItem)
			} else {
				zlog.Warnf(ctx, "GetSubTaskDetail.formatPersonSubTaskDetail unknown status[%d]", receiver.ErrCode)
				studentList = append(studentList, dtowxmass.GetPersonSubTaskDetailItem{
					StudentUid:  receiver.ReceiverId,
					StudentName: student.StudentName,
					WxName:      wxName,
					ErrMsg:      "未知状态",
					Solution:    "",
				})
			}
		}
		respItem.StudentList = studentList

		resp.List = append(resp.List, respItem)
	}
	resp.ExecUids = len(execUids)
	return
}

func buildVoiceContent(ctx *gin.Context, content lpcmsg.SubMessagePagedItem) interface{} {
	var voiceInfo map[string]interface{}
	if content.MsgType != defines.MessageTypeVoice {
		return content
	}
	voiceInfo = content.MsgContent.(map[string]interface{})
	var err error
	var voiceName string
	voiceName, err = helpers.BaiduBucket2.GetUrlByFileName(ctx, voiceInfo["voiceName"].(string), time.Second*3600)
	if err != nil {
		zlog.Warnf(ctx, "getVoiceCosUrlErr,voiceName:%s", voiceInfo["voiceName"])
	}
	voiceInfo["voiceName"] = voiceName
	return voiceInfo
}

func getStudentList(ctx *gin.Context, list []lpcmsg.MainTaskPagedItem) (students map[int64]dau.StudentInfo, err error) {
	if len(list) == 0 {
		return
	}
	//收集@的人uid
	atMemberList := make([]int64, 0)
	for _, mainTask := range list {
		groupTask := mainTask.GroupTasks
		if len(groupTask) == 0 {
			continue
		}
		for _, subTask := range groupTask {
			subItem := subTask.SubTasks
			if len(subItem) == 0 {
				continue
			}
			for _, item := range subItem {
				if len(item.AtMembers) == 0 {
					continue
				}
				atMemberList = append(atMemberList, item.AtMembers...)
			}
		}
	}
	if len(atMemberList) <= 0 {
		return
	}
	//去重
	atMemberList = utils.ArrayUnique(atMemberList)
	//批量查询学生信息
	students, err = dau.GetStudents(ctx, atMemberList, []string{"studentUid", "studentName", "phone"})
	if err != nil {
		return
	}
	return
}

func buildAtMembers(atMembers []int64, studentList map[int64]dau.StudentInfo) []string {
	data := make([]string, 0)
	if len(atMembers) <= 0 || len(studentList) <= 0 {
		return data
	}
	for _, uid := range atMembers {
		//判断用户是否存在
		studentInfo, ok := studentList[uid]
		if !ok {
			continue
		}
		data = append(data, studentInfo.StudentName)
	}
	return data
}
