package wxmass

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/dto/dtowxmass"
	"assistantdeskgo/utils"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

func GetCourseAppList(ctx *gin.Context, courseList map[int64]dal.CourseLessonInfo) (data dtowxmass.GetCourseAppIdResp, err error) {
	//查询课程与app配置
	var courseAppConf map[string]map[string]assistantdesk.CourseAppInfo
	if err = assistantdesk.GetKeyForJson(ctx, assistantdesk.CourseAppKey, &courseAppConf); err != nil {
		return
	}
	//查询全量app配置
	var appConf map[string]map[string]assistantdesk.AppInfo
	if err = assistantdesk.GetKeyForJson(ctx, assistantdesk.AppKey, &appConf); err != nil {
		return
	}
	appList := appConf[assistantdesk.AppKey]

	appConfList := courseAppConf[assistantdesk.CourseAppKey]
	if len(appConfList) == 0 {
		return data, errors.New("未获取到courseApp配置")
	}
	//匹配课程对应的appId
	for courseId, courseInfo := range courseList {
		//获取匹配结果是否符合
		appIdList := checkCourseApp(courseInfo, appConfList)
		if len(appIdList) <= 0 {
			continue
		}
		tmp := dtowxmass.AppObject{
			CourseId: courseId,
		}
		for _, appId := range appIdList {
			tmpAppInfo := dtowxmass.AppInfo{
				AppId: appId,
			}
			if appInfo, ok := appList[appId]; ok {
				tmpAppInfo.AppName = appInfo.Name
				tmpAppInfo.IconUrl = appInfo.IconURL
				tmpAppInfo.IconType = appInfo.IconType
			}
			tmp.AppInfo = append(tmp.AppInfo, tmpAppInfo)
		}
		data.AppList = append(data.AppList, tmp)
	}
	return
}

func checkCourseApp(courseInfo dal.CourseLessonInfo, appConfList map[string]assistantdesk.CourseAppInfo) []string {
	var res []string
	for appId, courseAppConf := range appConfList {
		check1 := utils.InArrayInt64(courseInfo.NewCourseType, courseAppConf.NewCourseType)
		check2 := utils.InArrayInt64(courseInfo.MainGradeId, courseAppConf.Grade)
		check3 := utils.InArrayInt64(courseInfo.MainSubjectId, courseAppConf.Subject)
		check4 := utils.InArrayInt64(courseInfo.Source, courseAppConf.Source)
		check5 := utils.InArrayInt64(courseInfo.CourseId, courseAppConf.CourseIds)
		if check1 && check2 && check3 && check4 || check5 {
			res = append(res, appId)
		}
	}
	return res
}
