package leads

import (
	"assistantdeskgo/api/mercury"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type BlockedFeatureConfig struct {
	Name []string `json:"name"`
}

func GetBlockedFeatureConfig(ctx *gin.Context) (res BlockedFeatureConfig) {
	res = BlockedFeatureConfig{}
	if err := mercury.GetConfigForJson(ctx, "ark_blocked_feature_config", mercury.DefaultExpireTime, &res); err != nil {
		zlog.Warnf(ctx, "GetBlockedFeatureConfig get mercury config failed, err: %+v", err)
	}
	return
}

type LeadsFeatureConfig struct {
	Name string `json:"name"`
	Desc string `json:"desc"`
}

func GetLeadsFeatureConfig(ctx *gin.Context) (res []LeadsFeatureConfig, err error) {
	res = []LeadsFeatureConfig{}
	if err := mercury.GetConfigForJson(ctx, "ark_leads_feature_config", mercury.DefaultExpireTime, &res); err != nil {
		zlog.Warnf(ctx, "GetLeadsFeatureConfig get mercury config failed, err: %+v", err)
	}
	return res, nil
}
