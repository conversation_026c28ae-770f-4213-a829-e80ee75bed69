package leads

import (
	"context"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestFormatFeatureValue(t *testing.T) {
	// 创建测试用的 gin.Context
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	req, _ := http.NewRequestWithContext(context.Background(), "GET", "/", nil)
	ctx.Request = req

	tests := []struct {
		name     string
		config   LeadsFeatureConfig
		value    interface{}
		expected string
	}{
		// 测试原始值格式化
		{
			name: "raw value - string",
			config: LeadsFeatureConfig{
				Name: "test_field",
				Type: FeatureTypeRaw,
			},
			value:    "test_value",
			expected: "test_value",
		},
		{
			name: "raw value - number",
			config: LeadsFeatureConfig{
				Name: "test_field",
				Type: FeatureTypeRaw,
			},
			value:    123,
			expected: "123",
		},
		{
			name: "raw value - empty type defaults to raw",
			config: LeadsFeatureConfig{
				Name: "test_field",
				Type: "", // 空类型应该默认为 raw
			},
			value:    "test_value",
			expected: "test_value",
		},

		// 测试枚举值翻译
		{
			name: "enum value - found mapping",
			config: LeadsFeatureConfig{
				Name: "trans_level",
				Type: FeatureTypeEnum,
				Mapping: map[string]string{
					"0": "新",
					"1": "高",
					"2": "中",
					"3": "低",
				},
			},
			value:    "1",
			expected: "高",
		},
		{
			name: "enum value - not found mapping",
			config: LeadsFeatureConfig{
				Name: "trans_level",
				Type: FeatureTypeEnum,
				Mapping: map[string]string{
					"0": "新",
					"1": "高",
				},
			},
			value:    "5", // 不存在的值
			expected: "5", // 应该返回原值
		},
		{
			name: "enum value - nil mapping",
			config: LeadsFeatureConfig{
				Name:    "trans_level",
				Type:    FeatureTypeEnum,
				Mapping: nil,
			},
			value:    "1",
			expected: "1", // 应该返回原值
		},

		// 测试时间戳格式化
		{
			name: "timestamp - default format",
			config: LeadsFeatureConfig{
				Name: "login_time",
				Type: FeatureTypeTimestamp,
			},
			value:    int64(1640995200),     // 2022-01-01 00:00:00 UTC
			expected: "2022-01-01 08:00:00", // 转换为本地时间（假设UTC+8）
		},
		{
			name: "timestamp - custom format",
			config: LeadsFeatureConfig{
				Name:       "login_time",
				Type:       FeatureTypeTimestamp,
				TimeFormat: "2006-01-02 15:04",
			},
			value:    int64(1640995200),
			expected: "2022-01-01 08:00",
		},
		{
			name: "timestamp - string input",
			config: LeadsFeatureConfig{
				Name:       "login_time",
				Type:       FeatureTypeTimestamp,
				TimeFormat: "2006-01-02",
			},
			value:    "1640995200",
			expected: "2022-01-01",
		},
		{
			name: "timestamp - zero value",
			config: LeadsFeatureConfig{
				Name: "login_time",
				Type: FeatureTypeTimestamp,
			},
			value:    int64(0),
			expected: "", // 零值应该返回空字符串
		},
		{
			name: "timestamp - invalid string",
			config: LeadsFeatureConfig{
				Name: "login_time",
				Type: FeatureTypeTimestamp,
			},
			value:    "invalid",
			expected: "invalid", // 无法解析的字符串应该返回原值
		},

		// 测试百分比格式化
		{
			name: "percentage - default decimal place",
			config: LeadsFeatureConfig{
				Name: "conversion_rate",
				Type: FeatureTypePercentage,
			},
			value:    0.82,
			expected: "82.0%", // 默认1位小数
		},
		{
			name: "percentage - custom decimal place",
			config: LeadsFeatureConfig{
				Name:         "conversion_rate",
				Type:         FeatureTypePercentage,
				DecimalPlace: 2,
			},
			value:    0.8234,
			expected: "82.34%",
		},
		{
			name: "percentage - zero decimal place",
			config: LeadsFeatureConfig{
				Name:         "conversion_rate",
				Type:         FeatureTypePercentage,
				DecimalPlace: 0,
			},
			value:    0.82,
			expected: "82%",
		},
		{
			name: "percentage - string input",
			config: LeadsFeatureConfig{
				Name:         "conversion_rate",
				Type:         FeatureTypePercentage,
				DecimalPlace: 1,
			},
			value:    "0.75",
			expected: "75.0%",
		},
		{
			name: "percentage - integer input",
			config: LeadsFeatureConfig{
				Name:         "conversion_rate",
				Type:         FeatureTypePercentage,
				DecimalPlace: 1,
			},
			value:    1,
			expected: "100.0%",
		},
		{
			name: "percentage - invalid string",
			config: LeadsFeatureConfig{
				Name: "conversion_rate",
				Type: FeatureTypePercentage,
			},
			value:    "invalid",
			expected: "invalid", // 无法解析的字符串应该返回原值
		},

		// 测试 nil 值
		{
			name: "nil value",
			config: LeadsFeatureConfig{
				Name: "test_field",
				Type: FeatureTypeRaw,
			},
			value:    nil,
			expected: "", // nil 值应该返回空字符串
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatFeatureValue(ctx, tt.config, tt.value)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFormatFeatureValueEdgeCases(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	req, _ := http.NewRequestWithContext(context.Background(), "GET", "/", nil)
	ctx.Request = req

	// 测试未知的格式化类型
	t.Run("unknown format type", func(t *testing.T) {
		config := LeadsFeatureConfig{
			Name: "test_field",
			Type: "unknown_type",
		}
		result := FormatFeatureValue(ctx, config, "test_value")
		assert.Equal(t, "test_value", result) // 应该回退到原始值处理
	})

	// 测试负数小数位
	t.Run("negative decimal place", func(t *testing.T) {
		config := LeadsFeatureConfig{
			Name:         "conversion_rate",
			Type:         FeatureTypePercentage,
			DecimalPlace: -1,
		}
		result := FormatFeatureValue(ctx, config, 0.82)
		assert.Equal(t, "82.0%", result) // 负数应该被修正为默认值1
	})
}
