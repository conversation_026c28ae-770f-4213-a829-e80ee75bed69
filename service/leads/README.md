# LeadsFeatureConfig 字段格式化功能

## 概述

`LeadsFeatureConfig` 结构体已经重新设计，支持对不同类型字段的翻译和格式化功能。通过配置可以实现：

1. **枚举值翻译** - 将数字或代码转换为可读的文本
2. **时间戳格式化** - 将时间戳转换为指定格式的日期时间
3. **百分比格式化** - 将小数转换为百分比显示
4. **原始值显示** - 不做任何处理，直接显示原值

## 结构体定义

```go
type LeadsFeatureConfig struct {
    Name         string            `json:"name"`          // 字段名
    Desc         string            `json:"desc"`          // 字段描述
    Type         string            `json:"type"`          // 格式化类型
    Mapping      map[string]string `json:"mapping"`       // 枚举值映射
    TimeFormat   string            `json:"time_format"`   // 时间格式
    DecimalPlace int               `json:"decimal_place"` // 小数位数
}
```

## 支持的格式化类型

### 1. 原始值 (raw)
不做任何处理，直接显示原值。这是默认类型。

```json
{
  "name": "student_id",
  "desc": "学生ID", 
  "type": "raw"
}
```

### 2. 枚举值翻译 (enum)
将数字或代码转换为可读的文本。

```json
{
  "name": "trans_level",
  "desc": "转化等级",
  "type": "enum",
  "mapping": {
    "0": "新",
    "1": "高", 
    "2": "中",
    "3": "低"
  }
}
```

**示例**：
- 输入值：`"1"` → 输出：`"高"`
- 输入值：`"5"` (未配置) → 输出：`"5"` (返回原值)

### 3. 时间戳格式化 (timestamp)
将时间戳转换为指定格式的日期时间。

```json
{
  "name": "last_login_time",
  "desc": "最后登录时间",
  "type": "timestamp",
  "time_format": "2006-01-02 15:04"
}
```

**支持的输入类型**：
- `int64`, `int`, `float64` - 直接作为时间戳
- `string` - 尝试解析为时间戳

**时间格式**：
- 如果未指定 `time_format`，默认使用 `"2006-01-02 15:04:05"`
- 使用 Go 的时间格式规范

**示例**：
- 输入值：`1640995200` → 输出：`"2022-01-01 08:00"`
- 输入值：`0` → 输出：`""` (空字符串)

### 4. 百分比格式化 (percentage)
将小数转换为百分比显示。

```json
{
  "name": "conversion_rate",
  "desc": "转化率",
  "type": "percentage", 
  "decimal_place": 1
}
```

**支持的输入类型**：
- `float64`, `float32`, `int64`, `int` - 直接转换
- `string` - 尝试解析为浮点数

**小数位数**：
- 如果未指定 `decimal_place` 或为负数，默认使用 1 位小数
- 设置为 0 表示不显示小数

**示例**：
- 输入值：`0.82` + `decimal_place: 1` → 输出：`"82.0%"`
- 输入值：`0.8234` + `decimal_place: 2` → 输出：`"82.34%"`
- 输入值：`0.82` + `decimal_place: 0` → 输出：`"82%"`

## 使用方法

### 1. 配置示例

参考 `example_config.json` 文件中的完整配置示例。

### 2. 代码使用

在 `buildFeatures` 函数中，原来的简单字符串转换：

```go
// 旧代码
valueStr = fmt.Sprintf("%v", value)
```

已经替换为使用新的格式化函数：

```go
// 新代码
valueStr = FormatFeatureValue(ctx, config, value)
```

### 3. 手动调用格式化函数

```go
// 创建配置
config := LeadsFeatureConfig{
    Name: "conversion_rate",
    Type: "percentage",
    DecimalPlace: 1,
}

// 格式化值
result := FormatFeatureValue(ctx, config, 0.82)
// result = "82.0%"
```

## 错误处理

1. **类型转换失败**：如果无法将输入值转换为目标类型，会记录警告日志并返回原值
2. **配置缺失**：如果枚举类型缺少 mapping 配置，会记录警告日志并返回原值
3. **未知格式类型**：如果指定了不支持的格式类型，会回退到原始值处理
4. **nil 值**：输入值为 nil 时返回空字符串

## 向后兼容性

- 如果 `Type` 字段为空，默认使用 `"raw"` 类型
- 现有配置不会因为新增字段而出错
- 新增的字段都有合理的默认值

## 测试

运行测试以验证功能：

```bash
go test ./service/leads -v
```

测试覆盖了所有格式化类型的正常情况、边界情况和错误情况。
