package delayer

import (
	"assistantdeskgo/api/agg"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"time"
)

type TouchCallTimeData struct {
	DeviceId   int64  `json:"deviceUid"`
	CallId     int64  `json:"callId"`
	StartTime  int64  `json:"startTime"`
	CourseId   int64  `json:"courseId"`
	StudentUid int64  `json:"studentUid"`
	ClueId     string `json:"clueId"`
}

func (o *TouchCallTimeData) Consumer(ctx *gin.Context, input DelayerTaskMessage) (err error) {
	touchInfo := &TouchCallTimeData{}
	err = jsoniter.UnmarshalFromString(input.Content, touchInfo)
	if err != nil {
		zlog.Warnf(ctx, "once_task_delayer_consumer got split once task message failed, err: %+v", err)
		return err
	}

	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: touchInfo.DeviceId,
	}
	publicSeaOpLog, err := tblPublicSeaOpLog.GetListFirstByCond(ctx, map[string]interface{}{"clue_id": touchInfo.ClueId}, 0, 1, "last_access_time desc")
	if err != nil {
		zlog.Warnf(ctx, "delayer mqconsume TblPublicSeaOpLog GetListByCond err : %v", err)
		return err
	}
	if len(publicSeaOpLog) > 0 {
		currentTime := time.Now().Unix()
		twoHoursAgo := currentTime - defines.PublicSeaTouchExpireTime
		if publicSeaOpLog[0].LastAccessTime <= twoHoursAgo {
			// 释放线索锁定状态
			req := agg.RefreshLockStatusReq{
				StudentUid: touchInfo.StudentUid,
				CourseId:   touchInfo.CourseId,
				LockStatus: agg.LockStatusOff,
			}
			resp, ex := agg.RefreshLockStatus(ctx, req)
			if ex != nil {
				err = ex
				return
			}
			if !resp.Result {
				err = errors.New(fmt.Sprintf("数据更新失败,req:%+v", req))
				return
			}
		}
	}
	return
}
