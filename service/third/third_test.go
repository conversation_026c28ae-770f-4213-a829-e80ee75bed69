package third

import (
	dtofeedbackv2 "assistantdeskgo/dto/feedbackv2"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestDingBot(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/fudao_bzr/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	notFoundInfo := dtofeedbackv2.PointTargetExplainConfNotFoundInfo{
		CourseID:   111,
		CourseName: "测试",
		CpuID:      33,
		PointID:    10,
		PointName:  "因式分解",
		RightFrac:  0.92,
	}
	title := "知识目标解读话术未查到"
	text := "### 知识目标解读话术未查到\n\n" +
		fmt.Sprintf("课程id: %d\n\n", notFoundInfo.CourseID) +
		fmt.Sprintf("知识目标id:%d\n\n", notFoundInfo.PointID) +
		fmt.Sprintf("课程cpuId:%d\n\n", notFoundInfo.CpuID) +
		fmt.Sprintf("课程名称: %s\n\n", notFoundInfo.CourseName) +
		fmt.Sprintf("知识目标名称：%s\n\n", notFoundInfo.PointName) +
		fmt.Sprintf("互动题正确率：%f", notFoundInfo.RightFrac)
	bot := DingBot{}
	_ = bot.Init(ctx, "feedbackV2")
	ret, _ := bot.SendMarkdownMsg(title, text)
	fmt.Println(ret)
}
