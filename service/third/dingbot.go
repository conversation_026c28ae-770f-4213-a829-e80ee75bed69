package third

import (
	"assistantdeskgo/api/mercury"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"io"
	"net/http"
	neturl "net/url"
	"strings"
	"time"
)

// DingBot 钉钉机器人，开发文档：https://open.dingtalk.com/document/orgapp/custom-robots-send-group-messages
type DingBot struct {
	AccessToken string `json:"accessToken"`
	Secret      string `json:"secret"`
	ctx         *gin.Context
}

type DingBotConfItem struct {
	AccessToken string `json:"accessToken"`
	Secret      string `json:"secret"`
	Describe    string `json:"describe"`
}

type DingSendResult struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type DingActionBtn struct {
	Title         string `json:"title"`
	Url           string `json:"url"`
	OpenInBrowser bool   `json:"openInBrowser"` // 默认在钉钉右侧打开
}

const (
	BtnOrientationVertical   = "0" // 按钮竖直排列
	BtnOrientationHorizontal = "1" // 按钮横向排列
)

func (bot *DingBot) Init(ctx *gin.Context, name string) (err error) {
	bot.ctx = ctx
	ret := make(map[string]DingBotConfItem)
	err = mercury.GetConfigForJson(ctx, mercury.ConfigKeyForDingBot, mercury.DefaultExpireTime, &ret)
	if err != nil {
		zlog.Warnf(ctx, "DingBot GetConfigForJson fai,key:%+v,err:%+v", mercury.ConfigKeyForDingBot, err)
		return
	}
	if _conf, ok := ret[name]; ok {
		bot.AccessToken = _conf.AccessToken
		bot.Secret = _conf.Secret
	} else {
		err = errors.New(fmt.Sprintf("no ding bot config for name: %+v", name))
		return
	}
	return
}

func (bot *DingBot) getSign() (timestamp, sign string) {
	timestamp = fmt.Sprintf("%d", time.Now().UnixMilli())
	secret := bot.Secret
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(stringToSign))
	hmacCode := mac.Sum(nil)
	b64Sign := base64.StdEncoding.EncodeToString(hmacCode)
	sign = neturl.QueryEscape(b64Sign)
	return
}

func (bot *DingBot) sendDingReq(reqBody map[string]interface{}) (ret DingSendResult, err error) {
	timestamp, sign := bot.getSign()
	url := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=%s&timestamp=%s&sign=%s", bot.AccessToken, timestamp, sign)
	jsonData, _err := jsoniter.Marshal(reqBody)
	if _err != nil {
		zlog.Infof(bot.ctx, "jsoniter.Marshal error! data:%+v, err:%+v", reqBody, _err)
		return ret, _err
	}
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	req, _err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if _err != nil {
		zlog.Infof(bot.ctx, "http.NewRequest error! err:%+v", _err)
		return ret, _err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, _err := client.Do(req)
	if _err != nil {
		zlog.Infof(bot.ctx, "send ding message error! req:%+v, err:%+v", req, _err)
		return ret, _err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		zlog.Infof(bot.ctx, "send ding message resp error! req:%+v, resp:%+v", req, resp)
		return ret, errors.New("send ding message fail!")
	}

	if err = jsoniter.NewDecoder(resp.Body).Decode(&ret); err != nil {
		zlog.Infof(bot.ctx, "ding message decode error! req:%+v, resp:%+v, err:%+v", req, resp, err)
		return ret, err
	}
	return
}

func (bot *DingBot) SendTextMsg(text string) (ret DingSendResult, err error) {
	if text == "" {
		return
	}

	reqBody := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": text,
		},
	}

	return bot.sendDingReq(reqBody)
}

func (bot *DingBot) SendMarkdownMsg(title, text string) (ret DingSendResult, err error) {
	if text == "" {
		return
	}

	reqBody := map[string]interface{}{
		"msgtype": "markdown",
		"markdown": map[string]string{
			"title": title, // 首屏会话透出的展示内容。注意：不在消息体重展示
			"text":  text,  // 注意：Markdown中两个\n才换行
		},
	}

	return bot.sendDingReq(reqBody)
}

func (bot *DingBot) SendActionCardMsg(title, text string, btnList []DingActionBtn, btnOrientation string) (ret DingSendResult, err error) {
	if text == "" {
		return
	}
	if btnOrientation == "" {
		btnOrientation = BtnOrientationHorizontal
	}

	btns := make([]map[string]string, 0)
	for _, btn := range btnList {
		url := btn.Url
		if btn.OpenInBrowser {
			if strings.Contains(url, "://") {
				url = neturl.QueryEscape(url)
			}
			url = fmt.Sprintf("dingtalk://dingtalkclient/page/link?url=%s&pc_slide=false", url)
		}
		btns = append(btns, map[string]string{
			"title":     btn.Title,
			"actionURL": url,
		})
	}
	reqBody := map[string]interface{}{
		"msgtype": "actionCard",
		"actionCard": map[string]interface{}{
			"title":          title, // 首屏会话透出的展示内容。注意：不在消息体重展示
			"text":           text,  // Markdown格式。注意：Markdown中两个\n才换行
			"btnOrientation": btnOrientation,
			"btns":           btns,
		},
	}

	return bot.sendDingReq(reqBody)
}
