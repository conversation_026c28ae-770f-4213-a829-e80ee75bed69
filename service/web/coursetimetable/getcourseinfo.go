package coursetimetable

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtocoursetimetable"
	golibutils "git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetTeacherInfo(ctx *gin.Context, req dtocoursetimetable.GetTeacherInfoReq) (rsp *dtocoursetimetable.GetTeacherInfoRsp, err error) {
	var courseId int
	if courseId, err = golibutils.DecodeUid(req.CourseId); err != nil {
		zlog.Infof(ctx, "GetTeacherInfoReq decode uid failed, courseId: %s, err: %+v", req.CourseId, err)
		return nil, components.ErrorParamInvalid
	}

	courseFields := []string{"courseId", "courseName", "teacherUids"}
	lessonFields := []string{"lessonId"}

	var courseInfo dal.CourseLessonInfo
	// 获取课程信息
	if courseInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, int64(courseId), courseFields, lessonFields); err != nil {
		zlog.Warnf(ctx, "GetTeacherInfoReq 获取课程信息失败, courseId: %d, err: %+v", courseId, err)
		return nil, components.ErrorAPIGetCourseInfo
	}

	if len(courseInfo.TeacherUids) == 0 {
		zlog.Warnf(ctx, "GetTeacherInfoReq 获取课程老师信息失败, courseId: %d, courseInfo: %+v", courseId, courseInfo)
		return nil, components.ErrorAPIGetCourseInfo
	}

	teacherFields := []string{"teacherUid", "teacherName", "teacherAvatar"}
	teacherMap, err := dau.GetTeachers(ctx, courseInfo.TeacherUids, teacherFields)
	if err != nil {
		zlog.Warnf(ctx, "GetTeacherInfoReq 获取课程老师信息失败, courseId: %d, courseInfo: %+v", courseId, courseInfo)
		return nil, components.ErrorAPIGetCourseInfo
	}

	rsp = &dtocoursetimetable.GetTeacherInfoRsp{
		CourseName:  courseInfo.CourseName,
		TeacherName: "",
		AvatarUrl:   "",
	}

	for _, teacherId := range courseInfo.TeacherUids {
		if teacherInfo, ok := teacherMap[teacherId]; ok && teacherInfo.TeacherUid > 0 {
			rsp.TeacherName = teacherInfo.TeacherName
			rsp.AvatarUrl = teacherInfo.TeacherAvatar
			break
		}
	}

	return
}
