package coursetimetable

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/passport"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtocoursetimetable"
	"assistantdeskgo/middleware"
	"assistantdeskgo/utils"
	golibutils "git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func GetCourseTimeTableWeek(ctx *gin.Context, req dtocoursetimetable.GetCourseTimeTableReq) (rsp *dtocoursetimetable.GetCourseTimeTableRsp, err error) {
	var studentInfo *passport.SafCheckLoginRsp
	studentInfo, err = middleware.GetLoginStudentInfo(ctx)
	if err != nil {
		return nil, components.ErrorUserNotLogin.Wrap(err)
	}

	getCourseTimeTableReq := assistantdesk.GetCourseTimeTableReq{
		StartDay:   utils.GetMonday(time.Now()).Format("2006-01-02"),
		EndDay:     time.Now().AddDate(100, 0, 0).Format("2006-01-02"), // 默认往后不限制
		StudentUid: studentInfo.Uid,
	}
	if len(req.CurrentDay) > 0 {
		currentDay, timeErr := time.Parse("2006-01-02", req.CurrentDay)
		if timeErr != nil {
			zlog.Warnf(ctx, "GetCourseTimeTableWeek  params error, params: %+v", req)
			return nil, components.ErrorParamInvalid.Sprintf("时间格式错误")
		}
		getCourseTimeTableReq.StartDay = utils.GetMonday(currentDay).Format("2006-01-02")
		getCourseTimeTableReq.EndDay = utils.GetSunday(currentDay).Format("2006-01-02")
	}

	ret, err := assistantdesk.GetCourseTimeTable(ctx, getCourseTimeTableReq)
	if err != nil {
		return nil, err
	}

	// 首次进入定位到第一个有课程的日期
	nowDay := time.Now().Format("2006-01-02")
	currentDay := req.CurrentDay
	if len(currentDay) == 0 && len(ret.CourseDays) > 0 {
		for _, day := range ret.CourseDays {
			if day < nowDay {
				continue
			}
			currentDay = day
			break
		}
	}

	if len(currentDay) == 0 {
		currentDay = nowDay
	}

	currentTime, err := time.Parse("2006-01-02", currentDay)
	if err != nil {
		return nil, err
	}
	endTime := currentTime.Add(24*time.Hour).Unix() - 1
	startTime := currentTime.Unix()

	courseList := make([]dtocoursetimetable.CourseInfo, 0)
	var courseIdStr string
	for _, courseItem := range ret.CourseList {
		courseIdStr, err = golibutils.EncodeUid(int(courseItem.CourseId))
		if err != nil {
			zlog.Warnf(ctx, "GetCourseTimeTableWeek courseId encode err, courseInfo: %+v , studentUid: %d , err: %+v", courseItem, studentInfo.Uid, err)
			continue
		}

		if courseItem.StartTime >= startTime && courseItem.EndTime <= endTime {
			courseInfo := dtocoursetimetable.CourseInfo{
				CourseName:  courseItem.CourseName,
				CourseId:    courseIdStr,
				LessonName:  courseItem.LessonName,
				StartTime:   courseItem.StartTime,
				EndTime:     courseItem.EndTime,
				SubjectName: courseItem.SubjectName,
				SubjectId:   courseItem.SubjectID,
			}
			courseList = append(courseList, courseInfo)
		}
	}

	rsp = &dtocoursetimetable.GetCourseTimeTableRsp{
		CourseList: courseList,
		CourseDays: ret.CourseDays,
		CurrentDay: currentDay,
	}
	return
}
