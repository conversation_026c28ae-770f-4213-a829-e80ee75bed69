package device

import (
	"assistantdeskgo/dto/dtodevice"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetConfigForString(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	//helpers.InitResource(engine)
	//defer helpers.Release()

	req := dtodevice.GetUserDeviceListByCourseReq{}
	req.PeronUid = 3000034585
	req.CourseId = 518434

	ret, err := GetUserDeviceListByCourse(ctx, req)
	fmt.Println(ret, err)
}
