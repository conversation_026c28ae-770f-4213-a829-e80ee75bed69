package device

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/dto/dtodevice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
)

func GetUserDeviceListByCourse(ctx *gin.Context, req dtodevice.GetUserDeviceListByCourseReq) (rsp dtodevice.GetUserDeviceListByCourseRsp, err error) {
	//査老师设备
	staffMap, err := mesh.GetStaffInfoList(ctx, []int64{req.PeronUid})
	if err != nil {
		return
	}
	staff, ok := staffMap[utils.ToString(req.PeronUid)]
	if !ok {
		return
	}
	if staff.Mail == "" {
		return
	}

	deviceList, err := mesh.GetDeviceListByStaffMail(ctx, staff.Mail)
	if err != nil {
		return
	}
	if len(deviceList) == 0 {
		return
	}

	courseBindList, err := tower.GetCourseBindByCourseIds(ctx, []int64{req.CourseId})
	if err != nil {
		return
	}
	if len(courseBindList.CourseBindData) == 0 {
		return
	}
	rsp = dtodevice.GetUserDeviceListByCourseRsp{}
	for _, deviceInfo := range deviceList {
		find := false
		for _, courseBind := range courseBindList.CourseBindData {
			for _, bind := range courseBind {
				if find {
					break
				}
				if int64(deviceInfo.DeviceUid) == bind.DeviceUid {
					find = true
				}
			}

		}
		if find {
			rsp.DeviceList = append(rsp.DeviceList, dtodevice.GetUserDeviceListByCourseDevice{
				Id:      utils.ToString(deviceInfo.DeviceUid),
				KpUid:   utils.ToString(deviceInfo.DeviceUid),
				WxPhone: deviceInfo.Phone,
			})
		}
	}

	return
}
