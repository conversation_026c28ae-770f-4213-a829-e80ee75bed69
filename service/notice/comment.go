package notice

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models/notice"
	"assistantdeskgo/utils"
	"fmt"
	"sort"
	"sync"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetCommentList 获取评论列表及统计信息（按照精选或评论时间过滤、时间排序）
func GetCommentList(ctx *gin.Context, param dtonotice.CommentListParam) (rsp dtonotice.NoticeCommentListRsp, err error) {
	// 1. 构建一级评论查询条件
	conds := map[string]interface{}{
		"notice_id": param.NoticeId,
		"parent_id": 0, // 只查询一级评论
	}

	// 2. 时间范围筛选
	now := time.Now()
	var startTime, endTime int64
	switch param.TimeRange {
	case defines.TimeRangeLast7Days:
		startTime = now.AddDate(0, 0, -7).Unix()
	case defines.TimeRangeToday:
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()
	case defines.TimeRangeYesterday:
		yesterday := now.AddDate(0, 0, -1)
		startTime = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, now.Location()).Unix()
		endTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()
	case defines.TimeRangeBeforeYesterday:
		beforeYesterday := now.AddDate(0, 0, -2)
		yesterday := now.AddDate(0, 0, -1)
		startTime = time.Date(beforeYesterday.Year(), beforeYesterday.Month(), beforeYesterday.Day(), 0, 0, 0, 0, now.Location()).Unix()
		endTime = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, now.Location()).Unix()
	}

	// 3. 获取一级评论列表
	rootComments, err := notice.SystemNoticeCommentDao.ListByCreateTime(ctx, conds, startTime, endTime, nil)
	if err != nil || len(rootComments) == 0 {
		return
	}

	// 4. 统计数量和筛选评论
	var totalCount = len(rootComments)
	var selectedCount int
	var comments = make([]notice.SystemNoticeComment, 0, totalCount)

	isSelectedFilter := param.FilterType == defines.CommentTypeSeleted
	for _, comment := range rootComments {
		if comment.IsSelected {
			selectedCount++
		}
		// 只有在不需要筛选精选评论，或者评论是精选的情况下才添加
		if !isSelectedFilter || comment.IsSelected {
			comments = append(comments, comment)
		}
	}

	// 5. 获取一级评论的所有 ID
	var rootIds []int64
	for _, comment := range rootComments {
		rootIds = append(rootIds, comment.Id)
	}

	// 6. 获取所有子评论
	childComments, err := notice.SystemNoticeCommentDao.ListByTopIds(ctx, param.NoticeId, rootIds, nil)
	if err != nil {
		return
	}

	// 7. 合并一级评论和子评论
	allComments := []notice.SystemNoticeComment{}
	allComments = append(allComments, comments...)
	if len(childComments) > 0 {
		allComments = append(allComments, childComments...)
	}

	// 8. 构建评论树
	commentTree, err := buildCommentTree(ctx, allComments)
	if err != nil {
		return
	}

	// 9. 根据排序类型对一级评论进行排序
	sort.Slice(commentTree, func(i, j int) bool {
		if param.SortType == defines.CommentSortByTimeDesc {
			return (commentTree)[i].CommentTime > (commentTree)[j].CommentTime
		}
		// 按点赞数排序，点赞数相同时按时间倒序
		if (commentTree)[i].CommentLikeNum == (commentTree)[j].CommentLikeNum {
			return (commentTree)[i].CommentTime > (commentTree)[j].CommentTime
		}
		return (commentTree)[i].CommentLikeNum > (commentTree)[j].CommentLikeNum
	})

	rsp.List = commentTree
	rsp.TotalCount = totalCount
	rsp.SelectedCount = selectedCount
	return rsp, nil
}

// SetCommentSelected 设置精选评论
func SetCommentSelected(ctx *gin.Context, commentId int64, isSelected bool) (rsp *dtonotice.CommonRsp, err error) {
	err = notice.SystemNoticeCommentDao.UpdateById(ctx, commentId, map[string]interface{}{
		"is_selected": isSelected,
	}, nil)
	return
}

// DeleteComment 删除评论（及其回复）
func DeleteComment(ctx *gin.Context, commentId int64) (result *dtonotice.CommonRsp, err error) {
	// 开启事务
	tx := helpers.MysqlClientFuDao.Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// 获取评论信息
	comment, err := notice.SystemNoticeCommentDao.GetById(ctx, commentId, tx)
	if err != nil {
		return
	}

	// 如果是一级评论，需要删除所有子评论
	if comment.ParentId == 0 {
		err = notice.SystemNoticeCommentDao.Delete(ctx, map[string]interface{}{
			"top_id": commentId,
		}, tx)
		if err != nil {
			return
		}
	}

	// 删除评论本身
	err = notice.SystemNoticeCommentDao.Delete(ctx, map[string]interface{}{
		"id": commentId,
	}, tx)
	if err != nil {
		return
	}

	// 删除评论相关的点赞记录
	err = notice.SystemNoticeCommentLikeDao.Delete(ctx, map[string]interface{}{
		"comment_id": commentId,
	}, tx)
	if err != nil {
		return
	}

	return
}

// ReplyNoticeComment 通知评论
func ReplyNoticeComment(ctx *gin.Context, param dtonotice.UserNoticeCommentParam, isManager bool) (result dtonotice.IdRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	now := time.Now().Unix()
	systemNoticeComment := notice.SystemNoticeComment{
		NoticeId:   param.NoticeId,
		Uid:        int64(userInfo.UserId),
		Content:    param.Comment,
		CreateTime: now,
		UpdateTime: now,
	}

	// 判断是新增评论还是回复评论
	if param.ParentId > 0 {
		// 回复评论，需要检查父评论是否存在
		parent, err := notice.SystemNoticeCommentDao.GetById(ctx, param.ParentId, nil)
		if err != nil {
			return result, fmt.Errorf("父评论不存在")
		}
		systemNoticeComment.ParentId = param.ParentId

		// 设置 TopId：如果父评论是一级评论，TopId 就是父评论 ID；否则继承父评论的 TopId
		if parent.ParentId == 0 {
			systemNoticeComment.TopId = parent.Id
		} else {
			systemNoticeComment.TopId = parent.TopId
		}
	}

	// 管理员回复评论自动精选
	if isManager {
		systemNoticeComment.IsSelected = true
	}

	// 插入评论
	result.Id, err = notice.SystemNoticeCommentDao.Insert(ctx, systemNoticeComment, nil)
	if err != nil {
		return
	}

	return
}

// GetNoticeListForComment 评论管理页通知列表
func GetNoticeListForComment(ctx *gin.Context) (rsp []dtonotice.UserNoticeRsp, err error) {
	// 获取已发布通知列表
	list, err := notice.SystemNoticeDao.List(ctx, map[string]interface{}{
		"status": defines.SystemNoticeStatusPublish,
	}, nil)
	if err != nil || len(*list) == 0 {
		return
	}

	for _, sn := range *list {
		// 获取通知统计信息
		noticeStatistics, err := getUserNoticeStatistics(ctx, sn, false)
		if err != nil {
			return rsp, err
		}

		// 获取通知最后一条一级评论时间
		lastComment, err := notice.SystemNoticeCommentDao.FindLastTopComment(ctx, sn.Id, nil)
		if err != nil {
			return rsp, err
		}

		userNotice := dtonotice.UserNoticeRsp{
			Id:            sn.Id,
			Title:         sn.Title,
			ClassID:       sn.ClassID,
			ProfilePhoto:  sn.ProfilePhoto,
			Abstract:      sn.Abstract,
			PublishTime:   sn.PublishTime,
			LastCommentAt: lastComment.CreateTime,
			Statistics:    noticeStatistics,
		}

		rsp = append(rsp, userNotice)
	}

	// 按照最后评论时间倒序
	sort.Slice(rsp, func(i, j int) bool {
		return (rsp)[i].LastCommentAt > (rsp)[j].LastCommentAt
	})

	return
}

// 构建评论树
func buildCommentTree(ctx *gin.Context, comments []notice.SystemNoticeComment) (result []dtonotice.NoticeComment, err error) {
	if len(comments) == 0 {
		return
	}

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 收集所有评论者的 Uid
	uidSet := make(map[int64]struct{})
	for _, comment := range comments {
		if comment.Uid > 0 { // 避免无效 Uid
			uidSet[comment.Uid] = struct{}{}
		}
	}

	// 转换 Set 为 Slice
	uids := make([]int64, 0, len(uidSet))
	for uid := range uidSet {
		uids = append(uids, uid)
	}

	// 批量获取用户信息
	uidToNameMap, _ := getUserNameMapByUids(ctx, uids)
	if len(uidToNameMap) == 0 {
		return result, fmt.Errorf("获取用户信息失败")
	}

	// 使用并发执行器处理评论数据
	commentMap := make(map[int64]*dtonotice.NoticeComment, 0)
	var rootComments []*dtonotice.NoticeComment
	var lock sync.Mutex
	executor := utils.NewConcurrentExecutor(ctx, 10, func(ctx *gin.Context, comment notice.SystemNoticeComment) error {
		// 从预先获取的 map 中查找用户名
		userName, ok := uidToNameMap[comment.Uid]
		if !ok {
			userName = "未知用户" // 或者其他默认名称
			zlog.Warnf(ctx, "buildCommentTree: Username not found for uid %d, using default.", comment.Uid)
		}

		// 获取点赞信息
		likeCount, err := notice.SystemNoticeCommentLikeDao.Count(ctx, map[string]interface{}{
			"comment_id": comment.Id}, nil)
		if err != nil {
			return err
		}

		// 获取当前用户是否点赞
		currentUidLikeCount, err := notice.SystemNoticeCommentLikeDao.Count(ctx, map[string]interface{}{
			"comment_id": comment.Id,
			"uid":        int64(userInfo.UserId),
		}, nil)
		if err != nil {
			return err
		}

		noticeComment := &dtonotice.NoticeComment{
			Id:              comment.Id,
			Comment:         comment.Content,
			CommentTime:     comment.CreateTime,
			CommentUser:     userName,
			CommentLikeNum:  likeCount,
			LikeFlag:        currentUidLikeCount > 0,
			IsSelected:      comment.IsSelected,
			ChildrenComment: []*dtonotice.NoticeComment{},
		}

		// 安全地更新 map
		lock.Lock()
		commentMap[comment.Id] = noticeComment
		if comment.ParentId == 0 {
			rootComments = append(rootComments, noticeComment)
		}
		lock.Unlock()
		return nil
	})

	if err = executor.Execute(comments); err != nil {
		return
	}

	// 构建父子关系
	for _, comment := range comments {
		noticeComment, noticeExists := commentMap[comment.Id]
		if !noticeExists {
			continue // 如果评论本身未成功处理，则跳过
		}

		if comment.ParentId > 0 {
			if parentComment, parentExists := commentMap[comment.ParentId]; parentExists {
				// 确保子评论也存在于 map 中
				parentComment.ChildrenComment = append(parentComment.ChildrenComment, noticeComment)
			} else {
				zlog.Warnf(ctx, "buildCommentTree: Parent comment %d not found for child comment %d", comment.ParentId, comment.Id)
			}
		}
	}

	// 对评论树进行排序
	sortCommentsRecursive(rootComments)

	// 转换为返回值类型
	for _, comment := range rootComments {
		result = append(result, *comment)
	}

	return
}

// 将评论树按照每一层的点赞数和时间进行排序
func sortCommentsRecursive(comments []*dtonotice.NoticeComment) {
	// 对当前层级的评论进行排序
	sort.Slice(comments, func(i, j int) bool {
		if comments[i].CommentLikeNum == comments[j].CommentLikeNum {
			return comments[i].CommentTime > comments[j].CommentTime
		}
		return comments[i].CommentLikeNum > comments[j].CommentLikeNum
	})

	// 递归对每个评论的子评论进行排序
	for _, comment := range comments {
		if len(comment.ChildrenComment) > 0 {
			sortCommentsRecursive(comment.ChildrenComment)
		}
	}
}

// getCommentsByNoticeId 获取通知的评论列表
func getCommentsByNoticeId(ctx *gin.Context, noticeId int64, isManager bool) (result []dtonotice.NoticeComment, err error) {
	var comments []notice.SystemNoticeComment
	if isManager {
		comments, err = notice.SystemNoticeCommentDao.List(ctx, map[string]interface{}{
			"notice_id": noticeId,
		}, nil)
		if err != nil {
			return result, err
		}
	} else {
		userInfo, err := middleware.GetLoginUserInfo(ctx)
		if err != nil {
			return result, err
		}

		comments, err = notice.SystemNoticeCommentDao.GetVisibleComments(ctx, noticeId, int64(userInfo.UserId), nil)
		if err != nil {
			return result, err
		}
	}

	return buildCommentTree(ctx, comments)
}
