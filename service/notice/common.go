package notice

import (
	"assistantdeskgo/conf"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetClassDict 获取通知类型字典
func GetClassDict() (map[int8]string, error) {
	return defines.ClassDict, nil
}

// GetBosTmpToken 获取bos临时token
func GetBosTmpToken(ctx *gin.Context) (result *dtonotice.BosSignRsp, err error) {
	// 获取临时凭证
	token, err := helpers.BosBucket.GetTempKeys(ctx, 30*time.Minute)
	if err != nil {
		return nil, err
	}
	return &dtonotice.BosSignRsp{
		Credentials: token,
		Bucket:      conf.RConf.Cos["bos"].Bucket,
		Domain:      strings.TrimPrefix(conf.RConf.Cos["bos"].Region, "https://"),
		Endpoint:    strings.TrimPrefix(conf.RConf.Cos["bos"].Region, "https://"),
		Pid:         uuid.New().String(),
	}, nil
}
