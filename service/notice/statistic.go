package notice

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models/notice"
	"encoding/json"
	"fmt"
	"unicode"

	"math/rand"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// 获取每条通知的统计结果（用户和管理视角）
func getUserNoticeStatistics(ctx *gin.Context, noticeInfo notice.SystemNotice, viewMock bool) (ns dtonotice.UserNoticeStatistics, err error) {
	noticeId := noticeInfo.Id

	// 用户视角：根据noticeId获取缓存；管理视角：不使用缓存
	cacheKey := getNoticeCacheKey(noticeId)
	if viewMock {
		cacheKey := getNoticeCacheKey(noticeId)
		cacheBytes, err := helpers.RedisClient.Get(ctx, cacheKey)
		if err == nil && cacheBytes != nil {
			cacheStr := string(cacheBytes)
			if err = json.Unmarshal([]byte(cacheStr), &ns); err == nil {
				return ns, nil
			}
			zlog.Warnf(ctx, "getUserNoticeStatistics unmarshal cache fail, key: %s, cache: %s, err: %+v",
				cacheKey, cacheStr, err)
		}
	}

	// 统计已读人数
	ns.ReadNum, err = notice.SystemNoticeUserDao.Count(ctx, map[string]interface{}{
		"notice_id":       noticeId,
		"is_content_read": true,
	}, nil)
	if err != nil {
		return
	}
	if viewMock {
		// 加上mock阅读数
		ns.ReadNum += noticeInfo.MockReadNum
	}

	// 获取阅读用户名称
	if ns.ReadNum > 0 {
		ns.ReadUsers, err = getNoticeReadUsers(ctx, noticeId, ns.ReadNum)
		if err != nil {
			return
		}
	}

	// 统计评论数
	ns.CommentNum, err = notice.SystemNoticeCommentDao.Count(ctx, map[string]interface{}{
		"notice_id": noticeId,
		"parent_id": "0",
	}, nil)
	if err != nil {
		return
	}
	if viewMock {
		// 加上mock评论数
		ns.CommentNum += noticeInfo.MockCommentNum
	}

	// 统计点赞数
	ns.LikeNum, err = notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, defines.NoticeContentType, defines.NoticeFeedbackLikeType, nil)
	if err != nil {
		return
	}
	if viewMock {
		// 加上mock点赞数
		ns.LikeNum += noticeInfo.MockContentLikeNum
	}

	// 用户视角加缓存，管理视角不缓存
	if viewMock {
		cacheData, err := json.Marshal(ns)
		if err != nil {
			zlog.Warnf(ctx, "getUserNoticeStatistics marshal data fail, err: %v", err)
		} else {
			err = fwyyutils.RunWithRetry(func(idx int) error {
				return helpers.RedisClient.SetEx(ctx, cacheKey, string(cacheData), 60)
			})
			if err != nil {
				zlog.Warnf(ctx, "getUserNoticeStatistics set cache fail, key: %s, err: %v", cacheKey, err)
			}
		}
	}

	return
}

// 获取每条通知的统计结果（管理者视角，统计内容会更多）
func getManagerNoticeStatistic(ctx *gin.Context, noticeId int64) (statistic *dtonotice.ManagerNoticeStatistics, err error) {
	statistic = &dtonotice.ManagerNoticeStatistics{}
	// 统计总接收人数
	totalReceiver, err := notice.SystemNoticeUserDao.Count(ctx, map[string]interface{}{
		"notice_id": noticeId,
	}, nil)
	if err != nil || totalReceiver == 0 {
		return
	}
	statistic.TotalReceiver = totalReceiver

	// 统计内容已读人数
	contentViewer, err := notice.SystemNoticeUserDao.Count(ctx, map[string]interface{}{
		"notice_id":       noticeId,
		"is_content_read": true,
	}, nil)
	if err != nil {
		return
	}
	statistic.TotalContentViewer = contentViewer

	// 统计视频已读人数
	videoViewer, err := notice.SystemNoticeUserDao.Count(ctx, map[string]interface{}{
		"notice_id":     noticeId,
		"is_video_read": true,
	}, nil)
	if err != nil {
		return
	}
	statistic.TotalVideoViewer = videoViewer

	// 统计内容点赞数
	contentLiker, err := notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, defines.NoticeContentType,
		defines.NoticeFeedbackLikeType, nil)
	if err != nil {
		return
	}
	statistic.TotalContentLiker = contentLiker

	// 统计内容点踩数
	contentDisLiker, err := notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, defines.NoticeContentType,
		defines.NoticeFeedbackUnLikeType, nil)
	if err != nil {
		return
	}
	statistic.TotalContentDisLiker = contentDisLiker

	// 统计视频点赞数
	videoLiker, err := notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, defines.NoticeVideoType,
		defines.NoticeFeedbackLikeType, nil)
	if err != nil {
		return
	}
	statistic.TotalVideoLiker = videoLiker

	// 统计视频点踩数
	videoDisLiker, err := notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, defines.NoticeVideoType,
		defines.NoticeFeedbackUnLikeType, nil)
	if err != nil {
		return
	}
	statistic.TotalVideoDisLiker = videoDisLiker

	// 计算各项比率
	if totalReceiver > 0 {
		// 计算内容查看率
		statistic.ContentViewRate = fmt.Sprintf("%.2f", float64(contentViewer)/float64(totalReceiver)*100) + "%"
		// 计算内容点赞率
		statistic.ContentLikeRate = fmt.Sprintf("%.2f", float64(contentLiker)/float64(totalReceiver)*100) + "%"
		// 计算内容点踩率
		statistic.ContentDisLikeRate = fmt.Sprintf("%.2f", float64(contentDisLiker)/float64(totalReceiver)*100) + "%"
		// 计算视频查看率
		statistic.VideoViewRate = fmt.Sprintf("%.2f", float64(videoViewer)/float64(totalReceiver)*100) + "%"
	} else {
		// 无人接收时，各项比率都为 0%
		statistic.ContentViewRate = "0.00%"
		statistic.ContentLikeRate = "0.00%"
		statistic.ContentDisLikeRate = "0.00%"
		statistic.VideoViewRate = "0.00%"
	}
	// 视频相关比率需要判断观看人数
	if videoViewer > 0 {
		// 计算视频点赞率
		statistic.VideoLikeRate = fmt.Sprintf("%.2f", float64(videoLiker)/float64(videoViewer)*100) + "%"
		// 计算视频点踩率
		statistic.VideoDisLikeRate = fmt.Sprintf("%.2f", float64(videoDisLiker)/float64(videoViewer)*100) + "%"
	} else {
		// 无人观看时，点赞率和点踩率都为 0%
		statistic.VideoLikeRate = "0.00%"
		statistic.VideoDisLikeRate = "0.00%"
	}

	return
}

func getNoticeReadUsers(ctx *gin.Context, noticeId int64, readNum int64) (readUsers []string, err error) {
	// 获取已读用户列表
	readList, err := notice.SystemNoticeUserDao.GetLastReadUser(ctx, noticeId, defines.MaxReadUserDisplay, nil)
	if err != nil {
		return
	}

	// 处理真实用户名
	readUsers, err = processRealUserNames(ctx, readList)
	if err != nil {
		return
	}

	if len(readList) < defines.MaxReadUserDisplay && readNum > int64(defines.MaxReadUserDisplay) {
		// 随机补充用户名至限制人数
		needMore := defines.MaxReadUserDisplay - len(readList)
		// 使用通知ID作为随机数种子，确保同一通知每次生成的随机结果一致
		r := rand.New(rand.NewSource(noticeId))
		for i := 0; i < needMore; i++ {
			randomIndex := r.Intn(len(defines.RandomUserNameList))
			readUsers = append(readUsers, defines.RandomUserNameList[randomIndex])
		}
	}
	return
}

// 处理真实用户名列表，提取每个用户名中最后一个非数字字符
func processRealUserNames(ctx *gin.Context, readList []notice.SystemNoticeUser) ([]string, error) {
	// 1. 收集所有需要查询的UID
	uids := make([]int64, 0, len(readList))
	for _, item := range readList {
		uids = append(uids, item.Uid)
	}

	if len(uids) == 0 {
		return []string{}, nil
	}

	staffInfoResp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
		StaffUid: uids,
	})

	if err != nil {
		return nil, err
	}

	var readUsers []string
	for _, staff := range staffInfoResp.List {
		name := staff.StaffName

		// 获取名字中最后一个非数字字符
		nameRunes := []rune(name)
		var lastChar string
		for i := len(nameRunes) - 1; i >= 0; i-- {
			r := nameRunes[i]
			if !unicode.IsDigit(r) && unicode.In(r, unicode.Han) {
				lastChar = string(r)
				break
			}
		}

		if lastChar != "" {
			readUsers = append(readUsers, lastChar)
		}
	}

	return readUsers, nil
}

func getNoticeFeedbackStats(ctx *gin.Context, noticeId int64, tp int) (stats *dtonotice.NoticeFeedbackStats, err error) {
	// 获取通知信息，用于获取mock数据
	noticeInfo, err := notice.SystemNoticeDao.GetById(ctx, noticeId, nil)
	if err != nil {
		return
	}

	// 统计点赞数
	likeCount, err := notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, tp,
		defines.NoticeFeedbackLikeType, nil)
	if err != nil {
		return
	}

	// 统计点踩数
	dislikeCount, err := notice.SystemNoticeUserLikeDao.Count(ctx, noticeId, tp,
		defines.NoticeFeedbackUnLikeType, nil)
	if err != nil {
		return
	}

	// 根据内容类型添加对应的模拟数据
	if tp == defines.NoticeContentType {
		likeCount += noticeInfo.MockContentLikeNum
		dislikeCount += noticeInfo.MockContentDisLikeNum
	} else if tp == defines.NoticeVideoType {
		likeCount += noticeInfo.MockVideoLikeNum
		dislikeCount += noticeInfo.MockVideoDisLikeNum
	}

	return &dtonotice.NoticeFeedbackStats{
		FeedbackLikeNum:    likeCount,
		FeedbackDisLikeNum: dislikeCount,
	}, nil
}

func getNoticeCommentCount(ctx *gin.Context, noticeId int64) (count int64, err error) {
	sn, err := notice.SystemNoticeDao.GetById(ctx, noticeId, nil)
	if err != nil {
		return
	}

	// 统计评论数
	commentCount, err := notice.SystemNoticeCommentDao.Count(ctx, map[string]interface{}{
		"notice_id": noticeId,
		"parent_id": "0",
	}, nil)
	if err != nil {
		return
	}
	return commentCount + sn.MockCommentNum, nil
}

func getNoticeCacheKey(noticeId int64) string {
	return fmt.Sprintf("%s_%d", "assistantdeskgo_notice_statistics", noticeId)
}
