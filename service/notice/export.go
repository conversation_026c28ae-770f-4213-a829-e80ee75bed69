package notice

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/models/notice"
	"assistantdeskgo/utils"
	"net/http"
	"sync"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// ExportNoticeList 导出通知列表
func ExportNoticeList(ctx *gin.Context) {
	// 1. 获取所有通知
	list, err := notice.SystemNoticeDao.List(ctx, nil, nil)
	if err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	// 2. 转换数据结构
	var resultList []dtonotice.SystemNotice
	var lock sync.Mutex

	// 3. 并发获取统计信息和用户信息
	executor := utils.NewConcurrentExecutor(ctx, 10, func(ctx *gin.Context, n notice.SystemNotice) error {
		// 3.1 获取统计信息
		statistic, err := getManagerNoticeStatistic(ctx, n.Id)
		if err != nil {
			zlog.Errorf(ctx, "get notice statistic failed, noticeId: %d, err: %v", n.Id, err)
			return err
		}

		// 3.2 获取发布者信息
		userinfo, err := getUserInfoByUid(ctx, n.PublisherUID)
		if err != nil {
			zlog.Errorf(ctx, "get user info failed, uid: %d, err: %v", n.PublisherUID, err)
			return err
		}

		// 3.3 组织信息处理
		var groupNames []string
		for _, org := range userinfo.Organization {
			groupNames = append(groupNames, org.GroupName)
		}

		systemNotice := dtonotice.SystemNotice{
			Id:             n.Id,
			Title:          n.Title,
			PublisherUid:   n.PublisherUID,
			PublisherName:  userinfo.StaffName,
			PublisherGroup: utils.JoinSlice(groupNames, ";", nil),
			ClassId:        n.ClassID,
			Status:         n.Status,
			PublishTime:    n.PublishTime,
			UpdateTime:     n.UpdateTime,
			Scope:          n.Scope,
			Statistics:     statistic,
		}

		lock.Lock()
		resultList = append(resultList, systemNotice)
		lock.Unlock()
		return nil
	})

	if err = executor.Execute(*list); err != nil {
		zlog.Errorf(ctx, "execute get notice detail failed, err: %v", err)
		return
	}

	// 构建表头
	headers := []string{
		"通知ID", "标题", "发布人ID", "发布人姓名", "发布人组织",
		"通知类型", "状态", "发布时间", "更新时间", "发送范围",
		"总接收人数", "图文浏览人数", "图文点赞人数", "图文点踩人数",
		"视频浏览人数", "视频点赞人数", "视频点踩人数",
		"图文浏览率", "图文点赞率", "图文点踩率",
		"视频浏览率", "视频点赞率", "视频点踩率",
	}

	// 4. 构建数据
	var records [][]string
	for _, item := range resultList {
		record := []string{
			cast.ToString(item.Id),
			item.Title,
			cast.ToString(item.PublisherUid),
			item.PublisherName,
			item.PublisherGroup,
			getNoticeClassName(item.ClassId),
			getNoticeStatusName(item.Status),
			utils.ChangeStampToYmdHms(item.PublishTime),
			utils.ChangeStampToYmdHms(item.UpdateTime),
			getNoticeScopeName(item.Scope),
			cast.ToString(item.Statistics.TotalReceiver),
			cast.ToString(item.Statistics.TotalContentViewer),
			cast.ToString(item.Statistics.TotalContentLiker),
			cast.ToString(item.Statistics.TotalContentDisLiker),
			cast.ToString(item.Statistics.TotalVideoViewer),
			cast.ToString(item.Statistics.TotalVideoLiker),
			cast.ToString(item.Statistics.TotalVideoDisLiker),
			item.Statistics.ContentViewRate,
			item.Statistics.ContentLikeRate,
			item.Statistics.ContentDisLikeRate,
			item.Statistics.VideoViewRate,
			item.Statistics.VideoLikeRate,
			item.Statistics.VideoDisLikeRate,
		}
		records = append(records, record)
	}

	// 导出Excel
	fileName := "通知列表_" + utils.ChangeStampToYmdHms(time.Now().Unix()) + ".xlsx"
	if err := utils.ExportExcel(ctx, fileName, headers, records); err != nil {
		zlog.Errorf(ctx, "export excel failed, err: %v", err)
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
}

// ExportNoticeUserList 导出通知名单详情
func ExportNoticeUserList(ctx *gin.Context, noticeId int64) {
	param := dtonotice.ReceiverFilterParam{
		NoticeId: noticeId,
	}

	// 批量导出所有数据
	sheets, err := batchExportData(ctx, param)
	if err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	// 导出Excel
	fileName := "通知名单详情_" + utils.ChangeStampToYmdHms(time.Now().Unix()) + ".xlsx"
	if err := utils.ExportMultiSheetExcel(ctx, fileName, sheets); err != nil {
		zlog.Errorf(ctx, "export excel failed, err: %v", err)
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
}

// 定义导出函数类型
type exportListFunc func(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (headers []string, records [][]string, err error)

// 定义导出配置
type exportConfig struct {
	SheetName    string
	ExportFunc   exportListFunc
	Type         int8
	FeedbackType int8
}

// 获取所有导出配置
func getExportConfigs() []exportConfig {
	return []exportConfig{
		{SheetName: "发送列表", ExportFunc: exportInformList},
		{SheetName: "阅读列表(图文)", ExportFunc: exportReadList, Type: defines.NoticeContentType},
		{SheetName: "未读列表(图文)", ExportFunc: exportUnReadList, Type: defines.NoticeContentType},
		{SheetName: "点赞列表(图文)", ExportFunc: exportFeedbackList, Type: defines.NoticeContentType, FeedbackType: defines.NoticeFeedbackLikeType},
		{SheetName: "点踩列表(图文)", ExportFunc: exportFeedbackList, Type: defines.NoticeContentType, FeedbackType: defines.NoticeFeedbackUnLikeType},
		{SheetName: "阅读列表(视频)", ExportFunc: exportReadList, Type: defines.NoticeVideoType},
		{SheetName: "未读列表(视频)", ExportFunc: exportUnReadList, Type: defines.NoticeVideoType},
		{SheetName: "点赞列表(视频)", ExportFunc: exportFeedbackList, Type: defines.NoticeVideoType, FeedbackType: defines.NoticeFeedbackLikeType},
		{SheetName: "点踩列表(视频)", ExportFunc: exportFeedbackList, Type: defines.NoticeVideoType, FeedbackType: defines.NoticeFeedbackUnLikeType},
	}
}

// 批量导出数据
func batchExportData(ctx *gin.Context, param dtonotice.ReceiverFilterParam) ([]utils.ExcelSheet, error) {
	var sheets []utils.ExcelSheet

	// 遍历配置执行导出
	for _, config := range getExportConfigs() {
		param.Type = config.Type
		param.FeedbackType = config.FeedbackType
		headers, records, err := config.ExportFunc(ctx, param)
		if err != nil {
			zlog.Errorf(ctx, "export %s failed, err: %v", config.SheetName, err)
			return nil, err
		}

		sheets = append(sheets, utils.ExcelSheet{
			SheetName: config.SheetName,
			Headers:   headers,
			Records:   records,
		})
	}

	return sheets, nil
}

func exportInformList(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (headers []string, records [][]string, err error) {
	informList, err := InformList(ctx, param)
	if err != nil {
		return
	}
	headers = []string{
		"姓名", "UID", "业务线", "业务地区", "学部",
		"学科", "年级", "部门ID", "部门路径", "通知送达时间",
	}
	for _, user := range informList.List {
		record := []string{
			user.Name,
			cast.ToString(user.Uid),
			user.ProductLineName,
			user.Region,
			user.GradeLevel,
			user.Subject,
			user.Grade,
			user.GroupId,
			user.GroupPath,
			utils.ChangeStampToYmdHms(user.SendTime),
		}
		records = append(records, record)
	}
	return
}

func exportReadList(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (headers []string, records [][]string, err error) {
	readList, err := Readlist(ctx, param)
	if err != nil {
		return
	}
	if param.Type == defines.NoticeContentType {
		headers = []string{
			"姓名", "UID", "业务线", "业务地区", "学部",
			"学科", "年级", "部门ID", "部门路径", "最近浏览时间", "阅读时长(时分秒)", "阅读时长(秒)",
		}
	} else if param.Type == defines.NoticeVideoType {
		headers = []string{
			"姓名", "UID", "业务线", "业务地区", "学部",
			"学科", "年级", "部门ID", "部门路径", "最近浏览时间", "观看时长(时分秒)", "观看时长(秒)",
		}
	}
	for _, user := range readList.List {
		record := []string{
			user.Name,
			cast.ToString(user.Uid),
			user.ProductLineName,
			user.Region,
			user.GradeLevel,
			user.Subject,
			user.Grade,
			user.GroupId,
			user.GroupPath,
			utils.ChangeStampToYmdHms(user.ReadTime),
			user.BrowseDurationFormat,
			cast.ToString(user.BrowseDuration),
		}
		records = append(records, record)
	}
	return
}

func exportUnReadList(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (headers []string, records [][]string, err error) {
	unReadList, err := UnReadlist(ctx, param)
	if err != nil {
		return
	}
	headers = []string{
		"姓名", "UID", "业务线", "业务地区", "学部",
		"学科", "年级", "部门ID", "部门路径", "通知送达时间",
	}
	for _, user := range unReadList.List {
		record := []string{
			user.Name,
			cast.ToString(user.Uid),
			user.ProductLineName,
			user.Region,
			user.GradeLevel,
			user.Subject,
			user.Grade,
			user.GroupId,
			user.GroupPath,
			utils.ChangeStampToYmdHms(user.SendTime),
		}
		records = append(records, record)
	}
	return
}

func exportFeedbackList(ctx *gin.Context, param dtonotice.ReceiverFilterParam) (headers []string, records [][]string, err error) {
	likeList, err := Feedbacklist(ctx, param)
	if err != nil {
		return
	}
	if param.Type == defines.NoticeContentType {
		if param.FeedbackType == defines.NoticeFeedbackLikeType {
			headers = []string{
				"姓名", "UID", "业务线", "业务地区", "学部",
				"学科", "年级", "部门ID", "部门路径", "点赞时间", "点赞反馈", "阅读时长(时分秒)", "阅读时长(秒)",
			}
		} else if param.FeedbackType == defines.NoticeFeedbackUnLikeType {
			headers = []string{
				"姓名", "UID", "业务线", "业务地区", "学部",
				"学科", "年级", "部门ID", "部门路径", "点踩时间", "点踩反馈", "阅读时长(时分秒)", "阅读时长(秒)",
			}
		}
	} else if param.Type == defines.NoticeVideoType {
		if param.FeedbackType == defines.NoticeFeedbackLikeType {
			headers = []string{
				"姓名", "UID", "业务线", "业务地区", "学部",
				"学科", "年级", "部门ID", "部门路径", "点赞时间", "点赞反馈", "观看时长(时分秒)", "观看时长(秒)",
			}
		} else if param.FeedbackType == defines.NoticeFeedbackUnLikeType {
			headers = []string{
				"姓名", "UID", "业务线", "业务地区", "学部",
				"学科", "年级", "部门ID", "部门路径", "点踩时间", "点踩反馈", "观看时长(时分秒)", "观看时长(秒)",
			}
		}
	}
	for _, user := range likeList.List {
		record := []string{
			user.Name,
			cast.ToString(user.Uid),
			user.ProductLineName,
			user.Region,
			user.GradeLevel,
			user.Subject,
			user.Grade,
			user.GroupId,
			user.GroupPath,
			utils.ChangeStampToYmdHms(user.FeedbackTime),
			user.FeedbackContent,
			user.BrowseDurationFormat,
			cast.ToString(user.BrowseDuration),
		}
		records = append(records, record)
	}
	return
}

// 辅助函数
func getNoticeClassName(classId int8) string {
	if name, ok := defines.ClassDict[classId]; ok {
		return name
	}
	return "未知类型"
}

func getNoticeStatusName(status int8) string {
	if name, ok := defines.StatusDict[status]; ok {
		return name
	}
	return "未知状态"
}

func getNoticeScopeName(scope int8) string {
	if name, ok := defines.ScopeDict[scope]; ok {
		return name
	}
	return "未知范围"
}
