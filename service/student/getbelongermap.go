package student

import (
	"sort"

	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtostudent"

	"github.com/gin-gonic/gin"
)

func GetBelongerMap(ctx *gin.Context) (res dtostudent.WXBelongerMapListRes, err error) {
	res.List = make([]dtostudent.WXBelongerMapRes, 0)
	for key, v := range defines.BelongerMap {
		mapInfo := dtostudent.WXBelongerMapRes{
			Label: v,
			Value: key,
		}
		res.List = append(res.List, mapInfo)
	}
	sort.Slice(res.List, func(i, j int) bool {
		return res.List[i].Value < res.List[j].Value
	})

	return res, nil
}
