package student

import (
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/dto/dtostudent"

	"github.com/gin-gonic/gin"
)

func SetBelonger(ctx *gin.Context, req dtostudent.WXBelongerReq) (res dtostudent.WXBelongerRes, err error) {
	_, err = kunpeng.SetBelonger(ctx, req.AssistantUid, req.StudentUid, req.WeixinId, req.<PERSON>onger)
	if err != nil {
		res.Result = false
	} else {
		res.Result = true
	}

	return res, nil
}
