package student

import (
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/dto/dtostudent"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetStudentUidByWxId(ctx *gin.Context, req dtostudent.StudentUidByWxIdReq) (res dtostudent.StudentUidByWxIdRes, err error) {
	info, err := kunpeng.GetWXStudentRelation(ctx, req.AssistantUid, req.WeixinId)
	if err != nil {
		zlog.Warnf(ctx, "get student uid by wx id failed, err: %v", err)
		return res, err
	}
	res.StudentUid = info.StudentUid
	return res, nil

}
