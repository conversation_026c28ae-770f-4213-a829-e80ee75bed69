package student

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/service/bailingapi"
	"fmt"
	"strconv"
	"strings"

	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/passport"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtostudent"
	"assistantdeskgo/utils/task"

	utils2 "assistantdeskgo/utils"

	"git.zuoyebang.cc/pkg/golib/v2/utils"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type StudentInfo struct {
	studentMap   map[int64]dau.StudentInfo
	assistantMap map[string]mesh.DeviceInfo
	deviceInfo   *passport.GetDeviceListRes
	belonger     *kunpeng.GetBelongerRes
}

var studentFiled = []string{
	"studentUid",
	"studentName",
	"sex",
	"grade",
	"phone",
	"guardian",
	"guardianPhone",
	"parentWebchat",
	"fatherPhone",
	"motherPhone",
	"area",
	"school",
	"registerPhone", //注册手机号(平台)
	"avatar",        //头像(平台)
	"uname",         //昵称(平台)
	"regTime",       //注册时间(平台)
}

func StudentDetail(ctx *gin.Context, req dtostudent.StudentDetailReq) (res dtostudent.StudentDetailRes, err error) {
	// 根据辅导老师微信类型确定微信关联状态  assistantUid,studentuid

	studentInfo := StudentInfo{}
	t := task.NewAsyncTask()
	t.Add(func() { //获取辅导老师信息
		studentInfo.assistantMap, err = mesh.GetDeviceInfoListByDeviceUidList(ctx, []int64{req.AssistantUid})
	})
	t.Add(func() { //获取学生信息
		studentInfo.studentMap = make(map[int64]dau.StudentInfo)
		studentInfo.studentMap, err = dau.GetStudents(ctx, []int64{req.StudentUid}, studentFiled)
	})
	t.Add(func() { //获取微信归属人
		studentInfo.belonger, err = kunpeng.GetBelonger(ctx, strconv.FormatInt(req.AssistantUid, 10), strconv.FormatInt(req.StudentUid, 10), req.StudentRemoteId)
	})
	t.Add(func() {

		if bailingapi.GrayHit(ctx, defines.ASSISTANT_AI_BAILING_V1_2_0_GRAY_KEY, req.PersonUid) {
			studentInfo.deviceInfo, err = GetDeviceFromPointData(ctx, req.StudentUid)
		} else {
			studentInfo.deviceInfo, err = passport.GetDeviceList(ctx, req.StudentUid, 30)
		}
	})
	t.Wait()

	res = studentInfo.formatStudentInfo(ctx, req.StudentUid, req.AssistantUid)
	return res, nil
}

func (studentInfo *StudentInfo) formatStudentInfo(ctx *gin.Context, studentUid, assistantUid int64) (res dtostudent.StudentDetailRes) {
	res.StudentUid = int(studentUid)
	if encrytpedUid, err := utils.EncodeUid(res.StudentUid); err != nil {
		zlog.Infof(ctx, "encrytpedUid  uid failed, StudentUid: %d, err: %+v", studentUid, err)
	} else {
		res.EncryptedStudentUid = encrytpedUid
	}
	res.StudentName = studentInfo.studentMap[studentUid].StudentName
	res.NamePinyin = utils2.GetPinyin(studentInfo.studentMap[studentUid].StudentName)
	res.Nickname = studentInfo.studentMap[studentUid].Uname
	res.Sex = studentInfo.studentMap[studentUid].Sex
	grade := studentInfo.studentMap[studentUid].Grade
	res.Grade = defines.GradeMap[int64(grade)]
	res.School = studentInfo.studentMap[studentUid].School
	studentPhone := studentInfo.studentMap[studentUid].Phone
	res.Phone = utils2.MaskPhone11(studentPhone)
	res.Area = studentInfo.studentMap[studentUid].Area
	res.AssistantUid = assistantUid
	assistantPhone := studentInfo.assistantMap[strconv.FormatInt(assistantUid, 10)].Phone
	res.AssistantPhone = utils2.MaskPhone11(assistantPhone)
	res.BelongObj.BelongerStr = handleBelongStr(studentInfo.belonger.BelongerStr)
	res.BelongObj.Belongval = studentInfo.belonger.Belonger
	if len(studentInfo.deviceInfo.InfoList) > 0 {
		lastRecord := getLatestRecord(studentInfo.deviceInfo.InfoList)
		res.DeviceInfo = formatDeviceInfo(lastRecord, studentUid)
	}

	return res

}
func handleBelongStr(belongStr string) string {
	belongStr = strings.TrimSpace(belongStr)
	if belongStr == "我自己" {
		return "本人"
	}
	if belongStr == "家长" {
		return "其他"
	}
	return belongStr
}

func getLatestRecord(infoList []passport.Info) *passport.Info {
	if len(infoList) == 0 {
		return nil
	}

	maxIndex := 0
	for i, record := range infoList {
		if record.Ts > infoList[maxIndex].Ts {
			maxIndex = i
		}
	}
	return &infoList[maxIndex]
}

func formatDeviceInfo(latestRecord *passport.Info, studentUid int64) string {
	if latestRecord == nil {
		return ""
	}

	deviceStr := latestRecord.CtxObj.Device
	appID := latestRecord.AppID
	cuidStr := latestRecord.CtxObj.CUID
	dataStr := latestRecord.Date
	var result string

	if deviceValue, ok := defines.DeviceMap[deviceStr]; ok {
		result = fmt.Sprintf("设备：%s\n", deviceValue)
	} else {
		result = fmt.Sprintf("设备：%s\n", deviceStr)
	}
	if appValue, ok := defines.AppNameMap[appID]; ok {
		result += fmt.Sprintf("APP：%s\n", appValue)
	} else {
		result += fmt.Sprintf("APP：%s\n", appID)
	}

	result += fmt.Sprintf("用户 ID：%d\n", studentUid)
	result += fmt.Sprintf("cuid：%s\n", cuidStr)
	result += fmt.Sprintf("时间：%s\n", dataStr)

	return result
}

func GetDeviceFromPointData(ctx *gin.Context, uid int64) (res *passport.GetDeviceListRes, err error) {
	lastLoginApp, err := dataproxy.GetLastAppLoginList(ctx, dataproxy.GetLastAppLoginInfoReq{
		StudentUid: uid,
		Fields:     "user_id,cuid,log_timestamp,app,app_name,cuid,mc",
	})
	if err != nil {
		return nil, err
	}
	res = &passport.GetDeviceListRes{}

	if lastLoginApp.Total == 0 {
		return
	}

	res.Total = int(lastLoginApp.Total)
	for _, appInfo := range lastLoginApp.List {
		res.InfoList = append(res.InfoList, passport.Info{
			AppID: appInfo.AppName,
			CtxObj: passport.Ctx{
				CUID:   appInfo.CUID,
				Device: appInfo.MC,
			},
			Ts:   appInfo.LogTimestamp / 1000,
			Date: utils2.TimeStampFormat(appInfo.LogTimestamp / 1000),
		})
	}

	return
}
