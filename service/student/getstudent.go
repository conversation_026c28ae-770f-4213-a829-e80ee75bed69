package student

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtostudent"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/allocate"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func getLastYearTimeStamp() int64 {
	return time.Now().Unix() - 3600*360*24
}

func GetStudentByPhone(ctx *gin.Context, req dtostudent.GetStudentByPhoneReq) (*dtostudent.GetStudentByPhoneRsp, error) {
	if req.AssistantUid <= 0 || len(req.Phone) <= 0 {
		return nil, components.ErrorParamInvalid
	}

	leadsRsp, err := allocate.GetValidLeadsByDeviceId(ctx, allocate.GetValidLeadsByDeviceIdReq{
		DeviceId: req.AssistantUid,
		RegTime:  getLastYearTimeStamp(),
	})
	if err != nil {
		return nil, err
	}

	rsp := &dtostudent.GetStudentByPhoneRsp{
		StudentInfos: make([]dtostudent.GetStudentInfo, 0),
	}

	if leadsRsp == nil || len(leadsRsp.List) == 0 {
		return rsp, nil
	}

	studentIdSet := fwyyutils.NewInt64Set()
	for _, info := range leadsRsp.List {
		if info.Status != allocate.LeadsStatusOK {
			continue
		}
		studentIdSet.Add(info.StudentUid)
	}

	studentIds := studentIdSet.AsList()
	zlog.Infof(ctx, "[GetStudentByPhone] get student by phone, ids: %s", fwyyutils.MarshalIgnoreError(studentIds))
	studentsMap, err := dau.GetStudents(ctx, studentIds, []string{"studentUid", "studentName", "registerPhone", "avatar"})
	if err != nil {
		return nil, err
	}

	for _, studentInfo := range studentsMap {
		// 匹配手机后四位
		regPhone := studentInfo.RegisterPhone
		if regPhone[len(regPhone)-4:] != req.Phone[fwyyutils.MaxInt(len(req.Phone)-4, 0):] {
			continue
		}
		rsp.StudentInfos = append(rsp.StudentInfos, dtostudent.GetStudentInfo{
			StudentUid:  int64(studentInfo.StudentUid),
			Phone:       regPhone,
			StudentName: studentInfo.StudentName,
			Avatar:      studentInfo.Avatar,
		})
	}
	return rsp, nil
}

func GetStudentByUid(ctx *gin.Context, req dtostudent.GetStudentByUidReq) (*dtostudent.GetStudentByUidRsp, error) {
	if req.AssistantUid <= 0 || req.StudentUid <= 0 {
		return nil, components.ErrorParamInvalid
	}

	leadsRsp, err := allocate.GetValidLeadsByDeviceId(ctx, allocate.GetValidLeadsByDeviceIdReq{
		DeviceId:   req.AssistantUid,
		StudentUid: req.StudentUid,
		RegTime:    getLastYearTimeStamp(),
	})
	if err != nil {
		return nil, err
	}

	rsp := &dtostudent.GetStudentByUidRsp{
		StudentInfos: make([]dtostudent.GetStudentInfo, 0),
	}
	if leadsRsp == nil || len(leadsRsp.List) == 0 {
		return rsp, nil
	}

	studentIds := make([]int64, 0)
	for _, info := range leadsRsp.List {
		if info.Status != allocate.LeadsStatusOK {
			continue
		}
		studentIds = append(studentIds, info.StudentUid)
	}

	if len(studentIds) == 0 {
		return rsp, nil
	}

	studentsMap, err := dau.GetStudents(ctx, []int64{req.StudentUid}, []string{"studentUid", "studentName", "registerPhone", "avatar"})
	if err != nil {
		return nil, err
	}

	for _, studentInfo := range studentsMap {
		rsp.StudentInfos = append(rsp.StudentInfos, dtostudent.GetStudentInfo{
			StudentUid:  int64(studentInfo.StudentUid),
			Phone:       studentInfo.RegisterPhone,
			StudentName: studentInfo.StudentName,
			Avatar:      studentInfo.Avatar,
		})
	}
	return rsp, nil
}
