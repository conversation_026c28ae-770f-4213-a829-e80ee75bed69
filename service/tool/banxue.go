package tool

import (
	"assistantdeskgo/models/accompany"
	"github.com/gin-gonic/gin"
	"time"
)

type BanXueListReq struct {
	SearchTime int64 `json:"searchTime" form:"searchTime"`
}

type BanXueListRsp struct {
	List []BanXueItem `json:"list"`
}

type BanXueItem struct {
	LessonId       int64 `json:"lessonId"`
	HasSmallCourse bool  `json:"hasSmallCourse"`
	StartTime      int64 `json:"startTime"`
}

func GetBanXueList(ctx *gin.Context, req BanXueListReq) (rsp BanXueListRsp, err error) {
	// 获取今天的开始时间（00:00:00）
	startOfToday := time.Now().Truncate(24 * time.Hour)
	// 如果制定时间，则置为指定时间
	if req.SearchTime != 0 {
		startOfToday = time.Unix(req.SearchTime, 0).Truncate(24 * time.Hour)
	}
	time.Unix(req.SearchTime, 0)
	startTimestamp := startOfToday.Unix()

	// 获取今天的结束时间（23:59:59）
	endOfToday := startOfToday.Add(24 * time.Hour).Add(-1 * time.Second)
	endTimestamp := endOfToday.Unix()

	list, err := accompany.ObjAccompanyDetail.GetBanxueListByDateRange(ctx, startTimestamp, endTimestamp)
	if err != nil {
		return
	}

	var banxueLessonIds []int64
	for _, item := range list {
		banxueLessonIds = append(banxueLessonIds, item.LessonId)
	}

	dateRange, err := accompany.LongCourseBind.GetLessonIdByDateRange(ctx, banxueLessonIds, startTimestamp, endTimestamp)
	hasBindLessonIds := map[int64]*struct{}{}
	for _, lesson := range dateRange {
		hasBindLessonIds[lesson.LongCourseLessonId] = &struct{}{}
	}

	for _, banxue := range list {
		var hasSmallCourse bool
		if hasBindLessonIds[banxue.LessonId] != nil {
			hasSmallCourse = true
		}
		rsp.List = append(rsp.List, BanXueItem{
			HasSmallCourse: hasSmallCourse,
			LessonId:       banxue.LessonId,
			StartTime:      banxue.StartTime,
		})
	}
	return
}
