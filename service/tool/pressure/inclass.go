package pressure

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/dto/dtotool"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"encoding/csv"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"os"
	"time"
)

type ExportInClassItem struct {
	CourseId     int64 `json:"courseId"`
	LessonId     int   `json:"lessonId"`
	AssistantUid int64 `json:"assistantUid"`
}

func (e ExportInClassItem) ExportExcelUrl(ctx *gin.Context, task models.PressureTask) (string, error) {
	var items []ExportInClassItem
	param := dtotool.InClassTaskParam{}
	_ = jsoniter.Unmarshal([]byte(task.TaskParam), &param)
	courses, err := dal.GetAllKVByDate(ctx, param.Date, []string{"courseId"}, []string{"lessonId", "playType"})
	if err != nil {
		return "", err
	}
	for _, course := range courses {
		assistantList, err := GetAssistantsByCourse(ctx, course.CourseId)
		if err != nil {
			continue
		}
		for _, assistantInfo := range assistantList {
			items = append(items, ExportInClassItem{
				CourseId:     course.CourseId,
				LessonId:     course.LessonInfo.LessonId,
				AssistantUid: assistantInfo.KpUid,
			})
		}
	}
	classCsv, err := ExportInClassCsv(ctx, items)
	if err != nil {
		return "", nil
	}
	marshal, err := json.Marshal(dtotool.DownLoadTaskCsvRsp{InClassUrl: classCsv})
	return string(marshal), err
}

func ExportInClassCsv(ctx *gin.Context, csvData []ExportInClassItem) (string, error) {
	timeNow := time.Now().Unix()
	taskNamePrefix := fmt.Sprintf("%+v_%+v", "外呼压测数据", timeNow)
	csvFilePath := "./" + taskNamePrefix + ".csv"
	file, err := os.OpenFile(csvFilePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return "", err
	}
	defer file.Close()
	writerCsv := csv.NewWriter(file)
	header := []string{"XUID", "courseId", "lessonId"}
	err = writerCsv.Write(header)
	if err != nil {
		return "", err
	}
	writerCsv.Flush()
	if len(csvData) <= 0 {
		return "", err
	}
	for _, item := range csvData {
		err = writerCsv.Write([]string{
			cast.ToString(item.AssistantUid),
			cast.ToString(item.CourseId),
			cast.ToString(item.LessonId),
		})
		if err != nil {
			zlog.Warnf(ctx, "生成csv文件错误,err:%+v", err)
			return "", err
		}
	}
	writerCsv.Flush()
	_, err = helpers.BaiduBucket2.UploadLocalFile(ctx, csvFilePath, taskNamePrefix, "csv", false)
	if err != nil {
		zlog.Warnf(ctx, "上传csv文件错误,err:%+v", err)
		return "", err
	}
	return taskNamePrefix + ".csv", nil
}
