package pressure

import (
	"assistantdeskgo/api/coursesearch"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/miscourse"
	"assistantdeskgo/api/moatapi"
	"assistantdeskgo/api/plum"
	"assistantdeskgo/dto/dtotool"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	commonservice "assistantdeskgo/service/common"
	"assistantdeskgo/utils"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"os"
	"strconv"
	"time"
)

const (
	IsLockStr    = "nowLock"
	MaxFileCount = 100000
)

type ExecTask interface {
	ExportExcelUrl(ctx *gin.Context, task models.PressureTask) (string, error)
}

func GetExecTask(taskType int64) (exec ExecTask, err error) {
	switch taskType {
	case models.PressureTaskTypeCreateOrder:
		return ExportCreateOrderItem{}, nil
	case models.PressureTaskTypeWxStudentList:
		return ExportWxStudentList{}, nil
	case models.PressureTaskTypeInClass:
		return ExportInClassItem{}, nil
	default:
		err = errors.New("不支持的任务类型")
	}
	return
}

type ExportCreateOrderItem struct {
	XUID       int64  `json:"XUID"`
	PersonUid  int64  `json:"PersonUid"`
	StudentUid int64  `json:"studentUid"`
	GradeId    int64  `json:"gradeId"`
	SubjectId  int64  `json:"subjectId"`
	FromUid    int64  `json:"fromUid"`
	ToUid      int64  `json:"toUid"`
	FromPhone  string `json:"fromPhone"`
	LeadsId    int64  `json:"leadsId"`
	CourseId   int64  `json:"courseId"`
	LessonId   int64  `json:"lessonId"`
	Md5Phone   string `json:"md5Phone"`
	Phone      string `json:"phone"`
}

func (e ExportCreateOrderItem) ExportExcelUrl(ctx *gin.Context, task models.PressureTask) (string, error) {
	var csvData []ExportCreateOrderItem
	var wxStudentList []ExportWxStudentList
	var assistantUids []int64 // 先保留，后续从大促保障平台下掉入口后去掉导鲲鹏的单独任务

	// 根据参数获取所有课程
	courses, err := GetCourses(ctx, task)
	if err != nil {
		return "", err
	}

	// 遍历课程
	for _, course := range courses {
		// 课程转化，目前有AB课转化，后续其他转化逻辑可以放这里
		courseIds := TransCourseId(ctx, course.CourseId)
		for _, courseId := range courseIds {
			// 获取第一个章节id
			lessonId, err := GetFirstLessonId(ctx, courseId)
			if err != nil || lessonId == 0 {
				continue
			}
			// 根据课程获取排班老师
			list, err := GetAssistantsByCourse(ctx, courseId)
			if err != nil {
				zlog.Warnf(ctx, "CreateOrder_GetAssistantsByCourse_err, courseId:%d, err:%s", courseId, err.Error())
				continue
			}
			for _, assistant := range list {
				assistantUids = append(assistantUids, assistant.KpUid)
				assistantDetail, err := mesh.GetDeviceListByDeviceUid(ctx, assistant.KpUid)
				if err != nil {
					zlog.Warnf(ctx, "CreateOrder_GetDeviceListByDeviceUid_err, courseId:%d, assistant_uid:%d, err:%s", courseId, assistant.KpUid, err.Error())
					continue
				}
				// 根据课程+老师获取学生列表
				studentList, err := GetStudentList(ctx, courseId, assistant.KpUid)
				if err != nil {
					zlog.Warnf(ctx, "CreateOrder_GetDeviceListByDeviceUid_err, courseId:%d, assistant_uid:%d, err:%s", courseId, assistant.KpUid, err.Error())
					continue
				}
				// 获取学生详情
				studentDetails, err := GetDauStudent(ctx, studentList)
				if err != nil {
					zlog.Warnf(ctx, "CreateOrder_GetDauStudent_err, courseId:%d, assistant_uid:%d, err:%s", courseId, assistant.KpUid, err.Error())
					continue
				}
				for _, student := range studentList {
					csvData = append(csvData, FormatData(assistant, student, course, studentDetails[student.CustomUid], assistantDetail.Phone, lessonId, courseId))
				}
				wxStudentList = append(wxStudentList, buildWxStudentItem(assistant, course, studentDetails))
			}
		}
	}

	csvData = ShuffleArray(csvData)
	if len(csvData) > MaxFileCount {
		csvData = csvData[0:MaxFileCount]
	}

	assistantUids = utils.ArrayUnique(assistantUids)
	assistantUidsStr := utils.JoinArrayInt64ToString(assistantUids, ",")

	err = WriteAssistantUid(ctx, assistantUidsStr)
	if err != nil {
		return "", err
	}
	// 导出
	exportCsvUrl, err := ExportCsv(ctx, csvData, wxStudentList)
	if err != nil {
		return "", err
	}
	marshal, _ := json.Marshal(exportCsvUrl)
	return string(marshal), nil
}

func WriteAssistantUid(ctx *gin.Context, assistantUidsStr string) (err error) {
	timeStr := strconv.Itoa(int(time.Now().Unix()))
	fileName := "assistantUids_" + timeStr
	file, err := os.OpenFile(fileName+".txt", os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return
	}
	defer file.Close()

	// 写入字符串到文件
	_, err = file.WriteString(assistantUidsStr)
	if err != nil {
		return
	}

	url, err := helpers.BaiduBucket2.UploadLocalFile(ctx, "./"+fileName+".txt", fileName, "txt", false)
	fmt.Println(url)
	err = helpers.RedisClient.Set(ctx, UidsTxtUrl, fileName+".txt")
	return
}

func TransCourseId(ctx *gin.Context, courseId int64) (courseIds []int64) {
	courseIds = []int64{courseId}
	ret, err := miscourse.BatchGetGiftCourseInfo(ctx, []int64{courseId})
	if err != nil {
		return
	}
	giftInfo, exist := ret[courseId]
	if !exist {
		return
	}
	if len(giftInfo.GiftCourseIds) <= 0 {
		return
	}
	courseIds = giftInfo.GiftCourseIds

	return
}

// 随机打散数组
func ShuffleArray(arr []ExportCreateOrderItem) []ExportCreateOrderItem {
	var res []ExportCreateOrderItem
	csvMap := map[int64][]ExportCreateOrderItem{}
	for _, csvInfo := range arr {
		csvMap[csvInfo.XUID] = append(csvMap[csvInfo.XUID], csvInfo)
	}

	for len(csvMap) > 0 {
		for uid, csvDatas := range csvMap {
			if len(csvDatas) == 0 {
				break
			}
			res = append(res, csvDatas[0])
			csvMap[uid] = csvMap[uid][1:]
			if len(csvMap[uid]) == 0 {
				delete(csvMap, uid)
			}
		}
	}
	return res
}

func GetFirstLessonId(ctx *gin.Context, courseId int64) (int, error) {
	courseInfos, err := dal.GetCourseLessonInfoByCourseIds(ctx, []int64{courseId}, []string{"courseId"}, []string{"lessonId"})
	if err != nil {
		zlog.Warnf(ctx, "CreateOrder_GetCourseLessonInfoByCourseIds_err, courseId:%d, err:%s", courseId, err.Error())
		return 0, err
	}
	for lessonId, _ := range courseInfos[courseId].LessonList {
		return lessonId, nil
	}
	return 0, nil
}

func FormatData(assistant plum.CourseWxMap, student plum.LeadsData, course coursesearch.Course, studentDetail dau.StudentInfo, fromPhone string, lessonId int, courseId int64) ExportCreateOrderItem {
	toPhone := studentDetail.Phone
	return ExportCreateOrderItem{
		XUID:       assistant.KpUid,     // xuid资产
		PersonUid:  assistant.PersonUid, // 真人uid
		StudentUid: student.CustomUid,
		GradeId:    course.GradeId,
		SubjectId:  course.SubjectId,
		FromUid:    assistant.KpUid,
		ToUid:      student.CustomUid,
		FromPhone:  utils.MaskPhone11(fromPhone),
		LeadsId:    student.LeadsId,
		CourseId:   courseId,
		Md5Phone:   utils.AESEncrypt(toPhone, commonservice.AESSecret),
		Phone:      utils.MaskPhone11(toPhone),
		LessonId:   int64(lessonId),
	}
}

func buildWxStudentItem(assistant plum.CourseWxMap, course coursesearch.Course, studentDetails map[int64]dau.StudentInfo) ExportWxStudentList {
	studentUids := make([]int, 0, len(studentDetails))
	for _, detail := range studentDetails {
		studentUids = append(studentUids, detail.StudentUid)
	}

	uidStr, _ := jsoniter.MarshalToString(studentUids)
	return ExportWxStudentList{
		XUID:         assistant.KpUid,
		AssistantUid: assistant.KpUid,
		CourseId:     course.CourseId,
		StudentUids:  uidStr,
	}
}

func ExportCsv(ctx *gin.Context, csvData []ExportCreateOrderItem, wxStudentList []ExportWxStudentList) (rsp dtotool.DownLoadTaskCsvRsp, err error) {
	callOutUrl, err := ExportCallOutCsv(ctx, csvData)
	if err != nil {
		return
	}
	createOrderUrl, err := ExportCreateOrderCsv(ctx, csvData)
	if err != nil {
		return
	}
	readApiData, err := ExportReadDataCsv(ctx, csvData)
	if err != nil {
		return
	}
	wxCsv, err := ExportWxCsv(ctx, wxStudentList)
	if err != nil {
		return
	}
	return dtotool.DownLoadTaskCsvRsp{
		CallOutUrl:     callOutUrl,
		CreateOrderUrl: createOrderUrl,
		ReadApiUrl:     readApiData,
		WxStudentUrl:   wxCsv,
	}, nil
}

func ExportCallOutCsv(ctx *gin.Context, csvData []ExportCreateOrderItem) (string, error) {
	csvData = separateCsvDataByDeviceUid(csvData)

	timeNow := time.Now().Unix()
	taskNamePrefix := fmt.Sprintf("%+v_%+v", "外呼压测数据", timeNow)
	csvFilePath := "./" + taskNamePrefix + ".csv"
	file, err := os.OpenFile(csvFilePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return "", err
	}
	defer file.Close()
	writerCsv := csv.NewWriter(file)
	header := []string{"XUID", "studentUid", "gradeId", "subjectId", "fromUid", "fromPhone", "toUid", "leadsId", "courseId", "md5Phone", "toPhone"}
	err = writerCsv.Write(header)
	if err != nil {
		return "", err
	}
	writerCsv.Flush()
	if len(csvData) <= 0 {
		return "", err
	}
	for _, item := range csvData {
		err = writerCsv.Write([]string{
			cast.ToString(item.XUID),
			cast.ToString(item.StudentUid),
			cast.ToString(item.GradeId),
			cast.ToString(item.SubjectId),
			cast.ToString(item.FromUid),
			item.FromPhone,
			cast.ToString(item.ToUid),
			cast.ToString(item.LeadsId),
			cast.ToString(item.CourseId),
			item.Md5Phone,
			item.Phone,
		})
		if err != nil {
			zlog.Warnf(ctx, "生成csv文件错误,err:%+v", err)
			return "", err
		}
	}
	writerCsv.Flush()
	_, err = helpers.BaiduBucket2.UploadLocalFile(ctx, csvFilePath, taskNamePrefix, "csv", false)
	if err != nil {
		zlog.Warnf(ctx, "上传csv文件错误,err:%+v", err)
		return "", err
	}
	return taskNamePrefix + ".csv", nil
}

func separateCsvDataByDeviceUid(csvData []ExportCreateOrderItem) (newCsvData []ExportCreateOrderItem) {
	csvMap := map[int64][]ExportCreateOrderItem{}
	for _, csvInfo := range csvData {
		csvMap[csvInfo.XUID] = append(csvMap[csvInfo.XUID], csvInfo)
	}
	dataList := make([][]ExportCreateOrderItem, 0, len(csvMap))
	for _, items := range csvMap {
		dataList = append(dataList, items)
	}

	newCsvData = make([]ExportCreateOrderItem, 0, MaxFileCount)
	indices := make([]int, len(dataList))
	for i := 0; i < MaxFileCount; i++ {
		idx := i % len(dataList)
		subList := dataList[idx]

		if len(subList) == 0 {
			continue
		}

		pos := indices[idx] % len(subList)
		newCsvData = append(newCsvData, subList[pos])
		indices[idx]++
	}
	return
}

func ExportCreateOrderCsv(ctx *gin.Context, csvData []ExportCreateOrderItem) (string, error) {
	timeNow := time.Now().Unix()
	taskNamePrefix := fmt.Sprintf("%+v_%+v", "下单压测数据", timeNow)
	csvFilePath := "./" + taskNamePrefix + ".csv"
	file, err := os.OpenFile(csvFilePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return "", err
	}
	defer file.Close()
	writerCsv := csv.NewWriter(file)
	header := []string{"XUID", "studentUid", "gradeId", "subjectId"}
	err = writerCsv.Write(header)
	if err != nil {
		return "", err
	}
	writerCsv.Flush()
	if len(csvData) <= 0 {
		return "", err
	}
	for _, item := range csvData {
		err = writerCsv.Write([]string{
			cast.ToString(item.PersonUid),
			cast.ToString(item.StudentUid),
			cast.ToString(item.GradeId),
			cast.ToString(item.SubjectId),
		})
		if err != nil {
			zlog.Warnf(ctx, "生成csv文件错误,err:%+v", err)
			return "", err
		}
	}
	writerCsv.Flush()
	_, err = helpers.BaiduBucket2.UploadLocalFile(ctx, csvFilePath, taskNamePrefix, "csv", false)
	if err != nil {
		zlog.Warnf(ctx, "上传csv文件错误,err:%+v", err)
		return "", err
	}
	return taskNamePrefix + ".csv", nil
}

func ExportReadDataCsv(ctx *gin.Context, csvData []ExportCreateOrderItem) (string, error) {
	timeNow := time.Now().Unix()
	taskNamePrefix := fmt.Sprintf("%+v_%+v", "读链路压测数据", timeNow)
	csvFilePath := "./" + taskNamePrefix + ".csv"
	file, err := os.OpenFile(csvFilePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return "", err
	}
	defer file.Close()
	writerCsv := csv.NewWriter(file)
	header := []string{"XUID", "studentUid", "courseId", "lessonId", "leadsId"}
	err = writerCsv.Write(header)
	if err != nil {
		return "", err
	}
	writerCsv.Flush()
	if len(csvData) <= 0 {
		return "", err
	}
	for _, item := range csvData {
		err = writerCsv.Write([]string{
			cast.ToString(item.XUID),
			cast.ToString(item.StudentUid),
			cast.ToString(item.CourseId),
			cast.ToString(item.LessonId),
			cast.ToString(item.LeadsId),
		})
		if err != nil {
			zlog.Warnf(ctx, "生成csv文件错误,err:%+v", err)
			return "", err
		}
	}
	writerCsv.Flush()
	_, err = helpers.BaiduBucket2.UploadLocalFile(ctx, csvFilePath, taskNamePrefix, "csv", false)
	if err != nil {
		zlog.Warnf(ctx, "上传csv文件错误,err:%+v", err)
		return "", err
	}
	return taskNamePrefix + ".csv", nil
}

func GetCourses(ctx *gin.Context, task models.PressureTask) (courses []coursesearch.Course, err error) {
	paramStr := task.TaskParam
	param := dtotool.CreateOrderTaskParam{}
	_ = json.Unmarshal([]byte(paramStr), &param)

	if len(param.SkuIds) > 0 {
		// skuId列表不为空，则使用sku查询
		courses, err = GetCourseIdsBySkuIds(ctx, param.SkuIds)
	} else {
		// 否则使用学年学季查询
		courses, err = GetCourseIdsByCourseSearch(ctx, param)
	}
	return
}

func GetCourseIdsBySkuIds(ctx *gin.Context, skuIds []int64) ([]coursesearch.Course, error) {
	arrayInt64 := utils.ChunkArrayInt64(skuIds, 100)
	skuInfos := map[int64]moatapi.GetGoodsSkuKvInfo{}
	for _, tmpSkuIds := range arrayInt64 {
		tmpSkuInfos, err := moatapi.GetGoodsSkuKvBySkuIdV2(ctx, tmpSkuIds)
		for id, info := range tmpSkuInfos {
			skuInfos[id] = info
		}
		if err != nil {
			return []coursesearch.Course{}, err
		}
	}

	var courseIds []int64
	for _, skuInfo := range skuInfos {
		courseIds = append(courseIds, skuInfo.ThirdId)
	}
	conds := coursesearch.NewConds().OpAnd().AddCond("courseId", "in", courseIds)
	param := coursesearch.NewParam().
		SetDocType(coursesearch.DocTypeCourse).
		BuildConds(*conds).
		SetFields([]string{"courseId", "gradeId", "subjectId", "courseName"}).
		SetOrderBy("startTime", coursesearch.OrderDesc).
		SetPage(0, 500)
	resp, err := coursesearch.DoSearch(ctx, *param)
	return resp.List, err
}

func GetCourseIdsByCourseSearch(ctx *gin.Context, taskParam dtotool.CreateOrderTaskParam) ([]coursesearch.Course, error) {
	conds := coursesearch.NewConds().OpAnd()
	// 非内部课
	conds.AddCond("isInner", "eq", 0)
	// 班课
	conds.AddCond("newCourseType", "eq", 2)
	if len(taskParam.GradeIds) > 0 {
		conds.AddCond("gradeId", "in", taskParam.GradeIds)
	}
	if len(taskParam.SubjectIds) > 0 {
		conds.AddCond("subjectId", "in", taskParam.SubjectIds)
	}
	if len(taskParam.LearnSeasons) > 0 {
		conds.AddCond("learnSeason", "in", taskParam.LearnSeasons)
	}
	if taskParam.Year > 0 {
		conds.AddCond("year", "eq", taskParam.Year)
	}

	if taskParam.SearchTimes <= 0 {
		taskParam.SearchTimes = 10
	}
	var courses []coursesearch.Course
	for i := 0; i < int(taskParam.SearchTimes); i++ {
		param := coursesearch.NewParam().
			SetDocType(coursesearch.DocTypeCourse).
			BuildConds(*conds).
			SetFields([]string{"courseId", "gradeId", "subjectId", "courseName"}).
			SetOrderBy("startTime", coursesearch.OrderDesc).
			SetPage(0, 100)

		resp, err := coursesearch.DoSearch(ctx, *param)
		if err != nil {
			continue
		}
		courses = append(courses, resp.List...)
	}

	return courses, nil
}

func GetAssistantsByCourse(ctx *gin.Context, courseId int64) (list []plum.CourseWxMap, err error) {
	assistantList, err := plum.GetAssistantsByCourseIds(ctx, []int64{courseId})
	if err != nil {
		return
	}
	fmt.Println(assistantList)
	return assistantList[courseId].CourseWxMapList, nil
}

func GetStudentList(ctx *gin.Context, courseId, assistantUid int64) ([]plum.LeadsData, error) {
	leads, err := plum.GetLeadsByCa(ctx, courseId, assistantUid, 1, 1000)
	if err != nil {
		return nil, err
	}
	return leads.List, nil
}

func GetDauStudent(ctx *gin.Context, leads []plum.LeadsData) (map[int64]dau.StudentInfo, error) {
	var studentUids []int64
	for _, lead := range leads {
		studentUids = append(studentUids, lead.CustomUid)
	}
	students, err := dau.GetStudents(ctx, studentUids, []string{"phone", "registerPhone"})
	for _, student := range students {
		if student.Phone == "" {
			student.Phone = student.RegisterPhone
		}
	}
	return students, err
}
