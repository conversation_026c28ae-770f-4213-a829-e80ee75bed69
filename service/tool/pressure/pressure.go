package pressure

import (
	"assistantdeskgo/dto/dtotool"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	UidsTxtUrl      = "deskgo:pressure_uids_url_key"
	FileNameUidsTxt = "pressure_uids.txt"

	TaskTypeLock = "deskgo:pressure_task_lock_%d"
)

func CreateTask(ctx *gin.Context, req dtotool.CreateTaskReq) (rsp dtotool.CreateTaskRsp, err error) {
	if !utils.InArrayInt64(req.TaskType, models.SupportTaskType) {
		return rsp, errors.New("任务类型不合法")
	}
	marshal, _ := json.Marshal(req.TaskParam)
	pressureTaskObj := models.PressureTask{
		TaskType:  req.TaskType,
		Status:    models.PressureTaskStatusCreate,
		Operator:  req.Operator,
		TaskParam: string(marshal),
	}
	count, err := pressureTaskObj.CountByStatus(ctx, models.PressureTaskStatusProcess, req.TaskType)
	if err != nil {
		return rsp, err
	}
	if count > 0 {
		return rsp, errors.New("当前已有正在运行中的任务")
	}
	err = pressureTaskObj.Create(ctx)
	if err != nil {
		return
	}
	rsp.TaskId = pressureTaskObj.Id
	return rsp, nil
}

func UploadUidsTxt(ctx *gin.Context) (string, error) {
	file, err := ctx.FormFile("file")
	if err != nil {
		return "", err
	}
	fileTmp, err := file.Open()
	if err != nil {
		return "", err
	}
	url, err := helpers.BaiduBucket2.UploadContent(ctx, int(file.Size), fileTmp, file.Filename)
	if err != nil {
		return "", err
	}
	err = helpers.RedisClient.Set(ctx, UidsTxtUrl, file.Filename)
	if err != nil {
		// redis失败重试一次
		err = helpers.RedisClient.Set(ctx, UidsTxtUrl, file.Filename)
		if err != nil {
			return "", err
		}
	}
	return url, nil
}

func Fail(ctx *gin.Context, req dtotool.FailTaskCsvReq) (err error) {
	err = models.PressureTaskObj.Fail(ctx, req.TaskId, "手动失败")
	DelLock(ctx, req.TaskType)
	return
}

func DownLoadTaskCsv(ctx *gin.Context, req dtotool.DownLoadTaskCsvReq) (rsp map[string]string, err error) {
	if !utils.InArrayInt64(req.TaskType, models.SupportTaskType) {
		return rsp, errors.New("任务类型不合法")
	}
	if req.TaskId == 0 {
		return rsp, errors.New("任务类型不合法")
	}
	pressureTaskObj := models.PressureTask{
		Id:       req.TaskId,
		TaskType: req.TaskType,
		Status:   models.PressureTaskStatusSuccess,
	}
	task, err := pressureTaskObj.GetByTaskId(ctx)
	if err != nil {
		return
	}
	dwnUrl := task.DwnUrl
	if dwnUrl == "" {
		return rsp, nil
	}
	err = json.Unmarshal([]byte(dwnUrl), &rsp)
	if err != nil {
		return
	}

	for key, url := range rsp {
		if url != "" {
			rsp[key], err = helpers.BaiduBucket2.GetUrlByFileName(ctx, url, time.Hour)
			if err != nil {
				return
			}
		}
	}
	if err != nil {
		return
	}
	return rsp, err
}

func CanGetLock(ctx *gin.Context, taskType int64) bool {
	lockKey := fmt.Sprintf(TaskTypeLock, taskType)
	lock, err := helpers.RedisClient.Get(ctx, lockKey)
	if err != nil || len(lock) > 0 {
		return false
	}
	success, err := helpers.RedisClient.SetNxByEX(ctx, lockKey, "lock", 3600)
	if err != nil {
		success, err = helpers.RedisClient.SetNxByEX(ctx, lockKey, "lock", 3600)
		if err != nil {
			return false
		}
	}
	return success
}

func DelLock(ctx *gin.Context, taskType int64) bool {
	lockKey := fmt.Sprintf(TaskTypeLock, taskType)
	_, err := helpers.RedisClient.Del(ctx, lockKey)
	if err != nil {
		return false
	}
	return true
}
