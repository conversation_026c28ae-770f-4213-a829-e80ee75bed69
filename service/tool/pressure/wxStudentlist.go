package pressure

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/dto/dtotool"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"os"
	"strings"
	"time"
)

type ExportWxStudentList struct {
	XUID         int64  `json:"XUID"`
	AssistantUid int64  `json:"assistantUid"`
	CourseId     int64  `json:"courseId"`
	StudentUids  string `json:"studentUids"`
}

func (e ExportWxStudentList) ExportExcelUrl(ctx *gin.Context, task models.PressureTask) (string, error) {
	uids, err := GetAssistantUids(ctx)
	if err != nil {
		return "", err
	}

	var csvDatas []ExportWxStudentList
	for _, uid := range uids {
		courseIds, err := GetCoursesByAssistantUid(ctx, int(uid))
		if err != nil {
			continue
		}
		staffUid, err := GetStaffByAssistantUid(ctx, uid)
		if staffUid == 0 {
			continue
		}
		for _, courseId := range courseIds {
			studentUids, err := GetStudentListByDesk(ctx, staffUid, uid, courseId)
			if err != nil {
				continue
			}
			if len(studentUids) <= 0 {
				continue
			}
			marshal, err := json.Marshal(studentUids)
			if err != nil {
				continue
			}
			csvDatas = append(csvDatas, ExportWxStudentList{
				XUID:         uid,
				AssistantUid: uid,
				CourseId:     courseId,
				StudentUids:  string(marshal),
			})
			// 一个uid只能有一行数据，没err直接break
			break
		}
	}
	if len(csvDatas) <= 0 {
		return "", errors.New("查询数据失败，导出数据为空")
	}
	wxCsv, err := ExportWxCsv(ctx, csvDatas)
	marshal, _ := json.Marshal(dtotool.DownLoadTaskCsvRsp{WxStudentUrl: wxCsv})
	return string(marshal), err
}

func ExportWxCsv(ctx *gin.Context, csvData []ExportWxStudentList) (string, error) {
	timeNow := time.Now().Unix()
	taskNamePrefix := fmt.Sprintf("%+v_%+v", "微信群发压测数据", timeNow)
	csvFilePath := "./" + taskNamePrefix + ".csv"
	file, err := os.OpenFile(csvFilePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		return "", err
	}
	defer file.Close()
	writerCsv := csv.NewWriter(file)
	header := []string{"XUID", "assistantUid", "courseId", "studentUids"}
	err = writerCsv.Write(header)
	if err != nil {
		return "", err
	}
	writerCsv.Flush()
	if len(csvData) <= 0 {
		return "", err
	}
	for _, item := range csvData {
		err = writerCsv.Write([]string{
			cast.ToString(item.XUID),
			cast.ToString(item.AssistantUid),
			cast.ToString(item.CourseId),
			item.StudentUids,
		})
		if err != nil {
			zlog.Warnf(ctx, "生成csv文件错误,err:%+v", err)
			return "", err
		}
	}
	writerCsv.Flush()
	_, err = helpers.BaiduBucket2.UploadLocalFile(ctx, csvFilePath, taskNamePrefix, "csv", false)
	if err != nil {
		zlog.Warnf(ctx, "上传csv文件错误,err:%+v", err)
		return "", err
	}
	return taskNamePrefix + ".csv", nil
}

func GetStudentListByDesk(ctx *gin.Context, staffUid, assistantUid, courseId int64) ([]int64, error) {
	wxStudentList, err := assistantdesk.WxStudentList(ctx, staffUid, assistantUid, courseId)
	if err != nil {
		return []int64{}, err
	}
	var studentUids []int64
	for _, item := range wxStudentList.StudentList {
		studentUids = append(studentUids, item.StudentUid)
	}
	return studentUids, err
}

func GetAssistantUids(ctx *gin.Context) ([]int64, error) {
	uidsTxtUrl, err := helpers.RedisClient.Get(ctx, UidsTxtUrl)
	if err != nil {
		return []int64{}, err
	}
	content, err := helpers.BaiduBucket2.DownloadContent(ctx, string(uidsTxtUrl))
	if err != nil {
		return []int64{}, err
	}
	split := strings.Split(string(content), ",")
	assistantUids, err := utils.ConvertArrayStringToArrayInt64(split)
	if err != nil {
		return []int64{}, err
	}
	return assistantUids, nil
}

func GetCoursesByAssistantUid(ctx *gin.Context, assistantUid int) (courseIds []int64, err error) {
	now := time.Now()
	year := now.Year()
	courseBindData, err := tower.GetCourseBindByDeviceUid(ctx, []int{assistantUid}, int64(year))
	if err != nil {
		return
	}
	for courseId, _ := range courseBindData.CourseBindData {
		courseIds = append(courseIds, courseId)
	}
	return
}

func GetStaffByAssistantUid(ctx *gin.Context, assistantUid int64) (int64, error) {
	deviceInfo, err := mesh.GetDeviceInfoListByDeviceUids(ctx, assistantUid)
	if err != nil {
		return 0, err
	}
	return int64(deviceInfo.StaffUid), nil
}
