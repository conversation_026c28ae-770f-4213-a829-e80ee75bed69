package longlinkService

import (
	"assistantdeskgo/api/longlink"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"time"
)

func SendInterviewMsg(ctx *gin.Context, message models.StuAiMessage, startTime, count int64) (msgId int64, err error) {
	t := time.Unix(startTime, 0)
	title := fmt.Sprintf("已生成%s的通话摘要", t.Format("2006-01-02 15:04"))

	msgInfo := map[string]interface{}{
		"unReadNum":    count,
		"callId":       message.MsgId,
		"msgType":      message.MsgType,
		"content":      message.Content,
		"createTime":   time.Now().Unix(),
		"title":        title,
		"msgId":        message.Id,
		"studentUid":   message.StudentUid,
		"assistantUid": message.AssistantUid,
		"expires":      0,
	}

	msgContent := map[string]interface{}{
		"msgData":    msgInfo,
		"createTime": time.Now().Unix(),
		"timeStamp":  time.Now().Unix(),
		"source":     "support",
		"msgType":    2,
	}

	msgData := map[string]interface{}{
		"sig_no":      defines.LongLinkSignNoBailingMsg,
		"msg_content": msgContent,
	}
	msgDataStr, err := jsoniter.Marshal(msgData)
	if err != nil {
		zlog.Warnf(ctx, "SendMsg jsoniter.Marshal longLink Msg fail,  err:%s", err)
		return 0, err
	}
	sendReq := longlink.SendLongLinkReq{
		Product:         defines.LongLinkProduct,
		CmdNo:           defines.LongLinkCmdNo,
		MsgExpireTime:   time.Now().Unix() + 10,
		MsgData:         string(msgDataStr),
		MsgCleanType:    2,
		MsgDeliveryType: 1,
		ToUids:          []int64{message.PersonUid},
	}
	msgId, err = longlink.SendLongLinkMsg(ctx, sendReq)
	if err != nil {
		zlog.Warnf(ctx, "SendMsg SendLongLinkMsg fail,  err:%s", err)
		return 0, err
	}
	return msgId, nil
}
