package source

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/dto/dtorelation/dtosource"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	typePlayback = "playback" // 回放

	minPlaybackTime = 300

	playTypeLive    = 1 // 直播
	playTypeLbpLock = 3 // 录播，按时间解锁
	playTypeAI      = 5 // ai互动
	playTypeLbp     = 6 // 录播

	statusUndone  = 1
	statusSuccess = 2
	statusWait    = 3

	departmentPrimary = 1  // 小学
	departmentJunior  = 20 // 初中
	departmentSenior  = 30 // 高中
	departmentPrepare = 60 // 学前
)

func GetLessonList(ctx *gin.Context, req dtosource.GetLessonListReq) (resp dtosource.GetLessonListResp, err error) {
	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return
	}
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, courseId, []string{}, []string{})
	if err != nil {
		return
	}
	lessonIds := make([]int64, 0, len(courseInfo.LessonList))
	for lessonId, _ := range courseInfo.LessonList {
		lessonIds = append(lessonIds, int64(lessonId))
	}
	if len(lessonIds) == 0 {
		return
	}

	lessonsResp, err := dataproxy.GetLpcListByStudentsLessons(ctx, []int64{studentUid}, lessonIds, []string{})
	if err != nil {
		return
	}

	resp.List = make([]dtosource.LessonInfo, 0, len(lessonsResp.List))
	var totalNum, attendNum, finishedNum, playbackFinishNum, totalFinishNum int64
	for _, lessonResp := range lessonsResp.List {
		playType, isLbp007 := getPlayTypeLbp007(lessonResp.LessonId, courseInfo)

		if req.Type == typePlayback {
			if ok, _ := fwyyutils.InArrayInt(playType, []int{playTypeLbpLock, playTypeAI}); ok {
				continue
			}
		}
		totalNum += 1

		attendStatus := 0
		playbackFinishStatus := 0
		finishStatus := 0
		playbackStatus := 0
		if lessonResp.LessonStartTime > time.Now().Unix() {
			attendStatus = statusWait
			playbackFinishStatus = statusWait
			finishStatus = statusWait
			playbackStatus = statusWait
		} else {
			// attend
			attendStatus = getAttendStatus(playType, isLbp007, lessonResp)
			if attendStatus == statusSuccess {
				attendNum += 1
			}
			// playback finish
			if lessonResp.IsPlaybackFinish > 0 {
				playbackFinishStatus = statusSuccess
				playbackFinishNum += 1
			} else {
				playbackFinishStatus = statusUndone
			}
			// finish
			finishStatus = getFinishStatus(playType, isLbp007, lessonResp)
			if finishStatus == statusSuccess {
				finishedNum += 1
			}
			// playback
			if lessonResp.PlaybackTime >= minPlaybackTime {
				playbackStatus = statusSuccess
			} else {
				playbackStatus = statusUndone
			}
		}
		if playbackFinishStatus == statusSuccess || finishStatus == statusSuccess {
			totalFinishNum += 1
		}

		// preview
		previewStatus := getPreviewStatus(lessonResp)
		var lastPlaybackTime int64 = 0
		playbackTime := "0"
		if lessonResp.IsPlayback == 1 {
			lastPlaybackTime = lessonResp.LastPlaybackTime
		}

		playTypeDesc := "直播"
		if playType == playTypeLbp {
			playTypeDesc = "录播"
		}
		resp.List = append(resp.List, dtosource.LessonInfo{
			StartTime:        lessonResp.LessonStartTime,
			StopTime:         lessonResp.LessonStopTime,
			LastPlaybackTime: lastPlaybackTime,
			PlaybackTime:     playbackTime,
			Attend:           int64(attendStatus),
			Finished:         int64(finishStatus),
			Playback:         int64(playbackStatus),
			PlaybackFinished: int64(playbackFinishStatus),
			Preview:          int64(previewStatus),
			PlayType:         playTypeDesc,
		})
	}
	resp.Count.Total = totalNum
	resp.Count.Finished = finishedNum
	resp.Count.Attend = attendNum
	resp.Count.Playback = playbackFinishNum
	resp.Count.TotalFinished = totalFinishNum
	return
}

func getPlayTypeLbp007(lessonId int64, courseInfo dal.CourseLessonInfo) (playType int, isLbp007 bool) {
	playType = playTypeLive
	lessonInfo, ok := courseInfo.LessonList[int(lessonId)]
	if ok {
		playType = lessonInfo.PlayType
		isLbp007 = lessonInfo.T007Tag == 1
	}
	return
}

func getAttendStatus(playType int, isLbp007 bool, lessonResp dataproxy.GetLpcListByStudentsLessonsInfo) (attend int) {
	// lbp
	if playType == playTypeLbp {
		if lessonResp.IsLbpAttend > 0 {
			attend = statusSuccess
			return
		}
		attend = statusUndone
		return
	}

	// ai
	if ok, _ := fwyyutils.InArrayInt(playType, []int{playTypeLbpLock, playTypeAI}); ok {
		if lessonResp.IsAiAttend > 0 {
			attend = statusSuccess
			return
		}
		attend = statusUndone
		return
	}

	// 007
	if isLbp007 {
		if lessonResp.IsUnlockPlaybackAttend > 0 {
			attend = statusSuccess
			return
		}
		attend = statusUndone
		return
	}

	// 直播
	if lessonResp.Attend > 0 {
		attend = statusSuccess
		return
	}
	attend = statusUndone
	return
}

func getFinishStatus(playType int, isLbp007 bool, lessonResp dataproxy.GetLpcListByStudentsLessonsInfo) (finish int) {
	// lbp
	if playType == playTypeLbp {
		if lessonResp.IsLbpAttendFinish > 0 {
			finish = statusSuccess
			return
		}
		finish = statusUndone
		return
	}

	// ai
	if ok, _ := fwyyutils.InArrayInt(playType, []int{playTypeLbpLock, playTypeAI}); ok {
		if lessonResp.IsAiFinish > 0 {
			finish = statusSuccess
			return
		}
		finish = statusUndone
		return
	}

	// 007
	if isLbp007 {
		if lessonResp.IsUnlockPlaybackFinish > 0 {
			finish = statusSuccess
			return
		}
		finish = statusUndone
		return
	}

	// 直播
	if lessonResp.IsAttendFinish > 0 {
		finish = statusSuccess
		return
	}
	finish = statusUndone
	return
}

func getPreviewStatus(lessonResp dataproxy.GetLpcListByStudentsLessonsInfo) (preview int) {
	if lessonResp.MainDepartment == departmentPrimary || lessonResp.MainDepartment == departmentPrepare {
		// exam5
	}
	if lessonResp.MainDepartment == departmentJunior || lessonResp.MainDepartment == departmentSenior {
		// exam13
	}
	preview = statusUndone
	return
}
