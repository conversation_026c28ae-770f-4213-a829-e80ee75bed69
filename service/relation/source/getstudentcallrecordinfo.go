package source

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/dto/dtorelation/dtosource"
	"assistantdeskgo/middleware"
	"assistantdeskgo/utils"
	"github.com/gin-gonic/gin"
)

func GetStudentCallRecordInfo(ctx *gin.Context, req dtosource.GetStudentCallRecordInfoReq) (resp dtosource.GetStudentCallRecordInfoResp, err error) {
	info, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}

	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return
	}
	// 当前先从desk拿原课程数据
	getReq := assistantdesk.GetStudentCallRecordInfoReq{
		AssistantUid:  info.DeviceUid,
		StudentUid:    studentUid,
		CourseId:      courseId,
		BacktraceTime: 86400 * 28, // 公海最长服务周期=21天
	}
	getResp, err := assistantdesk.GetStudentCallRecordInfo(ctx, getReq)
	if err != nil {
		return
	}

	resp.CallList = make([]dtosource.CallInfo, 0, len(getResp.CallList))
	for _, callInfo := range getResp.CallList {
		resp.CallList = append(resp.CallList, dtosource.CallInfo{
			Name:      callInfo.Name,
			Phone:     callInfo.Phone,
			Md5Phone:  callInfo.Md5Phone,
			City:      callInfo.City,
			CityLevel: callInfo.CityLevel,
		})
	}

	resp.CallRecordList = make([]dtosource.CallRecordInfo, 0, len(getResp.CallRecordList))
	for _, recordInfo := range getResp.CallRecordList {
		resp.CallRecordList = append(resp.CallRecordList, dtosource.CallRecordInfo{
			Name:           recordInfo.Name,
			StartTime:      recordInfo.StartTime,
			Duration:       recordInfo.Duration,
			CallResult:     recordInfo.CallResult,
			CallId:         recordInfo.CallId,
			CallMode:       recordInfo.CallMode,
			FromPhone:      recordInfo.FromPhone,
			SourceTypeName: recordInfo.SourceTypeName,
			SourceType:     recordInfo.SourceType,
		})
	}

	resp.CallCountInfo = dtosource.CallCountInfo{
		TotalNum:    getResp.CallCountInfo.TotalNum,
		SuccessNum:  getResp.CallCountInfo.SuccessNum,
		SuccessRate: getResp.CallCountInfo.SuccessRate,
	}
	return
}
