package source

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/api/duxuesc"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtosource"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetRemarkList(ctx *gin.Context, req dtosource.GetRemarkListReq) (respList []dtosource.GetRemarkListResp, err error) {
	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return
	}

	leadsId, leadsLpcUid, err := getLeadsId(ctx, studentUid, courseId)
	if err != nil {
		return
	}
	if leadsId == 0 {
		err = components.ErrorLeadsCheck
		return
	}

	leadsReq := duxuesc.GetLeadsRemarkListReq{
		LeadsId:  leadsId,
		CourseId: courseId,
		LpcUid:   leadsLpcUid,
	}
	remarkList, err := duxuesc.GetLeadsRemarkList(ctx, leadsReq)
	if err != nil || len(remarkList.List) == 0 {
		return
	}

	respList = make([]dtosource.GetRemarkListResp, 0, len(remarkList.List))
	for _, remarkInfo := range remarkList.List {
		respList = append(respList, dtosource.GetRemarkListResp{
			Remark:    remarkInfo.Remark,
			Time:      remarkInfo.Time,
			Intention: remarkInfo.Intention,
			IsCall:    remarkInfo.IsCall,
			LeadsId:   remarkInfo.LeadsId,
		})
	}
	return
}

func getLeadsId(ctx *gin.Context, studentUid, courseId int64) (leadsId, leadsLpcUid int64, err error) {
	leadsReq := allocate.GetLeadsByCourseIdUidReq{
		CourseId: courseId,
		StuUid:   studentUid,
	}
	leadsRespList, err := allocate.GetLeadsByCourseIdUid(ctx, leadsReq)
	if err != nil {
		return
	}
	if len(leadsRespList) == 0 {
		zlog.Infof(ctx, "GetLeadsByCourseIdUid no found leads, studentUid:%d, courseId:%d", studentUid, courseId)
		return
	}

	for _, uidResp := range leadsRespList {
		if uidResp.ServiceType != allocate.ServiceTypeIn {
			continue
		}
		leadsId = uidResp.LeadsId
		leadsLpcUid = uidResp.PersonUid
		break
	}
	return
}
