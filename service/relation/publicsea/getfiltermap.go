package publicsea

import (
	"assistantdeskgo/dto/dtorelation/dtopublicsea"
	"assistantdeskgo/service/relation/fields"
	"github.com/gin-gonic/gin"
)

func GetFilterMap(ctx *gin.Context) (resp dtopublicsea.GetFilterMapResp, err error) {
	resp.FilterList = make([]dtopublicsea.FilterItem, 0)

	sourceCourseFields := getFilterSourceCourse(ctx)
	if len(sourceCourseFields) > 0 {
		resp.FilterList = append(resp.FilterList, dtopublicsea.FilterItem{
			ClassName: "所属课程",
			Children:  fieldsToDto(sourceCourseFields),
		})
	}

	studentInfoFields := getFilterStudentInfo(ctx)
	if len(studentInfoFields) > 0 {
		resp.FilterList = append(resp.FilterList, dtopublicsea.FilterItem{
			ClassName: "学员信息",
			Children:  fieldsToDto(studentInfoFields),
		})
	}

	studentLevelFields := getFilterStudentLevel(ctx)
	if len(studentLevelFields) > 0 {
		resp.FilterList = append(resp.FilterList, dtopublicsea.FilterItem{
			ClassName: "学员分层",
			Children:  fieldsToDto(studentLevelFields),
		})
	}
	return
}

func getFilterSourceCourse(ctx *gin.Context) []fields.Field {
	fieldList := []fields.Field{
		fields.FYearSeason,
		fields.FSeasonSemester,
		fields.FGrade,
		fields.FSubject,
		// fields.FLastFrom,
		fields.FAttendNum,
		fields.FFinishNum,
	}

	for idx, _ := range fieldList {
		fieldList[idx].InitOptions(ctx)
	}
	return fieldList
}

func getFilterStudentInfo(ctx *gin.Context) []fields.Field {
	fieldList := []fields.Field{
		fields.FLastFrom,
		fields.FBatch,
		fields.FCityLevel,
		fields.FAccessStatus,
	}

	for idx, _ := range fieldList {
		fieldList[idx].InitOptions(ctx)
	}
	return fieldList
}

func getFilterStudentLevel(ctx *gin.Context) []fields.Field {
	fieldList := []fields.Field{
		fields.FManualIntention,
		// fields.FUserType, // 暂时不支持筛选
	}

	for idx, _ := range fieldList {
		fieldList[idx].InitOptions(ctx)
	}
	return fieldList
}

func fieldsToDto(fields []fields.Field) []dtopublicsea.FieldDetail {
	fieldDetailList := make([]dtopublicsea.FieldDetail, 0, len(fields))
	for _, field := range fields {
		opList := make([]dtopublicsea.Option, 0, len(field.Options))
		if len(field.Options) > 0 {
			for _, option := range field.Options {
				opList = append(opList, dtopublicsea.Option{
					Value: option.Value,
					Text:  option.Text,
				})
			}
		}
		fd := dtopublicsea.FieldDetail{
			Key:         field.Key,
			Label:       field.Label,
			Type:        field.Type,
			PlaceHolder: field.Placeholder,
			IsMulti:     field.IsMulti,
			Options:     opList,
		}
		fieldDetailList = append(fieldDetailList, fd)
	}
	return fieldDetailList
}
