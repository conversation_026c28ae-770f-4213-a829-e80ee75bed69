package publicsea

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/coursetransgo"
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtopublicsea"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	commonservice "assistantdeskgo/service/common"
	"assistantdeskgo/service/relation/datalayer"
	"assistantdeskgo/service/relation/fields"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"strconv"
	"strings"
	"time"
)

func ClueList(ctx *gin.Context, req dtopublicsea.ClueListReq) (resp dtopublicsea.ClueListResp, err error) {
	resp.Fields = getListFields(ctx)
	resp.List = make([]dtopublicsea.ClueItem, 0)
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	info, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}
	resp.AssistantUid = info.DeviceUid
	resp.AssistantPhone = info.Phone

	// 获取当前用户的所在组织的全部id
	groupIds, err := getUserGroupIds(ctx, int64(userInfo.UserId))
	if err != nil {
		return
	}

	validMaps := map[string]interface{}{
		"status": []int{dataproxy.StatusValid, dataproxy.StatusBack}, // 1,4=有效
		"org_id": groupIds,                                           // 组织范围限制
	}
	totalResp, err := getClueList(ctx, []string{}, []string{}, validMaps, req.Pn, req.Rn, dataproxy.SelectTypeTotal, true)
	if err != nil {
		return
	}
	resp.Total = totalResp.Total

	// 走opLog过滤的key
	search, newFilters, orClueIds, exClueIds, err := getFilterClueIds(ctx, info.DeviceUid, req.Keyword, req.Filter, groupIds)
	if !search || err != nil {
		return
	}
	listResp, err := getClueList(ctx, orClueIds, exClueIds, newFilters, req.Pn, req.Rn, dataproxy.SelectTypeAll, false)
	if err != nil {
		return
	}
	resp.SelectedCnt = listResp.Total
	resp.List = buildClueList(ctx, info.DeviceUid, listResp.List)
	return
}

func getListFields(ctx *gin.Context) []dtopublicsea.FieldInfo {
	publicSeaFields := []fields.ListField{
		fields.LStudentUid,
		fields.LStudentName,
		fields.LLeftCallCount,
		fields.LLastFrom,
		fields.LBatch,
		fields.LSemesterName,
		fields.LManualIntention,
		fields.LManualCallRemark,
		fields.LAccessStatus,
		fields.LCurrentCallNum,
		fields.LAllCallNum,
		fields.LCityInfo,
		fields.LLearnReport,
		fields.LLatestAccessTime,
		fields.LLatestOrderTime,
		fields.LPublicSeaExpired,
	}

	listFields := make([]dtopublicsea.FieldInfo, 0, len(publicSeaFields))
	for _, field := range publicSeaFields {
		options := make([]dtopublicsea.Option, 0)
		field.InitOptions(ctx)
		if len(field.FilterMap) > 0 {
			for _, option := range field.FilterMap {
				options = append(options, dtopublicsea.Option{
					Value: option.Value,
					Text:  option.Text,
				})
			}
		}

		listFields = append(listFields, dtopublicsea.FieldInfo{
			FilterMap: options,
			FeConfig: dtopublicsea.FeConfig{
				Label:       field.FeConfig.Label,
				Prop:        field.FeConfig.Prop,
				Slot:        field.FeConfig.Slot,
				CName:       field.FeConfig.CName,
				Width:       field.FeConfig.Width,
				MinWidth:    field.FeConfig.MinWidth,
				Numerator:   field.FeConfig.Numerator,
				Denominator: field.FeConfig.Denominator,
			},
		})
	}
	return listFields
}

func getUserGroupIds(ctx *gin.Context, userId int64) (groupIds []int64, err error) {
	productLineGroupIdsMap, err := commonservice.GetUserProductLineGroupIds(ctx, userId)
	zlog.Debugf(ctx, "GetUserProductLineGroupIds.userId[%d],data:%+v", userId, productLineGroupIdsMap)
	if err != nil {
		return
	}

	groupIds = make([]int64, 0)
	for _, ids := range productLineGroupIdsMap {
		groupIds = append(groupIds, ids...)
	}
	return
}

func GetExtractKeywords(keyword string) (studentUids []int64, phoneAfters []string) {
	studentUids = make([]int64, 0)
	phoneAfters = make([]string, 0)
	if len(keyword) == 0 {
		return
	}

	var kws []string
	if strings.Contains(keyword, ",") {
		kws = strings.Split(keyword, ",")
	} else {
		kws = strings.Split(keyword, "，")
	}
	for _, k := range kws {
		nk := strings.TrimSpace(k)
		if len(nk) == 0 {
			continue
		}

		if len(nk) == 4 {
			phoneAfters = append(phoneAfters, nk)
			continue
		}

		uid, ex := cast.ToInt64E(nk)
		if ex != nil {
			continue
		}
		studentUids = append(studentUids, uid)
	}
	return
}

func getFilterClueIds(ctx *gin.Context, deviceUid int64, keyword string, filters map[string]interface{}, groupIds []int64) (search bool, newFilters map[string]interface{}, orClueIds, exClueIds []string, err error) {
	search = true
	newFilters = filters
	if newFilters == nil {
		newFilters = make(map[string]interface{})
	}
	newFilters["status"] = []int{dataproxy.StatusValid, dataproxy.StatusBack} // 1,4=有效
	newFilters["lock_status"] = dataproxy.LockStatusOff                       // 0=未锁定
	newFilters["org_id"] = groupIds                                           // 组织范围限制

	// keyword
	studentUids, phoneAfters := GetExtractKeywords(keyword)
	if len(studentUids)+len(phoneAfters) > 10 {
		err = errors.New("最多可以输入10个学生或手机号查询")
		return
	}
	if len(studentUids) > 0 {
		newFilters["student_uid"] = studentUids
	}
	if len(phoneAfters) > 0 {
		newFilters["phone_after"] = phoneAfters
	}

	// 模糊查询的这里转换一下；前端所有参数都是传的字符串数组，只能自己转
	lastFrom, ok := newFilters[fields.FLastFrom.Key]
	if ok {
		// 大概率是数组
		lastFromList := cast.ToStringSlice(lastFrom)
		if len(lastFromList) > 0 {
			newFilters[fields.FLastFrom.Key] = lastFromList[0] // 只取第一个
		} else {
			// 不排除是字符串
			lastFromStr := cast.ToString(lastFrom)
			if len(lastFromStr) > 0 {
				newFilters[fields.FLastFrom.Key] = lastFromStr
			}
		}
	}

	orClueIdMap := make(map[string]interface{}, 0)
	exClueIdMap := make(map[string]interface{}, 0)
	limitClueIdMap := make(map[string]interface{}, 0)
	// 接通状态
	selectAccessStatus, ok := newFilters[fields.FAccessStatus.Key]
	if ok {
		asList, ex := cast.ToIntSliceE(selectAccessStatus)
		if ex != nil {
			err = ex
			return
		}
		delete(newFilters, fields.FAccessStatus.Key)
		statusEnum := 0
		for _, accessStatus := range asList {
			statusEnum |= 1 << accessStatus
		}

		switch statusEnum {
		case 1 << fields.AccessStatusUndo:
			clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusCalled}, deviceUid)
			for _, clueId := range clueIds {
				exClueIdMap[clueId] = struct{}{}
			}
		case 1 << fields.AccessStatusCalled:
			clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusCalled}, deviceUid)
			if len(clueIds) == 0 {
				search = false
				return
			}
			for _, clueId := range clueIds {
				limitClueIdMap[clueId] = struct{}{}
			}
		case 1 << fields.AccessStatusReached:
			clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusReached}, deviceUid)
			if len(clueIds) == 0 {
				search = false
				return
			}
			for _, clueId := range clueIds {
				orClueIdMap[clueId] = struct{}{}
				limitClueIdMap[clueId] = struct{}{}
			}
		case 1<<fields.AccessStatusUndo | 1<<fields.AccessStatusCalled:
		case 1<<fields.AccessStatusUndo | 1<<fields.AccessStatusReached:
			clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusCalled}, deviceUid)
			for _, clueId := range clueIds {
				exClueIdMap[clueId] = struct{}{}
			}
			clueIds = datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusReached}, deviceUid)
			for _, clueId := range clueIds {
				orClueIdMap[clueId] = struct{}{}
			}
		case 1<<fields.AccessStatusCalled | 1<<fields.AccessStatusReached:
			clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusCalled}, deviceUid)
			for _, clueId := range clueIds {
				limitClueIdMap[clueId] = struct{}{}
			}
			clueIds = datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusReached}, deviceUid)
			for _, clueId := range clueIds {
				orClueIdMap[clueId] = struct{}{}
				limitClueIdMap[clueId] = struct{}{}
			}
			if len(limitClueIdMap) == 0 {
				search = false
				return
			}
		default:
			// 获取2h内接通状态=?的线索；获取自己锁定的线索
			clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusReached}, deviceUid)
			for _, clueId := range clueIds {
				orClueIdMap[clueId] = struct{}{}
			}
		}
	} else {
		// 获取2h内接通状态=?的线索；获取自己锁定的线索
		clueIds := datalayer.GetOpLogBetween2Hour(ctx, []int64{fields.AccessStatusReached}, deviceUid)
		for _, clueId := range clueIds {
			orClueIdMap[clueId] = struct{}{}
		}
	}

	// 关注状态
	selectIntentions, ok := newFilters[fields.FManualIntention.Key]
	if ok {
		iList, ex := cast.ToIntSliceE(selectIntentions)
		if ex != nil {
			err = ex
			return
		}
		delete(newFilters, fields.FManualIntention.Key)

		// 含待标注
		if utils.InArrayInt(int(fields.ManualIntentionUndo), iList) {
			exIntentionIds := make([]int, 0)
			for k, _ := range fields.GetManualIntentionMap() {
				if utils.InArrayInt(int(k), iList) {
					continue
				}
				exIntentionIds = append(exIntentionIds, int(k))
			}

			exOpLogClueIds := datalayer.GetOpLogByIntention(ctx, fwyyutils.ConvertArrayIntToArrayString(exIntentionIds), deviceUid)
			for _, clueId := range exOpLogClueIds {
				exClueIdMap[clueId] = struct{}{}
			}
		} else {
			// 获取手动关注=?的线索
			clueIds := datalayer.GetOpLogByIntention(ctx, fwyyutils.ConvertArrayIntToArrayString(iList), deviceUid)
			if len(clueIds) == 0 {
				search = false
				return
			}

			newLimitClueIdMap := make(map[string]interface{})
			newOrClueIdMap := make(map[string]interface{})
			for _, clueId := range clueIds {
				_, exist := limitClueIdMap[clueId]
				if len(limitClueIdMap) > 0 {
					if exist {
						newLimitClueIdMap[clueId] = struct{}{}
						newOrClueIdMap[clueId] = struct{}{}
					}
				} else {
					newLimitClueIdMap[clueId] = struct{}{}
				}
			}
			limitClueIdMap = newLimitClueIdMap
			orClueIdMap = newOrClueIdMap
			if len(limitClueIdMap) == 0 {
				search = false
				return
			}
		}

	}

	if len(orClueIdMap) > 0 {
		orClueIds = fwyyutils.ArrayKeysString(orClueIdMap)
	}
	if len(exClueIdMap) > 0 {
		exClueIds = fwyyutils.ArrayKeysString(exClueIdMap)
	}
	if len(limitClueIdMap) > 0 {
		newFilters["clue_id"] = fwyyutils.ArrayKeysString(limitClueIdMap) // 此条件必须加上，逻辑统一
	}
	return
}

func getClueList(ctx *gin.Context, orClueIds, exClueIds []string, filters map[string]interface{}, pn, rn int64, selectType dataproxy.SelectType, useCache bool) (resp dataproxy.ClueListResp, err error) {
	if useCache {
		cacheKey, ex := getCacheKey(orClueIds, filters, pn, rn)
		if ex != nil {
			err = ex
			zlog.Warnf(ctx, "ClueList.useCache.marshal key fail, filters:%+v, err:%+v", filters, err)
			return
		}
		rGet, ex := helpers.RedisClient.Get(ctx, cacheKey)
		if ex != nil {
			err = ex
			zlog.Warnf(ctx, "ClueList.useCache.get redis fail, cacheKey:%s, err:%+v", cacheKey, err)
			return
		}
		if len(rGet) > 0 {
			err = jsoniter.UnmarshalFromString(string(rGet), &resp)
			if err != nil {
				zlog.Warnf(ctx, "ClueList.useCache.unmarshal fail, cacheKey:%s, rGet:%s, err:%+v", cacheKey, string(rGet), err)
				return
			}
			return
		}
	}

	esReq := dataproxy.ClueListReq{
		Filter:     filters,
		OrClueIds:  orClueIds,
		ExClueIds:  exClueIds,
		SelectType: selectType,
		Pn:         pn,
		Rn:         rn,
	}
	resp, err = dataproxy.ClueList(ctx, esReq)
	if err != nil {
		return
	}

	if useCache {
		cacheKey, ex := getCacheKey(orClueIds, filters, pn, rn)
		if ex != nil {
			return
		}
		toString, ex := jsoniter.MarshalToString(resp)
		if ex != nil {
			return
		}
		_ = helpers.RedisClient.SetEx(ctx, cacheKey, toString, defines.CacheKeyRelationClueListExSeconds)
	}
	return
}

func getCacheKey(orClueIds []string, filters map[string]interface{}, pn, rn int64) (cacheKey string, err error) {
	keyData := map[string]interface{}{
		"orClueIds": orClueIds,
		"filters":   filters,
		"pn":        pn,
		"rn":        rn,
	}
	toString, err := jsoniter.MarshalToString(keyData)
	if err != nil {
		return
	}
	cacheKey = fmt.Sprintf(defines.CacheKeyRelationClueList, fwyyutils.Md5(toString))
	return
}

func buildClueList(ctx *gin.Context, deviceUid int64, dataList []dataproxy.ClueInfo) (data []dtopublicsea.ClueItem) {
	data = make([]dtopublicsea.ClueItem, 0, len(dataList))
	if len(dataList) == 0 {
		return
	}

	// 学生信息
	studentUidMap := make(map[int64]interface{})
	clueIdMap := make(map[string]interface{})
	clueIdGradeMap := make(map[string]int64)
	lastFromList := make(map[string]interface{})
	for _, clueInfo := range dataList {
		studentUidMap[clueInfo.StudentUid] = struct{}{}
		clueIdMap[clueInfo.ClueId] = struct{}{}
		clueIdGradeMap[clueInfo.ClueId] = clueInfo.MainGradeId
		lastFromList[clueInfo.LastFrom] = struct{}{}
	}
	studentsMap, _ := dau.GetStudents(ctx, fwyyutils.ArrayKeysInt64(studentUidMap), []string{"studentUid", "studentName", "registerPhone"})
	clueIds := fwyyutils.ArrayKeysString(clueIdMap)

	// 枚举
	cityLevelMap := fields.GetCityLevelMap()
	userTypeMap := fields.GetUserTypeMap()

	// 年级
	getKey, err := assistantdesk.SignalGetKey(ctx, assistantdesk.GradeKey)
	gradeIdMap := map[string]string{}
	if err == nil {
		gradeIdMap, _ = getKey[assistantdesk.GradeKey]
	}

	// 外呼倒计时和次数
	clueIdLeftSecond, _ := getClueCallLeftSeconds(ctx, clueIds)
	clueIdLeftCnt, _ := getClueCallLeftCnt(ctx, clueIds)
	clueIdAccessStatus, _ := getClueCallAccessStatus(ctx, clueIds, deviceUid)

	// 获取记录数据
	req := datalayer.GetCallRecordReq{
		FromUid: deviceUid,
		ClueIds: clueIds,
	}
	resp := datalayer.GetPublicSeaCallSopData(ctx, req)

	// 获取学情报告
	clueIdLearnReportMap := datalayer.GetClueIdLearnReportInfoMap(ctx, clueIdGradeMap)

	// 获取最后一次购课时间和用户类型
	uInfoMap, _ := dataproxy.GetUData(ctx, fwyyutils.ArrayKeysInt64(studentUidMap), []string{})

	// 获取lastFrom
	lastFromMap, _ := coursetransgo.GetLastFromMap(ctx, fwyyutils.ArrayKeysString(lastFromList))

	for _, clueInfo := range dataList {
		studentName := ""
		studentPhone := ""
		if len(studentsMap) > 0 {
			studentInfo, ok := studentsMap[clueInfo.StudentUid]
			if ok {
				studentName = studentInfo.StudentName
				studentPhone = utils.MaskPhone11(studentInfo.RegisterPhone)
			}
		}

		lastOrderTime := ""
		userTypeName := ""
		if len(uInfoMap) > 0 {
			uInfo, ok := uInfoMap[clueInfo.StudentUid]
			var lot int64 = 0
			if ok {
				lot = fwyyutils.MaxInt64(uInfo.LastLpcTradeTime, uInfo.LastBankeTradeTime)
				userTypeName, _ = userTypeMap[uInfo.UserLabel]
			}
			if lot > 0 {
				lastOrderTime = time.Unix(lot, 0).Format("2006-01-02 15:04:05")
			}
		}

		cityLevelName, _ := cityLevelMap[clueInfo.CityLevel]
		gradeName, _ := gradeIdMap[strconv.Itoa(int(clueInfo.MainGradeId))]

		leftCnt, _ := clueIdLeftCnt[clueInfo.ClueId]
		leftSecond, _ := clueIdLeftSecond[clueInfo.ClueId]
		accessStatus, _ := clueIdAccessStatus[clueInfo.ClueId]

		var manualIntention int64 = 0
		var manualCallStatus int64 = 0
		manualRemark := ""
		lastAccessTime := ""
		opLog, ok := resp.OpLogMapResp[clueInfo.ClueId]
		if ok {
			manualIntention = opLog.ManualIntention
			manualCallStatus = opLog.ManualCallStatus
			manualRemark = opLog.ManualRemark
			lastAccessTime = opLog.CallLastTime
		}

		// 外呼
		var callOwnerNum int64 = 0
		var callOwnerAccessNum int64 = 0
		current, ok := resp.CallRecordByAsssistantMap[clueInfo.ClueId]
		if ok {
			callOwnerNum = current.TotalcallCount
			callOwnerAccessNum = current.SuccessCallCount
		}
		var callAllNum int64 = 0
		var callAllAccessNum int64 = 0
		all, ok := resp.CallRecordByAsssistantsMap[clueInfo.ClueId]
		if ok {
			callAllNum = all.TotalcallCount
			callAllAccessNum = all.SuccessCallCount
		}

		// 学情报告
		var eduProbeStatus int64 = 0
		eduProbeUrl := ""
		eduProbeWriteUrl := ""
		reportInfo, ok := clueIdLearnReportMap[clueInfo.ClueId]
		if ok {
			eduProbeStatus = reportInfo.LearnReportStatus
			eduProbeUrl = reportInfo.LearnReportUrl
			eduProbeWriteUrl = reportInfo.LearnReportWriteUrl
		}

		// LastFrom
		lastFromStr := ""
		item, ok := lastFromMap[clueInfo.LastFrom]
		if ok {
			lastFromStr = item.Name
		}

		data = append(data, dtopublicsea.ClueItem{
			ClueId:               clueInfo.ClueId,
			Batch:                clueInfo.Batch,
			StudentUid:           clueInfo.StudentUid,
			CourseId:             clueInfo.CourseId,
			StudentPhone:         studentPhone,
			StudentName:          studentName,
			SourceGradeId:        clueInfo.MainGradeId,
			SourceGradeName:      gradeName,
			SourceSemesterName:   FormatSemesterName(clueInfo.SeasonSemester),
			ProvinceName:         clueInfo.ProvinceName,
			CityName:             clueInfo.CityName,
			CityLevelName:        cityLevelName,
			UserTypeName:         userTypeName,
			LeftAccessCnt:        leftCnt,
			LeftAccessSeconds:    leftSecond,
			AccessStatus:         accessStatus,
			LatestAccessTime:     lastAccessTime,
			LatestOrderTime:      lastOrderTime,
			NoCourseLastFrom:     clueInfo.LastFrom,
			NoCourseLastFromName: lastFromStr,
			ExpiredTime:          clueInfo.ExpireTime,
			ManualIntention:      manualIntention,
			ManualCallStatus:     manualCallStatus,
			ManualRemark:         manualRemark,
			CallOwnerNum:         callOwnerNum,
			CallOwnerAccessNum:   callOwnerAccessNum,
			CallAllNum:           callAllNum,
			CallAllAccessNum:     callAllAccessNum,
			EduProbeStatus:       eduProbeStatus,
			EduProbeUrl:          eduProbeUrl,
			EduProbeWriteUrl:     eduProbeWriteUrl,
			MaskStudentUid:       utils.MaskStudentUid(clueInfo.StudentUid),
		})
	}
	return
}

func FormatSemesterName(seasonSemester string) (name string) {
	if len(seasonSemester) == 0 {
		return
	}

	season := cast.ToInt(string(seasonSemester[0]))
	switch season {
	case 1:
		name = fmt.Sprintf("春%s期", seasonSemester[1:])
	case 2:
		name = fmt.Sprintf("暑%s期", seasonSemester[1:])
	case 3:
		name = fmt.Sprintf("秋%s期", seasonSemester[1:])
	case 4:
		name = fmt.Sprintf("寒%s期", seasonSemester[1:])
	}
	return
}
