package publicsea

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtopublicsea"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/relation/datalayer"
	"assistantdeskgo/service/relation/fields"
	"assistantdeskgo/service/relation/touch"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

type callAccessStatus = int64

func ClueStatus(ctx *gin.Context, req dtopublicsea.ClueStatusReq) (respMap map[string]dtopublicsea.ClueStatusResp, err error) {
	if len(req.ClueIds) == 0 {
		return
	}
	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}

	// 剩余冷却时间
	clueIdCallLeftSecondMap, err := getClueCallLeftSeconds(ctx, req.ClueIds)
	if err != nil {
		return
	}

	// 剩余次数
	clueIdCallLeftCntMap, err := getClueCallLeftCnt(ctx, req.ClueIds)
	if err != nil {
		return
	}

	// 2h内接通状态
	clueIdCallAccessStatusMap, err := getClueCallAccessStatus(ctx, req.ClueIds, deviceInfo.DeviceUid)
	if err != nil {
		return
	}

	respMap = make(map[string]dtopublicsea.ClueStatusResp)
	for _, clueId := range req.ClueIds {
		callLeftSecond, _ := clueIdCallLeftSecondMap[clueId]
		callLeftCnt, _ := clueIdCallLeftCntMap[clueId]
		accessStatus, _ := clueIdCallAccessStatusMap[clueId]

		respMap[clueId] = dtopublicsea.ClueStatusResp{
			LeftAccessSeconds: callLeftSecond,
			LeftAccessCnt:     callLeftCnt,
			AccessStatus:      accessStatus,
		}
	}
	return
}

func getClueCallLeftSeconds(ctx *gin.Context, clueIds []string) (clueIdCallLeftSecondMap map[string]int64, err error) {
	clueIdCallLeftSecondMap = make(map[string]int64)
	if len(clueIds) == 0 {
		return
	}

	for _, clueId := range clueIds {
		key := fmt.Sprintf(defines.CacheKeyRelationCallTime, components.TouchTypeForPublicSea, clueId)

		exist, ex := helpers.RedisClient.Exists(ctx, key)
		if ex != nil {
			zlog.Errorf(ctx, "getClueCallLeftSeconds check exist ex, key[%s]", key)
			clueIdCallLeftSecondMap[clueId] = touch.DefaultCallTimeLimit
			continue
		}
		if !exist {
			// 没呼过
			clueIdCallLeftSecondMap[clueId] = 0
			continue
		}

		revRange, ex := helpers.RedisClient.ZRevRange(ctx, key, 0, 0, true)
		if ex != nil {
			zlog.Errorf(ctx, "getClueCallLeftSeconds get time cache ex, key[%s]", key)
			clueIdCallLeftSecondMap[clueId] = touch.DefaultCallTimeLimit
			continue
		}

		now := int(time.Now().Unix())
		leftSeconds := 0
		for _, bytes := range revRange {
			if len(bytes) == 0 {
				continue
			}

			score, e := strconv.Atoi(string(bytes))
			if e != nil {
				zlog.Errorf(ctx, "getClueCallLeftSeconds format score ex, key[%s], score[%s]", key, string(bytes))
				continue
			}

			leftSeconds = fwyyutils.MaxInt(leftSeconds, score+touch.DefaultCallTimeLimit-now)
		}
		clueIdCallLeftSecondMap[clueId] = int64(leftSeconds)
	}
	return
}

func getClueCallLeftCnt(ctx *gin.Context, clueIds []string) (clueIdCallLeftCntMap map[string]int64, err error) {
	clueIdCallLeftCntMap = make(map[string]int64)
	if len(clueIds) == 0 {
		return
	}

	config, err := touch.GetCallConfig(ctx, components.TouchTypeForPublicSea)
	if err != nil {
		return
	}
	if len(config) == 0 {
		zlog.Infof(ctx, "getClueCallLeftCnt found no config limit")
		return
	}

	for _, clueId := range clueIds {
		key := fmt.Sprintf(defines.CacheKeyRelationCallTime, components.TouchTypeForPublicSea, clueId)

		exist, ex := helpers.RedisClient.Exists(ctx, key)
		if ex != nil {
			zlog.Errorf(ctx, "getClueCallLeftCnt check exist ex, key[%s]", key)
			clueIdCallLeftCntMap[clueId] = 0
			continue
		}
		minLeft := 86400
		if !exist {
			// 没呼过
			for _, cfg := range config {
				minLeft = fwyyutils.MinInt(minLeft, cfg.CntLimit)
			}
			clueIdCallLeftCntMap[clueId] = int64(minLeft)
			continue
		}

		now := time.Now().Unix()
		for _, cfg := range config {
			min := now - int64(cfg.TimeRange*3600)
			max := now
			count, e := helpers.RedisClient.ZCount(ctx, key, strconv.Itoa(int(min)), strconv.Itoa(int(max)))
			if e != nil {
				zlog.Errorf(ctx, "getClueCallLeftCnt count call ex, key[%s], min[%d], max[%d]", key, min, max)
				count = 0
			}
			minLeft = fwyyutils.MinInt(minLeft, cfg.CntLimit-int(count))
		}
		if minLeft < 0 {
			minLeft = 0
		}
		clueIdCallLeftCntMap[clueId] = int64(minLeft)
	}
	return
}

func getClueCallAccessStatus(ctx *gin.Context, clueIds []string, deviceUid int64) (clueIdCallAccessStatusMap map[string]callAccessStatus, err error) {
	if len(clueIds) == 0 {
		return
	}

	clueIdCallAccessStatusMap = make(map[string]callAccessStatus)
	cuaKeyOpLogMap := datalayer.GetPublicSeaOpLogMapByClueIdsDeviceId(ctx, clueIds, deviceUid)

	for _, clueId := range clueIds {
		cs := fields.AccessStatusUndo
		now := time.Now().Unix()
		opLog, ok := cuaKeyOpLogMap[clueId]
		if ok {
			// 2h内已外呼
			if now-opLog.LastCallTime <= defines.PublicSeaTouchExpireTime {
				cs = fields.AccessStatusCalled
			}
			// 2h内已接通
			if now-opLog.LastAccessTime <= defines.PublicSeaTouchExpireTime {
				cs = fields.AccessStatusReached
			}
		}
		clueIdCallAccessStatusMap[opLog.ClueId] = cs
	}
	return
}
