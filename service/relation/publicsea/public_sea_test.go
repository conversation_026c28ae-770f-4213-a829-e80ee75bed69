package publicsea

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func getCtx() *gin.Context {
	env.SetRootPath("../../../")
	gin.SetMode(gin.TestMode)
	engine := gin.New()
	helpers.Init(engine)
	return gin.CreateNewContext(engine)
}

func TestGetFilterMap(t *testing.T) {
	ctx := getCtx()

	fields := getFilterSourceCourse(ctx)
	for _, field := range fields {
		fmt.Println(field.Options)
	}

	fields = getFilterStudentInfo(ctx)
	for _, field := range fields {
		fmt.Println(field.Options)
	}

	fields = getFilterStudentLevel(ctx)
	for _, field := range fields {
		fmt.Println(field.Options)
	}
}

func TestGetUserGroupIds(t *testing.T) {
	ctx := getCtx()

	ids, _ := getUserGroupIds(ctx, 3000040874)
	fmt.Println(ids)
}
