package clue

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtoclue"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"github.com/gin-gonic/gin"
	"time"
)

func EditManualIntention(ctx *gin.Context, req *dtoclue.ManualIntentionReq) (err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}
	staffUid := userInfo.SelectedBusinessUid
	if staffUid <= 0 {
		return components.ErrorAssistantUidInvalid
	}

	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return errors.New("businessKey error")
	}
	err = checkIsExit(ctx, req.ClueId, staffUid)
	if err == nil {
		tblPublicSeaOpLog := models.TblPublicSeaOpLog{
			DeviceUid: staffUid,
		}
		cond := map[string]interface{}{"clue_id": req.ClueId, "device_uid": staffUid}
		udpateData := map[string]interface{}{
			"manual_intention": req.ManualIntention,
			"update_time":      time.Now().Unix(),
		}
		tblPublicSeaOpLog.UpdateByCondition(ctx, cond, udpateData)
	} else {
		tblPublicSeaOpLog := models.TblPublicSeaOpLog{
			CourseId:        courseId,
			StudentUid:      studentUid,
			DeviceUid:       staffUid,
			ClueId:          req.ClueId,
			ManualIntention: req.ManualIntention,
			CreateTime:      time.Now().Unix(),
			UpdateTime:      time.Now().Unix(),
		}
		err = tblPublicSeaOpLog.Insert(ctx, tblPublicSeaOpLog)
	}
	if err != nil {
		return err
	}
	return nil
}
