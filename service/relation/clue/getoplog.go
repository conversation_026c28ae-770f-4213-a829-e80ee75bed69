package clue

import (
	"assistantdeskgo/dto/dtorelation/dtoclue"
	"assistantdeskgo/models"
	"assistantdeskgo/service/relation/datalayer"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func GetOpLogByClueDeviceId(ctx *gin.Context, req dtoclue.ApiOpLogReq) (resp map[string]dtoclue.ApiOpLogResp, err error) {
	resp = make(map[string]dtoclue.ApiOpLogResp)
	res := make(map[string]datalayer.OpLogResp)
	res = datalayer.GetPublicSeaOpLogData(ctx, req.ClueIds, req.FromUid)
	for k, v := range res {
		resp[k] = dtoclue.ApiOpLogResp{
			OpLogId:          v.OpLogId,
			CallLastTime:     v.CallLastTime,
			ManualCallStatus: v.ManualCallStatus,
			ManualIntention:  v.ManualIntention,
			ManualRemark:     v.ManualRemark,
		}
	}
	return resp, nil
}

func ResetTimeOpLogByClueDeviceId(ctx *gin.Context, req dtoclue.ResetTimeOpLogByClueDeviceIdReq) (resp dtoclue.ResetTimeOpLogByClueDeviceIdResp, err error) {
	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: req.DeviceUid,
	}
	cond := map[string]interface{}{
		"clue_id":    req.ClueId,
		"device_uid": req.DeviceUid,
	}
	updateData := map[string]interface{}{
		"last_call_time":   0,
		"last_access_time": 0,
		"update_time":      time.Now().Unix(),
	}
	err = tblPublicSeaOpLog.UpdateByCondition(ctx, cond, updateData)
	if err != nil {
		zlog.Errorf(ctx, "call clearOpLogCallTime failed, clueId[%s], deviceUid[%d], err: %+v", req.ClueId, req.DeviceUid, err)
		return
	}
	resp.Result = true
	return
}
