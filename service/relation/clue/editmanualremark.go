package clue

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtoclue"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"github.com/gin-gonic/gin"
	"time"
)

func EditManualRemark(ctx *gin.Context, req *dtoclue.ManualRemarkReq) (err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}
	staffUid := userInfo.SelectedBusinessUid
	if staffUid <= 0 {
		return components.ErrorAssistantUidInvalid
	}

	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return errors.New("businessKey error")
	}
	err = checkIsExit(ctx, req.ClueId, staffUid)
	if err == nil {
		tblPublicSeaOpLog := models.TblPublicSeaOpLog{
			DeviceUid: staffUid,
		}
		cond := map[string]interface{}{"clue_id": req.ClueId, "device_uid": staffUid}
		udpateData := map[string]interface{}{
			"manual_remark": req.ManualRemark,
			"update_time":   time.Now().Unix(),
		}
		tblPublicSeaOpLog.UpdateByCondition(ctx, cond, udpateData)
	} else {
		tblPublicSeaOpLog := models.TblPublicSeaOpLog{
			CourseId:     courseId,
			StudentUid:   studentUid,
			DeviceUid:    staffUid,
			ClueId:       req.ClueId,
			ManualRemark: req.ManualRemark,
			CreateTime:   time.Now().Unix(),
			UpdateTime:   time.Now().Unix(),
		}
		err = tblPublicSeaOpLog.Insert(ctx, tblPublicSeaOpLog)
	}
	if err != nil {
		return err
	}
	return nil
}

func checkIsExit(ctx *gin.Context, clueId string, staffUid int64) error {
	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: staffUid,
	}
	_, err := tblPublicSeaOpLog.GetListFirstByCond(ctx, map[string]interface{}{"clue_id": clueId, "device_uid": staffUid}, 0, 1, "")
	if err != nil {
		return err
	}
	return nil
}
