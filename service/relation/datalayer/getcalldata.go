package datalayer

import (
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"assistantdeskgo/utils/task"
	"github.com/gin-gonic/gin"
	"time"
)

type GetCallRecordReq struct {
	FromUid int64
	ClueIds []string
}
type GetCallRecordResp struct {
	CallRecordByAsssistantsMap map[string]CallCountInfo //当期所有外呼 key clueId
	CallRecordByAsssistantMap  map[string]CallCountInfo //当期本人外呼 key clueId
	CallStatusByTimeMap        map[string]int64         //两小时内的外呼状态 key clueId
	OpLogMapResp               map[string]OpLogResp     //sop操作clueId
	CallLastTime               map[string]string        //线索的最新接通时间 key clueId
}
type OpLogResp struct {
	OpLogId          int64
	ManualIntention  int64  //手动意向
	ManualCallStatus int64  //手动外呼
	ManualRemark     string //手动备注
	CallLastTime     string //当前老师下线索最新接通时间
}

type CallCountInfo struct {
	SuccessCallCount int64
	TotalcallCount   int64
}

func GetPublicSeaCallSopData(ctx *gin.Context, req GetCallRecordReq) GetCallRecordResp {
	getCallRecordResp := GetCallRecordResp{}
	getCallRecordResp.CallRecordByAsssistantsMap = make(map[string]CallCountInfo)
	getCallRecordResp.CallRecordByAsssistantMap = make(map[string]CallCountInfo) //
	getCallRecordResp.CallStatusByTimeMap = make(map[string]int64)
	getCallRecordResp.OpLogMapResp = make(map[string]OpLogResp)
	getCallRecordResp.CallLastTime = make(map[string]string)

	callRecordByAsssistants := make([]touchmisgo.CallRecordLisInfo, 0)
	callRecordByAsssistant := make([]touchmisgo.CallRecordLisInfo, 0)
	//callRecordByTime := make([]touchmisgo.CallRecordLisInfo, 0)
	lastCallTimeMap := make(map[string]models.TblPublicSeaOpLog)
	publicSeaOpLog := make(map[string]models.TblPublicSeaOpLog)

	t := task.NewAsyncTask()
	//查询所有资产的
	t.Add(func() {
		callRecordByAsssistants = getCallDataByAllAssistant(ctx, req.ClueIds)
	})
	//查询当前资产的
	t.Add(func() {
		callRecordByAsssistant = getCallDataBySingleAssistant(ctx, req.ClueIds, req.FromUid)
	})
	//公海中查询2小时之内的接通状态
	//t.Add(func() {
	//	callRecordByTime = getCallStatusBetween2Hour(ctx, req.ClueIds, req.FromUid)
	//})
	//最新接通时间
	t.Add(func() {
		lastCallTimeMap = getLastOpLogMapByAllAssistant(ctx, req.ClueIds)
	})
	//学员意向、手动备注、外呼状态
	t.Add(func() {
		publicSeaOpLog = getOpLogMapBySingleAssistant(ctx, req.ClueIds, req.FromUid)
	})

	t.Wait()

	//当期所有外呼的次数
	aggregatedAllData := formatCallDataByAllAssistant(callRecordByAsssistants)
	for key, data := range aggregatedAllData {
		getCallRecordResp.CallRecordByAsssistantsMap[key] = data
	}
	//当期本人外呼的次数
	aggregatedData := formatCallDataBySingleAssistant(callRecordByAsssistant)
	for key, data := range aggregatedData {
		getCallRecordResp.CallRecordByAsssistantMap[key] = data
	}
	//2小时内的外呼状态callRecordByTime
	//aggregatedStatus := formatCallStatusBetween2Hour(callRecordByTime)
	//for k, v := range aggregatedStatus {
	//	getCallRecordResp.CallStatusByTimeMap[k] = v
	//}

	//最后一次接通时间
	formatLastCallTime := formatLastCallTimeByAllAssistant(lastCallTimeMap)
	for k, v := range formatLastCallTime {
		getCallRecordResp.CallLastTime[k] = v
	}
	//学员意向、手动备注、外呼状态
	formatOpLog := formatOpLogMapBySingleAssistant(publicSeaOpLog)
	for k, v := range formatOpLog {
		getCallRecordResp.OpLogMapResp[k] = v
	}

	return getCallRecordResp
}

func GetOpLogByIntention(ctx *gin.Context, intentionList []string, deviceUid int64) (clueIds []string) {
	clueIds = make([]string, 0)
	publicSeaOpLogs := GetPublicSeaOpLogByIntention(ctx, intentionList, deviceUid)
	for _, i := range publicSeaOpLogs {
		clueIds = append(clueIds, i.ClueId)
	}
	return
}
func GetOpLogBetween2Hour(ctx *gin.Context, accessStatusList []int64, deviceUid int64) (clueIds []string) {
	publicSeaOpLogs := GetPublicSeaOpLogByAccessTime(ctx, accessStatusList, deviceUid)
	for _, i := range publicSeaOpLogs {
		clueIds = append(clueIds, i.ClueId)
	}
	return
}

func GetCallDataBySingleAssistant(ctx *gin.Context, ClueIds []string, devideUid int64) (res map[string]CallCountInfo) {

	//查询当前资产的
	callRecordByAsssistant := getCallDataBySingleAssistant(ctx, ClueIds, devideUid)

	//当期本人外呼的次数
	res = formatCallDataBySingleAssistant(callRecordByAsssistant)

	return res
}

func GetLastAcessTimeBySingleAssistant(ctx *gin.Context, ClueIds []string, devideUid int64) (res map[string]string) {

	//查询当前资产的
	callRecordByAsssistant := getCallDataBySingleAssistant(ctx, ClueIds, devideUid)

	groupedRecords := make(map[string][]touchmisgo.CallRecordLisInfo)
	for _, i := range callRecordByAsssistant {
		key := i.BusinessKey
		if _, exists := groupedRecords[key]; !exists {
			groupedRecords[key] = []touchmisgo.CallRecordLisInfo{}
		}
		groupedRecords[key] = append(groupedRecords[key], i)
	}
	res = make(map[string]string)
	for key, group := range groupedRecords {
		var latestRecord touchmisgo.CallRecordLisInfo
		for _, record := range group {
			if latestRecord.StartTime == 0 || record.StartTime > latestRecord.StartTime {
				latestRecord = record
			}
		}
		res[key] = time.Unix(latestRecord.StartTime/1000, 0).Format("2006-01-02 15:04:05")
	}
	return res
}

func GetCallDataByAllAssistant(ctx *gin.Context, ClueIds []string) (res map[string]CallCountInfo) {
	//查询所有资产
	callRecordByAsssistants := getCallDataByAllAssistant(ctx, ClueIds)

	//所有资产外呼的次数
	res = formatCallDataByAllAssistant(callRecordByAsssistants)

	return res
}

func GetPublicSeaOpLogData(ctx *gin.Context, ClueIds []string, devideUid int64) (res map[string]OpLogResp) {
	//学员意向、手动备注、外呼状态
	publicSeaOpLog := getOpLogMapBySingleAssistant(ctx, ClueIds, devideUid)
	//学员意向、手动备注、外呼状态
	res = formatOpLogMapBySingleAssistant(publicSeaOpLog)

	return res
}

func getCallDataByAllAssistant(ctx *gin.Context, clueIds []string) (callRecordByAsssistants []touchmisgo.CallRecordLisInfo) {
	callRecordByAsssistants = make([]touchmisgo.CallRecordLisInfo, 0)

	now := time.Now()
	startTime := now.AddDate(0, 0, -31).Unix()
	endTime := now.Unix()

	recordReq := touchmisgo.GetCallRecordListReq{}
	recordReq.ToUid = []int64{}
	for _, v := range clueIds {
		uid, _, _ := utils.SplitBusinessKey(v)
		recordReq.ToUid = append(recordReq.ToUid, uid)
	}
	recordReq.CallMode = []int64{defines.CallOutModeBBD, defines.CallOutModeSIP}
	recordReq.SourceType = defines.SOURCE_TYPE_24
	recordReq.BusinessType = defines.CALLOUT_BUSINESSTYPE_PUBLICSEA
	recordReq.BusinessKey = clueIds
	recordReq.StartTime = startTime
	recordReq.EndTime = endTime

	callRecordByAsssistants, _ = touchmisgo.GetCallRecordList(ctx, recordReq)

	return
}

func formatCallDataByAllAssistant(callRecordByAsssistants []touchmisgo.CallRecordLisInfo) map[string]CallCountInfo {
	aggregatedAllData := make(map[string]CallCountInfo)
	for _, i := range callRecordByAsssistants {
		key := i.BusinessKey
		if _, exists := aggregatedAllData[key]; !exists {
			aggregatedAllData[key] = CallCountInfo{}
		}
		info := aggregatedAllData[key]
		info.TotalcallCount++
		if i.CallResult == touchmisgo.CallResultAccess {
			info.SuccessCallCount++
		}
		aggregatedAllData[key] = info
	}
	return aggregatedAllData
}

func getCallDataBySingleAssistant(ctx *gin.Context, clueIds []string, fromUid int64) (callRecordByAsssistant []touchmisgo.CallRecordLisInfo) {
	callRecordByAsssistant = make([]touchmisgo.CallRecordLisInfo, 0)

	now := time.Now()
	startTime := now.AddDate(0, 0, -31).Unix()
	endTime := now.Unix()

	recordReq := touchmisgo.GetCallRecordListReq{}
	recordReq.ToUid = []int64{}
	for _, v := range clueIds {
		uid, _, _ := utils.SplitBusinessKey(v)
		recordReq.ToUid = append(recordReq.ToUid, uid)
	}
	recordReq.CallMode = []int64{defines.CallOutModeBBD, defines.CallOutModeSIP}
	recordReq.SourceType = defines.SOURCE_TYPE_24
	recordReq.BusinessType = defines.CALLOUT_BUSINESSTYPE_PUBLICSEA
	recordReq.BusinessKey = clueIds
	recordReq.StartTime = startTime
	recordReq.EndTime = endTime
	recordReq.FromUid = fromUid

	callRecordByAsssistant, _ = touchmisgo.GetCallRecordList(ctx, recordReq)
	return
}

func formatCallDataBySingleAssistant(callRecordByAsssistant []touchmisgo.CallRecordLisInfo) map[string]CallCountInfo {
	aggregatedData := make(map[string]CallCountInfo)
	for _, i := range callRecordByAsssistant {
		key := i.BusinessKey
		if _, exists := aggregatedData[key]; !exists {
			aggregatedData[key] = CallCountInfo{}
		}
		info := aggregatedData[key]
		info.TotalcallCount++
		if i.CallResult == touchmisgo.CallResultAccess {
			info.SuccessCallCount++
		}
		aggregatedData[key] = info
	}
	return aggregatedData
}
func getCallStatusBetween2Hour(ctx *gin.Context, clueIds []string, fromUid int64) (callRecordByTime []touchmisgo.CallRecordLisInfo) {
	callRecordByTime = make([]touchmisgo.CallRecordLisInfo, 0)

	now := time.Now()
	startTime := now.Add(-defines.PublicSeaTouchExpireTime * time.Second).Unix()
	endTime := now.Unix()

	recordReq := touchmisgo.GetCallRecordListReq{}
	recordReq.ToUid = []int64{}
	for _, v := range clueIds {
		uid, _, _ := utils.SplitBusinessKey(v)
		recordReq.ToUid = append(recordReq.ToUid, uid)
	}
	recordReq.CallMode = []int64{defines.CallOutModeBBD, defines.CallOutModeSIP}
	recordReq.SourceType = defines.SOURCE_TYPE_24
	recordReq.BusinessType = defines.CALLOUT_BUSINESSTYPE_PUBLICSEA
	recordReq.BusinessKey = clueIds
	recordReq.StartTime = startTime
	recordReq.EndTime = endTime
	recordReq.FromUid = fromUid

	callRecordByTime, _ = touchmisgo.GetCallRecordList(ctx, recordReq)
	return
}

func formatCallStatusBetween2Hour(callRecordByTime []touchmisgo.CallRecordLisInfo) map[string]int64 {
	aggregatedStatus := make(map[string]int64)
	for _, i := range callRecordByTime {
		key := i.BusinessKey
		aggregatedStatus[key] = i.CallResult
	}
	return aggregatedStatus
}

func getLastOpLogMapByAllAssistant(ctx *gin.Context, clueIds []string) (publicSeaOpLog map[string]models.TblPublicSeaOpLog) {
	publicSeaOpLog = make(map[string]models.TblPublicSeaOpLog)
	publicSeaOpLog = GetLastPublicSeaOpLogMapByClueIds(ctx, clueIds)
	return
}

func formatLastCallTimeByAllAssistant(callLastTime map[string]models.TblPublicSeaOpLog) map[string]string {
	formatCallLastTime := make(map[string]string)

	for key, info := range callLastTime {
		if info.LastAccessTime > 0 {
			formatCallLastTime[key] = time.Unix(info.LastAccessTime, 0).Format("2006-01-02 15:04:05")
		}
	}
	return formatCallLastTime
}

func getOpLogMapBySingleAssistant(ctx *gin.Context, clueIds []string, deviceUid int64) (publicSeaOpLog map[string]models.TblPublicSeaOpLog) {
	publicSeaOpLog = make(map[string]models.TblPublicSeaOpLog)
	publicSeaOpLog = GetPublicSeaOpLogMapByClueIdsDeviceId(ctx, clueIds, deviceUid)
	return
}

func formatOpLogMapBySingleAssistant(publicSeaOpLog map[string]models.TblPublicSeaOpLog) map[string]OpLogResp {
	formatPublicSeaOpLog := make(map[string]OpLogResp)

	for key, info := range publicSeaOpLog {
		formattedTime := ""
		if info.LastAccessTime > 0 {
			formattedTime = time.Unix(info.LastAccessTime, 0).Format("2006-01-02 15:04:05")
		}
		formatPublicSeaOpLog[key] = OpLogResp{
			OpLogId:          info.Id,
			CallLastTime:     formattedTime,
			ManualCallStatus: info.ManualCallStatus,
			ManualIntention:  info.ManualIntention,
			ManualRemark:     info.ManualRemark,
		}
	}
	return formatPublicSeaOpLog
}
