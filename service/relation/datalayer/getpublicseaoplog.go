package datalayer

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"assistantdeskgo/service/relation/fields"
	"fmt"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

func GetPublicSeaOpLogByClueIds(ctx *gin.Context, clueIds []string, deviceUid int64, order string) []*models.TblPublicSeaOpLog {
	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: deviceUid,
	}
	cond := map[string]interface{}{
		"clue_id": clueIds,
	}
	if deviceUid > 0 {
		cond["device_uid"] = deviceUid
	}
	publicSeaOpLog, _ := tblPublicSeaOpLog.GetListByByCond(ctx, cond, 0, 0, order)
	return publicSeaOpLog
}

// GetLastPublicSeaOpLogMapByClueIds clueIds 的数据
func GetLastPublicSeaOpLogMapByClueIds(ctx *gin.Context, clueIds []string) map[string]models.TblPublicSeaOpLog {
	publicOplogMap := make(map[string]models.TblPublicSeaOpLog)
	list := GetPublicSeaOpLogByClueIds(ctx, clueIds, 0, "")
	for _, i := range list {
		if _, exists := publicOplogMap[i.ClueId]; !exists {
			publicOplogMap[i.ClueId] = models.TblPublicSeaOpLog{}
		}
		if i.LastAccessTime > publicOplogMap[i.ClueId].LastAccessTime {
			publicOplogMap[i.ClueId] = *i
		}
	}
	return publicOplogMap
}

// GetPublicSeaOpLogMapByClueIdsDeviceId clueIds+deviceId 的数据
func GetPublicSeaOpLogMapByClueIdsDeviceId(ctx *gin.Context, clueIds []string, deviceId int64) map[string]models.TblPublicSeaOpLog {
	publicOplogMap := make(map[string]models.TblPublicSeaOpLog)
	list := GetPublicSeaOpLogByClueIds(ctx, clueIds, deviceId, "")
	for _, i := range list {
		publicOplogMap[i.ClueId] = *i
	}
	return publicOplogMap
}

func GetPublicSeaOpLogByIntention(ctx *gin.Context, intentionList []string, deviceUid int64) []*models.TblPublicSeaOpLog {
	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: deviceUid,
	}
	cond := map[string]interface{}{
		"manual_intention": intentionList,
		"device_uid":       deviceUid,
	}
	publicSeaOpLog, _ := tblPublicSeaOpLog.GetListByByCond(ctx, cond, 0, 0, "")
	return publicSeaOpLog
}

func GetPublicSeaOpLogByAccessTime(ctx *gin.Context, accessStatusList []int64, deviceUid int64) []*models.TblPublicSeaOpLog {
	now := time.Now()
	startTime := now.Add(-defines.PublicSeaTouchExpireTime * time.Second).Unix()
	queryList := make([]string, 0, len(accessStatusList))
	for _, accessStatus := range accessStatusList {
		if accessStatus == fields.AccessStatusReached {
			queryList = append(queryList, fmt.Sprintf("device_uid = %d AND last_access_time >= %d ", deviceUid, startTime))
		} else if accessStatus == fields.AccessStatusCalled {
			queryList = append(queryList, fmt.Sprintf("device_uid = %d AND last_access_time < %d AND last_call_time >= %d ", deviceUid, startTime, startTime))
		} else if accessStatus == fields.AccessStatusUndo {
			queryList = append(queryList, fmt.Sprintf("device_uid = %d AND last_access_time < %d AND last_call_time < %d ", deviceUid, startTime, startTime))
		}
	}

	tblPublicSeaOpLog := models.TblPublicSeaOpLog{}
	publicSeaOpLog, _ := tblPublicSeaOpLog.GetListByAccessTime(ctx, deviceUid, fmt.Sprintf("(%s)", strings.Join(queryList, ") or (")))
	return publicSeaOpLog
}
