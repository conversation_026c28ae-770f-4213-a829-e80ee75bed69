package datalayer

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/eduprobe"
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/utils"
	"fmt"
	utils2 "git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	gradeC1 = 2
	gradeC2 = 3
	gradeC3 = 4

	gradeG1 = 5
	gradeG2 = 6
	gradeG3 = 7

	LearnReportKey          = "fwyy_desk_learning_report"
	LearnReportKeyExSeconds = 60 * 5
)

type LearnReportInfo struct {
	LearnReportStatus   int64
	LearnReportUrl      string
	LearnReportWriteUrl string
}

type LearnReportCfg struct {
	TestUrl   string
	WriteUrl  string
	ReportUrl string
}

// GetClueIdLearnReportInfoMap clueIdGradeMap; key=线索id, value=学生关联课程的年级mainGradeId
func GetClueIdLearnReportInfoMap(ctx *gin.Context, clueIdGradeMap map[string]int64) (clueIdLearnReportMap map[string]LearnReportInfo) {
	clueIdLearnReportMap = make(map[string]LearnReportInfo)
	if len(clueIdGradeMap) == 0 {
		zlog.Debugf(ctx, "GetClueIdLearnReportInfoMap.empty clueIdGradeMap")
		return
	}

	// 初中是cu维度
	czUidList := make(map[int64][]int64)
	// 高中是u维度
	gzUidList := make(map[int64][]int64)
	for clueId, grade := range clueIdGradeMap {
		studentUid, courseId, err := utils.SplitBusinessKey(clueId)
		if err != nil {
			continue
		}

		if grade == gradeC1 || grade == gradeC2 || grade == gradeC3 {
			czUids, ok := czUidList[courseId]
			if !ok {
				czUids = make([]int64, 0)
			}
			czUidList[courseId] = append(czUids, studentUid)
		}
		if grade == gradeG1 || grade == gradeG2 || grade == gradeG3 {
			gzUids, ok := gzUidList[courseId]
			if !ok {
				gzUids = make([]int64, 0)
			}
			gzUidList[courseId] = append(gzUids, studentUid)
		}
	}

	if len(czUidList) == 0 && len(gzUidList) == 0 {
		zlog.Debugf(ctx, "GetClueIdLearnReportInfoMap.empty formatted data, clueIdGradeMap:%+v", clueIdGradeMap)
		return
	}
	gradeIdUrlMap := getGradeReportUrl(ctx)

	// todo; 初中未完成，其他需求会改动报告
	if len(czUidList) > 0 {
		for courseId, uids := range czUidList {
			resp, err := dataproxy.GetLearningReportByStudentCourse(ctx, uids, courseId, []string{})
			if err != nil {
				continue
			}

			for _, info := range resp.List {
				clueId := utils.GetClueId(info.CourseId, info.StudentUid)
				clueIdLearnReportMap[clueId] = LearnReportInfo{
					LearnReportStatus:   0,
					LearnReportUrl:      "",
					LearnReportWriteUrl: "",
				}
			}
		}
	}

	if len(gzUidList) > 0 {
		// 高中测评从eduprobe获取
		urlResp, err := eduprobe.GetUrl(ctx, eduprobe.GetUrlReq{CourseId: eduprobe.NoCourseId})
		if err != nil {
			return
		}

		uids := make([]int64, 0)
		for _, us := range gzUidList {
			uids = append(uids, us...)
		}

		req := eduprobe.GetLearningReportByStudentReq{StudentUids: uids}
		resp, err := eduprobe.GetLearningReportByStudent(ctx, req)
		if err != nil {
			return
		}

		uidMap := make(map[int64]eduprobe.GetLearningReportByStudentItem)
		for _, info := range resp.List {
			uidMap[info.StudentUid] = info
		}

		for courseId, us := range gzUidList {
			for _, u := range us {
				clueId := utils.GetClueId(courseId, u)
				gradeId, _ := clueIdGradeMap[clueId]
				reportCfg, _ := gradeIdUrlMap[gradeId]

				reportInfo, ok := uidMap[u]
				encodeCourseId, _ := utils2.EncodeAQid(int(courseId))
				encodeStudentUid, _ := utils2.EncodeAQid(int(u))
				if !ok || reportInfo.CourseId == 0 {
					clueIdLearnReportMap[clueId] = LearnReportInfo{
						LearnReportStatus:   0,
						LearnReportUrl:      urlResp.Url,
						LearnReportWriteUrl: fmt.Sprintf(reportCfg.WriteUrl, encodeCourseId, encodeStudentUid),
					}
					continue
				}

				encodeCourseId, _ = utils2.EncodeAQid(int(reportInfo.CourseId))
				clueIdLearnReportMap[clueId] = LearnReportInfo{
					LearnReportStatus: 1,
					LearnReportUrl:    fmt.Sprintf(reportCfg.ReportUrl, encodeCourseId, encodeStudentUid),
				}
			}
		}
	}

	zlog.Debugf(ctx, "GetClueIdLearnReportInfoMap.czUidList:%+v, gzUidList:%+v, clueIdLearnReportMap:%+v", czUidList, gzUidList, clueIdLearnReportMap)
	return
}

func getGradeReportUrl(ctx *gin.Context) (gradeIdUrlMap map[int64]LearnReportCfg) {
	type Cfg struct {
		GradeIds      []int64 `json:"gradeIds"`
		TestUrl       string  `json:"testUrl"`
		TestDetailUrl string  `json:"testDetailUrl"`
		ReportUrl     string  `json:"reportUrl"`
	}

	var cfgList []Cfg
	err := mercury.GetConfigForJson(ctx, LearnReportKey, LearnReportKeyExSeconds, &cfgList)
	if err != nil {
		return
	}

	gradeIdUrlMap = make(map[int64]LearnReportCfg)
	for _, cfg := range cfgList {
		for _, gradeId := range cfg.GradeIds {
			gradeIdUrlMap[gradeId] = LearnReportCfg{
				TestUrl:   cfg.TestUrl,
				WriteUrl:  cfg.TestDetailUrl,
				ReportUrl: cfg.ReportUrl,
			}
		}
	}
	return
}
