package privatesea

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/api/coursetransgo"
	"assistantdeskgo/dto/dtorelation/dtoprivatesea"
	"assistantdeskgo/middleware"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"time"
)

func GetTransData(ctx *gin.Context) (resp dtoprivatesea.TransDataResp, err error) {
	resp.List = make([]dtoprivatesea.TransDataInfo, 0)
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}

	// 当前资产公海无课例子数据
	leadsIds, err := getDeviceLeadsMap(ctx, deviceInfo.DeviceUid)
	if err != nil {
		return
	}
	if len(leadsIds) == 0 {
		zlog.Infof(ctx, "GetTransData found device[%d] no leads", deviceInfo.DeviceUid)
		return
	}

	// 今日数据
	year, month, day := time.Now().Date()
	location, _ := time.LoadLocation("Asia/Shanghai")
	startOfDay := time.Date(year, month, day, 0, 0, 0, 0, location).Unix()
	req := coursetransgo.GetRelationTransByStaffReq{
		StaffUid:  int64(userInfo.UserId),
		LeadsId:   leadsIds,
		BeginTime: startOfDay,
		TransType: coursetransgo.TransTypeNoCourse,
	}
	transList, err := coursetransgo.GetRelationTransByStaff(ctx, req)
	if err != nil {
		return
	}

	transPv, refundPv, gmv := formatTransList(transList.List)
	resp.List = append(resp.List, dtoprivatesea.TransDataInfo{
		Text: "今日转化PV",
		Num:  cast.ToFloat64(fmt.Sprintf("%.2f", transPv)),
	})
	resp.List = append(resp.List, dtoprivatesea.TransDataInfo{
		Text: "今日退费PV",
		Num:  cast.ToFloat64(fmt.Sprintf("%.2f", refundPv)),
	})
	resp.List = append(resp.List, dtoprivatesea.TransDataInfo{
		Text: "今日转化GMV",
		Num:  cast.ToFloat64(fmt.Sprintf("%.2f", gmv/100)),
	})

	// 累计数据 从25年1月1日开始累计
	startOfYear := time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local).Unix()
	req = coursetransgo.GetRelationTransByStaffReq{
		StaffUid:  int64(userInfo.UserId),
		LeadsId:   leadsIds,
		BeginTime: startOfYear,
		TransType: coursetransgo.TransTypeNoCourse,
	}
	transList, err = coursetransgo.GetRelationTransByStaff(ctx, req)
	if err != nil {
		return
	}

	transPv, _, gmv = formatTransList(transList.List)
	resp.List = append(resp.List, dtoprivatesea.TransDataInfo{
		Text: "累计转化PV",
		Num:  cast.ToFloat64(fmt.Sprintf("%.2f", transPv)),
	})
	resp.List = append(resp.List, dtoprivatesea.TransDataInfo{
		Text: "累计转化GMV",
		Num:  cast.ToFloat64(fmt.Sprintf("%.2f", gmv/100)),
	})
	return
}

func getDeviceLeadsMap(ctx *gin.Context, deviceUid int64) (leadsIds []int, err error) {
	leadsList, err := allocate.GetValidNoCourseLeadsList(ctx, allocate.GetValidNoCourseLeadsListReq{
		DeviceUid: deviceUid,
		Source:    allocate.LeadsSource,
		Status:    -1,
	})
	if err != nil {
		return
	}

	leadsIds = make([]int, 0, len(leadsList.List))
	for _, item := range leadsList.List {
		leadsIds = append(leadsIds, int(item.LeadsId))
	}
	return
}

func formatTransList(transList []coursetransgo.GetRelationTransByStaffItem) (transPv, refundPv, gmv float64) {
	for _, transInfo := range transList {
		if transInfo.Refund > 0 && transInfo.PvCount > 0 {
			refundPv += 1 / float64(transInfo.PvCount)
			continue
		}

		var pv float64 = 1
		tradeFee := float64(transInfo.TradeFee)
		if transInfo.PvCount > 1 {
			pv = 1 / float64(transInfo.PvCount)
			tradeFee = tradeFee / float64(transInfo.PvCount)
		}
		transPv += pv
		gmv += tradeFee
	}
	return
}
