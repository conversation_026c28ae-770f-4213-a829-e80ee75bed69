package serviceend

import (
	"assistantdeskgo/dto/dtorelation/dtoserviceend"
	"assistantdeskgo/service/relation/fields"
	"github.com/gin-gonic/gin"
)

func GetFilterMap(ctx *gin.Context) (resp dtoserviceend.GetFilterMapResp, err error) {
	courseFieldsList := getCourseFilterField(ctx)
	studentFieldsList := getStudentFilterField(ctx)

	courseFieldDetailList := make([]dtoserviceend.FieldDetail, 0, len(courseFieldsList))
	for _, field := range courseFieldsList {
		opList := make([]dtoserviceend.Option, 0, len(field.Options))
		for _, option := range field.Options {
			opList = append(opList, dtoserviceend.Option{
				Value: option.Value,
				Text:  option.Text,
			})
		}
		fd := dtoserviceend.FieldDetail{
			Key:         field.Key,
			Label:       field.Label,
			Type:        field.Type,
			PlaceHolder: field.Placeholder,
			IsMulti:     field.IsMulti,
			Options:     opList,
		}
		courseFieldDetailList = append(courseFieldDetailList, fd)
	}
	studentFieldDetailList := make([]dtoserviceend.FieldDetail, 0, len(studentFieldsList))
	for _, field := range studentFieldsList {
		opList := make([]dtoserviceend.Option, 0, len(field.Options))
		for _, option := range field.Options {
			opList = append(opList, dtoserviceend.Option{
				Value: option.Value,
				Text:  option.Text,
			})
		}
		fd := dtoserviceend.FieldDetail{
			Key:         field.Key,
			Label:       field.Label,
			Type:        field.Type,
			PlaceHolder: field.Placeholder,
			IsMulti:     field.IsMulti,
			Options:     opList,
		}
		studentFieldDetailList = append(studentFieldDetailList, fd)
	}

	resp.FilterList = make([]dtoserviceend.FilterItem, 0)
	courseFilterItem := dtoserviceend.FilterItem{
		ClassId:   0,
		ClassName: "所属课程",
		Children:  courseFieldDetailList,
	}
	studentFilterItem := dtoserviceend.FilterItem{
		ClassId:   0,
		ClassName: "学员信息",
		Children:  studentFieldDetailList,
	}
	resp.FilterList = append(resp.FilterList, courseFilterItem, studentFilterItem)
	return
}

func getCourseFilterField(ctx *gin.Context) []fields.Field {
	fieldList := []fields.Field{
		fields.FYearSeason,     //学季
		fields.FSeasonSemester, //期次
		fields.FGrade,          //年级
		fields.FSubject,        //学科
		//fields.FLastFrom,       //购课渠道来源
	}

	for idx, _ := range fieldList {
		fieldList[idx].InitOptions(ctx)
	}
	return fieldList
}

func getStudentFilterField(ctx *gin.Context) []fields.Field {
	fieldList := []fields.Field{
		fields.FLastFrom,     // 购课来源
		fields.FBatch,        //公海批次
		fields.FTranStatus,   //转换状态
		fields.FAddWxStatus,  //加微状态
		fields.FCityLevel,    //城市级别
		fields.FRetrieveType, //回捞方式

	}

	for idx, _ := range fieldList {
		fieldList[idx].InitOptions(ctx)
	}
	return fieldList
}
