package serviceend

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/coursetransgo"
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtoserviceend"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/relation/fields"
	"assistantdeskgo/service/relation/publicsea"
	utils2 "assistantdeskgo/utils"
	"assistantdeskgo/utils/task"
	"errors"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"strings"
	"time"
)

func RecordList(ctx *gin.Context, req dtoserviceend.RecordListReq) (res dtoserviceend.RecordListResp, err error) {
	res.Fields = getServiceEndListFields(ctx)
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return res, err
	}
	deviceUid := userInfo.SelectedBusinessUid

	if deviceUid <= 0 {
		return res, components.ErrorAssistantUidInvalid
	}
	res.AssistantUid = deviceUid

	assistantMap := make(map[string]mesh.DeviceInfo)
	signalGetKey := make(map[string]map[string]string)
	var recordlistRes dataproxy.RecordListResp
	var recordlistTotal dataproxy.RecordListResp

	filterMap := map[string]interface{}{
		"assistant_uid": deviceUid,
	}
	newFilterMap, err := mergeFilter(filterMap, req)
	if err != nil {
		return
	}

	t := task.NewAsyncTask()
	t.Add(func() { //获取辅导老师信息
		assistantMap, err = mesh.GetDeviceInfoListByDeviceUidList(ctx, []int64{deviceUid})
	})
	t.Add(func() {
		recordlistRes, err = dataproxy.RecordList(ctx, dataproxy.RecordListReq{
			Filter:     newFilterMap,
			SelectType: dataproxy.SelectTypeAll,
			Pn:         req.Pn,
			Rn:         req.Rn,
		})
	})
	t.Add(func() {
		recordlistTotal, err = dataproxy.RecordList(ctx, dataproxy.RecordListReq{
			Filter:     filterMap,
			SelectType: dataproxy.SelectTypeTotal,
			Pn:         1,
			Rn:         1,
		})
	})
	t.Add(func() {
		signalGetKey, err = assistantdesk.SignalGetKey(ctx, assistantdesk.GradeKey)
	})
	t.Wait()

	uids := make([]int64, 0)
	leadIds := make([]int64, 0)
	courseIds := make([]int64, 0)
	lastFromList := make(map[string]interface{})
	for _, recordInfo := range recordlistRes.List {
		uids = append(uids, recordInfo.StudentUid)
		leadIds = append(leadIds, recordInfo.LeadsId)
		courseIds = append(courseIds, recordInfo.CourseId)
		lastFromList[recordInfo.LastFrom] = struct{}{}
	}

	studentMap := make(map[int64]dau.StudentInfo)

	studentMap, err = dau.GetStudents(ctx, uids, []string{"studentUid", "studentName", "registerPhone"})
	if err != nil {
		return
	}

	// 获取lastFrom
	lastFromMap, _ := coursetransgo.GetLastFromMap(ctx, fwyyutils.ArrayKeysString(lastFromList))

	res.AssistantPhone = assistantMap[cast.ToString(deviceUid)].Phone
	res.Total = recordlistTotal.Total
	res.SelectedCnt = recordlistRes.Total
	res.List = make([]dtoserviceend.RecordItem, 0)
	for _, recordInfo := range recordlistRes.List {
		transSubject := make([]string, 0)
		if len(recordInfo.TransSubject) > 0 {
			_ = jsoniter.UnmarshalFromString(recordInfo.TransSubject, &transSubject)
		}

		// LastFrom
		lastFromStr := ""
		item, ok := lastFromMap[recordInfo.LastFrom]
		if ok {
			lastFromStr = item.Name
		}

		res.List = append(res.List, dtoserviceend.RecordItem{
			StudentUid:           recordInfo.StudentUid,
			StudentPhone:         utils2.MaskPhone11(studentMap[recordInfo.StudentUid].RegisterPhone),
			IsTrans:              recordInfo.TransStatus,
			AddWxStatus:          recordInfo.AddWxStatus,
			StudentName:          studentMap[recordInfo.StudentUid].StudentName,
			SourceGradeId:        recordInfo.MainGradeId,
			SourceGradeName:      signalGetKey[assistantdesk.GradeKey][cast.ToString(recordInfo.MainGradeId)],
			UserTypeName:         fields.GetUserTypeMap()[recordInfo.UserType],
			RetrieveType:         fields.GetRetrieveTypeMap()[recordInfo.RetrieveType],
			ClueId:               recordInfo.ClueId,
			Batch:                recordInfo.Batch,
			SourceSemesterName:   publicsea.FormatSemesterName(recordInfo.SeasonSemester),
			NoCourseLastFrom:     recordInfo.LastFrom,
			NoCourseLastFromName: lastFromStr,
			CallOwnerNum:         recordInfo.CallrecordTotalCnt,
			CallOwnerAccessNum:   recordInfo.CallrecordConnectCnt,
			ProvinceName:         recordInfo.ProvinceName,
			CityName:             recordInfo.CityName,
			CityLevelName:        fields.GetCityLevelMap()[recordInfo.CityLevel],
			AllocTime:            time.Unix(recordInfo.AllocateTime, 0).Format("2006-01-02 15:04:05"),
			TransSubject:         transSubject,
			CourseId:             recordInfo.CourseId,
			LeadsId:              recordInfo.LeadsId,
		})
	}
	return
}

func mergeFilter(filterMap map[string]interface{}, req dtoserviceend.RecordListReq) (map[string]interface{}, error) {
	studentUids, phoneAfters := publicsea.GetExtractKeywords(req.Keyword)
	if len(studentUids)+len(phoneAfters) > 10 {
		return filterMap, errors.New("最多可以输入10个学生或手机号查询")
	}
	newFilterMap := make(map[string]interface{})
	for key, v := range filterMap {
		newFilterMap[key] = v
	}
	if len(studentUids) > 0 {
		newFilterMap["student_uid"] = studentUids
	}
	if len(phoneAfters) > 0 {
		newFilterMap["phone_after"] = phoneAfters
	}
	if len(req.Filter) > 0 {
		for k, v := range req.Filter {
			newFilterMap[k] = v
		}
	}

	lastFrom, ok := newFilterMap[fields.FLastFrom.Key]
	if ok {
		// 大概率是数组
		lastFromList := cast.ToStringSlice(lastFrom)
		if len(lastFromList) > 0 {
			newFilterMap[fields.FLastFrom.Key] = strings.ToLower(lastFromList[0]) // 只取第一个
		} else {
			// 不排除是字符串
			lastFromStr := cast.ToString(lastFrom)
			if len(lastFromStr) > 0 {
				newFilterMap[fields.FLastFrom.Key] = strings.ToLower(lastFromStr)
			}
		}
	}
	return newFilterMap, nil
}

func getServiceEndListFields(ctx *gin.Context) []dtoserviceend.FieldInfo {
	serviceEndFields := []fields.ListField{
		fields.LStudentUid,
		fields.LStudentName,
		fields.LLastFrom,
		fields.LBatch,
		fields.LSemesterName,
		fields.LCurrentCallNum,
		fields.LCityInfo,
		fields.LAllocateTime,
		fields.LTransSubject,
	}

	listFields := make([]dtoserviceend.FieldInfo, 0, len(serviceEndFields))
	for _, field := range serviceEndFields {
		options := make([]dtoserviceend.Option, 0)
		field.InitOptions(ctx)
		if len(field.FilterMap) > 0 {
			for _, option := range field.FilterMap {
				options = append(options, dtoserviceend.Option{
					Value: option.Value,
					Text:  option.Text,
				})
			}
		}

		listFields = append(listFields, dtoserviceend.FieldInfo{
			FilterMap: options,
			FeConfig: dtoserviceend.FeConfig{
				Label:       field.FeConfig.Label,
				Prop:        field.FeConfig.Prop,
				Slot:        field.FeConfig.Slot,
				CName:       field.FeConfig.CName,
				Width:       field.FeConfig.Width,
				MinWidth:    field.FeConfig.MinWidth,
				Numerator:   field.FeConfig.Numerator,
				Denominator: field.FeConfig.Denominator,
			},
		})
	}
	return listFields
}
