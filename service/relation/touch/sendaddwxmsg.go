package touch

import (
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/middleware"
	"fmt"
	"github.com/gin-gonic/gin"
)

func SendAddWxMsg(ctx *gin.Context, req *dtotouch.SendAddWXMsgReq) (res *dtotouch.SendAddWxMsgRes, err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return res, err
	}
	staffUid := userInfo.SelectedBusinessUid
	if staffUid <= 0 {
		return nil, components.ErrorAssistantUidInvalid
	}

	res = &dtotouch.SendAddWxMsgRes{}
	cardSendReq := touchmisgo.CardSendReq{}
	cardSendReq.StudentList = req.StudentList
	cardSendReq.AssistantUID = staffUid
	cardSendReq.SourceType = defines.SourceTypePrivateSea
	cardSendReq.SourceKey = fmt.Sprintf("%d_%s", staffUid, defines.SourceTypePrivateSea)
	cardSendRes, err := touchmisgo.WechatCardSend(ctx, cardSendReq)
	if err != nil {
		return res, err
	}
	res.CardSendRes = *cardSendRes
	return res, nil
}
