package touch

import (
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/service/relation/datalayer"
	"github.com/gin-gonic/gin"
)

func GetTouchCallDataBySingleAssistant(ctx *gin.Context, req dtotouch.ApiTouchCallReq) (res map[string]dtotouch.CallCountInfo, err error) {
	res = make(map[string]dtotouch.CallCountInfo)
	callData := datalayer.GetCallDataBySingleAssistant(ctx, req.ClueIds, req.FromUid)
	for k, v := range callData {
		res[k] = dtotouch.CallCountInfo{
			SuccessCallCount: v.SuccessCallCount,
			TotalcallCount:   v.TotalcallCount,
		}
	}
	return res, nil
}

func GetTouchCallDataByAllAssistant(ctx *gin.Context, req dtotouch.ApiTouchCallAllReq) (res map[string]dtotouch.CallCountInfo, err error) {
	res = make(map[string]dtotouch.CallCountInfo)
	callData := datalayer.GetCallDataByAllAssistant(ctx, req.ClueIds)
	for k, v := range callData {
		res[k] = dtotouch.CallCountInfo{
			SuccessCallCount: v.SuccessCallCount,
			TotalcallCount:   v.TotalcallCount,
		}
	}
	return res, nil
}

func GetLastAccessTimeBySingleAssistant(ctx *gin.Context, req dtotouch.ApiTouchCallReq) (res map[string]string, err error) {
	res = make(map[string]string)
	res = datalayer.GetLastAcessTimeBySingleAssistant(ctx, req.ClueIds, req.FromUid)
	return res, nil
}
