package touch

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/middleware"
	"assistantdeskgo/utils"
	"fmt"
	"github.com/gin-gonic/gin"
)

func StudentSmsFilter(ctx *gin.Context, req *dtotouch.StudentSmsFilterReq) (res *touchmisgo.WeChatCheckRes, err error) {
	var userInfo *userprofile.UserInfo
	userInfo, err = middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return res, err
	}
	staffUid := userInfo.SelectedBusinessUid
	if staffUid <= 0 {
		return nil, components.ErrorAssistantUidInvalid
	}

	sourceKey := fmt.Sprintf("%d_%s", staffUid, defines.SourceTypePrivateSea)
	sendLimit, err := getSendLimit(ctx, staffUid, defines.SourceTypePrivateSea, sourceKey)
	if err != nil {
		return
	}

	//过滤已加微的学生
	checkRelationReq := touchmisgo.CheckRelationReq{}
	checkRelationReq.AssistantUID = staffUid
	checkRelationReq.StudentUids = req.StudentUids
	checkRelationRes, err := touchmisgo.WechatCheckRelation(ctx, checkRelationReq)
	if err != nil {
		return res, err
	}

	studentMap := make(map[int64]dau.StudentInfo)
	studentMap, err = dau.GetStudents(ctx, req.StudentUids, []string{})

	canNotSendList := []touchmisgo.CanNotSendInfo{}
	canNotSendUidList := []int64{}
	for _, bindStudentUid := range checkRelationRes.BindStudentUids {
		canNotSendInfo := touchmisgo.CanNotSendInfo{
			StudentUid: bindStudentUid,
			Tips:       "已加微",
		}
		if studentInfo, exists := studentMap[bindStudentUid]; exists {
			canNotSendInfo.StudentName = studentInfo.StudentName
			canNotSendInfo.StudentPhone = utils.MaskPhone11(studentInfo.RegisterPhone)
		}
		canNotSendList = append(canNotSendList, canNotSendInfo)
		canNotSendUidList = append(canNotSendUidList, bindStudentUid)
	}
	if len(checkRelationRes.UnbindStudentUids) == 0 {
		return &touchmisgo.WeChatCheckRes{
			AssistantUid:   staffUid,
			StudentUids:    req.StudentUids,
			CanNotSendList: canNotSendList,
			SendLimit:      sendLimit,
		}, nil
	}

	//校验学生是否可发送
	checkReq := touchmisgo.CardCheckMsgReq{}
	checkReq.AssistantUID = staffUid
	checkReq.StudentUids = checkRelationRes.UnbindStudentUids
	checkReq.SourceType = defines.SourceTypePrivateSea
	checkReq.SourceKey = sourceKey

	res, err = touchmisgo.WechatCardCheck(ctx, checkReq)
	if err != nil {
		return res, err
	}

	for i, canSendInfo := range res.CanSendList {
		if studentInfo, exists := studentMap[canSendInfo.StudentUid]; exists {
			res.CanSendList[i].StudentName = studentInfo.StudentName
			res.CanSendList[i].StudentPhone = utils.MaskPhone11(studentInfo.RegisterPhone)
		}
	}
	res.CanNotSendList = append(res.CanNotSendList, canNotSendList...)
	for i, canNotSendInfo := range res.CanNotSendList {
		if studentInfo, exists := studentMap[canNotSendInfo.StudentUid]; exists {
			res.CanNotSendList[i].StudentName = studentInfo.StudentName
			res.CanNotSendList[i].StudentPhone = utils.MaskPhone11(studentInfo.RegisterPhone)
		}
	}
	res.SendLimit = sendLimit
	res.StudentUids = append(res.StudentUids, canNotSendUidList...)
	return res, nil
}

func getSendLimit(ctx *gin.Context, staffUid int64, sourceType, sourceKey string) (int64, error) {
	limitReq := touchmisgo.CardSendLimitReq{
		AssistantUID: staffUid,
		SourceType:   sourceType,
		SourceKey:    sourceKey,
	}
	ret, err := touchmisgo.CardSendLimit(ctx, limitReq)
	if err != nil {
		return 0, err
	}

	var sendLimit int64
	if ret.DailyLimit > ret.UsedCnt {
		sendLimit = ret.DailyLimit - ret.UsedCnt
	}

	return sendLimit, nil
}
