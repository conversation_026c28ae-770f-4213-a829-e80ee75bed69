package touch

import (
	"assistantdeskgo/api/muse"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	commonservice "assistantdeskgo/service/common"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

func Call(ctx *gin.Context, req dtotouch.CallReq) (resp dtotouch.CallResp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		err = components.ErrorUserNotLogin
		return
	}
	if req.CallMode == 0 {
		// 默认BBD外呼
		req.CallMode = defines.CallOutModeBBD
	}
	if req.CallMode != defines.CallOutModeBBD && req.CallMode != defines.CallOutModeSIP {
		err = components.ErrorHasBeenLocked.Sprintf("暂未支持的外呼方式")
		return
	}

	// 先lock
	lockKey := fmt.Sprintf(defines.LockKeyRelationCall, req.ClueId)
	lock, err := helpers.RedisClient.SetNxByEX(ctx, lockKey, 1, defines.LockKeyRelationCallExSecond)
	if err != nil {
		return
	}
	if !lock {
		err = components.ErrorHasBeenLocked.Sprintf("该例子正在被别的LPC外呼中，请试试别的例子")
		return
	}
	defer func() {
		_, _ = helpers.RedisClient.Del(ctx, lockKey)
	}()

	// 在check一次
	checkReq := dtotouch.CallCheckReq{
		FromPhone: req.FromPhone,
		ToPhone:   req.ToPhone,
		ClueId:    req.ClueId,
		TouchType: req.TouchType,
	}
	checkResp := CallCheck(ctx, checkReq, req.FromUid)
	if !checkResp.CanCall {
		err = components.ErrorHasBeenLocked.Sprintf(fmt.Sprintf("当前无法外呼:%s", checkResp.CanNotCallReason))
		return
	}
	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return
	}
	phone, err := commonservice.Student.DecryptPhone(ctx, studentUid, req.ToPhone)
	if err != nil {
		return
	}

	// 帮帮盾外呼使用通用的业务线 - 辅导
	// sip外呼需要根据业务线区分模板来分隔预算, 需要传实际使用的业务线, 本期只有lpc, 后续具体怎么区分待定
	businessLine := int64(defines.BusinessLineForFuDao)
	if req.CallMode == defines.CallOutModeSIP {
		businessLine = defines.BusinessLineForLPC
	}
	// 写入callRecord
	crReq := muse.AddCallRecordReq{
		SourceType:   defines.SOURCE_TYPE_24_VALUE,
		Line:         businessLine,
		FromUid:      req.FromUid,
		DeviceUid:    req.FromUid,
		PersonUid:    int64(userInfo.UserId),
		FromPhone:    req.FromPhone,
		ToUid:        studentUid,
		ToPhone:      phone,
		IsCommon:     1,
		CallMode:     req.CallMode,
		BusinessType: defines.CALLOUT_BUSINESSTYPE_PUBLICSEA,
		BusinessKey:  req.ClueId,
	}
	addCallRecord, err := muse.AddCallRecord(ctx, crReq)
	if err != nil {
		return
	}
	if addCallRecord.CallId == 0 {
		err = components.ErrorHasBeenLocked.Sprintf(fmt.Sprintf("外呼失败，请确认外呼信息是否正确;logId[%s]", zlog.GetLogID(ctx)))
		return
	}

	// 唤起外呼
	coReq := muse.CallOutReq{
		StaffUID:  int64(userInfo.UserId),
		Uname:     utils.GetMailPrefix(userInfo.Mail),
		CallID:    addCallRecord.CallId,
		FromPhone: req.FromPhone,
		ToPhone:   phone,
		CourseID:  courseId,
		DeviceUID: req.FromUid,
		CallMode:  req.CallMode,
		Line:      businessLine,
		ExtData:   "[]", // 下游此字段不能为空
	}

	callRet, err := muse.CallOut(ctx, coReq)
	if err != nil {
		return
	}
	if callRet.ErrNo > 0 {
		err = components.ErrorHasBeenLocked.Sprintf(fmt.Sprintf("外呼失败:%s logId[%s]", callRet.ErrStr, zlog.GetLogID(ctx)))
		return
	}
	resp.CallId = addCallRecord.CallId

	// 成功之后记录外呼
	cacheKey := fmt.Sprintf(defines.CacheKeyRelationCallTime, req.TouchType, req.ClueId)
	now := time.Now().Unix()
	scoreData := map[string]float64{
		strconv.Itoa(int(now)): float64(now),
	}

	err = fwyyutils.RunWithRetry(func(idx int) error {
		_, err = helpers.RedisClient.ZAdd(ctx, cacheKey, scoreData)
		if err != nil {
			time.Sleep(300 * time.Millisecond)
			return err
		}
		_, _ = helpers.RedisClient.Expire(ctx, cacheKey, defines.CacheKeyRelationCallTimeExSecond)
		return nil
	})
	if err != nil {
		zlog.Errorf(ctx, "Call.set.CacheKeyRelationCallTime error,cacheKey: %s, err: %+v", cacheKey, err)
		err = nil
	}

	// 只有公海需要更新
	if req.TouchType == components.TouchTypeForPublicSea {
		_ = fwyyutils.RunWithRetry(func(idx int) error {
			return updateLastAccessTime(ctx, req.ClueId, req.FromUid, now)
		})
	}
	return
}

func updateLastAccessTime(ctx *gin.Context, clueId string, deviceUid, lastCallTime int64) (err error) {
	tblPublicSeaOpLog := models.TblPublicSeaOpLog{
		DeviceUid: deviceUid,
	}
	cond := map[string]interface{}{
		"clue_id":    clueId,
		"device_uid": deviceUid,
	}
	updateData := map[string]interface{}{
		"last_call_time": lastCallTime,
		"update_time":    time.Now().Unix(),
	}
	err = tblPublicSeaOpLog.UpdateByCondition(ctx, cond, updateData)
	if err != nil {
		zlog.Errorf(ctx, "call updateLastAccessTime failed, clueId[%s], deviceUid[%d], time[%d], err: %+v", clueId, deviceUid, lastCallTime, err)
	}
	return
}
