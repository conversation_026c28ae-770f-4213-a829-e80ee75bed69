package touch

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

const (
	DefaultCallTimeLimit    = 60 * 20 // 20min冷却
	DefaultRetrieveCntLimit = 300     // 回捞例子数限制
)

func CallCheck(ctx *gin.Context, req dtotouch.CallCheckReq, deviceUid int64) (resp dtotouch.CallCheckResp) {
	if req.TouchType != components.TouchTypeForPublicSea && req.TouchType != components.TouchTypeForPrivateSea {
		resp.CanNotCallReason = "暂不支持的外呼类型"
		return
	}

	key := fmt.Sprintf(defines.CacheKeyRelationCallTime, req.TouchType, req.ClueId)
	exist, err := helpers.RedisClient.Exists(ctx, key)
	if err != nil {
		resp.CanNotCallReason = "获取外呼限制失败，请稍后重试"
		return
	}
	// 公海外呼需有CD
	if exist && req.TouchType == components.TouchTypeForPublicSea {
		revRange, ex := helpers.RedisClient.ZRevRange(ctx, key, 0, 0, true)
		if ex != nil {
			resp.CanNotCallReason = "获取最新一次外呼时间失败，请稍后重试"
			return
		}

		now := int(time.Now().Unix())
		leftSeconds := 0
		for _, bytes := range revRange { // 取最晚外呼时间, 判断是否在20分钟内
			if len(bytes) == 0 {
				continue
			}

			score, e := strconv.Atoi(string(bytes))
			if e != nil {
				continue
			}

			leftSeconds = fwyyutils.MaxInt(leftSeconds, score+DefaultCallTimeLimit-now)
		}
		if leftSeconds > 0 {
			resp.CanNotCallReason = "外呼正在冷却中，请稍后重试"
			return
		}
	}

	// touch limit
	config, err := GetCallConfig(ctx, req.TouchType)
	if err != nil {
		resp.CanNotCallReason = "获取外呼配置失败，请稍后重试"
		return
	}
	if len(config) == 0 {
		zlog.Info(ctx, "CallCheck found no config limit")
		return checkRetrieveCount(ctx, deviceUid, req.TouchType)
	}
	if !exist {
		// 没呼过
		for _, cfg := range config {
			if cfg.CntLimit <= 0 {
				resp.CanNotCallReason = fmt.Sprintf("当前外呼次数[0]已超过[%d]小时内最大限制[%d]，请稍后重试", cfg.TimeRange, cfg.CntLimit)
				return
			}
		}
	}

	now := time.Now().Unix()
	for _, cfg := range config {
		min := now - int64(cfg.TimeRange*3600)
		max := now
		count, e := helpers.RedisClient.ZCount(ctx, key, strconv.Itoa(int(min)), strconv.Itoa(int(max)))
		if e != nil {
			zlog.Errorf(ctx, "getClueCallLeftCnt count call ex, key[%s], min[%d], max[%d]", key, min, max)
			count = 0
		}
		if cfg.CntLimit-int(count) <= 0 {
			resp.CanNotCallReason = fmt.Sprintf("当前外呼次数[%d]已超过[%d]小时内最大限制[%d]，请稍后重试", count, cfg.TimeRange, cfg.CntLimit)
			return
		}
	}
	return checkRetrieveCount(ctx, deviceUid, req.TouchType)
}

func checkRetrieveCount(ctx *gin.Context, deviceUid int64, touchType string) (resp dtotouch.CallCheckResp) {
	// 只有公海需要限制
	if touchType != components.TouchTypeForPublicSea {
		resp.CanCall = true
		return
	}
	data, err := allocate.GetValidNoCourseLeadsList(ctx, allocate.GetValidNoCourseLeadsListReq{
		DeviceUid: deviceUid,
	})
	if err != nil {
		resp.CanNotCallReason = "获取已回捞例子数失败，请稍后重试"
		return
	}

	if len(data.List) >= DefaultRetrieveCntLimit {
		resp.CanNotCallReason = fmt.Sprintf("您服务中的回捞例子已达%d上限，等例子失效再来吧", DefaultRetrieveCntLimit)
		return
	}
	resp.CanCall = true
	return
}
