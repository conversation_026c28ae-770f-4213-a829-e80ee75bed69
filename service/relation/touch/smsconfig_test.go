package touch

import (
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func init() {
	env.SetRootPath("../../../")
	helpers.PreInit()
	helpers.InitMysql()
	helpers.InitRedis()
	helpers.InitApiClient()
	defer helpers.Release()
}

func TestGetSmsConfig(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)

	ret, err := GetSmsConfig(ctx)
	t.Log(ret, err)
}

func TestGetCallConfig(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)

	ret, err := GetCallConfig(ctx, components.TouchTypeForPrivateSea)
	t.Log(ret, err)
}

func TestWechatCardSend(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)

	params := dtotouch.SendAddWXMsgReq{StudentList: []touchmisgo.CanSendInfoReq{
		touchmisgo.CanSendInfoReq{
			StudentUid:    2135507181,
			RemoteId:      7881300165942401,
			RobotRemoteId: 1688856941579593,
		},
	}}
	ret, err := SendAddWxMsg(ctx, &params)
	t.Log(ret, err)
}
