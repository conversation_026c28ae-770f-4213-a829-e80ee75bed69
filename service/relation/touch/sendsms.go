package touch

import (
	"assistantdeskgo/api/assistantdesk"
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	commonservice "assistantdeskgo/service/common"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"strings"
	"time"
)

func SendSmsSingle(ctx *gin.Context, req dtotouch.SendSmsSingleReq) (resp dtotouch.SendSmsSingleResp, err error) {
	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		return
	}
	studentUid, courseId, err := utils.SplitBusinessKey(req.ClueId)
	if err != nil {
		return
	}
	phone, err := commonservice.Student.DecryptPhone(ctx, studentUid, req.ToPhone)
	if err != nil {
		return
	}
	if len(phone) == 0 {
		err = components.ErrorInvalidPhone
		return
	}

	// 先lock
	lockKey := fmt.Sprintf(defines.LockKeyRelationSms, req.ClueId)
	lock, err := helpers.RedisClient.SetNxByEX(ctx, lockKey, 1, defines.LockKeyRelationSmsExSecond)
	if err != nil {
		return
	}
	if !lock {
		err = components.ErrorHasBeenLocked.Sprintf("当前正在发送短信中")
		return
	}
	defer func() {
		_, _ = helpers.RedisClient.Del(ctx, lockKey)
	}()

	// 计数
	incr, err := setSendSmsCounter(ctx, req.ClueId)
	if err != nil {
		return
	}

	// check
	config, err := GetSmsConfig(ctx)
	if err != nil {
		return
	}
	if incr > config.Limit {
		err = components.ErrorUseExceed
		return
	}
	now := time.Now().Unix()
	var tplTemplate touchmisgo.GetTplInfoRsp
	for _, tplInfo := range config.TplList {
		if now < tplInfo.UseTimeBegin || now > tplInfo.UseTimeEnd {
			continue
		}
		if tplInfo.TplID == req.TplId {
			tplTemplate = tplInfo
			break
		}
	}
	if tplTemplate.TplID == 0 {
		err = components.ErrorUseFailed
		return
	}

	departmentId, err := getDepartmentIdByCourseId(ctx, courseId)
	if err != nil {
		return
	}
	if departmentId == 0 {
		departmentId = defines.PrimarySchool
	}

	// send
	smsSendReq := touchmisgo.SmsSendReq{
		AssistantUid: deviceInfo.DeviceUid,
		StaffUid:     int64(info.StaffUid),
		CourseId:     courseId,
		StudentId:    studentUid,
		StudentPhone: cast.ToInt64(phone),
		TplParams:    tplTemplate.Vars,
		TplId:        tplTemplate.TplID,
		SceneId:      defines.TouchConfScenePublicSea,
		BusinessLine: defines.BusinessLineForFuDao,
		DepartmentId: departmentId,
	}
	smsSendResp, err := touchmisgo.SmsSend(ctx, smsSendReq)
	if err != nil {
		return
	}
	resp.Result = smsSendResp.Res
	resp.Type = smsSendResp.Type
	resp.Msg = smsSendResp.Msg
	return
}

func getDepartmentIdByCourseId(ctx *gin.Context, courseId int64) (departmentId int64, err error) {
	keyListMap, err := assistantdesk.SignalGetKey(ctx, assistantdesk.AllocateCourseTagDepartmentKey)
	if err != nil {
		return
	}
	allocatePrDeMap, ok := keyListMap[assistantdesk.AllocateCourseTagDepartmentKey]
	if !ok {
		return
	}
	priceTagIdDepartMap := make(map[int64]int64)
	for key, value := range allocatePrDeMap {
		priceTagInfos := strings.Split(key, "_")
		if len(priceTagInfos) < 1 {
			continue
		}
		priceTagId := cast.ToInt64(priceTagInfos[0])
		if priceTagId == 0 {
			continue
		}
		departmentInfos := strings.Split(value, "_")
		if len(departmentInfos) < 1 {
			continue
		}
		deptId := cast.ToInt64(departmentInfos[0])
		if deptId == 0 {
			continue
		}
		priceTagIdDepartMap[priceTagId] = deptId
	}

	courseInfo, err := tower.GetCourseInfo(ctx, courseId)
	if err != nil {
		return
	}
	departmentId, _ = priceTagIdDepartMap[courseInfo.CoursePriceTag]
	return
}

func setSendSmsCounter(ctx *gin.Context, clueId string) (incr int64, err error) {
	cacheKey := fmt.Sprintf(defines.CacheKeyRelationSmsSend, clueId)
	incr, err = helpers.RedisClient.Incr(ctx, cacheKey)
	if err != nil {
		zlog.Errorf(ctx, "SendSmsSingle.incr error,%s", cacheKey)
		return
	}
	now := time.Now()
	year, month, day := now.Date()
	nextDayTime := time.Date(year, month, day+1, 0, 0, 0, 0, now.Location()).Unix()
	exTime := nextDayTime - now.Unix()
	if exTime <= 0 {
		exTime = 1
	}
	_, _ = helpers.RedisClient.Expire(ctx, cacheKey, exTime)
	return
}
