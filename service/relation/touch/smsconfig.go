package touch

import (
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/api/touchmisgo"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sort"
	"time"
)

type SmsConfigInfo struct {
	Limit   int64                      `json:"limit"`
	TplList []touchmisgo.GetTplInfoRsp `json:"tplList"`
}

func GetSmsConfig(ctx *gin.Context) (*SmsConfigInfo, error) {
	var config mercury.TouchConfig
	if err := mercury.GetConfigForJson(ctx, mercury.ConfigKeyForPublicSea, mercury.DefaultExpireTime, &config); err != nil {
		zlog.Warnf(ctx, "[GetSmsConfig] get mercury config failed, err: %+v", err)
		return nil, err
	}

	ret := &SmsConfigInfo{
		Limit: config.SmsConfig.Limit,
	}

	tplVars := make(map[int64][]string)
	var getTplInfoReq touchmisgo.GetTplInfoReq
	for _, configItem := range config.SmsConfig.TplConfig {
		tplItem := touchmisgo.TplInfo{
			TplId: configItem.TplID,
			Vars:  configItem.Vars,
		}
		getTplInfoReq.TplList = append(getTplInfoReq.TplList, tplItem)

		tplVars[configItem.TplID] = configItem.Vars
	}

	tplMap, err := touchmisgo.GetSmsTplInfo(ctx, &getTplInfoReq)
	if err != nil {
		return nil, err
	}

	tplList := make([]touchmisgo.GetTplInfoRsp, 0, len(tplMap))
	for _, tplInfo := range tplMap {
		tplInfo.Vars = make([]string, 0)
		if value, ok := tplVars[tplInfo.TplID]; ok {
			tplInfo.Vars = value
		}
		tplList = append(tplList, tplInfo)
	}
	sort.Slice(tplList, func(i, j int) bool {
		return tplList[i].TplID < tplList[j].TplID
	})

	ret.TplList = tplList

	return ret, nil
}

func GetCallConfig(ctx *gin.Context, sourceType string) ([]mercury.CallLimitInfo, error) {
	var config mercury.TouchConfig
	if err := mercury.GetConfigForJson(ctx, mercury.ConfigKeyForPublicSea, mercury.DefaultExpireTime, &config); err != nil {
		zlog.Warnf(ctx, "[GetSmsConfig] get mercury config failed, err: %+v", err)
		return nil, err
	}

	var limitInfo mercury.LimitConfig
	switch sourceType {
	case components.TouchTypeForPublicSea:
		limitInfo = config.CallConfig.PublicSea
	case components.TouchTypeForPrivateSea:
		limitInfo = config.CallConfig.PrivateSea
	default:
		return nil, fmt.Errorf("sourceType undefined, sourceType: %s", sourceType)
	}
	ret := make([]mercury.CallLimitInfo, 0)
	if len(limitInfo.Limit) > 0 {
		ret = limitInfo.Limit
	}
	return ret, nil
}

func GetSmsSendConfig(ctx *gin.Context, req dtotouch.GetSmsSendConfigReq) (resp dtotouch.GetSmsSendConfigResp, err error) {
	config, err := GetSmsConfig(ctx)
	if err != nil {
		return
	}
	if config == nil || len(config.TplList) == 0 {
		err = components.ErrorSmsConfig
		return
	}

	get, err := helpers.RedisClient.Get(ctx, fmt.Sprintf(defines.CacheKeyRelationSmsSend, req.ClueId))
	if err != nil {
		return
	}
	resp.SendUsed = cast.ToInt64(string(get))

	resp.SendLimit = config.Limit
	resp.TplList = make([]dtotouch.SmsTplInfo, 0, len(config.TplList))
	now := time.Now().Unix()
	for _, tplInfo := range config.TplList {
		if tplInfo.UseTimeBegin > now || tplInfo.UseTimeEnd < now {
			// 模版已失效
			zlog.Infof(ctx, "GetSmsSendConfig.found invalid tpl:%+v", tplInfo)
			continue
		}
		resp.TplList = append(resp.TplList, dtotouch.SmsTplInfo{
			TplId:      tplInfo.TplID,
			TplContent: tplInfo.Content,
		})
	}
	// recheck
	if len(resp.TplList) == 0 {
		err = components.ErrorSmsConfig
		return
	}
	return
}
