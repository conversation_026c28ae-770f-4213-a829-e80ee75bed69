package relation

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/service/relation/datalayer"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetCallRecord(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	req := datalayer.GetCallRecordReq{
		FromUid: 2700134530,
		ClueIds: []string{"2135528585_542415"},
	}
	err := datalayer.GetPublicSeaCallSopData(ctx, req)
	zlog.Infof(ctx, "err :%+v", err)
}
