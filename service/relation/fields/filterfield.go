package fields

import (
	"assistantdeskgo/api/assistantdesk"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sort"
)

const (
	lLike = "lLike_" // 左模糊查询，目前仅支持:1.字符串类型列 2.等值查询
)

var FYearSeason = Field{
	Key:          "year_season",
	Label:        "学季",
	Type:         typeSelect,
	IsMulti:      false,
	optionSource: initYearSeason,
}

func initYearSeason(ctx *gin.Context) (ops []Option) {
	signalGetKey, err := assistantdesk.SignalGetKey(ctx, assistantdesk.YearKey)
	if err != nil {
		return
	}
	yearMap, ok := signalGetKey[assistantdesk.YearKey]
	if !ok {
		return
	}
	yearList := make([]string, 0, len(yearMap))
	for k, _ := range yearMap {
		yearList = append(yearList, k)
	}
	sort.SliceStable(yearList, func(i, j int) bool {
		return yearList[i] < yearList[j]
	})

	ops = make([]Option, 0)
	for _, k := range yearList {
		ops = append(ops, Option{
			Value: fmt.Sprintf("%s%d", k, 1),
			Text:  fmt.Sprintf("%s%s", k, "春"),
		})
		ops = append(ops, Option{
			Value: fmt.Sprintf("%s%d", k, 2),
			Text:  fmt.Sprintf("%s%s", k, "暑"),
		})
		ops = append(ops, Option{
			Value: fmt.Sprintf("%s%d", k, 3),
			Text:  fmt.Sprintf("%s%s", k, "秋"),
		})
		ops = append(ops, Option{
			Value: fmt.Sprintf("%s%d", k, 4),
			Text:  fmt.Sprintf("%s%s", k, "寒"),
		})
	}
	return
}

var FSeasonSemester = Field{
	Key:          "season_semester",
	Label:        "期次",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initSeasonSemester,
}

func initSeasonSemester(ctx *gin.Context) (ops []Option) {
	signalGetKey, err := assistantdesk.SignalGetKey(ctx, assistantdesk.SeasonMapKey)
	if err != nil {
		return
	}
	seasonSemesterMap, ok := signalGetKey[assistantdesk.SeasonMapKey]
	if !ok {
		return
	}

	springList := make([]string, 0)
	summerList := make([]string, 0)
	autumnList := make([]string, 0)
	winterList := make([]string, 0)
	defaultList := make([]string, 0)
	for k, _ := range seasonSemesterMap {
		season := cast.ToInt(string(k[0]))
		switch season {
		case 1:
			springList = append(springList, k)
		case 2:
			summerList = append(summerList, k)
		case 3:
			autumnList = append(autumnList, k)
		case 4:
			winterList = append(winterList, k)
		default:
			defaultList = append(defaultList, k)
		}
	}
	sort.SliceStable(springList, func(i, j int) bool {
		return cast.ToInt64(springList[i]) < cast.ToInt64(springList[j])
	})
	sort.SliceStable(summerList, func(i, j int) bool {
		return cast.ToInt64(summerList[i]) < cast.ToInt64(summerList[j])
	})
	springList = append(springList, summerList...)
	sort.SliceStable(autumnList, func(i, j int) bool {
		return cast.ToInt64(autumnList[i]) < cast.ToInt64(autumnList[j])
	})
	springList = append(springList, autumnList...)
	sort.SliceStable(winterList, func(i, j int) bool {
		return cast.ToInt64(winterList[i]) < cast.ToInt64(winterList[j])
	})
	springList = append(springList, winterList...)
	springList = append(springList, defaultList...)

	ops = make([]Option, 0, len(springList))
	for _, s := range springList {
		name, _ := seasonSemesterMap[s]
		ops = append(ops, Option{
			Value: s,
			Text:  name,
		})
	}
	return
}

var FGrade = Field{
	Key:          "main_grade_id",
	Label:        "年级",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initGrade,
}

func initGrade(ctx *gin.Context) (ops []Option) {
	signalGetKey, err := assistantdesk.SignalGetKey(ctx, assistantdesk.GradeKey)
	if err != nil {
		return
	}
	gradeMap, ok := signalGetKey[assistantdesk.GradeKey]
	if !ok {
		return
	}

	ops = make([]Option, 0, len(gradeMap))
	for k, name := range gradeMap {
		ops = append(ops, Option{
			Value: cast.ToInt64(k),
			Text:  name,
		})
	}
	sort.SliceStable(ops, func(i, j int) bool {
		return cast.ToString(ops[i].Value) < cast.ToString(ops[j].Value)
	})
	return
}

var FSubject = Field{
	Key:          "main_subject_id",
	Label:        "学科",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initSubject,
}

func initSubject(ctx *gin.Context) (ops []Option) {
	signalGetKey, err := assistantdesk.SignalGetKey(ctx, assistantdesk.SubjectKey)
	if err != nil {
		return
	}
	subjectMap, ok := signalGetKey[assistantdesk.SubjectKey]
	if !ok {
		return
	}

	ops = make([]Option, 0, len(subjectMap))
	for k, name := range subjectMap {
		ops = append(ops, Option{
			Value: cast.ToInt64(k),
			Text:  name,
		})
	}
	sort.SliceStable(ops, func(i, j int) bool {
		return cast.ToInt64(ops[i].Value) < cast.ToInt64(ops[j].Value)
	})
	return
}

var FLastFrom = Field{
	Key:   lLike + "last_from",
	Label: "购课来源",
	Type:  typeInput,
}

var FAttendNum = Field{
	Key:     "attend_times",
	Label:   "按次数（到课）",
	Type:    typeSelect,
	IsMulti: true,
	Options: []Option{
		{Value: 0, Text: "0"},
		{Value: 1, Text: "1"},
		{Value: 2, Text: "2"},
		{Value: 3, Text: "3"},
		{Value: 4, Text: "4"},
		{Value: 5, Text: "5"},
		{Value: 6, Text: "6"},
		{Value: 7, Text: "7"},
		{Value: 8, Text: "8"},
		{Value: 9, Text: "9"},
	},
}

var FFinishNum = Field{
	Key:     "finish_times",
	Label:   "按次数（完课）",
	Type:    typeSelect,
	IsMulti: true,
	Options: []Option{
		{Value: 0, Text: "0"},
		{Value: 1, Text: "1"},
		{Value: 2, Text: "2"},
		{Value: 3, Text: "3"},
		{Value: 4, Text: "4"},
		{Value: 5, Text: "5"},
		{Value: 6, Text: "6"},
		{Value: 7, Text: "7"},
		{Value: 8, Text: "8"},
		{Value: 9, Text: "9"},
	},
}

var FBatch = Field{
	Key:   "batch",
	Label: "公海批次ID",
	Type:  typeInput,
}

var FCityLevel = Field{
	Key:          "city_level",
	Label:        "城市级别",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initCityLevel,
}

func initCityLevel(ctx *gin.Context) (ops []Option) {
	opList := GetCityLevelMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

var FUserType = Field{
	Key:          "user_type",
	Label:        "用户类型",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initUserType,
}

func initUserType(ctx *gin.Context) (ops []Option) {
	opList := GetUserTypeMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

var FTranStatus = Field{
	Key:          "trans_status",
	Label:        "转换状态",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initTransStatus,
}

func initTransStatus(ctx *gin.Context) (ops []Option) {
	opList := GetTransStatusMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

var FAddWxStatus = Field{
	Key:          "add_wx_status",
	Label:        "加微状态",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initAddWxStatus,
}

func initAddWxStatus(ctx *gin.Context) (ops []Option) {
	opList := GetAddWxStatusMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

var FRetrieveType = Field{
	Key:          "retrieve_type",
	Label:        "回捞方式",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initRetrieveTypeMap,
}

func initRetrieveTypeMap(ctx *gin.Context) (ops []Option) {
	opList := GetRetrieveTypeMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

/* 以下字段筛选需要走opLog前置 */

var FAccessStatus = Field{
	Key:          "accessStatus",
	Label:        "2h内接通状态",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initAccessStatus,
}

func initAccessStatus(ctx *gin.Context) (ops []Option) {
	opList := GetAccessStatusMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

var FManualIntention = Field{
	Key:          "manualIntention",
	Label:        "关注状态（手动标记）",
	Type:         typeSelect,
	IsMulti:      true,
	optionSource: initManualIntention,
}

func initManualIntention(ctx *gin.Context) (ops []Option) {
	opList := GetManualIntentionMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}

func initManualCallStatus(ctx *gin.Context) (ops []Option) {
	opList := GetManualCallStatusMap()
	ops = make([]Option, 0, len(opList))
	for v, name := range opList {
		ops = append(ops, Option{
			Value: v,
			Text:  name,
		})
	}
	return
}
