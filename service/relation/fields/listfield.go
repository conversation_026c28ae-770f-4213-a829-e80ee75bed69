package fields

var LStudentUid = ListField{
	FeConfig: FeConfig{
		Label:    "学员UID",
		CName:    "StudentUid",
		Slot:     true,
		MinWidth: 140,
	},
}

var LStudentName = ListField{
	FeConfig: FeConfig{
		Label:    "学员姓名",
		CName:    "StudentName",
		Slot:     true,
		MinWidth: 140,
	},
}

var LLeftCallCount = ListField{
	FeConfig: FeConfig{
		Label:    "可外呼次数",
		CName:    "LeftCallCount",
		Slot:     true,
		MinWidth: 120,
	},
}

var LLastFrom = ListField{
	FeConfig: FeConfig{
		Label:    "购课来源",
		CName:    "LastFrom",
		Slot:     true,
		MinWidth: 240,
	},
}

var LBatch = ListField{
	FeConfig: FeConfig{
		Label:    "公海批次ID",
		Prop:     "batch",
		MinWidth: 100,
	},
}

var LSemesterName = ListField{
	FeConfig: FeConfig{
		Label:    "期次",
		Prop:     "sourceSemesterName",
		MinWidth: 80,
	},
}

var LManualIntention = ListField{
	FeConfig: FeConfig{
		Label:    "关注状态",
		Prop:     "manualIntention",
		CName:    "Intention",
		Slot:     true,
		MinWidth: 100,
	},
	filterSource: initManualIntention,
}

var LManualCallRemark = ListField{
	FeConfig: FeConfig{
		Label:    "手动备注",
		Prop:     "manualCallStatus",
		CName:    "ManualCallRemark",
		Slot:     true,
		MinWidth: 240,
	},
	filterSource: initManualCallStatus,
}

var LAccessStatus = ListField{
	FeConfig: FeConfig{
		Label:    "2h内外呼状态",
		Prop:     "accessStatus",
		CName:    "AccessStatus",
		Slot:     true,
		MinWidth: 100,
	},
	filterSource: initAccessStatus,
}

var LCurrentCallNum = ListField{
	FeConfig: FeConfig{
		Label:       "当期本人外呼",
		CName:       "Rate",
		Slot:        true,
		MinWidth:    100,
		Numerator:   "callOwnerAccessNum",
		Denominator: "callOwnerNum",
	},
}

var LAllCallNum = ListField{
	FeConfig: FeConfig{
		Label:       "当期所有外呼",
		CName:       "Rate",
		Slot:        true,
		MinWidth:    100,
		Numerator:   "callAllAccessNum",
		Denominator: "callAllNum",
	},
}

var LCityInfo = ListField{
	FeConfig: FeConfig{
		Label:    "归属地区",
		CName:    "CityInfo",
		Slot:     true,
		MinWidth: 100,
	},
}

var LLearnReport = ListField{
	FeConfig: FeConfig{
		Label:    "学情诊断报告",
		CName:    "LearnReport",
		Slot:     true,
		MinWidth: 100,
	},
}

var LLatestAccessTime = ListField{
	FeConfig: FeConfig{
		Label:    "最新接通时间",
		Prop:     "latestAccessTime",
		MinWidth: 100,
	},
}

var LLatestOrderTime = ListField{
	FeConfig: FeConfig{
		Label:    "最新购课时间",
		Prop:     "latestOrderTime",
		MinWidth: 100,
	},
}

var LPublicSeaExpired = ListField{
	FeConfig: FeConfig{
		Label:    "公海存续倒计时",
		Prop:     "expiredTime",
		CName:    "PublicSeaExpired",
		Slot:     true,
		MinWidth: 100,
	},
}

var LAllocateTime = ListField{
	FeConfig: FeConfig{
		Label:    "分配时间",
		Prop:     "allocTime",
		MinWidth: 100,
	},
}

var LTransSubject = ListField{
	FeConfig: FeConfig{
		Label:    "转化详情",
		CName:    "TransSubject",
		Slot:     true,
		MinWidth: 100,
	},
}
