package fields

import "github.com/gin-gonic/gin"

const (
	typeSelect = "select"
	typeInput  = "input"
)

const (
	CityLevelFirst   CityLevel = 1
	CityLevelFirstBk CityLevel = 2
	CityLevelSecond  CityLevel = 3
	CityLevelThird   CityLevel = 4
	CityLevelFourth  CityLevel = 5
	CityLevelFifth   CityLevel = 6
	CityLevelOther   CityLevel = 255

	UserTypeLongCourse  UserType = 1
	UserTypeLongHistory UserType = 2
	UserTypeShortCourse UserType = 3
	UserTypeNewUser     UserType = 4

	AccessStatusUndo    AccessStatus = 0 // 未拨打
	AccessStatusCalled  AccessStatus = 1 // 未接通
	AccessStatusReached AccessStatus = 2 // 已接通

	ManualIntentionUndo   ManualIntention = 0 // 待标注
	ManualIntentionNever  ManualIntention = 1 // 不在关注
	ManualIntentionNormal ManualIntention = 2 // 一般关注
	ManualIntentionFocus  ManualIntention = 3 // 重点关注
	ManualIntentionTrans  ManualIntention = 4 // 待付款

	ManualCallStatusUndo       ManualCallStatus = 0 // 待沟通
	ManualCallStatusEmpty      ManualCallStatus = 1 // 空号
	ManualCallStatusShutdown   ManualCallStatus = 2 // 停机
	ManualCallStatusNotService ManualCallStatus = 3 // 不在服务区
	ManualCallStatusNotAccess  ManualCallStatus = 4 // 未接通
	ManualCallStatusDeny       ManualCallStatus = 5 // 拒接
	ManualCallStatusAccess     ManualCallStatus = 6 // 已接通
	ManualCallStatusFocus      ManualCallStatus = 7 // 关注

	PublicSeaRetrieveType RetrieveType = 100007

	IsTransSuccess TransType = 1
	IsTransFailure TransType = 0

	AddWxUnbind WxType = 0
	AddWxBind   WxType = 1
)

type CityLevel = int64
type UserType = int64
type AccessStatus = int64
type ManualIntention = int64
type ManualCallStatus = int64
type RetrieveType = int64
type TransType = int64
type WxType = int64

// Field 筛选字段
type Field struct {
	Key          string
	Label        string
	Type         string
	Placeholder  string
	IsMulti      bool
	Options      []Option
	optionSource func(ctx *gin.Context) []Option
}

func (f *Field) InitOptions(ctx *gin.Context) {
	if f.optionSource != nil {
		f.Options = f.optionSource(ctx)
	}
}

type Option struct {
	Value interface{}
	Text  string
}

// ListField 列表字段
type ListField struct {
	FilterMap    []Option
	FeConfig     FeConfig
	filterSource func(ctx *gin.Context) []Option
}

func (f *ListField) InitOptions(ctx *gin.Context) {
	if f.filterSource != nil {
		f.FilterMap = f.filterSource(ctx)
	}
}

type FeConfig struct {
	Label       string
	Prop        string
	Slot        bool
	CName       string
	Width       int64  // 固定宽度；当页面过大时，容易占不满一个横轴
	MinWidth    int64  // 最小宽度；当页面过大时，会被分配超过所有字段宽度的部分
	Numerator   string // 分子
	Denominator string // 分母
}

func GetCityLevelMap() map[int64]string {
	return map[int64]string{
		CityLevelFirst:   "一线",
		CityLevelFirstBk: "准一线",
		CityLevelSecond:  "二线",
		CityLevelThird:   "三线",
		CityLevelFourth:  "四线",
		CityLevelFifth:   "五线",
		CityLevelOther:   "其他",
	}
}

func GetUserTypeMap() map[int64]string {
	return map[int64]string{
		UserTypeLongCourse:  "在读班课用户",
		UserTypeLongHistory: "历史班课用户",
		UserTypeShortCourse: "拉新课用户",
		UserTypeNewUser:     "纯新用户",
	}
}

func GetAccessStatusMap() map[int64]string {
	return map[int64]string{
		AccessStatusUndo:    "未拨打",
		AccessStatusCalled:  "未接通",
		AccessStatusReached: "已接通",
	}
}

func GetManualIntentionMap() map[int64]string {
	return map[int64]string{
		ManualIntentionUndo:   "待标注",
		ManualIntentionNever:  "不再关注",
		ManualIntentionNormal: "一般关注",
		ManualIntentionFocus:  "重点关注",
		ManualIntentionTrans:  "待付款",
	}
}

func GetManualCallStatusMap() map[int64]string {
	return map[int64]string{
		ManualCallStatusUndo:       "待沟通",
		ManualCallStatusEmpty:      "空号",
		ManualCallStatusShutdown:   "停机",
		ManualCallStatusNotService: "不在服务区",
		ManualCallStatusNotAccess:  "未接通",
		ManualCallStatusDeny:       "拒接",
		ManualCallStatusAccess:     "已接通",
		ManualCallStatusFocus:      "关注",
	}
}

func GetRetrieveTypeMap() map[int64]string {
	return map[int64]string{
		PublicSeaRetrieveType: "公海回捞",
	}
}

func GetTransStatusMap() map[int64]string {
	return map[int64]string{
		IsTransFailure: "未转化",
		IsTransSuccess: "转化",
	}
}

func GetAddWxStatusMap() map[int64]string {
	return map[int64]string{
		AddWxUnbind: "未加微",
		AddWxBind:   "已加微",
	}
}
