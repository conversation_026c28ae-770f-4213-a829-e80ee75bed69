package exerciseNote

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"encoding/csv"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

func GetMistakeByCourseAndTask(ctx *gin.Context, courseId, taskId int64) (list [][]string, err error) {
	userInfo := components.GetUserInfo(ctx)
	if userInfo == nil {
		return
	}
	assistantUid := userInfo.SelectedBusinessUid
	//查询错题详情信息
	fields := "student_uid,tid,tid_idx,is_repeat_wrong"
	req := dataproxy.GetCourseTaskTiStuDataReq{
		CourseId:     courseId,
		TaskId:       taskId,
		AssistantUid: assistantUid,
		Fields:       fields,
	}
	resultData, err := dataproxy.GetCourseTaskTiStuData(ctx, req)
	if len(resultData.List) < 0 {
		return
	}
	//查询用户信息
	var studentUidList []int64
	for _, info := range resultData.List {
		studentUidList = append(studentUidList, info.StudentUid)
	}
	studentMap, err := dau.GetStudents(ctx, studentUidList, []string{"studentUid", "studentName", "phone"})
	if err != nil {
		return
	}
	//组装CSV数据
	for _, tiInfo := range resultData.List {
		var tmpData []string
		studentUidStr := strconv.Itoa(int(tiInfo.StudentUid))
		isRepeatWrong := "是"
		if tiInfo.IsRepeatWrong == 0 {
			isRepeatWrong = "否"
		}
		tmpData = append(tmpData, strconv.Itoa(tiInfo.TidIndex), strconv.Itoa(int(tiInfo.Tid)), studentUidStr, studentMap[tiInfo.StudentUid].StudentName, isRepeatWrong)
		list = append(list, tmpData)
	}
	return
}

func ExportMistake(ctx *gin.Context, data [][]string) error {
	//设置请求头
	//TODO::text/csv
	ctx.Writer.Header().Set("Content-Type", "application/octet-stream;charset=UTF-8")
	ctx.Writer.Header().Set("Content-Disposition", "attachment; filename=classmistakelist.csv")
	ctx.Writer.Header().Set("Cache-Control", "no-cache")
	ctx.Writer.Header().Set("Connection", "keep-alive")
	ctx.Writer.Header().Set("Transfer-Encoding", "chunked")
	flusher, ok := ctx.Writer.(http.Flusher)
	if !ok {
		zlog.Errorf(ctx, "Streaming unsupported")
		return nil
	}
	header := []string{"小题ID", "大题ID", "用户UID", "用户姓名", "是否重复做错"}
	//写入svc
	FullGenerateCSVCommon(ctx, ctx.Writer, flusher, header, data)

	return nil
}

func FullGenerateCSVCommon(ctx *gin.Context, w http.ResponseWriter, flusher http.Flusher, header []string, data [][]string) {

	// Create a CSV writer
	csvWriter := csv.NewWriter(w)

	// Write the custom header
	if err := csvWriter.Write(header); err != nil {
		_ = ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if len(data) > 0 {
		for i := 0; i < len(data); i++ {
			if err := csvWriter.Write(data[i]); err != nil {
				_ = ctx.AbortWithError(http.StatusInternalServerError, err)
				return
			}
		}
	}
	csvWriter.Flush()
	flusher.Flush()
}
