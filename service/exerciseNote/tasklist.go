package exerciseNote

import (
	"assistantdeskgo/api/exercise"
	"assistantdeskgo/api/moatapi"
	"assistantdeskgo/dto/dtoexercisenote"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetTaskUrl(ctx *gin.Context, courseId int64, appId string, taskId int64) (result dtoexercisenote.TaskUrlResponse, err error) {
	//单纯只获取列表的时候，appId走默认就可以
	if len(appId) <= 0 {
		appId = "airclass"
	}
	//通过appId和courseId获取任务列表
	taskListReq := exercise.TaskListReq{
		CourseId: courseId,
		AppId:    appId,
	}
	taskList, err := exercise.GetTaskList(ctx, taskListReq)
	if err != nil || len(taskList) <= 0 {
		zlog.AddNotice(ctx, "getTaskListErr", err)
		return
	}

	for _, taskInfo := range taskList {
		if taskInfo.TaskId != taskId {
			continue
		}
		shortUrl, err := moatapi.GetShortUrl(ctx, taskInfo.ExamUrl)
		if err != nil {
			return result, err
		}
		result.TaskUrl = shortUrl
		result.ReportUrl = taskInfo.ResultUrl
	}
	return
}
