package filter

import (
	"assistantdeskgo/api/moatapi"
	"assistantdeskgo/dto/dtofilter"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/exercise"
	"github.com/gin-gonic/gin"
)

func GetPronunciationUrl(ctx *gin.Context, courseId, lessonId, studentUid int64) (resp dtofilter.GetPronunciationUrlRsp, err error) {

	reportList, err := exercise.GetPronunciationUrl(ctx, exercise.GetPronunciationUrlReq{
		CourseId:    courseId,
		LessonId:    lessonId,
		StudentUids: []int64{studentUid},
	})
	if err != nil {
		return
	}
	for _, report := range reportList {
		if report.StudentUid == studentUid {

			shortUrl, shortUrlErr := moatapi.GetShortUrl(ctx, report.ReportUrl)
			if shortUrlErr != nil {
				resp.Url = report.ReportUrl
			} else {
				resp.Url = shortUrl
			}
			break
		}
	}

	return
}

func GetVoiceUrl(ctx *gin.Context, courseId, lessonId, studentUid int64) (resp dtofilter.GetVoiceUrlRsp, err error) {

	reportList, err := exercise.GetDubUrl(ctx, exercise.GetDubReportUrlReq{
		CourseId:    courseId,
		LessonId:    lessonId,
		StudentUids: []int64{studentUid},
	})
	if err != nil {
		return
	}
	for _, report := range reportList {
		if report.StudentUid == studentUid {

			shortUrl, shortUrlErr := moatapi.GetShortUrl(ctx, report.ReportUrl)
			if shortUrlErr != nil {
				resp.Url = report.ReportUrl
			} else {
				resp.Url = shortUrl
			}
			break
		}
	}

	return
}
