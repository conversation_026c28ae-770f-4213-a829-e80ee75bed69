package filter

import (
	"assistantdeskgo/api/assistantcourse"
	"assistantdeskgo/dto/dtofilter"
	"errors"
	"github.com/gin-gonic/gin"
)

const ASSISANT_COURSE_FILTER_KEY = "assistantcourseAppointmentCourseDetail.detail"
const LPC_ASSISANT_COURSE_FILTER_KEY = "lpcAssistantcourseAppointmentCourseDetail.lpcAssistantcourseAppointmentCourseDetailFilter"

type getRemoteSelectFunc func(ctx *gin.Context, courseId, lessonId int64) (dtofilter.GetRemoteSelectResp, error)

var remoteSelectFuncMap = map[string]getRemoteSelectFunc{
	ASSISANT_COURSE_FILTER_KEY:     getRemoteSelectAssistantCourse,
	LPC_ASSISANT_COURSE_FILTER_KEY: getRemoteSelectAssistantCourse,
}

func getRemoteSelectAssistantCourse(ctx *gin.Context, courseId, lessonId int64) (dtofilter.GetRemoteSelectResp, error) {
	itemList, err := assistantcourse.GetSelectItem(ctx, assistantcourse.GetSelectItemReq{
		CourseId: courseId,
	})
	if err != nil {
		return dtofilter.GetRemoteSelectResp{}, err
	}
	options := []dtofilter.GetRemoteSelectOption{}
	for _, item := range *itemList {
		options = append(options, dtofilter.GetRemoteSelectOption{item.Value, item.Text, item.LessonId})
	}
	return dtofilter.GetRemoteSelectResp{Key: ASSISANT_COURSE_FILTER_KEY, Options: options}, nil
}

func RemoteSelect(ctx *gin.Context, courseId, lessonId int64, keys []string) (resp map[string]dtofilter.GetRemoteSelectResp, err error) {
	resp = map[string]dtofilter.GetRemoteSelectResp{}
	for _, key := range keys {
		fun, ok := remoteSelectFuncMap[key]
		if !ok {
			return nil, errors.New("key not find")
		}
		selectData, err := fun(ctx, courseId, lessonId)
		if err != nil {
			return nil, err
		}
		resp[key] = selectData
	}
	return
}
