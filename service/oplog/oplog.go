package oplog

import (
	"assistantdeskgo/dto/dtooplog"
	"assistantdeskgo/models/oplog"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

var (
	concurrency = 5
	sem         = make(chan dtooplog.OperateLogAddReq, concurrency)
)

func InsertLog(ctx *gin.Context, req dtooplog.OperateLogAddReq) error {

	zlog.Infof(ctx, "add operate log: %+v", req)
	model := oplog.OperateLog{
		Content:      strings.Trim(req.Content, " "),
		Before:       strings.Trim(req.Before, " "),
		OperateTime:  getOperateTime(req.OperateTime),
		RelationId:   strings.Trim(req.RelationId, " "),
		RelationType: strings.Trim(req.RelationType, " "),
		CreateTime:   time.Now().Unix(),
		Refer:        strings.Trim(req.Refer, " "),
		Remark:       strings.Trim(req.Remark, " "),
		AssistantUid: req.AssistantUid,
		PersonUid:    req.PersonUid,
		Service:      strings.Trim(req.Service, " "),
		RequestId:    zlog.GetRequestID(ctx),
		Module:       strings.Trim(req.Module, " "),
		LogId:        zlog.GetLogID(ctx),
	}
	return oplog.OperateLogRef.Insert(ctx, model)

}

func getOperateTime(operatorTime int64) int64 {
	if operatorTime == 0 {
		return time.Now().Unix()
	}
	return operatorTime
}

func AddBaseLog(ctx *gin.Context, relationId, relationType, refer, module, service string, content interface{}, personUid int64, remark string) {
	AddLog(ctx, relationId, relationType, refer, module, service, content, personUid, remark, "", 0)
}

func doPost(ctx *gin.Context, req dtooplog.OperateLogAddReq) { //类似线程池的作用
	sem <- req
	{
		// 模拟工作
		err := InsertLog(ctx, req)
		if err != nil {
			zlog.Warnf(ctx, "InsertOperateLog Err data=%+v,err=%v", req, err)
		}
	}
	// 释放semaphore
	<-sem
}

func AddLog(ctx *gin.Context, relationId, relationType, refer, module, service string, content interface{}, personUid int64, remark, before string, assistantUid int64) {

	if relationId == "" || relationType == "" || refer == "" || module == "" || service == "" {
		zlog.Warnf(ctx, "AddLog Fail,param err")
		return
	}

	contentStr, err := json.Marshal(content)
	if err != nil {
		zlog.Warnf(ctx, "AddLog Fail,content err=%v", err)
		return
	}
	Add(ctx, dtooplog.OperateLogAddReq{
		RelationId:   relationId,
		RelationType: relationType,
		Before:       before,
		Remark:       remark,
		Refer:        refer,
		Module:       module,
		Service:      service,
		Content:      string(contentStr),
		PersonUid:    personUid,
		AssistantUid: assistantUid,
		LogId:        zlog.GetLogID(ctx),
		RequestId:    zlog.GetRequestID(ctx),
		OperateTime:  time.Now().Unix(),
	})

}

func Add(ctx *gin.Context, logReq dtooplog.OperateLogAddReq) {
	GoWithRecover(ctx, func() {
		doPost(ctx, logReq)
	})
}

func GoWithRecover(ctx *gin.Context, f func()) {
	go func() {
		defer func() {
			if _err := recover(); _err != nil {
				zlog.Errorf(ctx, "[GoWithRecover] the function catch panic err, msg: %+v", _err)
			}
		}()
		f()
	}()
}

func QueryLog(ctx *gin.Context, logReq dtooplog.OperateLogQueryReq) (res []dtooplog.OperateLogQueryRsp, err error) {

	page := 1
	pageSize := 20
	if logReq.Page > 0 && logReq.PageSize > 0 {
		page = logReq.Page
		pageSize = logReq.PageSize
	}

	opInfoList, err := oplog.OperateLogRef.Query(ctx, logReq.RelationId, logReq.RelationType, logReq.PersonUid, page, pageSize)
	if err != nil {
		return nil, err
	}
	for _, opInfo := range opInfoList {
		res = append(res, dtooplog.OperateLogQueryRsp{
			Refer:        opInfo.Refer,
			Content:      opInfo.Content,
			Remark:       opInfo.Remark,
			Service:      opInfo.Service,
			Module:       opInfo.Module,
			Before:       opInfo.Before,
			RelationId:   opInfo.RelationId,
			RelationType: opInfo.RelationType,
			AssistantUid: opInfo.AssistantUid,
			LogId:        opInfo.LogId,
			RequestId:    opInfo.RequestId,
			PersonUid:    opInfo.PersonUid,
			OperateTime:  opInfo.OperateTime,
		})
	}
	return
}
