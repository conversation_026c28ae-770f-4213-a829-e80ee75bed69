package commonservice

import (
	"assistantdeskgo/api/userprofile"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

func GetUserProductLineGroupIds(ctx *gin.Context, userId int64) (lineGroupIdsMap map[int64][]int64, err error) {
	lineGroupIdsMap = make(map[int64][]int64)
	if userId == 0 {
		return
	}

	uidInfoMap, err := userprofile.GetStaffInfoByUIDs(ctx, userprofile.GetStaffInfoByUIDsReq{
		UserIds: []int64{userId},
	})
	if err != nil {
		return
	}
	userInfo, ok := uidInfoMap[userId]
	if !ok {
		zlog.Infof(ctx, "GetUserProductLineGroupIds.search userId[%d], but found:%+v", userId, userInfo)
		return
	}
	if len(userInfo.Group) == 0 {
		zlog.Infof(ctx, "GetUserProductLineGroupIds.search userId[%d] no group", userId)
		return
	}

	groupIdLineMap := make(map[int64]int64)
	groupIds := make([]int64, 0)
	for _, group := range userInfo.Group {
		groupIdLineMap[group.GroupId] = group.ProductLine
		groupIds = append(groupIds, group.GroupId)
	}

	detailMap, err := userprofile.GetGroupDetailByIds(ctx, userprofile.GetGroupDetailByIdsReq{
		GroupIds: groupIds,
	})
	if err != nil {
		return
	}

	for groupId, line := range groupIdLineMap {
		detailInfo, exist := detailMap[groupId]
		if !exist {
			zlog.Warnf(ctx, "GetGroupDetailByIds not found groupId[%d]", groupId)
			continue
		}

		levelStr := fmt.Sprintf("%s%d", detailInfo.LevelStr, groupId)
		levelIds := strings.Split(levelStr, ",")
		lineGroupIdsMap[line], _ = fwyyutils.ConvertArrayStringToArrayInt64(levelIds)
	}
	return
}
