package commonservice

import (
	dtocommon "assistantdeskgo/dto/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/mercury"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

const (
	MercuryConfigKeyForSopTaskFilterRule = "touchmisgo_mercury_once_task_config"
)

func GetOnceTaskMercuryConfig(ctx *gin.Context) (*dtocommon.OnceTaskMercuryConfig, error) {
	var config *dtocommon.OnceTaskMercuryConfig
	if err := mercury.GetConfigForJson(ctx, MercuryConfigKeyForSopTaskFilterRule, mercury.DefaultExpireTime, &config); err != nil {
		return nil, errors.WithMessagef(err, "[GetOnceTaskFilterRuleConfig] GetConfigForJson failed")
	}

	return config, nil
}
