package commonservice

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/components"
	"assistantdeskgo/conf"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"git.zuoyebang.cc/saas/saaslib/ucloud"
	"github.com/gin-gonic/gin"
)

const (
	AESSecret = "5Yc&^uEz"
)

var Student StudentStruct

type StudentStruct struct{}

func (s *StudentStruct) DecryptPhone(ctx *gin.Context, studentUid int64, encryptPhone string) (phone string, err error) {
	phone = utils.AESDecrypt(encryptPhone, AESSecret)
	if len(phone) == 0 {
		zlog.Warnf(ctx, "Student.DecryptPhone decrypt studentUid[%d],encryptPhone[%s] failed", studentUid, encryptPhone)
		err = components.ErrorInvalidPhone
		return
	}

	// get from ucloud
	userInfo, err := ucloud.NewUcloud(&conf.API.UCloud, "ucloud", ctx).GetUserInfo(studentUid)
	if err != nil {
		zlog.Warnf(ctx, "Student.DecryptPhone get ucloud studentUid[%d],encryptPhone[%s],err:%+v", studentUid, encryptPhone, err)
		return
	}
	if userInfo != nil && phone == userInfo.Phone {
		return
	}

	// get from dau
	studentUidMap, err := dau.GetStudents(ctx, []int64{studentUid}, []string{"phone", "registerPhone", "guardianPhone", "fatherPhone", "motherPhone"})
	if err != nil {
		return
	}
	studentInfo, ok := studentUidMap[studentUid]
	if !ok {
		return
	}
	if phone != studentInfo.RegisterPhone && phone != studentInfo.Phone && phone != studentInfo.GuardianPhone && phone != studentInfo.FatherPhone && phone != studentInfo.MotherPhone {
		zlog.Warnf(ctx, "Student.DecryptPhone decrypt studentUid[%d],invalid phone[%s]", studentUid, phone)
		err = components.ErrorInvalidPhone
		return
	}
	return
}
