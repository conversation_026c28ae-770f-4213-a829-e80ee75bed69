package commonservice

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/mesh"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

const (
	DeviceInfoCachePre    = "assistantdeskgo_device_info_"
	DeviceInfoCacheExpire = components.Minute * 10
)

func GetStaffUid(ctx *gin.Context, assistantUid int64) (int64, error) {
	cacheKey := fmt.Sprintf("%s%d", DeviceInfoCachePre, assistantUid)
	value, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err == nil && len(value) > 0 {
		return cast.ToInt64(string(value)), nil
	}

	deviceInfo, err := mesh.GetDeviceInfoListByDeviceUid(ctx, assistantUid)
	if err != nil {
		return 0, err
	}

	if deviceInfo.StaffUID > 0 {
		_ = helpers.RedisClient.SetEx(ctx, cacheKey, deviceInfo.StaffUID, DeviceInfoCacheExpire)
	}
	ret := deviceInfo.StaffUID

	return ret, nil
}
