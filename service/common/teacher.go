package commonservice

import (
	"assistantdeskgo/api/dat"
	"assistantdeskgo/api/dau"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

func GetTeacherNameByCourseIds(ctx *gin.Context, courseId int64) ([]string, error) {
	teacherIds, err := dat.GetCourseTeachersByCourseId(ctx, courseId)
	if err != nil {
		return nil, errors.WithMessagef(err, "[GetTeacherNameByCourseIds] get teachers by course id failed")
	}

	teacherInt64Ids := make([]int64, 0)
	for _, teacher := range teacherIds {
		teacherInt64Ids = append(teacherInt64Ids, int64(teacher))
	}
	teacherFields := []string{"teacherUid", "teacherName"}
	teacherMap, err := dau.GetTeachers(ctx, teacherInt64Ids, teacherFields)
	if err != nil {
		return nil, errors.WithMessagef(err, "[GetTeacherNameByCourseIds] get teachers failed")
	}

	teacherNames := make([]string, 0)
	for _, teacher := range teacherMap {
		tmpTeacher := teacher
		teacherNames = append(teacherNames, tmpTeacher.TeacherName)
	}
	return teacherNames, nil
}
