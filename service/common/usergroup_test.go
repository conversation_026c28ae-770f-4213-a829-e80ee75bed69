package commonservice

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetUserProductLineGroupIds(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	ret, err := GetUserProductLineGroupIds(ctx, 3000040874)
	t.Log(ret, err)
}

func TestMaskPhone(t *testing.T) {
	phone := "13512345678"

	fmt.Println("maskPhone: " + utils.MaskPhone11(phone))

	enc := utils.AESEncrypt(phone, AESSecret)
	fmt.Println("encrypt: " + enc)

	fmt.Println("decrypt: " + utils.AESDecrypt(enc, AESSecret))
}
