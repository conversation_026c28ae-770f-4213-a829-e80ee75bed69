package bailingapi

import (
	"assistantdeskgo/api/assistantai"
	"github.com/gin-gonic/gin"
)

func CheckUserInfo(ctx *gin.Context, ipsId int64, ipsName string, corpId string, userId string) (bool, error) {
	data, err := assistantai.CheckUserInfo(ctx, assistantai.CheckUserInfoReq{IpsId: ipsId, IpsName: ipsName, CorpId: corpId, UserId: userId})
	if err != nil {
		return false, err
	}
	return data.IsValid, nil
}
