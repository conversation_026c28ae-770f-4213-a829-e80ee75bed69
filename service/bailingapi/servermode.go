package bailingapi

import (
	"assistantdeskgo/api/assistantai"
	"github.com/gin-gonic/gin"
)

func SetServerMode(ctx *gin.Context, ipsId int64, ipsName string, mode int64) error {
	_, err := assistantai.SetServerMode(ctx, assistantai.SetServerModeReq{IpsId: ipsId, Mode: mode, IpsName: ipsName})
	return err
}

func GetServerMode(ctx *gin.Context, ipsId int64, name string) (interface{}, error) {
	return assistantai.GetServerMode(ctx, assistantai.GetServerModeReq{IpsId: ipsId, IpsName: name})
}
