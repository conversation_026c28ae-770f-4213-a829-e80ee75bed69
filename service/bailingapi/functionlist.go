package bailingapi

import (
	"assistantdeskgo/api/assistantai"
	"github.com/gin-gonic/gin"
)

func FunctionList(ctx *gin.Context, windowType int64, corpId string, userId string, uid int64, assistantUid int64) (interface{}, error) {
	return assistantai.GetFunctionList(ctx, assistantai.FunctionListReq{
		CorpId:       corpId,
		UserId:       userId,
		PersonUid:    uid,
		AssistantUid: assistantUid,
		Type:         windowType,
	})
}
