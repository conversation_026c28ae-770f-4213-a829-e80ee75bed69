package bailingapi

import (
	"assistantdeskgo/api/mercury"
	utils2 "assistantdeskgo/utils"
	"github.com/gin-gonic/gin"
)

type BailingGray struct {
	IpsIdList []int64 `json:"ipsIdList"`
	Percent   int64   `json:"percent"`
}

func GrayHit(ctx *gin.Context, key string, personUid int64) bool {

	var bailingGray BailingGray
	err := mercury.GetConfigForJson(ctx, key, 30, &bailingGray)
	if err != nil {
		return false
	}
	if utils2.InArrayInt64(personUid, bailingGray.IpsIdList) {
		return true
	}

	if personUid%100 < bailingGray.Percent {
		return true
	}
	return false
}
