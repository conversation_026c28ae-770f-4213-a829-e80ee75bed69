package sop

import (
	"encoding/json"

	"assistantdeskgo/dto/dtosop"
	"assistantdeskgo/models/remark"
	"assistantdeskgo/utils/task"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func StudentCourseRemark(ctx *gin.Context, req dtosop.StudentCourseRemarkReq) (res dtosop.StudentCourseRemarkList, err error) {
	res.List = make([]dtosop.StudentCourseRemarkInfo, 0)
	remarks := []remark.TblAssistantCourseStudent1History{}

	tblAssistantCourseStudent1History := remark.TblAssistantCourseStudent1History{}
	t := task.NewAsyncTask()
	t.Add(func() {
		cond := remark.FieldsOfHistory{
			CourseId:   req.CourseId,
			StudentUid: req.StudentUid,
			Page:       req.<PERSON>,
			Size:       req.PageSize,
		}
		remarks, err = tblAssistantCourseStudent1History.GetCourseStudentList(ctx, cond)
	})
	t.Add(func() {
		res.Total, _ = tblAssistantCourseStudent1History.GetCntByCond(ctx, map[string]interface{}{"course_id": req.CourseId, "student_uid": req.StudentUid})
	})
	t.Wait()
	for _, v := range remarks {
		var remarkInfo dtosop.StudentCourseRemarkInfo
		err := json.Unmarshal([]byte(v.ExtData), &remarkInfo)
		if err == nil && remarkInfo.ScRemark != "" {
			res.List = append(res.List, remarkInfo)
		} else if err != nil {
			zlog.Warnf(ctx, "Unmarshal remark info fail,remark=%v,err=%v", v.ExtData, err)
		}
	}

	return res, nil

}
