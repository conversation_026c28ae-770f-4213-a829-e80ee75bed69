package pictask

import (
	"assistantdeskgo/api/officeserver"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtocreatepictask"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"os"
	"strconv"
	"strings"
)

const (
	PicPrefix = "jpg"
	PicSuffix = ".jpg"

	RedisKeyChildTask           = "deskgo:capture_key_%s"
	RedisKeyChildTaskExpireTime = 30
)

func CreateChildTask(ctx *gin.Context, p mcpack.V2Map) error {
	childTask := dtocreatepictask.ChildTaskItem{
		PicUrl:       cast.ToString(p["picUrl"]),
		SendId:       cast.ToString(p["sendId"]),
		ParentTaskId: cast.ToInt(p["parentTaskId"]),
		UseType:      cast.ToInt(p["useType"]),
		Width:        cast.ToInt(p["width"]),
		Height:       cast.ToInt(p["height"]),
		ExtData:      cast.ToString(p["extData"]),
		Cookies:      cast.ToString(p["cookieStr"]),
	}
	zlog.Infof(ctx, "214200_CreateChildTask_Param: %+v", childTask)

	if childTask.ParentTaskId <= 0 || childTask.PicUrl == "" || childTask.SendId == "" {
		return errors.New("参数错误")
	}

	task := models.CreateCaptureTask{Id: childTask.ParentTaskId}
	if taskInfo, err := task.GetTaskInfoById(ctx, false); err != nil || taskInfo.Status != models.DoCreating {
		// 可能是主从延迟，强制读主库
		if taskInfo, err = task.GetTaskInfoById(ctx, true); err != nil || taskInfo.Status != models.DoCreating {
			return errors.New("没有找到主任务")
		}
	}

	// 发送url
	query := map[string]string{
		"parentTaskId": strconv.Itoa(childTask.ParentTaskId),
		"sendId":       childTask.SendId,
		"Type":         strconv.Itoa(childTask.UseType),
	}
	callbackUrl := GetCurrentUrl(ctx, defines.CreateCaptureApi, query)
	param := officeserver.GenerateOffLineUrlParam(ctx, childTask, callbackUrl)
	capture, err := officeserver.CreateCapture(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "sendSplitTaskError, studentUid:%i, err:%v", childTask.ParentTaskId, err)
		SetTaskError(ctx, task, err)
		return err
	}

	// 创建子任务
	extData, _ := jsoniter.MarshalToString(map[string]interface{}{
		"linkUrl": childTask.PicUrl,
	})
	createChildTask := models.CreateCaptureChildTask{
		ParentTaskId:   childTask.ParentTaskId,
		GenerateTaskId: capture.TaskId,
		PicUrl:         childTask.PicUrl,
		SendId:         childTask.SendId,
		Type:           childTask.UseType,
		ExtData:        extData,
	}
	err = createChildTask.Create(ctx, createChildTask)
	if err != nil {
		zlog.Warnf(ctx, "sendSplitTaskError, studentUid:%s, err:%v", childTask.ParentTaskId, err)
		SetTaskError(ctx, task, err)
		return err
	}

	return nil
}

func GetCurrentUrl(ctx *gin.Context, api string, param map[string]string) string {
	query := utils.HttpBuildQuery(param)
	if env.GetRunEnv() == env.RunEnvTest {
		shipEnvName := os.Getenv("SHIP_ENV_NAME")
		if shipEnvName == "" {
			shipEnvName = "base"
		}
		return fmt.Sprintf(defines.DomainOffline, shipEnvName) + api + "?" + query
	}
	clusterName := os.Getenv("CLUSTER_NAME")
	zlog.Infof(ctx, "当前所在集群,env:%s", clusterName)
	if strings.Contains(clusterName, defines.ENV_STABLE_KEY) {
		// 命中 stable
		param[defines.StableMatchKey] = defines.StableMatchValue
		query = utils.HttpBuildQuery(param)
		return defines.DomainFeature + api + "?" + query
	}
	return defines.DomainOnline + api + "?" + query
}

func PreCheckChildTask(ctx *gin.Context, query dtocreatepictask.QueryOfflineCallback, body dtocreatepictask.BodyOfflineCallback) error {
	if !getLock(ctx, body.TaskId) {
		return errors.New("该任务已经被消费")
	}
	task := models.CreateCaptureTask{Id: query.ParentTaskId}
	// 判断状态码是否正确，接口回调失败可能是负载原因，主动查询是否成功
	if query.ErrCode != 0 || len(body.ImgDwnURLs) <= 0 {
		detail, err := officeserver.GetTaskDetail(ctx, body.TaskId)
		if err != nil || detail.List[0].Status != "success" {
			zlog.Warnf(ctx, "param err:%s", err)
			SetTaskError(ctx, task, err)
			return err
		}
		if len(detail.List) <= 0 {
			err = errors.New("detail empty, taskId:" + body.TaskId)
			SetTaskError(ctx, task, err)
			return err
		}
		body.ImgDwnURLs = []string{detail.List[0].DwnUrl}
	}
	return HandleChildTask(ctx, query, body.TaskId, body.ImgDwnURLs[0])
}

func getLock(ctx *gin.Context, taskId string) bool {
	key := fmt.Sprintf(RedisKeyChildTask, taskId)
	if value, err := helpers.RedisClient.Get(ctx, key); len(value) > 0 || err != nil {
		return false
	}
	err := helpers.RedisClient.SetEx(ctx, key, 1, RedisKeyChildTaskExpireTime)
	if err != nil {
		zlog.Warnf(ctx, "capture_setEx_taskId, err:%s", err.Error())
		return false
	}
	return true
}

func HandleChildTask(ctx *gin.Context, query dtocreatepictask.QueryOfflineCallback, taskId, url string) error {
	task := models.CreateCaptureTask{Id: query.ParentTaskId}

	picUrl, err := utils.DownloadImgToBos(ctx, url)
	if err != nil {
		SetTaskError(ctx, task, err)
		return err
	}
	childTask := models.CreateCaptureChildTask{
		ParentTaskId:   query.ParentTaskId,
		GenerateTaskId: taskId,
		Status:         models.CreateCaptureChildTaskStatusSuccess,
		PicUrl:         picUrl,
	}
	err = childTask.ChangeStatus(ctx)
	if err != nil {
		SetTaskError(ctx, task, err)
		return err
	}

	// 更新子任务并同步触达
	redisKey := fmt.Sprintf(defines.RedisKeyCreateCaptureTask, query.ParentTaskId)
	incr, err := helpers.RedisClient.Incr(ctx, redisKey)
	if err != nil {
		// redis 执行重试一次
		incr, err = helpers.RedisClient.Incr(ctx, redisKey)
		if err != nil {
			SetTaskError(ctx, task, err)
		}
	}
	taskInfo, err := task.GetTaskInfoById(ctx, false)
	// 如果没有查询到任务信息，认为主从延迟，从主库读取一次
	if err != nil || taskInfo.Id == 0 {
		taskInfo, err = task.GetTaskInfoById(ctx, true)
		if err != nil {
			SetTaskError(ctx, task, err)
			return err
		}
	}

	// 发送截图任务完成mq
	if taskInfo.ChildNum == int(incr) {
		taskInfo.Status = models.CreateSuccess
		_ = taskInfo.ChangeStatus(ctx)

		finishTask, err := FinishParamFormat(ctx, taskInfo)
		zlog.Infof(ctx, "sendOverCaptureTask_param: %+v", finishTask)
		resp, err := rmq.SendCmd(ctx, "self_product", 214201, "core", "zb", finishTask, "")
		if err != nil {
			zlog.Warnf(ctx, "sendOverCaptureTask error, msgId:%s, err:%v", resp.TransID, err)
			return err
		}
	}

	return nil
}
