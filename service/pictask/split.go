package pictask

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtocreatepictask"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"strconv"
)

const (
	ExpireTimeCaptureTask = 30 * 60
)

func CreateTask(ctx *gin.Context, paramTask dtocreatepictask.CreateCaptureTask) (int, error) {
	var childNum int
	for _, picUrlItem := range paramTask.PicUrlItems {
		childNum += len(picUrlItem.PicUrl)
	}
	task := models.CreateCaptureTask{
		AssistantUid: paramTask.AssistantUid,
		ChildNum:     childNum,
		UseType:      paramTask.UseType,
		ExtData:      paramTask.ExtData,
		UnionId:      paramTask.UnionId,
	}
	err := task.CreateTask(ctx, &task)
	if err != nil {
		return 0, err
	}

	// 超时时间设置30分钟
	redisKey := fmt.Sprintf(defines.RedisKeyCreateCaptureTask, task.Id)
	err = helpers.RedisClient.SetEx(ctx, redisKey, 0, ExpireTimeCaptureTask)
	if err != nil {
		return 0, err
	}

	SplitChildTask(ctx, paramTask.PicUrlItems, &task)
	return task.Id, err
}

func SplitChildTask(ctx *gin.Context, childItems []dtocreatepictask.PicUrlItem, task *models.CreateCaptureTask) {
	context := ctx.Copy()

	for _, childTask := range childItems {
		childTask.TaskId = task.Id
		for _, url := range childTask.PicUrl {
			go SendCreateChildTask(context, task, childTask, url)
		}
	}
}

func SendCreateChildTask(ctx *gin.Context, task *models.CreateCaptureTask, childTask dtocreatepictask.PicUrlItem, url string) {
	cookies := ctx.Request.Cookies()
	var cookieMaps []map[string]string
	for _, cookie := range cookies {
		cookieMaps = append(cookieMaps, map[string]string{
			"name":  cookie.Name,
			"value": cookie.Value,
			"url":   url,
		})
	}
	cookieStr, _ := jsoniter.MarshalToString(cookieMaps)
	data := map[string]interface{}{
		"picUrl":       url,
		"parentTaskId": childTask.TaskId,
		"useType":      task.UseType,
		"height":       childTask.Height,
		"width":        childTask.Width,
		"extData":      task.ExtData,
		"cookieStr":    cookieStr,
	}
	if task.UseType == models.UseTypeSendSig {
		data["sendId"] = strconv.Itoa(childTask.StudentUid)
	} else if task.UseType == models.UseTypeSendGroup {
		data["sendId"] = childTask.GroupId
	}
	zlog.Infof(ctx, "sendCaptureChildTaskMQ, param:%+v", data)
	resp, err := rmq.SendCmd(ctx, "self_product", 214200, "core", "zb", data, "")
	if err != nil {
		task := models.CreateCaptureTask{Id: task.Id, Status: models.CreateFailed}
		SetTaskError(ctx, task, err)
		zlog.Warnf(ctx, "sendSplitTaskError, msgId:%+v, err:%v", resp, err)
	}
}

func FinishParamFormat(ctx *gin.Context, taskInfo models.CreateCaptureTask) (map[string]interface{}, error) {

	childTask := models.CreateCaptureChildTask{ParentTaskId: taskInfo.Id}
	childTasks, err := childTask.GetChildTaskByPId(ctx, false)
	if err != nil {
		return nil, err
	}

	// 判断是否所有都完成，如果有一个状态没更新，认为是主从延迟，从主库读取
	if !isAllChildTaskReady(childTasks) {
		childTasks, err = childTask.GetChildTaskByPId(ctx, true)
		if err != nil {
			return nil, err
		}
	}

	finishPics := map[string][]string{}
	linkPicUrlMap := make(map[string]string)
	for _, child := range childTasks {
		if len(finishPics[child.SendId]) <= 0 {
			finishPics[child.SendId] = []string{child.PicUrl}
		} else {
			finishPics[child.SendId] = append(finishPics[child.SendId], child.PicUrl)
		}

		extDataStr := child.ExtData
		if extDataStr != "" {
			extData := make(map[string]interface{})
			_ = jsoniter.UnmarshalFromString(extDataStr, &extData)
			if linkUrlInf, ok := extData["linkUrl"]; ok {
				linkUrl := cast.ToString(linkUrlInf)
				linkPicUrlMap[linkUrl] = child.PicUrl
			}
		}
	}

	finishTask := map[string]interface{}{
		"finishPics":    finishPics,
		"linkPicUrlMap": linkPicUrlMap,
		"unionId":       taskInfo.UnionId,
		"type":          taskInfo.UseType,
		"assistantUid":  taskInfo.AssistantUid,
	}
	return finishTask, nil
}

func isAllChildTaskReady(childTasks []models.CreateCaptureChildTask) bool {
	for _, task := range childTasks {
		if task.Status != models.CreateCaptureChildTaskStatusSuccess {
			return false
		}
	}
	return true
}

func SetTaskError(ctx *gin.Context, task models.CreateCaptureTask, sendErr error) {
	if task.Id <= 0 {
		zlog.Warnf(ctx, "taskId 错误")
		return
	}
	taskInfo, err := task.GetTaskInfoById(ctx, false)
	if err != nil {
		zlog.Warnf(ctx, "没有该任务")
		return
	}

	task.Status = models.CreateFailed
	_ = task.ChangeStatus(ctx)

	if sendErr == nil {
		sendErr = errors.New("截图任务生成失败")
	}
	errTaskParam := map[string]interface{}{
		"unionId":      taskInfo.UnionId,
		"type":         taskInfo.UseType,
		"failMsg":      sendErr.Error(),
		"assistantUid": taskInfo.AssistantUid,
	}
	zlog.Infof(ctx, "sendOverCaptureTask_param: %+v", errTaskParam)
	resp, err := rmq.SendCmd(ctx, "self_product", 214201, "core", "zb", errTaskParam, "")
	if err != nil {
		zlog.Warnf(ctx, "sendOverCaptureTask error, msgId:%s, err:%v", resp.TransID, err)
	}
}
