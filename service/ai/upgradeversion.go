package ai

import (
	"assistantdeskgo/api/longlink"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"bytes"
	"encoding/csv"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/transform"
)

func ImportVersionCsv(ctx *gin.Context, csvFile *multipart.FileHeader) error {

	// 文件基础验证
	if err := validateFile(csvFile); err != nil {
		return err
	}

	// 打开文件并准备清理
	file, closeFile, err := openUploadedFile(csvFile)
	if err != nil {
		return components.ErrorParamInvalidFormat.Sprintf("处理文件时异常")
	}
	defer closeFile(ctx)

	// 编码检测和处理
	utf8Reader, cleanupTemp, err := handleFileEncoding(ctx, file)
	if err != nil {
		return err
	}
	defer cleanupTemp(ctx)

	// CSV读取和验证
	records, err := readAndValidateCSV(ctx, utf8Reader)
	if err != nil {
		return err
	}

	// 处理所有记录
	if err := processAllRecords(ctx, records); err != nil {
		return err
	}

	return nil
}

// 文件基础验证
func validateFile(file *multipart.FileHeader) error {
	// 文件大小限制
	if file.Size > 2000000 { // ~= 1MB
		return components.ErrorParamInvalidFormat.Sprintf("文件过大")
	}

	// 在 validateFile 中处理错误
	ext, err := getFileExtension(file)
	if err != nil {
		return components.ErrorParamInvalidFormat.Sprintf("文件无扩展名")
	}
	if ext != "csv" {
		return components.ErrorParamInvalidFormat.Sprintf("文件格式错误")
	}
	return nil
}

// 打开上传文件并返回清理函数
func openUploadedFile(file *multipart.FileHeader) (multipart.File, func(ctx *gin.Context), error) {
	f, err := file.Open()
	if err != nil {
		return nil, nil, err
	}

	closeFn := func(ctx *gin.Context) {
		if err := f.Close(); err != nil {
			zlog.Errorf(ctx, "文件关闭失败: %v", err)
		}
	}

	return f, closeFn, nil
}

// 处理文件编码相关逻辑
func handleFileEncoding(ctx *gin.Context, file multipart.File) (io.Reader, func(ctx *gin.Context), error) {
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, nil, components.ErrorParamInvalidFormat.Sprintf("文件读取失败")
	}

	if utf8.Valid(content) {
		return bytes.NewReader(content), func(ctx *gin.Context) {}, nil
	}

	// 转换 GBK 到 UTF-8. 只处理 GBK 到 UTF-8.
	decoder := simplifiedchinese.GBK.NewDecoder()
	transformed, err := io.ReadAll(transform.NewReader(bytes.NewReader(content), decoder))
	if err != nil {
		return nil, nil, components.ErrorParamInvalidFormat.Sprintf("编码转换失败")
	}

	tmpFile, cleanup, err := createTempFile()
	if err != nil {
		return nil, nil, components.ErrorParamInvalidFormat.Sprintf("临时文件创建失败")
	}
	if _, err := tmpFile.Write(transformed); err != nil {
		cleanup(ctx)
		return nil, nil, components.ErrorParamInvalidFormat.Sprintf("写入临时文件失败")
	}
	tmpFile.Seek(0, 0)

	return tmpFile, cleanup, nil
}

// 创建临时文件并返回清理函数
func createTempFile() (*os.File, func(context2 *gin.Context), error) {
	tmpFile, err := os.CreateTemp("", "converted-*.csv")
	if err != nil {
		return nil, nil, err
	}

	cleanup := func(ctx *gin.Context) {
		tmpFile.Close()
		if err := os.Remove(tmpFile.Name()); err != nil {
			zlog.Errorf(ctx, "临时文件删除失败: %v", err)
		}
	}

	return tmpFile, cleanup, nil
}

// CSV读取和验证
func readAndValidateCSV(ctx *gin.Context, r io.Reader) ([][]string, error) {
	// 创建 UTF-8 解码器
	utf8Decoder := unicode.UTF8.NewDecoder()

	// 使用 BOM 检测包装解码器
	bomAwareDecoder := unicode.BOMOverride(utf8Decoder)

	// 创建 transform.Reader
	bomReader := transform.NewReader(r, bomAwareDecoder)

	// 创建 CSV Reader
	reader := csv.NewReader(bomReader)
	// 读取表头
	header, err := reader.Read()
	if err == io.EOF {
		return nil, components.ErrorParamInvalidFormat.Sprintf("空文件")
	}
	if err != nil {
		zlog.Errorf(ctx, "CSV读取失败: %v", err)
		return nil, components.ErrorParamInvalidFormat.Sprintf("文件读取错误")
	}

	// 验证表头
	if !validHeader(header) {
		return nil, components.ErrorParamInvalidFormat.Sprintf("表头格式错误")
	}

	// 读取记录
	records, err := reader.ReadAll()
	if err != nil {
		zlog.Errorf(ctx, "CSV记录读取失败: %v", err)
		return nil, components.ErrorParamInvalidFormat.Sprintf("数据解析错误")
	}

	return records, nil
}

// 验证CSV表头
func validHeader(header []string) bool {
	if len(header) != 4 {
		return false
	}
	return header[0] == "uid" && header[1] == "vc" && header[2] == "url" && header[3] == "md5"
}

// 处理所有记录
func processAllRecords(ctx *gin.Context, records [][]string) error {
	for _, record := range records {
		if len(record) < 4 {
			return components.ErrorParamInvalidFormat.Sprintf("记录字段不完整")
		}

		msgData, err := buildMessageData(record)
		if err != nil {
			return err
		}

		if _, err := longlink.SendLongLinkMsg(ctx, msgData); err != nil {
			return components.ErrorParamInvalidFormat.Sprintf("消息发送失败")
		}
	}
	return nil
}

// 构建消息数据
func buildMessageData(record []string) (longlink.SendLongLinkReq, error) {
	uid, vc, url, md5 := record[0], record[1], record[2], record[3]
	msgContent := map[string]interface{}{
		"targetVc":      vc,
		"downloadUrl":   url,
		"bagMd5":        md5,
		"stealthUpdate": 0,
		"createTime":    time.Now().Unix(),
	}

	msgData := map[string]interface{}{
		"sig_no":      10015,
		"msg_content": msgContent,
	}

	msgDataStr, err := jsoniter.Marshal(msgData)
	if err != nil {
		return longlink.SendLongLinkReq{}, err
	}

	uidInt, _ := strconv.ParseInt(uid, 10, 64)
	return longlink.SendLongLinkReq{
		Product:         defines.LongLinkProduct,
		CmdNo:           defines.LongLinkCmdNo,
		MsgExpireTime:   time.Now().Unix() + 10,
		MsgData:         string(msgDataStr),
		MsgCleanType:    2,
		MsgDeliveryType: 1,
		ToUids:          []int64{uidInt},
	}, nil
}

func getFileExtension(file *multipart.FileHeader) (string, error) {
	ext := filepath.Ext(file.Filename)
	if ext == "" {
		return "", fmt.Errorf("文件无扩展名")
	}
	return strings.ToLower(ext[1:]), nil
}
