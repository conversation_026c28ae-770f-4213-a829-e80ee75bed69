package ai

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/api/transcribe"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtoai"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	utils2 "gorm.io/gorm/utils"
	"strconv"
	"strings"
	"time"
)

func AddFeedBack(ctx *gin.Context, req dtoai.AddFeedBackRequest, teacherUid int) (err error) {

	aiTask, err := models.AiCallRecordTaskRef.GetByCallId(ctx, req.CallId)
	if err != nil {
		return
	}
	if aiTask.CallId == "" {
		return errors.New("未找到通话记录")
	}

	accurateList := utils.FilterStrDuplicatesAndEmpty(strings.Split(aiTask.Accurate, ","))
	inAccurateList := utils.FilterStrDuplicatesAndEmpty(strings.Split(aiTask.Inaccurate, ","))

	if req.Accurate == defines.FEED_BACK_ACCURATE {
		if utils.InArrayString(strconv.Itoa(teacherUid), accurateList) {
			return
		} else {
			accurateList = append(accurateList, strconv.Itoa(teacherUid))
		}
		inAccurateList = utils.RemoveStrArrayStr(inAccurateList, strconv.Itoa(teacherUid))

	} else {
		if utils.InArrayString(strconv.Itoa(teacherUid), inAccurateList) {
			return
		} else {
			inAccurateList = append(inAccurateList, strconv.Itoa(teacherUid))
		}
		accurateList = utils.RemoveStrArrayStr(accurateList, strconv.Itoa(teacherUid))
	}
	accurateList = utils.FilterStrDuplicatesAndEmpty(accurateList)
	inAccurateList = utils.FilterStrDuplicatesAndEmpty(inAccurateList)
	err = models.AiCallRecordTaskRef.UpdateFeedBack(ctx, aiTask.CallId, strings.Join(accurateList, ","), strings.Join(inAccurateList, ","))
	return
}

func GetFeedBack(ctx *gin.Context, req dtoai.GetFeedBackRequest) (response dtoai.GetFeedBackResponse, err error) {
	if strings.Trim(req.CallId, " ") == "" {
		return response, errors.New("参数错误")
	}
	callIdList := strings.Split(strings.Trim(req.CallId, " "), ",")

	list, err := models.AiCallRecordTaskRef.ListByCallIdList(ctx, callIdList)
	if err != nil {
		return dtoai.GetFeedBackResponse{}, err
	}

	var responseData []dtoai.FeedBackData
	for _, task := range list {
		responseData = append(responseData, dtoai.FeedBackData{
			CallId:          task.CallId,
			InaccurateCount: len(utils.FilterStrDuplicatesAndEmpty(strings.Split(task.Inaccurate, ","))),
			AccurateCount:   len(utils.FilterStrDuplicatesAndEmpty(strings.Split(task.Accurate, ","))),
		})
	}
	response = dtoai.GetFeedBackResponse{
		FeedBackData: responseData,
	}

	return

}

type SentenceList struct {
	SentenceId int    `json:"sentence_id"`
	Role       int    `json:"role"`
	Content    string `json:"content"`
}

func AddCallRecord(ctx *gin.Context, callId int64, onlyAbstract int64) (err error) {
	if callId <= 0 {
		return errors.New("参数错误")
	}
	if onlyAbstract > 0 {
		existTask, err := models.AiCallRecordTaskRef.GetByCallId(ctx, utils2.ToString(callId))
		if err != nil {
			return err
		}
		info, err := mesh.GetDeviceInfoListByDeviceUids(ctx, existTask.DeviceUid)
		if err != nil {
			zlog.Warnf(ctx, "CallRecordTextTransform GetDeviceInfoListByDeviceUids fail,deviceUid=%v,err=%v", existTask.DeviceUid, err)
			return err
		}
		courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, existTask.CourseId, []string{}, []string{})
		if err != nil {
			zlog.Warnf(ctx, "CallRecordTextTransform GetCourseLessonInfoByCourseId fail,courseId=%v,err=%v", existTask.CourseId, err)
			return err
		}
		var sentenceList []SentenceList
		err = jsoniter.Unmarshal([]byte(existTask.Content), &sentenceList)
		if err != nil {
			zlog.Warnf(ctx, "processAi Unmarshal fail,callId=%v,content=%v", existTask.CallId, existTask.Content)
			return err
		}
		coursePriceTag, err := tower.GetCourseInfo(ctx, courseInfo.CourseId)
		if err != nil {
			return err
		}

		return aiturbo.PostTextAbstractV3(ctx, getTextAbstractRequest(existTask, info, courseInfo, sentenceList, coursePriceTag.CoursePriceTag))

	}
	callInfoMap, err := touchmisgo.GetCallRecordInfo(ctx, &touchmisgo.GetCallRecordInfoReq{CallIds: []int64{callId}})
	if err != nil {
		return err
	}
	if len(callInfoMap) == 0 {
		return errors.New("通话记录未找到")
	}

	callInfo := callInfoMap[callId]
	if callInfo.CallId <= 0 {
		return errors.New("通话记录未找到")
	}
	callInfoTask := models.AiCallRecordTaskRef.From2(callInfo)
	existTask, err := models.AiCallRecordTaskRef.GetByCallId(ctx, callInfoTask.CallId)
	if err != nil {
		return err
	}
	if existTask.CallId != "" {
		zlog.Infof(ctx, "callId has exist,callId=%v", callId)
	} else {
		err = models.AiCallRecordTaskRef.Insert(ctx, callInfoTask)
	}
	if err != nil {
		return err
	}
	reqList := []transcribe.AudioReqModel{}

	link, err := muse.GetLink(ctx, muse.GetRecordLinkReq{
		Path:    callInfoTask.RecordFile,
		Type:    callInfoTask.ResourseType,
		Day:     1,
		IsInner: 1,
	})
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio getLink Fail,callId=%v,err=%v", callInfoTask.CallId, err)
		return err
	}
	if link.Link == "" {
		zlog.Warnf(ctx, "processPostAudio getLink is empty,callId=%v", callInfoTask.CallId)
		return nil
	}

	reqList = append(reqList, transcribe.AudioReqModel{
		defines.AI_BUSSINE_TYPE + "_" + callInfoTask.CallId,
		link.Link,
		callInfoTask.Duration,
	})
	err = transcribe.ReqTranscribeV2(ctx, reqList)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio ReqTranscribe fail,callId=%v", callInfoTask.CallId)
		return nil
	}
	err = models.AiCallRecordTaskRef.UpdateStatus(ctx, []string{callInfoTask.CallId}, models.STATUS_CALL_AUDIO)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio Updates fail,callId=%v", callInfoTask.CallId)
		return nil
	}
	return err
}

func AddCallRecordV2(ctx *gin.Context, callId int64) (err error) {
	if callId <= 0 {
		return errors.New("参数错误")
	}

	oldTask, err := models.AiCallRecordTaskRef.GetByCallId(ctx, utils2.ToString(callId))
	if err != nil {
		return err
	}
	now := time.Now().Unix()
	newCall := models.AiCallRecordRef.From2(oldTask)
	newCall.Status = 0
	newCall.CreateTime = now
	newCall.UpdateTime = now
	newData, err := models.AiCallRecordRef.GetByCallId(ctx, newCall.CallId, newCall.StudentUid)
	if err != nil {
		return err
	}
	if newData.CallId <= 0 {
		err = models.AiCallRecordRef.Insert(ctx, newCall)
		if err != nil {
			zlog.Warnf(ctx, "AddCallRecordV2 Updates fail,callId=%v", newCall.CallId)
			return nil
		}
	} else {
		newCall = newData
	}

	reqList := []transcribe.AudioReqModel{}

	link, err := muse.GetLink(ctx, muse.GetRecordLinkReq{
		Path:    newCall.RecordFile,
		Type:    newCall.ResourceType,
		Day:     1,
		IsInner: 1,
	})
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio getLink Fail,callId=%v,err=%v", newCall.CallId, err)
		return err
	}
	if link.Link == "" {
		zlog.Warnf(ctx, "processPostAudio getLink is empty,callId=%v", newCall.CallId)
		return nil
	}

	reqList = append(reqList, transcribe.AudioReqModel{
		defines.AI_BUSSINE_CALL_TYPE + "_" + utils2.ToString(newCall.StudentUid) + "_" + utils2.ToString(newCall.CallId),
		link.Link,
		newCall.Duration,
	})
	err = transcribe.ReqTranscribe(ctx, reqList)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio ReqTranscribe fail,callId=%v", newCall.CallId)
		return nil
	}
	err = models.AiCallRecordRef.UpdateStatus(ctx, []int64{newCall.CallId}, models.STATUS_CALL_AUDIO, oldTask.ToUid)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio Updates fail,callId=%v", newCall.CallId)
		return nil
	}
	return err
}

func getTextAbstractRequest(task models.AiCallRecordTask, info mesh.DeviceInfo, courseInfo dal.CourseLessonInfo, sentenceList []SentenceList, priceTag int64) aiturbo.PostTextAbstractReq {
	req := aiturbo.PostTextAbstractReq{}
	req.Calltype = task.CallMode
	req.CallId = task.CallId
	req.Source = defines.AI_TOKEN
	req.Token = defines.AI_TOKEN
	req.Topic = GetTopic(task.SourceType)
	req.DeviceNumber = []aiturbo.DeviceNumber{{Number: utils2.ToString(task.FromUid), Type: "in"}, {Number: utils2.ToString(task.ToUid), Type: "out"}}
	if utils.InArrayInt(defines.ROLE_TEACHER_DUXUE, info.KpAscriptionList) { //督学给AI是1
		req.Teacherjs = defines.AI_TUBOR_TEACHER_DUXUE
	}
	req.TeacherId = utils2.ToString(task.DeviceUid)
	req.StudentUid = utils2.ToString(task.ToUid)

	req.Xuebu = defines.Grade2DepartMap[courseInfo.MainGradeId]
	req.Classname = courseInfo.CourseName
	var contentReq []aiturbo.PostTextAbstractReqContent
	for _, sentence := range sentenceList {
		contentReq = append(contentReq, aiturbo.PostTextAbstractReqContent{
			Sentenceid: sentence.SentenceId,
			Content:    sentence.Content,
			Role:       sentence.Role,
		})

	}
	req.Contents = contentReq
	req.Token = defines.AI_TOKEN
	req.PriceTag = priceTag
	return req
}

func ListCallId(ctx *gin.Context, createTime int64, size int) ([]string, int64, error) {

	callList, err := models.AiCallRecordTaskRef.ListByTimeAndStatusDuration(ctx, createTime, []int64{-1, 0, 1}, defines.THREE_MINITE_OF_MILLI_SECOND, size)
	if err != nil {
		return nil, 0, err
	}
	lastCreateTime := int64(0)
	callIdList := []string{}
	for _, callInfo := range callList {
		callIdList = append(callIdList, callInfo.CallId)
		if lastCreateTime < callInfo.CreateTime {
			lastCreateTime = callInfo.CreateTime

		}
		callIdInt, _ := strconv.ParseInt(callInfo.CallId, 10, 64)
		if callIdInt == 0 {
			continue
		}
		err = AddCallRecord(ctx, callIdInt, 0)
		time.Sleep(time.Second)
		if err != nil {
			zlog.Infof(ctx, "AddCallRecord fail,callId=%v", callIdInt)
		}
		zlog.Infof(ctx, "AddCallRecord success callId=%v", callIdInt)
	}

	zlog.Infof(ctx, "AddCallRecordFinish")

	return callIdList, lastCreateTime, nil
}
