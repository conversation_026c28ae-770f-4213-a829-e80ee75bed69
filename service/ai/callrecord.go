package ai

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtoai"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"strconv"
	"strings"
	"time"
)

const (
	PAGE_TAB_SELF   = 2
	PAGE_TAB_COURSE = 1

	PAGE_TAB_ALL = 0
)

func StudentCallRecord(ctx *gin.Context, teacherUid int64, req dtoai.StudentCallRecordRequest) (response dtoai.StudentCallRecordResponse, err error) {

	queryTeacherUid := int64(0)
	queryCourseId := int64(0)
	if req.StudentUid <= 0 {
		err = components.ErrorParamInvalid.Wrap(errors.New("学生ID为空"))
		return
	}

	if req.PageTab == PAGE_TAB_COURSE {
		if req.CourseId <= 0 {
			err = components.ErrorParamInvalid.Wrap(errors.New("课程id为空"))
			return
		}
		queryCourseId = req.CourseId
	} else if req.PageTab == PAGE_TAB_SELF {
		queryTeacherUid = teacherUid
	}

	var page, pageSize int
	pageSize = 10
	if req.Page > 0 {
		page = req.Page
	}
	if req.PageSize > 0 {
		pageSize = req.PageSize
	}
	var callDetailList []dtoai.CallDetail
	callIdMap := map[string]bool{}
	//新老一起查
	repeatCnt := int64(0)
	newList, newTotal, err := models.AiCallRecordRef.PageByByCond(ctx, req.StudentUid, queryTeacherUid, defines.CALL_TYPE, queryCourseId, page, pageSize)
	if err != nil {
		return dtoai.StudentCallRecordResponse{}, err
	}
	if len(newList) > 0 {
		var courseIdList []int64
		for _, l := range newList {
			courseIdList = append(courseIdList, l.CourseId)
		}
		courseMap, err := dal.GetCourseLessonInfoByCourseIds(ctx, utils.FilterInt64Duplicates(courseIdList), []string{"courseId", "courseName"}, []string{})
		if err != nil {
			return dtoai.StudentCallRecordResponse{}, err
		}

		for _, record := range newList {
			callDetailList = append(callDetailList, getNewResponseData(ctx, teacherUid, record, courseMap))
			callIdMap[utils2.ToString(record.CallId)] = true
		}
	}
	//查老的

	oldPage := 1
	if page*pageSize > int(newTotal) {
		oldPage = (page*pageSize - int(newTotal)) / pageSize
	}

	list, oldTotal, err := models.AiCallRecordTaskRef.PageByByCond(ctx, req.StudentUid, queryTeacherUid, defines.CALL_TYPE, queryCourseId, oldPage, pageSize)
	if err != nil {
		return dtoai.StudentCallRecordResponse{}, err
	}
	if len(list) > 0 && len(newList) < pageSize {
		var courseIdList []int64
		for _, l := range list {
			courseIdList = append(courseIdList, l.CourseId)
		}
		courseMap, err := dal.GetCourseLessonInfoByCourseIds(ctx, utils.FilterInt64Duplicates(courseIdList), []string{"courseId", "courseName"}, []string{})
		if err != nil {
			return dtoai.StudentCallRecordResponse{}, err
		}

		for _, record := range list {
			if callIdMap[record.CallId] {
				repeatCnt++
				continue
			}
			if len(callDetailList) >= pageSize {
				continue
			}
			callDetailList = append(callDetailList, getResponseData(ctx, teacherUid, record, courseMap))
		}
	}

	response = dtoai.StudentCallRecordResponse{
		Total:          newTotal + oldTotal - repeatCnt,
		Page:           page,
		PageSize:       pageSize,
		CallDetailList: callDetailList,
	}

	return
}
func getNewResponseData(ctx *gin.Context, teacherUid int64, record models.AiCallRecord, courseMap map[int64]dal.CourseLessonInfo) dtoai.CallDetail {
	var tags []dtoai.Tags
	for _, v := range record.GetAbstractTags() {
		label := v.Label
		value := v.Value

		if label == "" {
			label = v.TagKey
			value = v.TagInfo
		}

		tags = append(tags, dtoai.Tags{
			Label:  label,
			Value:  value,
			Remark: v.Remark,
		})
	}

	var contentVal []dtoai.SentenceList
	if record.Status >= models.STATUS_CALL_AUDIO_FINISH {
		content, _err := helpers.BaiduBucket2.DownloadContent(ctx, record.Content)
		if _err != nil {
			zlog.Warnf(ctx, "downLoadContent,file=%v,err=%v", record.Content, _err)
		}
		if len(content) > 0 {
			err := json.Unmarshal(content, &contentVal)
			if err != nil {
				zlog.Warnf(ctx, "downLoadContent Unmarshal fail,file=%v,content=%v,err=%v", record.Content, string(content), _err)
			}
		}

	}

	sourceType := []string{}
	for key, value := range defines.SourceTypeValueMap {
		if record.SourceType&value != 0 {
			sourceType = append(sourceType, defines.CallSourceValueMap[key])
		}
	}
	recordFile := ""
	if record.RecordFile != "" {
		rsp, err := muse.GetLink(ctx, muse.GetRecordLinkReq{
			Path:    record.RecordFile,
			Type:    record.ResourceType,
			Day:     1,
			IsInner: 1,
		})
		if err != nil {
			zlog.Warnf(ctx, "StudentCallRecord GetLink fail,recordFile=%v,err=%v", record.RecordFile, err)
		}
		recordFile = rsp.Link
	}
	accurate := 0
	inaccurate := 0

	now := time.Now().Unix()

	return dtoai.CallDetail{
		CallId:      utils2.ToString(record.CallId),
		StartTime:   record.StartTime,
		StopTime:    record.StopTime,
		Type:        record.CallType,
		Status:      record.Status,
		CallMode:    record.CallMode,
		CallModeStr: defines.CallModelMap[record.CallMode],
		Duration:    record.Duration,
		Abstract:    record.Abstract,
		Tags:        tags,
		RecordFile:  recordFile,
		CourseId:    record.CourseId,
		CourseName:  courseMap[record.CourseId].CourseName,
		Content:     contentVal,
		CurrentTime: now,
		Inaccurate:  int64(inaccurate),
		Accurate:    int64(accurate),
		SourceType:  strings.Join(sourceType, ","),
		CreateTime:  record.CreateTime,
		Version:     1,
	}
}
func getResponseData(ctx *gin.Context, teacherUid int64, record models.AiCallRecordTask, courseMap map[int64]dal.CourseLessonInfo) dtoai.CallDetail {
	var tags []dtoai.Tags
	for _, v := range record.GetAbstractTags() {
		label := v.Label
		value := v.Value

		if label == "" {
			label = v.TagKey
			value = v.TagInfo
		}

		tags = append(tags, dtoai.Tags{
			Label:  label,
			Value:  value,
			Remark: v.Remark,
		})
	}
	content := getContent(ctx, record)
	sourceType := []string{}
	for key, value := range defines.SourceTypeValueMap {
		if record.SourceType&value != 0 {
			sourceType = append(sourceType, defines.CallSourceValueMap[key])
		}
	}
	recordFile := ""
	if record.RecordFile != "" {
		rsp, err := muse.GetLink(ctx, muse.GetRecordLinkReq{
			Path:    record.RecordFile,
			Type:    record.ResourseType,
			Day:     1,
			IsInner: 1,
		})
		if err != nil {
			zlog.Warnf(ctx, "StudentCallRecord GetLink fail,recordFile=%v,err=%v", record.RecordFile, err)
		}
		recordFile = rsp.Link
	}
	accurate := 0
	inaccurate := 0

	if utils.InArrayString(strconv.Itoa(int(teacherUid)), utils.FilterStrDuplicatesAndEmpty(strings.Split(record.Accurate, ","))) {
		accurate = 1
	} else if utils.InArrayString(strconv.Itoa(int(teacherUid)), utils.FilterStrDuplicatesAndEmpty(strings.Split(record.Inaccurate, ","))) {
		inaccurate = 1
	}

	now := time.Now().Unix()

	return dtoai.CallDetail{
		CallId:      record.CallId,
		StartTime:   record.StartTime,
		StopTime:    record.StopTime,
		Type:        record.CallType,
		Status:      record.Status,
		CallMode:    record.CallMode,
		CallModeStr: defines.CallModelMap[record.CallMode],
		Duration:    record.Duration,
		Abstract:    record.Abstract,
		Tags:        tags,
		RecordFile:  recordFile,
		CourseId:    record.CourseId,
		CourseName:  courseMap[record.CourseId].CourseName,
		Content:     content,
		CurrentTime: now,
		Inaccurate:  int64(inaccurate),
		Accurate:    int64(accurate),
		SourceType:  strings.Join(sourceType, ","),
		CreateTime:  record.CreateTime,
	}
}

func getContent(ctx *gin.Context, record models.AiCallRecordTask) (content []dtoai.SentenceList) {
	if record.Status <= models.STATUS_DEFAULT {
		return
	}
	jsonErr := json.Unmarshal([]byte(record.Content), &content)
	if jsonErr != nil {
		zlog.Warnf(ctx, "StudentCallRecord Unmarshal tags fail,tag=%v,err=%v", record.Tags, jsonErr)
	}
	return
}
