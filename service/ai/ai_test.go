package ai

import (
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestAi(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)
	req := kunpeng.GetUidByWxIdReq{}
	req.AppId = 2
	req.StaffUid = 4082346987
	req.WxIds = []string{"7881300418241654"}
	response, err := kunpeng.GetUidByWxId(ctx, req)
	if err != nil {
		return
	}
	fmt.Println(response)

	//req.CallIdList = []string{"4922470"}
	//GetFeedBack(ctx, req)

	//req := aiturbo.PostTextAbstractReq{}
	//
	//req.Token = defines.AI_TOKEN
	//req.Xuebu = 1
	//req.Teacherjs = 0
	//req.Calltype = 9
	//req.Classname = "测试班级"
	//req.CallId = "2333"
	//content := []aiturbo.PostTextAbstractReqContent{}
	//content = append(content, aiturbo.PostTextAbstractReqContent{
	//	Content:    "家长你好",
	//	Role:       0,
	//	Sentenceid: 0,
	//})
	//content = append(content, aiturbo.PostTextAbstractReqContent{
	//	Content:    "老师你好，我家孩子表现怎么样嘛",
	//	Role:       1,
	//	Sentenceid: 1,
	//})
	//req.Contents = content
	//
	//err := aiturbo.PostTextAbstract(ctx, req)
	//if err != nil {
	//	return
	//}

}
