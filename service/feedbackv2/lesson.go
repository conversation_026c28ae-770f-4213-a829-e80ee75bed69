package feedbackv2

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/components"
	dtofeedbackv2 "assistantdeskgo/dto/feedbackv2"
	dalconst "git.zuoyebang.cc/fwyybase/fwyylibs/consts/dal"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"time"
)

func LessonInfo(ctx *gin.Context, req dtofeedbackv2.LessonInfoReq) (rsp dtofeedbackv2.LessonInfoRsp, err error) {
	if req.CourseID <= 0 {
		return rsp, components.ErrorParamInvalid
	}

	// 获取课程章节
	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, req.CourseID, []string{"courseId", "courseName", "mainGradeId", "cpuId"},
		[]string{"lessonId", "lessonName", "lessonType", "mode", "startTime", "stopTime"})
	if err != nil {
		zlog.Infof(ctx, "dal.GetCourseLessonInfoByCourseId error! err:%+v", err)
		return rsp, components.DefaultError("获取章节信息失败")
	}
	rsp.List = make([]dtofeedbackv2.LessonInfoItem, 0)
	for _, lesson := range courseLessonInfo.LessonList {
		item := dtofeedbackv2.LessonInfoItem{
			CourseID:    courseLessonInfo.CourseId,
			CourseName:  courseLessonInfo.CourseName,
			MainGradeID: courseLessonInfo.MainGradeId,
			CpuID:       courseLessonInfo.CpuId,
			LessonID:    int64(lesson.LessonId),
			LessonName:  lesson.LessonName,
			LessonType:  int64(lesson.LessonType),
			Mode:        int64(lesson.Mode),
			StartTime:   int64(lesson.StartTime),
			StopTime:    int64(lesson.StopTime),
		}

		// 章节支持多选，章节类型分正式章节和习题课章节，仅可选择正式章节（习题课章节正常展示但不可选）
		// 正式章节的判定方式：课程属性是核心课，且大纲模式是正式课
		if item.LessonType == dalconst.LessonTypeCore && (item.Mode == 0 || item.Mode == dalconst.OutlineModeFormal) { // 可能没有大纲模式，也允许选择
			item.CanSelect = true
		}
		// 默认选中已下课的正式章节
		if time.Now().Unix() > item.StopTime && item.CanSelect {
			item.DefaultSelect = true
		}
		rsp.List = append(rsp.List, item)
	}
	// 按章节开始时间排序
	sort.Slice(rsp.List, func(i, j int) bool {
		return rsp.List[i].StartTime < rsp.List[j].StartTime
	})
	rsp.Total = len(rsp.List)
	return
}
