package feedbackv2

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/examcore"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	dtofeedbackv2 "assistantdeskgo/dto/feedbackv2"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"assistantdeskgo/service/third"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/zbtikuapi"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
)

type LessonInfoItem struct {
	LessonId     int64  `json:"lessonId"`
	LessonName   string `json:"lessonName"`
	StartTime    int64  `json:"startTime"`
	StopTime     int64  `json:"stopTime"`
	OutlineId    int64  `json:"outlineId"`
	BindHomework bool   `json:"bindHomework"` // 是否绑定了巩固练习
}

type CourseInfoItem struct {
	CourseId    int64  `json:"courseId"`
	CourseName  string `json:"courseName"`
	MainGradeId int64  `json:"mainGradeId"`
	CpuId       int64  `json:"cpuId"`
}

type FeedbackDataContext struct {
	CommonLuMap        map[int64]dataproxy.CommonLuItem                           // key为lessonId
	LuActionMap        map[int64]dataproxy.LessonStudentActionItem                // key为lessonId
	LuPointMap         map[int64]map[int64]dataproxy.LessonStudentPointActionItem // key为pointId, lessonId
	CourseInfo         CourseInfoItem
	LessonInfoMap      map[int64]LessonInfoItem
	PointTargetName    map[int64]string                              // 知识目标id->名称
	PointTargetExplain map[int64][]models.FeedbackPointTargetExplain // 解读话术，key为pointId
}

func FeedbackData(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq) (rsp dtofeedbackv2.FeedbackDataRsp, err error) {
	// 参数检查
	err = checkParams(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "FeedbackData.checkParams fail! req:%+v, err:%+v", req, err)
		return rsp, components.ErrorParamInvalid
	}
	if len(req.LessonIDList) == 0 {
		return
	}
	context := FeedbackDataContext{}

	// 获取es数据
	err = getEsData(ctx, req, &context)
	if err != nil {
		zlog.Warnf(ctx, "FeedbackData.getEsData error! req:%+v, err:%+v", req, err)
		return rsp, components.DefaultError("获取es数据失败")
	}

	// 获取课程章节数据
	err = getCourseLessonInfo(ctx, req, &context)
	if err != nil {
		zlog.Warnf(ctx, "FeedbackData.getLessonInfo error! req:%+v, err:%+v", req, err)
		return rsp, components.DefaultError("获取课程章节数据失败")
	}

	// 构建学习概览
	err = buildStudyView(ctx, req, &context, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "FeedbackData.buildStudyView error! req:%+v, err:%+v", req, err)
		return rsp, components.DefaultError("计算学习概览失败")
	}

	// 获取知识目标数据
	err = getPointData(ctx, req, &context)
	if err != nil {
		zlog.Warnf(ctx, "FeedbackData.getPointData error! req:%+v, err:%+v", req, err)
		return rsp, components.DefaultError("获取知识目标数据失败")
	}

	// 构建知识目标详情
	err = buildPointDetail(ctx, req, &context, &rsp)
	if err != nil {
		zlog.Warnf(ctx, "FeedbackData.buildPointDetail error! req:%+v, err:%+v", req, err)
		return rsp, components.DefaultError("构建知识目标详情失败")
	}

	return
}

func checkParams(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq) (err error) {
	if req.CourseID <= 0 || req.StudentUid <= 0 {
		zlog.Infof(ctx, "checkParams fail! req:%+v", req)
		return components.ErrorParamInvalid
	}
	if len(req.LessonIDList) > defines.LessonIdListLenMax {
		zlog.Infof(ctx, "checkParams lessonId too big. req:%+v", req)
		return components.ErrorParamInvalid
	}
	return
}

func getEsData(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq, context *FeedbackDataContext) (err error) {
	commonLuRsp := dataproxy.CommonLuRsp{}
	luActionRsp := dataproxy.LessonStudentActionRsp{}
	luPointRsp := dataproxy.LessonStudentPointActionRsp{}
	lpCommonRsp := dataproxy.LpCommonPointLessonIdsRsp{}
	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "middleware.GetLoginUserInfo error! err:%+v", err)
		return
	}
	assistantUid := info.SelectedBusinessUid
	commonLuReq := dataproxy.LuStudentLessonsReq{
		StudentUid: req.StudentUid,
		LessonIds:  fwyyutils.JoinArrayInt64ToString(req.LessonIDList, ","),
		Fields: strings.Join([]string{"course_id", "lesson_id", "student_uid", "is_inclass_teacher_room_attend_30minute", "is_inclass_teacher_room_attend_finish", "inclass_teacher_room_attend_duration", "inclass_teacher_room_total_playback_time_v1",
			"inclass_participate_cnt", "playback_participate_cnt", "exam1", "playback_right_cnt", "inclass_right_cnt", "exam7"}, ","),
	}
	luActionReq := dataproxy.LuCourseLessonIdsStudentUidsAssistantUidReq{
		CourseId:     req.CourseID,
		LessonIds:    fwyyutils.JoinArrayInt64ToString(req.LessonIDList, ","),
		StudentUids:  fwyyutils.JoinArrayInt64ToString([]int64{req.StudentUid}, ","),
		AssistantUid: assistantUid,
		Fields:       strings.Join([]string{"course_id", "lesson_id", "student_uid", "assistant_uid", "trade_status", "playback_time_in_7d", "is_view_finished", "homework_first_correct_cnt", "homework_first_right_cnt"}, ","),
	}

	lessonIdChunks := fwyyutils.ChunkArrayInt64(req.LessonIDList, 10)
	luPointReqChunks := make([]dataproxy.LupLessonsStudentUidReq, 0)
	for _, lessonIdChunk := range lessonIdChunks {
		luPointReq := dataproxy.LupLessonsStudentUidReq{
			LessonIds:  fwyyutils.JoinArrayInt64ToString(lessonIdChunk, ","),
			StudentUid: req.StudentUid,
			Fields:     strings.Join([]string{"lesson_id", "student_uid", "point_id", "inclass_question_cnt", "inclass_right_cnt", "inclass_participate_cnt", "playback_right_cnt", "playback_participate_cnt"}, ","),
		}
		luPointReqChunks = append(luPointReqChunks, luPointReq)
	}
	lpCommonReqChunks := make([]dataproxy.LpCommonPointLessonIdsReq, 0)
	for _, lessonIdChunk := range lessonIdChunks {
		lpCommonReq := dataproxy.LpCommonPointLessonIdsReq{
			LessonIds: fwyyutils.JoinArrayInt64ToString(lessonIdChunk, ","),
			Fields:    strings.Join([]string{"lesson_id", "point_id", "inclass_question_cnt"}, ","),
		}
		lpCommonReqChunks = append(lpCommonReqChunks, lpCommonReq)
	}

	commonLuRsp, err = dataproxy.GetCommonListByStudentLessons(ctx, commonLuReq)
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetCommonListByStudentLessons error! req:%+v, err:%+v", commonLuReq, err)
		return err
	}
	luActionRsp, err = dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid(ctx, luActionReq)
	if err != nil {
		zlog.Infof(ctx, "dataproxy.GetListByCourseIdLessonIdsStudentUidsAssistantUid error! req:%+v, err:%+v", luActionReq, err)
		return err
	}
	for _, luPointReq := range luPointReqChunks {
		luPointRspChunk, _err := dataproxy.GetPointDataByLessonIdsStudentUid(ctx, luPointReq)
		if _err != nil {
			zlog.Infof(ctx, "dataproxy.GetPointDataByLessonIdsStudentUid error! req:%+v, err:%+v", luPointReq, _err)
			return _err
		}
		luPointRsp.Total += luPointRspChunk.Total
		luPointRsp.List = append(luPointRsp.List, luPointRspChunk.List...)
	}
	for _, lpCommonReq := range lpCommonReqChunks {
		lpCommonRspChunk, _err := dataproxy.GetLpPointCommonByLessonIds(ctx, lpCommonReq)
		if _err != nil {
			zlog.Infof(ctx, "dataproxy.GetLpPointCommonByLessonIds error! req:%+v, err:%+v", lpCommonReq, _err)
			return _err
		}
		lpCommonRsp.Total += lpCommonRspChunk.Total
		lpCommonRsp.List = append(lpCommonRsp.List, lpCommonRspChunk.List...)
	}

	context.CommonLuMap = make(map[int64]dataproxy.CommonLuItem)
	context.LuActionMap = make(map[int64]dataproxy.LessonStudentActionItem)
	context.LuPointMap = make(map[int64]map[int64]dataproxy.LessonStudentPointActionItem)

	for _, lu := range commonLuRsp.List {
		if lu.StudentUid != req.StudentUid {
			continue
		}
		context.CommonLuMap[lu.LessonID] = lu
	}
	for _, lu := range luActionRsp.List {
		if lu.StudentUid != req.StudentUid {
			continue
		}
		context.LuActionMap[lu.LessonID] = lu
	}
	// 先装填默认数据，再装填学生数据
	for _, lp := range lpCommonRsp.List {
		if _, ok := context.LuPointMap[lp.PointID]; !ok {
			context.LuPointMap[lp.PointID] = make(map[int64]dataproxy.LessonStudentPointActionItem)
		}
		context.LuPointMap[lp.PointID][lp.LessonID] = dataproxy.LessonStudentPointActionItem{
			LessonID:           lp.LessonID,
			StudentUid:         req.StudentUid,
			PointID:            lp.PointID,
			InclassQuestionCnt: lp.InclassQuestionCnt,
		}
	}
	for _, lup := range luPointRsp.List {
		if lup.StudentUid != req.StudentUid {
			continue
		}
		if _, ok := context.LuPointMap[lup.PointID]; !ok {
			context.LuPointMap[lup.PointID] = make(map[int64]dataproxy.LessonStudentPointActionItem)
		}
		context.LuPointMap[lup.PointID][lup.LessonID] = lup
	}

	return
}

func getCourseLessonInfo(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq, context *FeedbackDataContext) (err error) {
	// 获取课程、章节基础信息
	var courseLessonInfo dal.CourseLessonInfo
	courseLessonInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, req.CourseID, []string{"courseId", "courseName", "mainGradeId", "cpuId"}, []string{"lessonId", "lessonName", "startTime", "stopTime", "outlineId"})
	if err != nil {
		zlog.Warnf(ctx, "dal.GetCourseLessonInfoByCourseId error! courseId:%+v, err:%+v", req.CourseID, err)
		return err
	}

	courseInfo := CourseInfoItem{
		CourseId:    req.CourseID,
		CourseName:  courseLessonInfo.CourseName,
		MainGradeId: courseLessonInfo.MainGradeId,
		CpuId:       courseLessonInfo.CpuId,
	}
	context.CourseInfo = courseInfo
	context.LessonInfoMap = make(map[int64]LessonInfoItem)
	for _, lessonId := range req.LessonIDList {
		if lesson, ok := courseLessonInfo.LessonList[int(lessonId)]; ok {
			lessonInfoItem := LessonInfoItem{
				LessonId:   lessonId,
				LessonName: lesson.LessonName,
				StartTime:  int64(lesson.StartTime),
				StopTime:   int64(lesson.StopTime),
				OutlineId:  int64(lesson.OutlineId),
			}
			context.LessonInfoMap[lessonId] = lessonInfoItem
		}
	}

	// 获取章节试卷绑定情况
	bindStrs := make([]string, 0)
	lessonBindMap := make(map[string]int64)
	for _, lessonId := range req.LessonIDList {
		// 章节绑定的巩固练习
		bindKey := examcore.FormatBindStr(lessonId, components.BuyTypeLesson, components.ExamTypePracticeStrength)
		bindStrs = append(bindStrs, bindKey)
		lessonBindMap[bindKey] = lessonId

		// 巩固练习也可能绑定在大纲上
		outlineId := context.LessonInfoMap[lessonId].OutlineId
		if outlineId > 0 {
			outlineBindKey := examcore.FormatBindStr(outlineId, components.BuyTypeOutline, components.ExamTypePracticeStrength)
			bindStrs = append(bindStrs, outlineBindKey)
			lessonBindMap[outlineBindKey] = lessonId
		}
	}
	getBindReq := examcore.GetBindInfoReq{
		BindStrs: bindStrs,
	}
	var bindInfo *examcore.GetBindInfoRsp
	bindInfo, err = examcore.GetBindInfo(ctx, getBindReq)
	if err != nil {
		zlog.Warnf(ctx, "examcore.GetBindInfo error! req:%+v, err:%+v", getBindReq, err)
		return err
	}

	for bindKey, bindMap := range bindInfo.List {
		lessonId := lessonBindMap[bindKey]
		lessonInfoItem, ok := context.LessonInfoMap[lessonId]
		if lessonId > 0 && len(bindMap) > 0 && ok {
			lessonInfoItem.BindHomework = true
			context.LessonInfoMap[lessonId] = lessonInfoItem
		}
	}
	return
}

func buildStudyView(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq, context *FeedbackDataContext, rsp *dtofeedbackv2.FeedbackDataRsp) (err error) {
	studyView := dtofeedbackv2.StudyViewStruct{}
	// 章节总数
	studyView.TotalLessonNum = int64(len(req.LessonIDList))
	for _, item := range context.CommonLuMap {
		// 直播到课
		studyView.InclassAttendLessonNum += item.IsInclassTeacherRoomAttend30Minute
		// 直播完课
		studyView.InclassFinishLessonNum += item.IsInclassTeacherRoomAttendFinish

		participateCnt := item.InclassParticipateCnt + item.PlaybackParticipateCnt
		studyView.InteractionParticipateCnt += participateCnt
		exam1Str := item.Exam1
		exam1 := make(map[string]int64)
		if exam1Str != "" {
			_err := jsoniter.UnmarshalFromString(exam1Str, &exam1)
			if _err != nil {
				zlog.Infof(ctx, "jsoniter.UnmarshalFromString error! item:%+v, exam1Str data:%+v, err:%+v", item, exam1Str, _err)
				continue
			}
		}
		exam1TotalNum := exam1["total_num"]
		studyView.InteractionTotalNum += exam1TotalNum

		rightCnt := item.InclassRightCnt + item.PlaybackRightCnt
		studyView.InteractionRightCnt += rightCnt

		// 巩固练习提交
		exam7Str := item.Exam7
		exam7 := make(map[string]int64)
		if exam7Str != "" {
			_err := jsoniter.UnmarshalFromString(exam7Str, &exam7)
			if _err != nil {
				zlog.Infof(ctx, "jsoniter.UnmarshalFromString error! item:%+v, exam7Str data:%+v, err:%+v", item, exam7Str, _err)
				continue
			}
		}
		exam7IsSubmit := exam7["is_submit"]
		if exam7IsSubmit > 0 {
			studyView.HomeworkSubmitNum++
		}
	}

	// 巩固练习布置
	for _, lessonId := range req.LessonIDList {
		if context.LessonInfoMap[lessonId].BindHomework {
			studyView.HomeworkAssignNum++
		}
	}

	// 互动题参与率
	studyView.InteractionParticipateRate = getPercent(studyView.InteractionParticipateCnt, studyView.InteractionTotalNum, defines.DefaultFloatPlace)
	// 互动题正确率
	studyView.InteractionRightRate = getPercent(studyView.InteractionRightCnt, studyView.InteractionParticipateCnt, defines.DefaultFloatPlace)

	// 观看到课
	mainGradeId := context.CourseInfo.MainGradeId
	gradeStage := consts.CourseTransMap[mainGradeId]
	for _, lessonId := range req.LessonIDList {
		lu := context.CommonLuMap[lessonId] // 无需判断存在性，因为0值不影响后续判断
		if lu.InclassTeacherRoomAttendDuration >= defines.SecondsOf30Minutes {
			studyView.WatchAttendLessonNum++
		} else {
			// 初高和小学逻辑不同
			if gradeStage == consts.GradeStageJunior || gradeStage == consts.GradeStageSenior {
				if lu.InclassTeacherRoomTotalPlaybackTimeV1 >= defines.SecondsOf30Minutes {
					studyView.WatchAttendLessonNum++
				}
			} else {
				luAction := context.LuActionMap[lessonId]
				if luAction.PlaybackTimeIn7D >= defines.SecondsOf30Minutes && luAction.TradeStatus == consts.TradeStatusPaid {
					studyView.WatchAttendLessonNum++
				}
			}
		}
	}

	for _, item := range context.LuActionMap {
		// 观看完课
		if item.IsViewFinished > 0 && item.TradeStatus == consts.TradeStatusPaid {
			studyView.WatchFinishLessonNum++
		}

		studyView.HomeworkFirstRightCnt += item.HomeworkFirstRightCnt
		studyView.HomeworkFirstCorrectCnt += item.HomeworkFirstCorrectCnt
	}

	// 巩固练习首答正确率
	studyView.HomeworkFirstCorrectRate = getPercent(studyView.HomeworkFirstRightCnt, studyView.HomeworkFirstCorrectCnt, defines.DefaultFloatPlace)
	rsp.StudyView = studyView
	return
}

func getPointData(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq, context *FeedbackDataContext) (err error) {
	// 获取解读话术
	cpuId := context.CourseInfo.CpuId
	pointIdList := make([]int64, 0)
	for pointId := range context.LuPointMap {
		pointIdList = append(pointIdList, pointId)
	}
	explainList, err := models.FeedbackPointTargetExplainRef.GetExplainByPointIdList(ctx, cpuId, pointIdList)
	if err != nil {
		zlog.Infof(ctx, "models.FeedbackPointTargetExplainRef.GetExplainByPointIdList error! cpuId:%+v, pointIdList:%+v, err:%+v", cpuId, pointIdList, err)
		return err
	}
	context.PointTargetExplain = make(map[int64][]models.FeedbackPointTargetExplain)
	for _, explain := range explainList {
		pointId := explain.PointId
		if _, ok := context.PointTargetExplain[pointId]; !ok {
			context.PointTargetExplain[pointId] = make([]models.FeedbackPointTargetExplain, 0)
		}
		context.PointTargetExplain[pointId] = append(context.PointTargetExplain[pointId], explain)
	}

	// 获取知识目标名称
	if len(pointIdList) > 0 {
		pointName, _err := zbtikuapi.GetPointName(ctx, pointIdList)
		if _err != nil {
			zlog.Infof(ctx, "zbtikuapi.GetPointName error! pointIdList:%+v, err:%+v", pointIdList, _err)
			return _err
		}
		zlog.Debugf(ctx, "zbtikuapi.GetPointName pointIdList:%+v, pointName:%+v", pointIdList, pointName)
		context.PointTargetName = pointName
	}
	return
}

func buildPointDetail(ctx *gin.Context, req dtofeedbackv2.FeedbackDataReq, context *FeedbackDataContext, rsp *dtofeedbackv2.FeedbackDataRsp) (err error) {
	rsp.PointDetail.List = make([]dtofeedbackv2.PointDetailItem, 0)
	for pointId, pointLessonMap := range context.LuPointMap {
		item := dtofeedbackv2.PointDetailItem{
			PointID:   pointId,
			PointName: context.PointTargetName[pointId],
		}

		var participateCnt, rightCnt, totalCnt int64
		lessonList := make([]dtofeedbackv2.LessonListItem, 0)
		for lessonId, lup := range pointLessonMap {
			participateCnt += lup.InclassParticipateCnt + lup.PlaybackParticipateCnt
			rightCnt += lup.InclassRightCnt + lup.PlaybackRightCnt
			totalCnt += lup.InclassQuestionCnt
			lesson := dtofeedbackv2.LessonListItem{
				LessonID:     lessonId,
				LessonName:   context.LessonInfoMap[lessonId].LessonName,
				LessonStatus: context.LuActionMap[lessonId].IsViewFinished,
			}
			lessonList = append(lessonList, lesson)
		}
		// 有的知识目标的发题数为0，需要过滤掉
		if totalCnt == 0 {
			continue
		}
		sort.Slice(lessonList, func(i, j int) bool {
			return lessonList[i].LessonID < lessonList[j].LessonID
		})
		item.InteractionParticipateRate = getPercent(participateCnt, totalCnt, defines.DefaultFloatPlace)
		item.InteractionRightRate = getPercent(rightCnt, participateCnt, defines.DefaultFloatPlace)
		item.LessonList = lessonList
		item.ExplainList = make([]dtofeedbackv2.ExplainListItem, 0)

		if participateCnt == 0 {
			// 正确率无值时（分母为0）：对应未学
			item.MasterLevel = defines.MasterLevelZ
			item.MasterStatus = defines.MasterStatusNotLearn
			// 正确率无值时（分母为0）：固定写死的话术
			item.ExplainList = append(item.ExplainList, dtofeedbackv2.ExplainListItem{
				Tag:  defines.NotLearnExplainTag,
				Text: defines.NotLearnExplainText,
			})
			rsp.PointDetail.List = append(rsp.PointDetail.List, item)
			continue
		}

		rightFrac := float64(rightCnt) / float64(participateCnt)
		item.InteractionRightFrac = rightFrac
		explains := context.PointTargetExplain[pointId]
		found := false
		for _, explain := range explains {
			flag := true
			LowerLimit, err1 := strconv.ParseFloat(explain.LowerLimit, 64)
			upperLimit, err2 := strconv.ParseFloat(explain.UpperLimit, 64)
			if err1 != nil || err2 != nil {
				zlog.Infof(ctx, "strconv.ParseFloat error! explain:%+v, err1:%+v, err2:%+v", explain, err1, err2)
				return components.DefaultError("知识目标上下限配置错误")
			}
			if explain.LowerIsOpen == models.IsOpenYes {
				flag = flag && (rightFrac > LowerLimit)
			} else {
				flag = flag && (rightFrac >= LowerLimit)
			}
			if !flag {
				continue
			}
			if explain.UpperIsOpen == models.IsOpenYes {
				flag = flag && (rightFrac < upperLimit)
			} else {
				flag = flag && (rightFrac <= upperLimit)
			}
			if !flag {
				continue
			}
			found = true
			level := explain.MasterLevel
			item.MasterLevel = defines.MasterLevelMap[level]
			item.MasterStatus = defines.MasterLevelStatusMap[item.MasterLevel]

			explainConf := make([]models.ExplainConfItem, 0)
			if explain.ExplainConf != "" {
				_err := jsoniter.UnmarshalFromString(explain.ExplainConf, &explainConf)
				if _err != nil {
					// 这里不能阻塞流程，因此错误不返回，但是，这里出错属于是配置问题，基本不会出现，故适当提升告警优先级
					zlog.Warnf(ctx, "[buildPointDetail] jsoniter.UnmarshalFromString error! explain:%+v, err:%+v", explain, _err)
					// 查不到：不展示。
				}
			}
			for _, conf := range explainConf {
				item.ExplainList = append(item.ExplainList, dtofeedbackv2.ExplainListItem{
					Tag:  conf.Tag,
					Text: conf.Text,
				})
			}
			break
		}
		notFoundInfos := make([]dtofeedbackv2.PointTargetExplainConfNotFoundInfo, 0)
		if !found {
			// 查不到：对应未知
			item.MasterLevel = defines.MasterLevelY
			item.MasterStatus = defines.MasterStatusUnknown

			notFoundInfo := dtofeedbackv2.PointTargetExplainConfNotFoundInfo{
				CourseID:   req.CourseID,
				CourseName: context.CourseInfo.CourseName,
				CpuID:      context.CourseInfo.CpuId,
				PointID:    pointId,
				PointName:  item.PointName,
				RightFrac:  rightFrac,
			}
			zlog.Warnf(ctx, "[buildPointDetail] point target explain conf not found! notFoundInfo:%+v", notFoundInfo)
			notFoundInfos = append(notFoundInfos, notFoundInfo)
		}
		rsp.PointDetail.List = append(rsp.PointDetail.List, item)

		if len(notFoundInfos) > 0 {
			var wg sync.WaitGroup
			wg.Add(len(notFoundInfos))
			fwyyutils.BatchExec(ctx, func(item interface{}) {
				defer wg.Done()
				notFoundInfo := item.(dtofeedbackv2.PointTargetExplainConfNotFoundInfo)
				SendExplainNotFoundNotice(ctx, notFoundInfo)
			}, len(notFoundInfos), notFoundInfos)
			wg.Wait()
		}
	}

	// 列表按互动题正确率由高到低排序
	sort.Slice(rsp.PointDetail.List, func(i, j int) bool {
		if rsp.PointDetail.List[i].InteractionRightFrac != rsp.PointDetail.List[j].InteractionRightFrac {
			return rsp.PointDetail.List[i].InteractionRightFrac > rsp.PointDetail.List[j].InteractionRightFrac
		}
		return rsp.PointDetail.List[i].PointID < rsp.PointDetail.List[j].PointID
	})
	rsp.PointDetail.Total = len(rsp.PointDetail.List)
	return
}

func SendExplainNotFoundNotice(ctx *gin.Context, notFoundInfo dtofeedbackv2.PointTargetExplainConfNotFoundInfo) {
	lockKey := fmt.Sprintf("assistantdeskgo:SendExplainNotFoundNotice_%+v_%+v", notFoundInfo.CourseID, notFoundInfo.PointID)
	redisLock, err := helpers.RedisClient.SetNxByEX(ctx, lockKey, "1", defines.ExplainConfigNotFoundSameNoticeGapSecs)
	if err != nil {
		zlog.Warnf(ctx, "SendExplainNotFoundNotice redis.SetNxByEX error! notFoundInfo:%+v, lockKey:%+v, err:%+v", notFoundInfo, lockKey, err)
		return
	}
	if !redisLock {
		zlog.Infof(ctx, "SendExplainNotFoundNotice redis.SetNxByEX get redisLock fail! notFoundInfo:%+v, lockKey:%+v", notFoundInfo, lockKey)
		return
	}

	bot := third.DingBot{}
	err = bot.Init(ctx, defines.DingBotNameFeedbackV2)
	if err != nil {
		zlog.Infof(ctx, "SendExplainNotFoundNotice bot init error! err:%+v", err)
		return
	}

	title := "知识目标解读话术未查到"
	text := "### 知识目标解读话术未查到\n\n" +
		fmt.Sprintf("课程id: %d\n\n", notFoundInfo.CourseID) +
		fmt.Sprintf("知识目标id:%d\n\n", notFoundInfo.PointID) +
		fmt.Sprintf("课程cpuId:%d\n\n", notFoundInfo.CpuID) +
		fmt.Sprintf("课程名称: %s\n\n", notFoundInfo.CourseName) +
		fmt.Sprintf("知识目标名称：%s\n\n", notFoundInfo.PointName) +
		fmt.Sprintf("互动题正确率：%f", notFoundInfo.RightFrac)
	ret, err := bot.SendMarkdownMsg(title, text)
	if err != nil {
		zlog.Infof(ctx, "SendExplainNotFoundNotice SendTextMsg error! err:%+v", err)
		return
	}
	zlog.Infof(ctx, "SendExplainNotFoundNotice notFoundInfo:%+v, ret:%+v", notFoundInfo, ret)
	return
}

// getPercent 计算a/b，结果以百分比字符串表示，四舍五入最多保留place位小数，若分母为0返回空字符串
func getPercent(a int64, b int64, place int) string {
	if b == 0 || place < 0 {
		return ""
	}
	rounded := math.Pow(10, float64(place))
	roundedValue := math.Round(float64(a)*100*rounded/float64(b)) / rounded
	s := strconv.FormatFloat(roundedValue, 'f', place, 64)
	if place > 0 {
		s = strings.TrimRight(s, "0")
		s = strings.TrimRight(s, ".")
	}
	return s + "%"
}
