package feedbackv2

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/zbtikuapi"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetPointName(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("/Users/<USER>/fudao_bzr/assistantdeskgo")
	helpers.PreInit()
	helpers.InitResourceForCron(engine)
	helpers.InitApiClient()
	defer helpers.Release()

	pointIdList := []int64{10}
	rsp, err := zbtikuapi.GetPointName(ctx, pointIdList)
	fmt.Println(rsp)
	fmt.Println(err)
}
