package feedbackv2

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	dtofeedbackv2 "assistantdeskgo/dto/feedbackv2"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"time"
)

func List(ctx *gin.Context, req dtofeedbackv2.PointExplainListReq) (rsp dtofeedbackv2.PointExplainListRsp, err error) {
	if req.Rn == 0 {
		req.Rn = 20
	}
	list, count, err := models.FeedbackPointTargetExplainRef.GetExplainList(ctx, req.CpuID, req.PointID, req.Pn, req.Rn)
	if err != nil {
		zlog.Infof(ctx, "FeedbackPointTargetExplainRef.GetExplainList error! req:%+v, err:%+v", req, err)
		return
	}
	rsp.List = make([]dtofeedbackv2.PointExplainListItem, 0)
	for _, v := range list {
		rsp.List = append(rsp.List, dtofeedbackv2.PointExplainListItem{
			Id:           v.Id,
			CpuId:        v.CpuId,
			PointId:      v.PointId,
			MasterLevel:  v.MasterLevel,
			LowerLimit:   v.LowerLimit,
			LowerIsOpen:  v.LowerIsOpen,
			UpperLimit:   v.UpperLimit,
			UppperIsOpen: v.UpperIsOpen,
			ExplainConf:  v.ExplainConf,
			Creator:      v.Creator,
			Updater:      v.Updater,
			CreateTime:   v.CreateTime,
			UpdateTime:   v.UpdateTime,
		})
	}
	rsp.Total = count
	return
}

func Upsert(ctx *gin.Context, req dtofeedbackv2.PointExplainUpsertReq) (rsp dtofeedbackv2.PointExplainUpsertRsp, err error) {
	info, _ := middleware.GetLoginUserInfo(ctx)
	assistantUid := info.SelectedBusinessUid

	if req.CpuID == 0 || req.PointID == 0 || len(req.ExplainConf) == 0 {
		zlog.Infof(ctx, "empty value in req:%+v", req)
		return rsp, components.ErrorParamInvalid
	}
	allMasterLevel := []int{defines.MasterLevelIntS, defines.MasterLevelIntA, defines.MasterLevelIntB}
	exist, _ := fwyyutils.InArray(req.MasterLevel, allMasterLevel)
	if !exist {
		zlog.Infof(ctx, "invalid masterLevel. req:%+v", req)
		return rsp, components.ErrorParamInvalid
	}
	_, err1 := strconv.ParseFloat(req.LowerLimit, 64)
	_, err2 := strconv.ParseFloat(req.UpperLimit, 64)
	if err1 != nil || err2 != nil {
		zlog.Infof(ctx, "invalid limit, req:%+v", req)
		return rsp, components.ErrorParamInvalid
	}
	allIsOpen := []int{models.IsOpenYes, models.IsOpenNo}
	e1, _ := fwyyutils.InArray(req.LowerIsOpen, allIsOpen)
	e2, _ := fwyyutils.InArray(req.UpperIsOpen, allIsOpen)
	if !e1 || !e2 {
		zlog.Infof(ctx, "invalid isOpen, req:%+v", req)
		return rsp, components.ErrorParamInvalid
	}
	explainConfStr, _err := jsoniter.MarshalToString(req.ExplainConf)
	if _err != nil {
		zlog.Infof(ctx, "jsoniter.MarshalToString error! data:%+v, err:%+v", req.ExplainConf, _err)
		return rsp, components.ErrorParamInvalid
	}

	if req.ID == 0 {
		// 插入
		explain := models.FeedbackPointTargetExplain{
			CpuId:       req.CpuID,
			PointId:     req.PointID,
			MasterLevel: req.MasterLevel,
			LowerLimit:  req.LowerLimit,
			LowerIsOpen: req.LowerIsOpen,
			UpperLimit:  req.UpperLimit,
			UpperIsOpen: req.UpperIsOpen,
			ExplainConf: explainConfStr,
		}
		explain.Creator = assistantUid
		explain.Updater = assistantUid
		now := time.Now().Unix()
		explain.CreateTime = now
		explain.UpdateTime = now
		err = models.FeedbackPointTargetExplainRef.BatchInsert(ctx, []models.FeedbackPointTargetExplain{explain}, nil)
		if err != nil {
			zlog.Infof(ctx, "FeedbackPointTargetExplainRef.BatchInsert error! explain:%+v, err:%+v", explain, err)
			return
		}
	} else {
		// 修改
		explain := models.FeedbackPointTargetExplain{
			Id:          req.ID,
			CpuId:       req.CpuID,
			PointId:     req.PointID,
			MasterLevel: req.MasterLevel,
			LowerLimit:  req.LowerLimit,
			LowerIsOpen: req.LowerIsOpen,
			UpperLimit:  req.UpperLimit,
			UpperIsOpen: req.UpperIsOpen,
			ExplainConf: explainConfStr,
		}
		explain.Updater = assistantUid
		now := time.Now().Unix()
		explain.UpdateTime = now
		err = models.FeedbackPointTargetExplainRef.UpdateByPrimaryId(ctx, req.ID, explain)
		if err != nil {
			zlog.Infof(ctx, "FeedbackPointTargetExplainRef.UpdateByPrimaryId error! explain:%+v, err:%+v", explain, err)
			return
		}
	}
	return
}

func Delete(ctx *gin.Context, req dtofeedbackv2.PointExplainDeleteReq) (rsp dtofeedbackv2.PointExplainDeleteRsp, err error) {
	if len(req.IdList) == 0 {
		return
	}
	err = models.FeedbackPointTargetExplainRef.DeleteByIdList(ctx, req.IdList, nil)
	if err != nil {
		zlog.Infof(ctx, "FeedbackPointTargetExplainRef.DeleteByIdList error! req:%+v, err:%+v", req, err)
		return
	}
	return
}
