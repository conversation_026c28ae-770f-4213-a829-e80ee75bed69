package feedbackv2

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/feedbackv2"
	"assistantdeskgo/models"
	"bytes"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"net/http"
	"regexp"
	"strings"
	"testing"
)

func parseExplainConf(raw string) (explainConf []feedbackv2.ExplainConfItem, err error) {
	// 分割行并过滤空行
	lines := strings.Split(raw, "\n")
	nonEmptyLines := make([]string, 0, len(lines))
	for _, line := range lines {
		if trimmed := strings.TrimSpace(line); trimmed != "" {
			nonEmptyLines = append(nonEmptyLines, trimmed)
		}
	}

	tagRe := regexp.MustCompile(`^【(.*?)】\s*(.*)$`)
	var currentTag string
	var currentText strings.Builder

	for _, line := range nonEmptyLines {
		if line == "本讲为期末复习讲次，暂无解读话术" {
			line = "【期末复习】本讲为期末复习讲次，暂无解读话术"
		}
		matches := tagRe.FindStringSubmatch(line)
		if matches != nil {
			// 处理标签行
			tag := strings.TrimSpace(matches[1])
			if tag == "" {
				return nil, fmt.Errorf("empty tag in line: %q", line)
			}
			textPart := strings.TrimSpace(matches[2])

			// 提交前一个配置项
			if currentTag != "" {
				explainConf = append(explainConf, feedbackv2.ExplainConfItem{
					Tag:  currentTag,
					Text: strings.TrimSpace(currentText.String()),
				})
				currentText.Reset()
			}

			currentTag = tag
			if textPart != "" {
				currentText.WriteString(textPart)
			}
		} else {
			// 处理内容行
			if currentTag != "" {
				trimmedLine := strings.TrimSpace(line)
				if currentText.Len() > 0 {
					currentText.WriteByte('\n')
				}
				currentText.WriteString(trimmedLine)
			}
		}
	}

	// 提交最后一个配置项
	if currentTag != "" {
		explainConf = append(explainConf, feedbackv2.ExplainConfItem{
			Tag:  currentTag,
			Text: strings.TrimSpace(currentText.String()),
		})
	}

	return explainConf, nil
}

// TestImportPointTargetExplain 个性化学情反馈 解读话术导入脚本
func TestImportPointTargetExplain(t *testing.T) {
	url := "https://assistantdesk.zuoyebang.cc/assistantdeskgo/api/studyfeedbackv2/pointexplain/upsert"
	cookie := ""
	excelPath := "/Users/<USER>/Desktop/春3学情解读产研录入表.xlsx"

	f, err := excelize.OpenFile(excelPath)
	if err != nil {
		return
	}
	sheetName := f.GetSheetName(f.GetActiveSheetIndex())
	rows := f.GetRows(sheetName)

	for i, row := range rows {
		if i == 0 {
			continue
		}

		// 二级	课程产品id	知识目标id	知识点名称	难度	下限	下限开闭	上限	上限开闭	解读话术
		_cpuId, _pointId, _level, lowerLimit, _lowerIsOpen, upperLimit, _upperIsOpen, _explainConf := row[1], row[2], row[4], row[5], row[6], row[7], row[8], row[9]
		cpuId := cast.ToInt64(_cpuId)
		pointId := cast.ToInt64(_pointId)
		masterLevel := defines.MasterLevelIntMap[_level]
		if masterLevel == 0 {
			fmt.Printf("[Error] rowIndex=%d, invalid level.\n", i+1)
			continue
		}
		var lowerIsOpen, upperIsOpen int
		if strings.TrimSpace(_lowerIsOpen) == "开" {
			lowerIsOpen = models.IsOpenYes
		}
		if strings.TrimSpace(_upperIsOpen) == "开" {
			upperIsOpen = models.IsOpenYes
		}

		// 解析解读话术
		explainConf, _err := parseExplainConf(_explainConf)
		if _err != nil {
			fmt.Printf("[Error] rowIndex=%d, invalid explain conf.\n", i+1)
			continue
		}

		data := map[string]interface{}{
			"cpuId":       cpuId,
			"pointId":     pointId,
			"masterLevel": masterLevel,
			"lowerLimit":  lowerLimit,
			"lowerIsOpen": lowerIsOpen,
			"upperLimit":  upperLimit,
			"upperIsOpen": upperIsOpen,
			"explainConf": explainConf,
		}
		jsonData, _ := jsoniter.Marshal(data)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Cookie", cookie)
		client := &http.Client{}
		resp, _err := client.Do(req)
		if _err != nil {
			fmt.Printf("[Error] rowIndex=%d request error! err:%+v\n", i+1, _err)
			continue
		}
		buf := new(bytes.Buffer)
		_, _ = buf.ReadFrom(resp.Body)
		respBody := buf.String()
		fmt.Printf("OK rowIndex=%d, respBody:%+v, data:%+v\n", i+1, respBody, string(jsonData))
		_ = resp.Body.Close()
	}
}
