package servicebase

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func GetJsonContentByMessageType(ctx *gin.Context, messageType int64, content interface{}) (jsonContent string, availableRange string, err error) {
	var marshal []byte
	if marshal, err = json.Marshal(content); err != nil {
		return
	}

	jsonContent = string(marshal)
	messageInfo := models.Message{
		MessageType: messageType,
		Content:     jsonContent,
	}

	var msgContent models.BaseMessage
	if msgContent, err = messageInfo.GetMessageContent(); err != nil {
		return
	}

	availableRange = msgContent.GetAvailableRange()

	if err = msgContent.Check(models.DefaultMsgCheckParams); err != nil {
		return
	}

	if messageType == defines.MessageTypeVoice {
		voice := &models.Voice{}
		if err = models.DecodeContent(jsonContent, voice); err != nil {
			return
		}

		ret := models.Voice{
			VoiceList: make([]models.VoiceInfo, 0),
		}
		var silkName string
		for _, item := range voice.VoiceList {
			if silkName, err = utils.GetSilkName(ctx, item.VoiceName, item.VoiceType); err != nil {
				return
			}
			item.SilkVoiceLink = silkName

			ret.VoiceList = append(ret.VoiceList, item)
		}

		var data []byte
		if data, err = json.Marshal(ret); err != nil {
			return
		}
		jsonContent = string(data)
	}
	return
}

func GetJsonStructByMessageType(ctx *gin.Context, messageType int64, content string) (contentStruct interface{}, err error) {
	str := []byte(content)

	switch messageType {
	case defines.MessageTypeWord:
		word := &models.Word{}
		if err = json.Unmarshal(str, word); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeWord, content, err)
			return
		}
		contentStruct = word

	case defines.MessageTypePicture:
		img := &models.Img{}
		if err = json.Unmarshal(str, img); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypePicture, content, err)
			return
		}
		contentStruct = img

	case defines.MessageTypeVoice:
		voice := &models.Voice{}
		if err = json.Unmarshal(str, voice); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeVoice, content, err)
			return
		}
		url, _err := helpers.BaiduBucket2.GetUrlByFileName(ctx, voice.VoiceList[0].VoiceName, 24*60*time.Second)
		if _err != nil {
			err = _err
			return
		}
		voice.VoiceList[0].Url = url
		contentStruct = voice

	case defines.MessageTypeFile:
		file := &models.File{}
		if err = json.Unmarshal(str, file); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}
		url, _err := helpers.BaiduBucket2.GetUrlByFileName(ctx, file.FileList[0].FileName, 24*60*time.Second)
		if err != nil {
			err = _err
			return
		}
		file.FileList[0].Url = url
		contentStruct = file

	case defines.MessageTypeCardLink:
		contentStruct = &models.Card{}
		if err = json.Unmarshal(str, contentStruct); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}

	case defines.MessageTypeVideoMaterial:
		contentStruct = &models.VideoMaterial{}
		if err = json.Unmarshal(str, contentStruct); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}

	case defines.MessageTypeVideo:
		videoInfo := &models.Video{}
		if err = json.Unmarshal(str, videoInfo); err != nil {
			zlog.Warnf(ctx, "json unmarshal data failed, type:%+v, message:%+v, err:%+v", defines.MessageTypeFile, content, err)
			return
		}
		for index := range videoInfo.VideoList {
			url, _err := helpers.BaiduBucket2.GetUrlByFileName(ctx, videoInfo.VideoList[index].Name, 24*60*time.Second)
			if err != nil {
				err = _err
				return
			}
			videoInfo.VideoList[index].Url = url
		}

		contentStruct = videoInfo
	}
	return contentStruct, nil
}

func GetMessageContent(msgType int64, content string) (models.BaseMessage, error) {
	if _, ok := models.ContentEntityMap[msgType]; !ok {
		return nil, errors.New("不支持的消息类型")
	}

	ret := models.ContentEntityMap[msgType]
	ret = ret.Init()
	err := ret.SetContent(content)

	return ret, err
}
