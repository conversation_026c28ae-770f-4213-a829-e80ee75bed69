package servicebase

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"encoding/json"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

const REDIS_MD5_KEY = "assistantdeskgo:md5_file:%v"

type Md5UrlData struct {
	URL string `json:"url" form:"url"`
	MD5 string `json:"md5" form:"md5"`
}

func GetRedisKey(url string) (string, error) {
	key := strings.Replace(url, "://", "/", 1)
	split := strings.Split(key, "?")
	if len(split) == 0 {
		return "", base.Error{components.ErrorParamInvalid.ErrNo, "格式不正确"}
	}
	splitV2 := strings.Split(split[0], "/")
	if len(splitV2) == 3 {
		return fmt.Sprintf(REDIS_MD5_KEY, splitV2[2]), nil
	}
	return "", base.Error{components.ErrorParamInvalid.ErrNo, "格式异常"}
}

func SaveMd5(ctx *gin.Context, url string, md5 string) error {
	if url == "" || md5 == "" {
		return components.ErrorParamInvalid
	}

	if !strings.HasPrefix(url, "https://") && !strings.HasPrefix(url, "http://") {
		return base.Error{components.ErrorParamInvalid.ErrNo, "url must start with https:// or http://"}
	}
	redisKey, err := GetRedisKey(url)
	if err != nil {
		return err
	}
	redisVal, _ := json.Marshal(Md5UrlData{URL: url, MD5: md5})
	return helpers.RedisClient.Set(ctx, redisKey, string(redisVal))
}

func GetMd5(ctx *gin.Context, url string) (string, error) {
	cacheKey, err := GetRedisKey(url)

	cacheData, hit := helpers.Cache3.Get(cacheKey)
	if hit {
		data, ok := cacheData.(Md5UrlData)
		if ok {
			return data.MD5, nil
		}

	}

	if err != nil {
		return "", err
	}

	if err != nil {
		return "", err
	}

	data, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err != nil || data == nil {
		return "", err
	}
	var result Md5UrlData
	err = json.Unmarshal(data, &result)
	zlog.Infof(ctx, "RedisGetVal:%+v", result)
	if err != nil {
		return "", nil
	}
	helpers.Cache3.SetDefault(cacheKey, result)
	return result.MD5, nil
}
