package studyfeedback

import (
	"assistantdeskgo/components"
	dtostudyfeedback "assistantdeskgo/dto/studyfeedback"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	sf "assistantdeskgo/models/studyfeedback"
	"assistantdeskgo/utils"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

func GetPointByTid(ctx *gin.Context, req dtostudyfeedback.GetPointByTidReq) (rsp dtostudyfeedback.GetPointByTidRsp, err error) {
	tidList := req.TidList
	list, _err := sf.TblStudyFeedbackQuestionPointMappingRef.GetPointMappingByTid(ctx, tidList)
	if _err != nil {
		err = _err
		zlog.Error(ctx, "GetPointMappingByTid error! req:%+v", req)
		return
	}
	rsp.PointList = make([]dtostudyfeedback.PointInfoWithTid, 0)

	pointIdSet := make(map[int64]bool)
	for _, mapping := range list {
		pointId := mapping.PointId
		pointIdSet[pointId] = true
	}
	pointIdList := make([]int64, 0)
	for pointId, _ := range pointIdSet {
		pointIdList = append(pointIdList, pointId)
	}

	pointList, _err := sf.TblStudyFeedbackPointRef.GetPointByIdList(ctx, pointIdList)
	if _err != nil {
		err = _err
		zlog.Error(ctx, "GetPointByIdList error! req:%+v, pointIdList:%+v", req, pointIdList)
		return
	}
	pointMap := make(map[int64]sf.TblStudyFeedbackPoint)
	for _, point := range pointList {
		pointMap[point.Id] = point
	}

	for _, mapping := range list {
		tid := mapping.Tid
		pointId := mapping.PointId

		if point, ok := pointMap[pointId]; ok {
			item := dtostudyfeedback.PointInfoWithTid{
				Tid: tid,
			}
			item.Id = pointId
			item.Point = point.Point
			item.Creator = point.Creator
			item.Updater = point.Updater
			item.CreateTime = point.CreateTime
			item.UpdateTime = point.UpdateTime
			item.Deleted = point.Deleted
			rsp.PointList = append(rsp.PointList, item)
		}
	}
	return
}

func AddPointTids(ctx *gin.Context, req dtostudyfeedback.AddPointTidsReq) (rsp dtostudyfeedback.AddPointTidsRsp, err error) {

	info, _err := middleware.GetLoginUserInfo(ctx)
	if _err != nil {
		err = _err
		zlog.Warnf(ctx, "middleware.GetLoginUserInfo error! err:%+v", _err)
		return
	}
	assistantUid := info.SelectedBusinessUid

	point := req.Point
	var redisLock bool
	hash := md5.Sum([]byte(point))
	pointMd5 := hex.EncodeToString(hash[:])
	pointRedisKey := fmt.Sprintf("assistantdeskgo:study_explain_point_lock_%+v", pointMd5)
	pointRedisValue := point
	redisLock, err = helpers.RedisClient.SetNxByEX(ctx, pointRedisKey, pointRedisValue, 10)
	if err != nil {
		zlog.Warnf(ctx, "redis.SetNxByEX error!", err)
		err = components.DefaultError("获取锁失败，请稍后重试")
		return
	}
	if !redisLock {
		err = components.DefaultError("获取锁失败")
		return
	}

	defer func() {
		_, _ = utils.ReleaseLockByValue(ctx, pointRedisKey, pointRedisValue)
	}()

	needInsertPoint := false
	var pointInst sf.TblStudyFeedbackPoint
	pointInst, err = sf.TblStudyFeedbackPointRef.GetPointByName(ctx, point, false)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		pointInst, err = sf.TblStudyFeedbackPointRef.GetPointByName(ctx, point, true)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			needInsertPoint = true
		}
	}
	if err != nil && !needInsertPoint {
		zlog.Warnf(ctx, "GetPointByName fail! error:%+v", err)
		return
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) (err error) {
		if needInsertPoint {
			pointInst = sf.TblStudyFeedbackPoint{
				Point:      point,
				Creator:    assistantUid,
				Updater:    assistantUid,
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			}
			err = sf.TblStudyFeedbackPointRef.Insert(ctx, &pointInst, tx)
			if err != nil {
				zlog.Warnf(ctx, "TblStudyFeedbackPointRef.Insert error! error: %+v", err)
				return
			}
		}

		pointId := pointInst.Id
		if pointId == 0 {
			err = errors.New("pointId is 0")
			zlog.Warnf(ctx, "pointId = 0, error: %+v", err)
			return
		}

		mappingList := make([]sf.TblStudyFeedbackQuestionPointMapping, 0)
		for _, tid := range req.TidList {
			mapping := sf.TblStudyFeedbackQuestionPointMapping{
				Tid:        tid,
				PointId:    pointId,
				Creator:    assistantUid,
				Updater:    assistantUid,
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			}
			mappingList = append(mappingList, mapping)
		}
		err = sf.TblStudyFeedbackQuestionPointMappingRef.BatchInsert(ctx, mappingList, tx)
		if err != nil {
			zlog.Warnf(ctx, "TblStudyFeedbackQuestionPointMappingRef.BatchInsert error! error: %+v", err)
			return
		}
		return
	})
	return
}

func RemovePoint(ctx *gin.Context, req dtostudyfeedback.RemovePointReq) (rsp dtostudyfeedback.RemovePointRsp, err error) {
	pointId := req.PointId
	info, _err := middleware.GetLoginUserInfo(ctx)
	if _err != nil {
		err = _err
		zlog.Warnf(ctx, "middleware.GetLoginUserInfo error! err:%+v", _err)
		return
	}
	assistantUid := info.SelectedBusinessUid

	err = helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) (err error) {

		err = sf.TblStudyFeedbackQuestionPointMappingRef.DeleteMappingByPointId(ctx, pointId, assistantUid, tx)
		if err != nil {
			zlog.Warnf(ctx, "TblStudyFeedbackQuestionPointMappingRef.DeleteMappingByPointId error! error: %+v", err)
			return
		}

		err = sf.TblStudyFeedbackPointExplainRef.DeleteMappingByPointId(ctx, pointId, assistantUid, tx)
		if err != nil {
			zlog.Warnf(ctx, "TblStudyFeedbackPointExplainRef.DeleteMappingByPointId error! error: %+v", err)
			return
		}

		err = sf.TblStudyFeedbackPointRef.DeleteMappingByPointId(ctx, pointId, assistantUid, tx)
		if err != nil {
			zlog.Warnf(ctx, "TblStudyFeedbackPointRef.DeleteMappingByPointId error! error: %+v", err)
			return
		}
		return
	})

	return
}
