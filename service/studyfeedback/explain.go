package studyfeedback

import (
	"assistantdeskgo/components"
	dtostudyfeedback "assistantdeskgo/dto/studyfeedback"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	sf "assistantdeskgo/models/studyfeedback"
	"assistantdeskgo/utils"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

func GetExplain(ctx *gin.Context, req dtostudyfeedback.GetExplainReq) (rsp dtostudyfeedback.GetExplainRsp, err error) {
	pointIdList := req.PointIdList
	list, _err := sf.TblStudyFeedbackPointExplainRef.GetExplainByPointIdList(ctx, pointIdList)
	if _err != nil {
		err = _err
		zlog.Error(ctx, "GetExplainByPointIdList error! req:%+v", req)
		return
	}
	rsp.PointExplainList = make([]dtostudyfeedback.PointExplain, 0)
	for _, record := range list {
		item := dtostudyfeedback.PointExplain{
			Id:           record.Id,
			PointId:      record.PointId,
			ExplainIndex: record.ExplainIndex,
			Explain:      record.Explain,
			Creator:      record.Creator,
			Updater:      record.Updater,
			CreateTime:   record.CreateTime,
			UpdateTime:   record.UpdateTime,
			Deleted:      record.Deleted,
		}
		rsp.PointExplainList = append(rsp.PointExplainList, item)
	}
	return
}

func AddPointExplains(ctx *gin.Context, req dtostudyfeedback.AddPointExplainsReq) (rsp dtostudyfeedback.AddPointExplainsRsp, err error) {

	info, _err := middleware.GetLoginUserInfo(ctx)
	if _err != nil {
		err = _err
		zlog.Warnf(ctx, "middleware.GetLoginUserInfo error! err:%+v", _err)
		return
	}
	assistantUid := info.SelectedBusinessUid

	point := req.Point
	var redisLock bool
	hash := md5.Sum([]byte(point))
	pointMd5 := hex.EncodeToString(hash[:])
	pointRedisKey := fmt.Sprintf("assistantdeskgo:study_explain_point_lock_%+v", pointMd5)
	pointRedisValue := point
	redisLock, err = helpers.RedisClient.SetNxByEX(ctx, pointRedisKey, pointRedisValue, 10)
	if err != nil {
		zlog.Warnf(ctx, "redis.SetNxByEX error!", err)
		err = components.DefaultError("获取锁失败，请稍后重试")
		return
	}
	if !redisLock {
		err = components.DefaultError("获取锁失败")
		return
	}
	defer func() {
		_, _ = utils.ReleaseLockByValue(ctx, pointRedisKey, pointRedisValue)
	}()

	needInsertPoint := false
	var pointInst sf.TblStudyFeedbackPoint
	pointInst, err = sf.TblStudyFeedbackPointRef.GetPointByName(ctx, point, false)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		pointInst, err = sf.TblStudyFeedbackPointRef.GetPointByName(ctx, point, true)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			needInsertPoint = true
		}
	}
	if err != nil && !needInsertPoint {
		zlog.Warnf(ctx, "GetPointByName fail! error:%+v", err)
		return
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Transaction(func(tx *gorm.DB) (err error) {
		if needInsertPoint {
			pointInst = sf.TblStudyFeedbackPoint{
				Point:      point,
				Creator:    assistantUid,
				Updater:    assistantUid,
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			}
			err = sf.TblStudyFeedbackPointRef.Insert(ctx, &pointInst, tx)
			if err != nil {
				zlog.Warnf(ctx, "TblStudyFeedbackPointRef.Insert error! error: %+v", err)
				return
			}
		}

		pointId := pointInst.Id
		if pointId == 0 {
			err = errors.New("pointId is 0")
			zlog.Warnf(ctx, "pointId = 0, error: %+v", err)
			return
		}

		explainList := make([]sf.TblStudyFeedbackPointExplain, 0)
		for _, explain := range req.Explains {
			explainRecord := sf.TblStudyFeedbackPointExplain{
				PointId:      pointId,
				ExplainIndex: explain.ExplainIndex,
				Explain:      explain.Explain,
				Creator:      assistantUid,
				Updater:      assistantUid,
				CreateTime:   time.Now().Unix(),
				UpdateTime:   time.Now().Unix(),
			}
			explainList = append(explainList, explainRecord)
		}
		err = sf.TblStudyFeedbackPointExplainRef.BatchInsert(ctx, explainList, tx)
		if err != nil {
			zlog.Warnf(ctx, "TblStudyFeedbackPointExplainRef.BatchInsert error! error: %+v", err)
			return
		}
		return
	})

	return
}
