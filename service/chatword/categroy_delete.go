package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/oplog"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"time"
)

func CategoryDelete(ctx *gin.Context, req dtochatword.DeleteCategoryReq) error {
	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}
	if !IsChatWordSupper(ctx) {
		return base.Error{components.ErrorParamInvalid.ErrNo, "无删除分类权限"}
	}

	category, err := cw.ChatWordCategroyRef.GetById(ctx, req.CategoryId)
	if err != nil {
		return err
	}
	if category.Id <= 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "分类不存在"}
	}

	category.Deleted = cw.DeletedYes
	category.UpdateTime = time.Now().Unix()

	err = cw.ChatWordCategroyRef.Update(ctx, category)

	if err == nil {
		helpers.RedisClient.Del(ctx, CATEGORY_CACHE_KEY)
		AllCategory(ctx) //重新加一下缓存
	}

	oplog.AddBaseLog(ctx, utils2.ToString(category.Id), defines.RELATION_TYPE_CATEGORY,
		defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_DELETE_CATEGORY,
		req, int64(info.UserId),
		fmt.Sprintf("%v删除了话术分类，分类Id:%v", info.UserName, utils2.ToString(category.Id)))
	return err

}
