package chatword

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/utils"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"sort"
	"strconv"
)

const CATEGORY_CACHE_KEY = "assistantdeskgo:chatword:category_all"
const CATEGORY_LCACHE_KEY = "assistantdeskgo:chatword:category_list:%v"
const CATEGORY_CACHE_Time = 600

func AllCategory(ctx *gin.Context) (resp []dtochatword.CategoryInfoRsp, err error) {

	data, err := helpers.RedisClient.Get(ctx, CATEGORY_CACHE_KEY)
	if err == nil && data != nil {
		err := json.Unmarshal(data, &resp)
		if err == nil {
			return resp, nil
		}

	}
	categoryList, err := cw.ChatWordCategroyRef.ListAll(ctx)
	if err != nil {
		return
	}
	for index := range categoryList {
		resp = append(resp, dtochatword.CategoryInfoRsp{
			CategoryId:   categoryList[index].Id,
			CategoryName: categoryList[index].Category,
			CreateTime:   categoryList[index].CreateTime,
		})
	}
	redisVal, _ := json.Marshal(resp)
	helpers.RedisClient.Set(ctx, CATEGORY_CACHE_KEY, string(redisVal), CATEGORY_CACHE_Time)
	return
}

func ListCategory(ctx *gin.Context) (resp []dtochatword.CategoryWithKeywordRsp, err error) {

	loginUser, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return nil, err
	}
	cacheKey := fmt.Sprintf(CATEGORY_LCACHE_KEY, loginUser.UserId)
	data, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err == nil && data != nil {
		err = json.Unmarshal(data, &resp)
		if err == nil {
			return resp, nil
		}
	}

	groupIdList := []string{}
	if !IsWecomChatWordSupper(ctx) {
		groupIdList, err = getQueryGroupIdList(ctx, int64(loginUser.UserId))
		if err != nil {
			return nil, err
		}
	}

	talkEsData, err := dataproxy.ChatWordTalkSearch(ctx, dataproxy.ChatWordTalkSearchReq{
		Fields:      []string{"talk_id", "category_id", "category_name", "keyword"},
		GroupIdList: groupIdList,
		Page:        defines.PAGE_START,
		PageSize:    defines.ES_DEFAULT_SIZE,
	})
	if err != nil {
		return nil, err
	}
	if talkEsData == nil || talkEsData.Total == 0 {
		return nil, nil
	}

	allCategoryList, err := AllCategory(ctx)
	if err != nil {
		return nil, err
	}

	categoryKeywordMap := map[int64][]string{}
	categoryNameMap := map[int64]string{}
	categorySortMap := map[int64]int64{}
	for _, esData := range talkEsData.List {
		categoryId, _ := strconv.ParseInt(esData.CategoryId, 10, 64)
		if categoryId == 0 {
			continue
		}
		categoryKeywordMap[categoryId] = append(categoryKeywordMap[categoryId], esData.Keyword...)
		categoryNameMap[categoryId] = esData.CategoryName
	}
	for _, categoryInfo := range allCategoryList {
		categorySortMap[categoryInfo.CategoryId] = categoryInfo.CreateTime
	}
	for categoryId, categoryName := range categoryNameMap {
		resp = append(resp, dtochatword.CategoryWithKeywordRsp{
			CategoryInfoRsp: dtochatword.CategoryInfoRsp{
				CategoryId:   categoryId,
				CategoryName: categoryName,
				CreateTime:   categorySortMap[categoryId],
			},
			Keyword: utils.FilterStrDuplicates(categoryKeywordMap[categoryId]),
		})
	}

	sort.Slice(resp, func(i, j int) bool {
		return categorySortMap[resp[j].CategoryId]-categorySortMap[resp[i].CategoryId] > 0
	})
	redisVal, _ := json.Marshal(resp)
	helpers.RedisClient.Set(ctx, cacheKey, redisVal, CATEGORY_CACHE_Time)
	return
}
