package chatword

import (
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/middleware"
	"assistantdeskgo/utils"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
)

var supportVariable = map[string]variableFunc{
	"#学生名#":    getStudentVariable,
	"#服务老师真名#": getPersonVariable,
}

type variableFunc func(ctx *gin.Context, req dtochatword.GetVariableReq) (string, error)

func VariableGet(ctx *gin.Context, req dtochatword.GetVariableReq) (resp string, err error) {
	if len(req.Variable) == 0 {
		return "", components.ErrorParamInvalid
	}
	valueArray := []string{}
	for _, variable := range req.Variable {
		fun, ok := supportVariable[variable]
		if !ok {
			valueArray = append(valueArray, variable)
			continue
		}
		value, err := fun(ctx, req)
		if err != nil {
			return "", err
		}
		valueArray = append(valueArray, value)
	}
	return strings.Join(valueArray, ""), nil
}

func getStudentVariable(ctx *gin.Context, req dtochatword.GetVariableReq) (string, error) {
	user, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return "", err
	}

	// 根据remoteid查询企微信息
	staffInfoMap, err := kunpeng.IdExchangeStaffInfo(ctx, kunpeng.IdExchangeReq{
		IdList: []string{user.RemoteId},
		Type:   3,
		CorpId: user.CorpId,
	})
	if err != nil {
		return "", err
	}
	staffUid := staffInfoMap[user.RemoteId].StaffUid
	if staffUid == 0 {
		return "", errors.New("资产信息未找到")
	}

	wxRelationList, err := kunpeng.GetUidByWxId(ctx, kunpeng.GetUidByWxIdReq{
		StaffUid: staffUid,
		WxIds:    []string{req.UserId},
		AppId:    2,
	})

	if len(wxRelationList) > 0 {
		//取第一个
		studentMap, err := dau.GetStudents(ctx, utils.Int64Collect(wxRelationList, "StudentUid"), []string{"studentUid", "studentName", "phone"})
		if err != nil {
			return "", err
		}
		for _, value := range studentMap {
			familyName, _err := muse.GetFamilyLastName(ctx, muse.MuseGetFamilyLastNameReq{value.StudentName})
			if _err != nil {
				return value.StudentName, nil
			}
			return familyName.StudentName, nil
		}
	}

	return "", errors.New("未找到学生信息")
}

func getPersonVariable(ctx *gin.Context, req dtochatword.GetVariableReq) (string, error) {
	loginUser, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return "", err
	}
	staffMap, err := mesh.GetStaffInfoList(ctx, []int64{int64(loginUser.UserId)})
	if err != nil {
		return "", err
	}
	staffInfo, ok := staffMap[strconv.Itoa(loginUser.UserId)]
	if !ok {
		return "", base.Error{components.ErrorParamInvalid.ErrNo, "未找到真人信息"}
	}
	return staffInfo.UserName, nil
}
