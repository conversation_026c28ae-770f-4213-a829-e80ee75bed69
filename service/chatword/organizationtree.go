package chatword

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/conf"
	"assistantdeskgo/dto/dtomessage"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sort"
)

func GetOrganizationTree(ctx *gin.Context, staffUid int64) (groupTrees []*dtomessage.GroupPermissionNode, err error) {
	if IsChatWordSupper(ctx) {
		return FormatOrganizationTreeForGlobal(ctx)
	}

	positions, err := mesh.GetStaffGroupPositions(ctx, staffUid)
	if err != nil {
		return
	}

	groupIds := []int64{}
	for _, position := range positions {
		groupIds = append(groupIds, position.GroupId)
	}

	// 向上取父节点
	var groupDetail map[string]mesh.GroupDetail
	if groupDetail, err = mesh.GetGroupDetailByIds(ctx, groupIds); err != nil {
		return nil, err
	}

	retMap := make(map[int64]struct{})
	nodeMap := make(map[int64]*dtomessage.GroupPermissionNode)
	// 标记链表中的节点是否被组装到树里
	childNodeMap := make(map[int64]map[int64]struct{})
	var item *mesh.GroupTree
	for _, groupId := range groupIds {
		if _, ok := groupDetail[cast.ToString(groupId)]; !ok {
			continue
		}

		permissionDetail := groupDetail[cast.ToString(groupId)]
		var permissionNode dtomessage.GroupPermissionNode
		if _, ok := nodeMap[permissionDetail.ParentTree.ID]; !ok {
			permissionNode = dtomessage.GroupPermissionNode{
				ID:       permissionDetail.ParentTree.ID,
				Name:     permissionDetail.ParentTree.Name,
				Children: make([]*dtomessage.GroupPermissionNode, 0),
			}
			nodeMap[permissionNode.ID] = &permissionNode // 通过map去重
		}
		permissionNode = *nodeMap[permissionDetail.ParentTree.ID]

		nodeMap, childNodeMap = BuildPermissionChildren(&permissionDetail.ParentTree, nodeMap, childNodeMap)

		// 每个节点向下取子节点
		if item, err = mesh.GetGroupAllChildren(ctx, groupId); err != nil {
			return nil, err
		}
		if item.Children == nil {
			continue
		}

		nodeMap = BuildPermission(item, nodeMap, childNodeMap)
		if _, ok := retMap[permissionNode.ID]; !ok {
			retMap[permissionNode.ID] = struct{}{}
		}
	}

	rootIds := make([]int, 0)
	for rootId, _ := range retMap {
		rootIds = append(rootIds, int(rootId))
	}
	sort.Ints(rootIds)

	ret := make([]*dtomessage.GroupPermissionNode, 0)
	for _, rootId := range rootIds {
		ret = append(ret, nodeMap[int64(rootId)])
	}

	return ret, err

}

func BuildPermission(permission *mesh.GroupTree, nodeMap map[int64]*dtomessage.GroupPermissionNode, childNodeMap map[int64]map[int64]struct{}) map[int64]*dtomessage.GroupPermissionNode {
	if permission.Children == nil {
		return nodeMap
	}

	node := nodeMap[int64(permission.Id)]
	node.WithPermission = true
	for _, permissionDetail := range permission.Children {
		if _, ok := nodeMap[int64(permissionDetail.Id)]; !ok {
			permissionNode := dtomessage.GroupPermissionNode{
				ID:             int64(permissionDetail.Id),
				Name:           permissionDetail.Name,
				WithPermission: true,
				Children:       make([]*dtomessage.GroupPermissionNode, 0),
			}
			nodeMap[permissionNode.ID] = &permissionNode
			nodeMap = BuildPermission(&permissionDetail, nodeMap, childNodeMap)
		}

		if _, exist := childNodeMap[node.ID]; !exist {
			childNodeMap[node.ID] = make(map[int64]struct{})
		}

		if _, exist := childNodeMap[node.ID][int64(permissionDetail.Id)]; !exist {
			childNodeMap[node.ID][int64(permissionDetail.Id)] = struct{}{}
			node.Children = append(node.Children, nodeMap[int64(permissionDetail.Id)])
		}
	}
	return nodeMap
}

func BuildPermissionChildren(node *mesh.ParentTree, nodeMap map[int64]*dtomessage.GroupPermissionNode, childNodeMap map[int64]map[int64]struct{}) (map[int64]*dtomessage.GroupPermissionNode, map[int64]map[int64]struct{}) {
	if node.Children == nil {
		return nodeMap, childNodeMap
	}

	nodeInfo := nodeMap[node.ID]

	childNode := node.Children
	if _, ok := childNodeMap[node.ID][childNode.ID]; !ok {
		if _, exist := nodeMap[childNode.ID]; !exist {
			childNodeInfo := dtomessage.GroupPermissionNode{
				ID:       childNode.ID,
				Name:     childNode.Name,
				Children: make([]*dtomessage.GroupPermissionNode, 0),
			}
			nodeMap[childNode.ID] = &childNodeInfo
		}

		if _, exist := childNodeMap[node.ID]; !exist {
			childNodeMap[node.ID] = make(map[int64]struct{})
		}

		if _, exist := childNodeMap[node.ID][childNode.ID]; !exist {
			childNodeMap[node.ID][childNode.ID] = struct{}{}
			nodeInfo.Children = append(nodeInfo.Children, nodeMap[childNode.ID])
		}
	}

	return BuildPermissionChildren(node.Children, nodeMap, childNodeMap)
}

func FormatOrganizationTreeForGlobal(ctx *gin.Context) (groupTrees []*dtomessage.GroupPermissionNode, err error) {
	var item *mesh.GroupTree
	permissionTree := make([]mesh.GroupTree, 0)
	for _, groupId := range conf.Custom.Mesh.GroupId {
		if item, err = mesh.GetGroupAllChildren(ctx, groupId); err != nil {
			return nil, err
		}

		if item != nil {
			permissionTree = append(permissionTree, *item)
		}
	}

	groupTrees = make([]*dtomessage.GroupPermissionNode, 0)
	for _, permissionItem := range permissionTree {
		node := BuildGroupPermissionTree(permissionItem, true)
		groupTrees = append(groupTrees, node)
	}
	return
}

func BuildGroupPermissionTree(permission mesh.GroupTree, withPermission bool) *dtomessage.GroupPermissionNode {
	node := &dtomessage.GroupPermissionNode{
		ID:             int64(permission.Id),
		Name:           permission.Name,
		WithPermission: withPermission,
		Children:       make([]*dtomessage.GroupPermissionNode, 0),
	}

	if len(permission.Children) == 0 {
		return node
	}

	for _, child := range permission.Children {
		node.Children = append(node.Children, BuildGroupPermissionTree(child, withPermission))
	}
	return node
}
