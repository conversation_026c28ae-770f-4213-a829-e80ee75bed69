package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/oplog"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"strings"
	"time"
	"unicode/utf8"
)

func CategoryCreate(ctx *gin.Context, req dtochatword.CreateCategoryReq) error {

	categoryName := strings.Trim(req.CategoryName, " ")
	now := time.Now().Unix()
	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}
	categoryList, err := AllCategory(ctx)
	if err != nil {
		return err
	}

	if utf8.RuneCountInString(req.CategoryName) > 6 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "分类名称必须六个字以内"}
	}

	for _, category := range categoryList {
		if category.CategoryName == categoryName {
			return base.Error{components.ErrorParamInvalid.ErrNo, "分类已存在"}
		}
	}

	if !IsChatWordSupper(ctx) {
		return base.Error{components.ErrorParamInvalid.ErrNo, "无添加分类权限"}
	}
	category := &cw.Category{
		Category:   categoryName,
		UpdateTime: now,
		CreateTime: now,
		PersonUid:  int64(info.StaffUid),
	}
	err = cw.ChatWordCategroyRef.Insert(ctx, category)

	if err == nil {
		helpers.RedisClient.Del(ctx, CATEGORY_CACHE_KEY)
		AllCategory(ctx) //重新加一下缓存
	}

	oplog.AddBaseLog(ctx, utils2.ToString(category.Id), defines.RELATION_TYPE_CATEGORY,
		defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_ADD_CATEGORY,
		req, int64(info.UserId),
		fmt.Sprintf("%v创建了话术分类，分类Id:%v", info.UserName, utils2.ToString(category.Id)))

	return err
}
