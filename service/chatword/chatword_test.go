package chatword

import (
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/backend/message"
	"assistantdeskgo/service/oplog"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"testing"
	"time"
)

func TestCategory(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)

	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   3000028542,
		StaffUid: 3000028542,
	})

	err := CategoryCreate(ctx, dtochatword.CreateCategoryReq{
		CategoryName: "测试标签2",
	})

	fmt.Println(err)
}

func TestTalkHide(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)

	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   3000028542,
		StaffUid: 3000028542,
	})

	TalkHide(ctx, dtochatword.HideTalkReq{
		TalkIdList: []int64{4},
	})
}

func TestTalkDelete(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)

	TalkDetail(ctx, dtochatword.TalkDetailReq{TalkId: 5})

	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   3000028542,
		StaffUid: 3000028542,
	})

	TalkDelete(ctx, dtochatword.DeleteTalkReq{
		TalkIdList: []int64{4},
	})
}

func TestTalkAdd(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)

	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   3000028542,
		StaffUid: 3000028542,
	})
	req := dtochatword.CreateTalkReq{}
	req.CategoryId = 8
	req.Keyword = []string{"测试01", "测试02"}
	req.Content = "这是一段文本"
	req.Title = "标题"
	composeLst := []dtochatword.CreateTalkComposeReq{}
	composeLst = append(composeLst, dtochatword.CreateTalkComposeReq{
		MessageList: []dtochatword.CreateTalkMessageReq{dtochatword.CreateTalkMessageReq{
			Type:         0,
			Content:      []string{"消息内容", "#学生真名"},
			Order:        0,
			IntervalTime: 1,
		}, dtochatword.CreateTalkMessageReq{
			Type:         1,
			Content:      []string{"www.baidu.com"},
			Order:        1,
			IntervalTime: 2,
		}},
	})
	composeLst = append(composeLst, dtochatword.CreateTalkComposeReq{
		MessageList: []dtochatword.CreateTalkMessageReq{dtochatword.CreateTalkMessageReq{
			Type:         0,
			Content:      []string{"消息内容", "#学生真名"},
			Order:        0,
			IntervalTime: 1,
		}, dtochatword.CreateTalkMessageReq{
			Type:         1,
			Content:      []string{"www.baidu.com"},
			Order:        1,
			IntervalTime: 2,
		}},
	})
	req.ComposeList = composeLst
	req.GroupId = 17707
	err := TalkCreate(ctx, req)
	if err != nil {
		return
	}
}

func TestIdExchange(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)

	ctx.Set(middleware.WecomHelperInfo, middleware.WecomHelperLoginInfo{
		UserId:   3000028542,
		CorpId:   "ww55f2721cf084c185",
		RemoteId: "1688856240579656",
	})

	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   3000028542,
		StaffUid: 3000028542,
	})
	GroupUpdate(ctx, dtochatword.UpdateGroupReq{
		TalkIdList: []int64{44},
		GroupId:    11446,
	})
	ToMessageGroup(ctx, dtochatword.ToMessageGroupReq{
		TalkId:    26,
		ComposeId: 1,
		FolderId:  168,
		GroupName: "左卫东测试1",
		GroupDesc: "左卫东测描述2",
	})

	SaveMd5(ctx, dtochatword.SaveMd5Req{
		URL: "https://testimg.zuoyebang.cc/qa_5c9836b6a3ea2b3dba009ecee5c0d8ec.png",
		MD5: "32323",
	})

	VariableGet(ctx, dtochatword.GetVariableReq{
		UserId:   "7881299737233071",
		Variable: []string{"学生名", "服务老师真名"},
	})

	kunpeng.IdExchange(ctx, kunpeng.IdExchangeReq{
		CorpId: "ww55f2721cf084c185",
		IdList: []string{"wm-0m2EQAAHCL221GYbX_paXkCZFRMhA"},
		Type:   1,
	})

	kunpeng.IdExchange(ctx, kunpeng.IdExchangeReq{
		CorpId: "ww55f2721cf084c185",
		IdList: []string{"1688850833986185"},
		Type:   3,
	})
}

func TestFeedback(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)

	ctx.Set(middleware.WecomHelperInfo, middleware.WecomHelperLoginInfo{
		UserId: 3000028542,
	})
	message.ListResource(ctx)

	Feedback(ctx, dtochatword.FeedBackReq{
		TalkId:  10,
		Type:    0,
		Content: "测试",
	})

}

func TestList(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)
	ctx.Set(middleware.WecomHelperInfo, middleware.WecomHelperLoginInfo{
		UserId: 3000028542,
	})
	ctx.Set("userInfo", &userprofile.UserInfo{
		UserId:   3000028542,
		StaffUid: 3000028542,
	})

	ListCategory(ctx)

	list1, err := WeworkTalkList(ctx, dtochatword.WeworkListTalkReq{SearchContent: "内容"})
	if err != nil {
		return
	}

	list2, err := AssistantTalkList(ctx, dtochatword.AssistantListTalkReq{MsgContent: "内容"})
	if err != nil {
		return
	}
	fmt.Println(list1)
	fmt.Println(list2)
}

func TestLog(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../")
	helpers.Init(engine)
	helpers.InitResourceForCron(engine)
	ctx.Set(middleware.WecomHelperInfo, middleware.WecomHelperLoginInfo{
		UserId: 3000028542,
	})
	for i := 0; i < 10; i++ {
		oplog.AddBaseLog(ctx, utils2.ToString(100), defines.RELATION_TYPE_CATEGORY,
			defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_DELETE_CATEGORY,
			dtochatword.AssistantListTalkReq{MsgContent: "内容"}, int64(200),
			fmt.Sprintf("%v删除了话术分类，分类Id:%v", "左卫东", 100))
		fmt.Println(i)
	}

	time.Sleep(time.Minute * 10)
}
