package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/servicebase"
	"github.com/gin-gonic/gin"
)

func SaveMd5(ctx *gin.Context, req dtochatword.SaveMd5Req) error {
	return servicebase.SaveMd5(ctx, req.URL, req.MD5)
}

func GetMd5(ctx *gin.Context, req dtochatword.GetMd5Req) (resp dtochatword.GetMd5Rsp, err error) {

	if req.URL == "" {
		return resp, components.ErrorParamInvalid
	}

	md5, err := servicebase.GetMd5(ctx, req.URL)
	if err != nil {
		return
	}
	resp = dtochatword.GetMd5Rsp{
		URL: req.URL,
		MD5: md5,
	}
	return
}
