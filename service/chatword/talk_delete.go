package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/oplog"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"time"
)

func TalkDelete(ctx *gin.Context, req dtochatword.DeleteTalkReq) (err error) {

	if len(req.TalkIdList) <= 0 {
		return components.ErrorParamInvalid
	}

	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}

	talkList, err := cw.ChatWordTalkRef.ListByIdList(ctx, req.TalkIdList)
	if err != nil {
		return err
	}
	if len(req.TalkIdList) != len(talkList) {
		return base.Error{components.ErrorParamInvalid.ErrNo, "话术不存在"}
	}

	if !IsChatWordSupper(ctx) {
		for _, talkInfo := range talkList {
			if talkInfo.PersonUid != int64(info.StaffUid) {
				return base.Error{components.ErrorParamInvalid.ErrNo, "无话术Id:" + utils2.ToString(talkInfo.Id) + "删除权限"}
			}
		}
	}

	redisLock := fmt.Sprintf(REDIS_LOCK_TALK, info.UserId)
	retry, err := utils.LockRetry(ctx, redisLock, "1", 1, 1)
	if err != nil {
		return err
	}
	if !retry {
		return base.Error{components.ErrorRedisSet.ErrNo, "1秒钟仅能操作一次"}
	}

	now := time.Now().Unix()
	tx := helpers.MysqlClientFuDao.Begin()

	for index := range talkList {
		talkList[index].UpdateTime = now
		talkList[index].Deleted = cw.DeletedYes
		talkList[index].UpdateName = info.Name
		talkList[index].UpdateUid = int64(info.StaffUid)
		err = cw.ChatWordTalkRef.Update(ctx, talkList[index], tx)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	messageList, err := cw.ChatWordMessageRef.ListByTalkId(ctx, req.TalkIdList)
	if err != nil {
		return err
	}
	for index := range messageList {
		messageList[index].Deleted = cw.DeletedYes
		messageList[index].UpdateTime = now
	}

	err = cw.ChatWordMessageRef.BatchUpdates(ctx, messageList, tx)
	if err != nil {
		tx.Rollback()
		return err
	}
	if tx.Error != nil {
		tx.Rollback()
		return err
	}

	err = NotifyTalkMq(ctx, req.TalkIdList, defines.CHATWORD_TALK_DELETE, info.UserId, 0, []int64{})
	if err != nil {
		tx.Rollback()
		return err
	}

	//删除es
	tx.Commit()
	for _, talkId := range req.TalkIdList {
		oplog.AddBaseLog(ctx, utils2.ToString(talkId), defines.RELATION_TYPE_TALK,
			defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_DELETE_TALK,
			req, int64(info.UserId),
			fmt.Sprintf("%v删除了话术，话术Id:%v", info.UserName, utils2.ToString(talkId)))

	}

	return nil
}
