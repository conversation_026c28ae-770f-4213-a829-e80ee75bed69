package chatword

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/servicebase"
	"assistantdeskgo/utils"
	"encoding/json"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"sort"
	"strconv"
	"strings"
)

const CACHAE_CHATWORD_USER_GROUP_LIST = "assistantdeskgo:chatword_user_group_list:%v"

func AssistantTalkList(ctx *gin.Context, req dtochatword.AssistantListTalkReq) (resp dtochatword.TalkListRsp, err error) {

	loginUser, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	groupIdList, err := accessGroupList(ctx, req.GroupId, loginUser.UserId)
	if err != nil {
		return
	}

	talkSortMap := map[int64]int{}
	talkIdList := []int64{}
	total := int64(0)

	if req.Keyword == "" && req.MsgContent == "" && req.CreateName == "" && req.GroupId <= 0 { //查数据库，实时查询
		queryDbGroupIdList := []int64{}
		for _, groupIdStr := range groupIdList {
			parseInt, _ := strconv.ParseInt(groupIdStr, 10, 64)
			if parseInt > 0 {
				queryDbGroupIdList = append(queryDbGroupIdList, parseInt)
			}
		}
		talkList, t, err := cw.ChatWordTalkRef.PageByGroupId(ctx, queryDbGroupIdList, req.CategoryId, req.Page, req.PageSize)
		if err != nil {
			return dtochatword.TalkListRsp{}, err
		}
		if len(talkList) == 0 {
			return resp, nil
		}
		total = t
		for index, talk := range talkList {
			talkIdList = append(talkIdList, talk.Id)
			talkSortMap[talk.Id] = index
		}
	} else {
		talkEsData, err := dataproxy.ChatWordTalkList(ctx, dataproxy.AssistantTalkListReq{
			GroupIdList: groupIdList,
			Keyword:     req.Keyword,
			Content:     req.MsgContent,
			CategoryId:  req.CategoryId,
			CreateName:  req.CreateName,
			Page:        req.Page,
			PageSize:    req.PageSize,
			Fields:      []string{"talk_id"},
		})
		if err != nil {
			return resp, err
		}
		if talkEsData == nil || talkEsData.Total == 0 {
			return resp, nil
		}
		total = talkEsData.Total

		for index, esData := range talkEsData.List {
			talkId, _ := strconv.ParseInt(esData.TalkId, 10, 64)
			if talkId == 0 {
				continue
			}
			talkIdList = append(talkIdList, talkId)
			talkSortMap[talkId] = index
		}
	}
	list, err := TalkListInfo(ctx, talkIdList, int64(loginUser.StaffUid))
	if err != nil {
		return
	}

	sort.Slice(list, func(i, j int) bool {
		return talkSortMap[list[i].TalkId]-talkSortMap[list[j].TalkId] < 0
	})

	resp = dtochatword.TalkListRsp{
		Total: total,
		List:  list,
	}
	return
}

func accessGroupList(ctx *gin.Context, groupId int64, userId int) (groupIdList []string, err error) {
	if groupId <= 0 {
		//没传，超管查所有，非超管查自己的
		if !IsChatWordSupper(ctx) {
			return getQueryGroupIdList(ctx, int64(userId))
		}
	} else {
		accessGroupIdList, err := getQueryGroupIdList(ctx, int64(userId))
		if err != nil {
			return groupIdList, err
		}

		if !IsChatWordSupper(ctx) && !utils.InArrayString(utils2.ToString(groupId), accessGroupIdList) {
			return groupIdList, base.Error{components.ErrorParamInvalid.ErrNo, "无该组织的查询权限"}
		}
		groupIdList, err = getGroupListByGroupId(ctx, groupId)
		if err != nil {
			return groupIdList, err
		}
	}
	return
}

func WeworkTalkList(ctx *gin.Context, req dtochatword.WeworkListTalkReq) (resp dtochatword.TalkSearchRsp, err error) {

	loginUser, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return
	}
	queryCollectPersonUid := ""
	if req.Collect == 1 {
		queryCollectPersonUid = utils2.ToString(loginUser.UserId)
	}

	groupIdList := []string{}
	if !IsWecomChatWordSupper(ctx) {
		groupIdList, err = getQueryGroupIdList(ctx, int64(loginUser.UserId))
		if err != nil {
			return
		}

	}

	talkEsData, err := dataproxy.ChatWordTalkSearch(ctx, dataproxy.ChatWordTalkSearchReq{
		GroupIdList: groupIdList,
		Content:     req.SearchContent,
		PersonUid:   utils2.ToString(queryCollectPersonUid),
		Page:        req.Page,
		PageSize:    req.PageSize,
		CategoryId:  req.CategoryId,
		Fields:      []string{"talk_id"},
	})
	if err != nil {
		return
	}
	if talkEsData == nil || talkEsData.Total == 0 {
		return
	}
	talkIdList := []int64{}
	talkMap := map[int64]map[string][]string{}
	talkSortMap := map[int64]int{}
	for index, esData := range talkEsData.List {
		talkId, _ := strconv.ParseInt(esData.TalkId, 10, 64)
		if talkId == 0 {
			continue
		}
		talkIdList = append(talkIdList, talkId)
		talkMap[talkId] = esData.HighLight
		talkSortMap[talkId] = index
	}

	talkList, err := TalkListInfo(ctx, talkIdList, int64(loginUser.UserId))
	if err != nil {
		return
	}

	sort.Slice(talkList, func(i, j int) bool {
		return talkSortMap[talkList[i].TalkId]-talkSortMap[talkList[j].TalkId] < 0
	})

	talkListData := []dtochatword.TalkDetailHighLightRsp{}
	for index := range talkList {
		esTalkInfo := talkMap[talkList[index].TalkId]
		talkListData = append(talkListData, dtochatword.TalkDetailHighLightRsp{
			TalkDetailRsp:       talkList[index],
			HighLightContent:    esTalkInfo["content"],
			HighLightTitle:      esTalkInfo["title"],
			HighLightKeyword:    esTalkInfo["keyword"],
			HighLightMesContent: esTalkInfo["msg_content"],
		})

	}
	resp = dtochatword.TalkSearchRsp{
		Total: talkEsData.Total,
		List:  talkListData,
	}
	return
}

func buildComposeMessageList(ctx *gin.Context, subTalkMessage []cw.Message) ([]dtochatword.TalkComposeRsp, error) {
	sort.Slice(subTalkMessage, func(i, j int) bool {
		if subTalkMessage[i].ComposeId != subTalkMessage[j].ComposeId {
			return subTalkMessage[j].ComposeId > subTalkMessage[j].ComposeId
		}
		return subTalkMessage[j].Order > subTalkMessage[j].Order
	})
	var composeMessageList []dtochatword.TalkComposeMessageRsp
	var composeRespList []dtochatword.TalkComposeRsp
	composeId := subTalkMessage[0].ComposeId
	for indexMessage := range subTalkMessage {
		talkMessage := subTalkMessage[indexMessage]
		contentStruct, _err := servicebase.GetJsonStructByMessageType(ctx, talkMessage.MessageType, talkMessage.Content)
		if _err != nil {
			return nil, _err
		}
		taskMessageData := dtochatword.TalkComposeMessageRsp{
			Type:         talkMessage.MessageType,
			Content:      contentStruct,
			IntervalTime: talkMessage.IntervalTime,
			Order:        talkMessage.Order,
			MessageId:    talkMessage.Id,
		}

		if composeId != talkMessage.ComposeId {
			//说明这话术组合已经全部组合完了
			composeRespList = append(composeRespList, dtochatword.TalkComposeRsp{
				ComposeId:   composeId,
				MessageList: append([]dtochatword.TalkComposeMessageRsp{}, composeMessageList...),
			})
			composeId = talkMessage.ComposeId
			composeMessageList = []dtochatword.TalkComposeMessageRsp{}
		}

		composeMessageList = append(composeMessageList, taskMessageData)

	}
	if len(composeMessageList) > 0 {
		composeRespList = append(composeRespList, dtochatword.TalkComposeRsp{
			ComposeId:   composeId,
			MessageList: append([]dtochatword.TalkComposeMessageRsp{}, composeMessageList...),
		})

	}
	return composeRespList, nil
}

func getQueryGroupIdList(ctx *gin.Context, userId int64) (groupList []string, err error) {
	cacheKey := fmt.Sprintf(CACHAE_CHATWORD_USER_GROUP_LIST, userId)
	if data, _ := helpers.RedisClient.Get(ctx, cacheKey); err == nil {
		if len(data) > 0 {
			jsonErr := json.Unmarshal(data, &groupList)
			if jsonErr == nil && len(groupList) > 0 {
				return
			}

		}
	}
	groupList, err = getPersonGroupTreeList(ctx, userId)
	if err != nil {
		return
	}
	cacheData, _ := json.Marshal(groupList)
	helpers.RedisClient.Set(ctx, cacheKey, cacheData, 15*defines.SECOND_OF_MINUTE)
	return
}

func getGroupListByGroupId(ctx *gin.Context, groupId int64) (groupList []string, err error) {
	groupList = append(groupList, utils2.ToString(groupId))
	childGroupList, err := mesh.GetGroupAllChildren(ctx, groupId)
	if err != nil {
		return
	}
	groupList = append(groupList, GetChildGroupId(childGroupList.Children)...)
	return
}

func getPersonGroupTreeList(ctx *gin.Context, userId int64) (groupList []string, err error) {
	positions, err := mesh.GetStaffGroupPositions(ctx, userId)
	if err != nil {
		return
	}
	positionGroupIdList := []int64{}
	for index := range positions {
		positionGroupIdList = append(positionGroupIdList, positions[index].GroupId)
		groupList = append(groupList, utils2.ToString(positions[index].GroupId))
		childGroupList, err := mesh.GetGroupAllChildren(ctx, positions[index].GroupId)
		if err != nil {
			return nil, err
		}
		groupList = append(groupList, GetChildGroupId(childGroupList.Children)...)
	}
	groupMap, err := mesh.GetGroupDetailByIds(ctx, positionGroupIdList)
	if err != nil {
		return
	}

	for _, groupDetail := range groupMap {
		for _, groupLevelId := range strings.Split(groupDetail.LevelStr, ",") {
			groupList = append(groupList, groupLevelId)
		}
	}
	groupList = utils.FilterStrDuplicates(groupList)
	return
}

func GetChildGroupId(childrenList []mesh.GroupTree) []string {
	result := []string{}
	if len(childrenList) > 0 {
		for _, children := range childrenList {
			result = append(result, utils2.ToString(children.Id))
			if len(children.Children) > 0 {
				result = append(result, GetChildGroupId(children.Children)...)
			}
		}
	}
	return result
}
