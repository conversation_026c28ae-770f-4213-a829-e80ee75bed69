package chatword

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/defines"
	"assistantdeskgo/middleware"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func IsChatWordSupper(ctx *gin.Context) bool {
	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "IsChatWordSupper login fail, err:%v", err)
		return false
	}

	accessList, err := userprofile.GetEditPermissionList(ctx, info.UserId)
	if err != nil {
		zlog.Warnf(ctx, "IsChatWordSupper get access fail, err:%v", err)
		return false
	}
	if !utils.InArrayString(defines.ACCESS_CHATWORD_CATEGORY, accessList) {
		return false
	}
	return true
}

func IsWecomChatWordSupper(ctx *gin.Context) bool {
	info, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		zlog.Warnf(ctx, "IsChatWordSupper login fail, err:%v", err)
		return false
	}

	accessList, err := userprofile.GetEditPermissionList(ctx, info.UserId)
	if err != nil {
		zlog.Warnf(ctx, "IsChatWordSupper get access fail, err:%v", err)
		return false
	}
	if !utils.InArrayString(defines.ACCESS_CHATWORD_CATEGORY, accessList) {
		return false
	}
	return true
}
