package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/oplog"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"time"
)

const TYPE_HIDE = 1
const TYPE_RESTORE = 0

var hideOpMap = map[int64]string{
	TYPE_HIDE:    defines.CHATWORD_TALK_HIDE,
	TYPE_RESTORE: defines.CHATWORD_TALK_RESTORE,
}

func TalkHide(ctx *gin.Context, req dtochatword.HideTalkReq) (err error) {

	if len(req.TalkIdList) <= 0 {
		return components.ErrorParamInvalid
	}
	op, ok := hideOpMap[req.Type]
	if !ok {
		return components.ErrorParamInvalid
	}

	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}

	redisLock := fmt.Sprintf(REDIS_LOCK_TALK, info.UserId)
	retry, err := utils.LockRetry(ctx, redisLock, "1", 1, 1)
	if err != nil {
		return err
	}
	if !retry {
		return base.Error{components.ErrorRedisSet.ErrNo, "1秒钟仅能操作一次"}
	}

	talkList, err := cw.ChatWordTalkRef.ListByIdList(ctx, req.TalkIdList)
	if err != nil {
		return err
	}
	if len(talkList) != len(req.TalkIdList) {
		return base.Error{components.ErrorParamInvalid.ErrNo, "话术不存在"}
	}

	accessGroupId, err := getQueryGroupIdList(ctx, int64(info.UserId))
	if err != nil {
		return err
	}
	for _, talk := range talkList {
		if !utils.InArrayString(utils2.ToString(talk.GroupId), accessGroupId) && !IsChatWordSupper(ctx) {
			return base.Error{components.ErrorParamInvalid.ErrNo, "无话术Id:" + utils2.ToString(talk.Id) + "隐藏权限"}
		}
	}

	now := time.Now().Unix()
	talkIdList := []int64{}
	tx := helpers.MysqlClientFuDao.Begin()
	for index := range talkList {

		if talkList[index].Hide == req.Type {
			continue
		}
		talkIdList = append(talkIdList, talkList[index].Id)
		talkList[index].UpdateTime = now
		talkList[index].Hide = req.Type
		talkList[index].UpdateUid = int64(info.UserId)
		talkList[index].UpdateName = info.Name
		err = cw.ChatWordTalkRef.Update(ctx, talkList[index], tx)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	if len(talkList) > 0 {
		err = NotifyTalkMq(ctx, talkIdList, op, info.UserId, 0, []int64{})
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()

	for _, talkId := range req.TalkIdList {

		template := "%v取消隐藏了话术，话术Id:%v"
		if req.Type == 1 {
			template = "%v隐藏了话术，话术Id:%v"
		}

		oplog.AddBaseLog(ctx, utils2.ToString(talkId), defines.RELATION_TYPE_TALK,
			defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_HIDE_CATEGORY,
			req, int64(info.UserId),
			fmt.Sprintf(template, info.UserName, utils2.ToString(talkId)))
	}

	return nil
}
