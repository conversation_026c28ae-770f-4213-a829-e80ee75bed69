package chatword

import (
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/backend/message"
	"assistantdeskgo/service/oplog"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
)

const REDIS_LOCK_TO_MESSAGE_GROUP = "assistantdeskgo:chatword:to_message_group:%v"

func ToMessageGroup(ctx *gin.Context, req dtochatword.ToMessageGroupReq) error {
	userInfo, err := middleware.GetWecomHelperDeviceInfo(ctx)
	if err != nil {
		return err
	}
	if req.TalkId <= 0 {
		return components.ErrorParamInvalid
	}
	redisLock := fmt.Sprintf(REDIS_LOCK_TO_MESSAGE_GROUP, userInfo.UserId)
	retry, err := utils.LockRetry(ctx, redisLock, "1", 1, 1)
	if err != nil {
		return err
	}
	if !retry {
		return base.Error{components.ErrorRedisSet.ErrNo, "1秒钟仅能操作一次"}
	}

	//设置登录
	ctx.Set("userInfo", &userprofile.UserInfo{
		UserName: userInfo.UserName,
		UserId:   userInfo.UserId,
	})

	if req.FolderId <= 0 {
		//创建文件夹
		folder, err := message.SaveFolder(ctx, dtomessage.SaveFolderReq{FolderName: req.FolderName})
		if err != nil {
			return err
		}
		req.FolderId = folder.FolderId
	}

	talkDetailList, err := TalkListInfo(ctx, []int64{req.TalkId}, int64(userInfo.UserId))
	if err != nil || len(talkDetailList) == 0 {
		return err
	}
	talkDetail := utils.ToMap(talkDetailList, "TalkId")[req.TalkId]
	if talkDetail.TalkId != req.TalkId {
		return base.Error{components.ErrorParamInvalid.ErrNo, "话术不存在"}
	}
	composeMessageList := []dtochatword.TalkComposeMessageRsp{}
	for index := range talkDetail.ComposeList {
		if talkDetail.ComposeList[index].ComposeId == req.ComposeId {
			composeMessageList = talkDetail.ComposeList[index].MessageList
			break
		}
	}
	if len(composeMessageList) == 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "话术组合不存在"}
	}
	if len(composeMessageList) > 20 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "消息组内消息个数不能超过20"}
	}

	messageList, err := toMessage(ctx, composeMessageList)
	if err != nil {
		return err
	}

	saveGroupReq := dtomessage.SaveMessageGroupReq{
		Type:           0,
		FolderId:       req.FolderId,
		GroupName:      req.GroupName,
		GroupDesc:      req.GroupDesc,
		GroupAuthority: 0,
		MessageList:    messageList,
	}

	rsp, err := message.SaveMessageGroup(ctx, saveGroupReq)
	if err != nil {
		return err
	}
	zlog.Infof(ctx, "ToMessageGroup result:%+v", rsp)
	if rsp.Success == 0 {

		oplog.AddBaseLog(ctx, utils2.ToString(req.TalkId), defines.RELATION_TYPE_TALK,
			defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_TO_MESSAGE_GROUP,
			req, int64(userInfo.UserId),
			fmt.Sprintf("%v将话术Id:%v组合%v转存到了消息组", userInfo.UserName, req.TalkId, req.ComposeId))

		return nil
	}

	return errors.New(rsp.Message)
}

func toMessage(ctx *gin.Context, list []dtochatword.TalkComposeMessageRsp) (resp []dtomessage.Message, err error) {
	for index := range list {
		resp = append(resp, dtomessage.Message{
			IntervalTime: list[index].IntervalTime,
			Content:      list[index].Content,
			Type:         list[index].Type,
		})
	}
	return
}
