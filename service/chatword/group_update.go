package chatword

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/oplog"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"time"
)

func GroupUpdate(ctx *gin.Context, req dtochatword.UpdateGroupReq) error {
	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return err
	}
	redisLock := fmt.Sprintf(REDIS_LOCK_TALK, info.UserId)
	retry, err := utils.LockRetry(ctx, redisLock, "1", 1, 1)
	if err != nil {
		return err
	}
	if !retry {
		return base.Error{components.ErrorRedisSet.ErrNo, "1秒钟仅能操作一次"}
	}

	talkList, err := cw.ChatWordTalkRef.ListByIdList(ctx, req.TalkIdList)
	if err != nil {
		return err
	}
	if len(talkList) != len(req.TalkIdList) {
		return base.Error{components.ErrorParamInvalid.ErrNo, "有话术不存"}
	}

	talkMap := utils.ToMap(talkList, "Id")
	if !IsChatWordSupper(ctx) {
		accessGroupId, err := getQueryGroupIdList(ctx, int64(info.UserId))
		if err != nil {
			return err
		}
		for _, talk := range talkList {
			talkGroupIdList, _err := utils.StringToInt64Slice(ctx, talk.GroupIdList)
			if _err != nil {
				return _err
			}
			if talk.GroupId > 0 {
				talkGroupIdList = utils.FilterInt64Duplicates(append(talkGroupIdList, talk.GroupId))
			}

			for _, talkGroupId := range talkGroupIdList {
				for _, reqGroupId := range req.GroupIdList {
					if !utils.InArrayString(utils2.ToString(talkGroupId), accessGroupId) ||
						!utils.InArrayString(utils2.ToString(reqGroupId), accessGroupId) {
						return base.Error{components.ErrorParamInvalid.ErrNo, "无话术Id:" + utils2.ToString(talk.Id) + "更新权限"}
					}

				}
			}

		}
	}

	groupDetailLst, err := mesh.GetGroupDetailByIds(ctx, req.GroupIdList)
	if err != nil {
		return err
	}
	if len(groupDetailLst) != len(req.GroupIdList) {
		return base.Error{components.ErrorParamInvalid.ErrNo, "组织架构不存在"}
	}
	now := time.Now().Unix()
	tx := helpers.MysqlClientFuDao.Begin()
	for index := range talkList {
		talkList[index].GroupId = req.GroupId
		talkList[index].GroupIdList = utils.Int64ArrayToString(req.GroupIdList)
		talkList[index].UpdateTime = now
		talkList[index].UpdateUid = int64(info.UserId)
		talkList[index].UpdateName = info.Name
		err := cw.ChatWordTalkRef.Update(ctx, talkList[index], tx)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	err = NotifyTalkMq(ctx, req.TalkIdList, defines.CHATWORD_TALK_UPDATE_GROUP, info.UserId, req.GroupId, req.GroupIdList)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	for _, talkId := range req.TalkIdList {
		oplog.AddBaseLog(ctx, utils2.ToString(talkId), defines.RELATION_TYPE_TALK,
			defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_CHANGE_GROUP,
			req, int64(info.UserId),
			fmt.Sprintf("%v更新了话术Id:%v的组织从%v->%v", info.UserName, utils2.ToString(talkId), talkMap[talkId].GroupId, req.GroupId))

	}
	return err
}
