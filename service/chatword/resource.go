package chatword

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/servicebase"
	"assistantdeskgo/utils"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
	"time"
)

func ListResource(ctx *gin.Context) (response []dtochatword.ListResourceRsp, err error) {
	userInfo, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return
	}
	groupIdList := []string{}
	if !IsWecomChatWordSupper(ctx) {
		groupIdList, err = getQueryGroupIdList(ctx, int64(userInfo.UserId))
		if err != nil {
			return nil, err
		}
	}
	talkEsData, err := dataproxy.ChatWordTalkSearch(ctx, dataproxy.ChatWordTalkSearchReq{
		GroupIdList: groupIdList,
		Page:        defines.PAGE_START,
		PageSize:    defines.ES_DEFAULT_SIZE,
		Resource:    defines.CHATWORD_MESSAGE_RESOURCE,
		Fields:      []string{"talk_id"},
	})
	if err != nil {
		return nil, err
	}
	if len(talkEsData.List) == 0 {
		return nil, nil
	}
	talkIdList := []int64{}
	for _, esData := range talkEsData.List {
		talkId, _ := strconv.ParseInt(esData.TalkId, 10, 64)
		if talkId == 0 {
			continue
		}
		talkIdList = append(talkIdList, talkId)
	}
	distinct := map[string]bool{}
	for _, subTalkIdList := range utils.SplitInt64Array(talkIdList, 500) {
		talkMessageList, err := cw.ChatWordMessageRef.ListByTalkIdResource(ctx, subTalkIdList)
		if err != nil {
			return nil, err
		}
		if len(talkMessageList) > 0 {
			for _, talkMessage := range talkMessageList {
				if talkMessage.MessageType == defines.MessageTypeFile ||
					talkMessage.MessageType == defines.MessageTypeVoice ||
					talkMessage.MessageType == defines.MessageTypePicture ||
					talkMessage.MessageType == defines.MessageTypeVideo {
					baseMessage, check := servicebase.GetMessageContent(talkMessage.MessageType, talkMessage.Content)
					if check != nil {
						continue
					}
					if check = baseMessage.Check(models.DefaultMsgCheckParams); check != nil {
						continue
					}
					for _, baseResource := range getResource(ctx, baseMessage) {
						if distinct[baseResource.Name] {
							//去重复
							continue
						}
						response = append(response, baseResource)
						distinct[baseResource.Name] = true
					}
				}
			}
		}
	}
	for index := range response {
		if response[index].Md5 == "" {
			//兜底查下缓存
			md5, err := servicebase.GetMd5(ctx, response[index].URL)
			if err != nil {
				continue
			}
			response[index].Md5 = md5
		}
	}

	return

}

func getResource(ctx *gin.Context, baseMessage models.BaseMessage) (response []dtochatword.ListResourceRsp) {
	if baseMessage.GetMsgType() == defines.MessageTypePicture {
		for _, item := range baseMessage.GetItemList() {
			img := item.(models.ImgInfo)
			if len(strings.Split(img.Name, ".")) != 2 {
				continue
			}
			response = append(response, dtochatword.ListResourceRsp{
				URL:    img.Url,
				Md5:    img.Md5,
				Suffix: strings.Split(img.Name, ".")[1],
				Name:   img.Name,
			})
		}
	}

	if baseMessage.GetMsgType() == defines.MessageTypeVoice {
		for _, item := range baseMessage.GetItemList() {
			voice := item.(models.VoiceInfo)
			if len(strings.Split(voice.VoiceName, ".")) != 2 {
				continue
			}
			url, downloadErr := helpers.BaiduBucket2.GetUrlByFileName(ctx, voice.VoiceName, 24*60*time.Second)
			if downloadErr != nil {
				continue
			}
			response = append(response, dtochatword.ListResourceRsp{
				URL:    url,
				Md5:    voice.Md5,
				Suffix: strings.Split(voice.VoiceName, ".")[1],
				Name:   voice.VoiceName,
			})
		}
	}

	if baseMessage.GetMsgType() == defines.MessageTypeFile {
		for _, item := range baseMessage.GetItemList() {
			file := item.(models.FileInfo)
			if len(strings.Split(file.FileName, ".")) != 2 {
				continue
			}
			url, downloadErr := helpers.BaiduBucket2.GetUrlByFileName(ctx, file.FileName, 24*60*time.Second)
			if downloadErr != nil {
				continue
			}
			response = append(response, dtochatword.ListResourceRsp{
				URL:      url,
				Md5:      file.Md5,
				Suffix:   strings.Split(file.FileName, ".")[1],
				Name:     file.FileName,
				FileName: file.Ori,
			})
		}
	}

	if baseMessage.GetMsgType() == defines.MessageTypeVideo {
		for _, item := range baseMessage.GetItemList() {
			video := item.(models.VideoInfo)
			if len(strings.Split(video.Name, ".")) != 2 {
				continue
			}
			url, downloadErr := helpers.BaiduBucket2.GetUrlByFileName(ctx, video.Name, 24*60*time.Second)
			if downloadErr != nil {
				continue
			}
			response = append(response, dtochatword.ListResourceRsp{
				URL:    url,
				Md5:    video.Md5,
				Suffix: strings.Split(video.Name, ".")[1],
				Name:   video.Name,
			})
		}
	}
	return response
}
