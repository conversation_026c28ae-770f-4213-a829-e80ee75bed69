package chatword

import (
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func NotifyTalkMq(ctx *gin.Context, talkIdList []int64, op string, personUid int, groupId int64, groupIdList []int64) error {
	data := map[string]interface{}{
		"talkIdList":  talkIdList,
		"op":          op,
		"personUid":   personUid,
		"groupId":     groupId,
		"groupIdList": groupIdList,
	}

	//插es
	res, err := rmq.SendCmd(ctx, "self_product", 214203, "core", "zb", data, "")
	if err != nil {
		zlog.Warnf(ctx, "NotifyChatWordTalkMq err, data:%+v,msgId:%+v,err:%v", data, res, err)
	}
	return err
}
