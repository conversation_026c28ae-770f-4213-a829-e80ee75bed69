package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/service/oplog"
	"assistantdeskgo/service/servicebase"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"strings"
	"time"
	"unicode/utf8"
)

const REDIS_LOCK_TALK = "assistantdeskgo:chatword:talk_operate:%v"

func TalkCreate(ctx *gin.Context, req dtochatword.CreateTalkReq) (err error) {

	err = checkCreateTalkReq(ctx, req)
	if err != nil {
		return
	}

	loginUser, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}
	redisLock := fmt.Sprintf(REDIS_LOCK_TALK, loginUser.UserId)
	retry, err := utils.LockRetry(ctx, redisLock, "1", 1, 1)
	if err != nil {
		return err
	}
	if !retry {
		return base.Error{components.ErrorRedisSet.ErrNo, "1秒钟内仅能转存一次"}
	}
	if !IsChatWordSupper(ctx) {
		accessGroupId, err := getQueryGroupIdList(ctx, int64(loginUser.UserId))
		if err != nil {
			return err
		}
		for _, groupId := range req.GroupIdList {
			if !utils.InArrayString(utils2.ToString(groupId), accessGroupId) {
				return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("无该组织ID:%v权限", groupId)}
			}
		}

	}

	now := time.Now().Unix()

	tx := helpers.MysqlClientFuDao.WithContext(ctx).Begin()
	talk := &cw.Talk{
		CategoryId:  req.CategoryId,
		Title:       strings.Trim(req.Title, " "),
		Content:     strings.Trim(req.Content, " "),
		GroupId:     req.GroupId,
		GroupIdList: utils.Int64ArrayToString(req.GroupIdList),
		Keyword:     strings.Join(utils.FilterStrDuplicates(req.Keyword), ","),
		PersonUid:   int64(loginUser.UserId),
		UpdateUid:   int64(loginUser.UserId),
		PersonName:  loginUser.Name,
		UpdateName:  loginUser.Name,
		CreateTime:  now,
		UpdateTime:  now,
	}

	err = cw.ChatWordTalkRef.Insert(ctx, talk, tx)
	if err != nil {
		tx.Rollback()
		return
	}

	var msgList []cw.Message
	composeId := int64(1)
	for _, compose := range req.ComposeList {
		order := int64(1)
		for _, message := range compose.MessageList {
			content, _, err := servicebase.GetJsonContentByMessageType(ctx, message.Type, message.Content)
			if err != nil {
				return err
			}
			msgList = append(msgList, cw.Message{
				MessageType:  message.Type,
				Content:      content,
				IntervalTime: message.IntervalTime,
				Order:        order,
				CreateTime:   now,
				UpdateTime:   now,
				TalkId:       talk.Id,
				ComposeId:    composeId,
			})
			order++
		}
		composeId++
	}

	err = cw.ChatWordMessageRef.BatchInsert(ctx, msgList, tx)
	if err != nil {
		tx.Rollback()
		return
	}

	err = NotifyTalkMq(ctx, []int64{talk.Id}, defines.CHATWORD_TALK_CREATE, loginUser.UserId, 0, []int64{})
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()

	oplog.AddBaseLog(ctx, utils2.ToString(talk.Id), defines.RELATION_TYPE_TALK,
		defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_ADD_TALK,
		req, int64(loginUser.UserId),
		fmt.Sprintf("%v创建了话术，话术Id:%v", loginUser.UserName, utils2.ToString(talk.Id)))

	return
}

func checkCreateTalkReq(ctx *gin.Context, req dtochatword.CreateTalkReq) error {

	if req.CategoryId <= 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "分类为空"}
	}
	if len(req.ComposeList) <= 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "组合列表为空"}
	}
	if strings.Trim(req.Content, " ") == "" {
		return base.Error{components.ErrorParamInvalid.ErrNo, "内容不能为空"}
	}

	if strings.Trim(req.Title, " ") == "" {
		return base.Error{components.ErrorParamInvalid.ErrNo, "标题不能为空"}
	}
	if utf8.RuneCountInString(strings.Trim(req.Title, " ")) > 100 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "标题超长"}
	}

	if len(req.Keyword) == 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "关键词不能为空"}
	}

	if len(req.Keyword) >= 20 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "关键词不能超过20个"}
	}
	for _, word := range req.Keyword {
		if utf8.RuneCountInString(word) > 6 {
			return base.Error{components.ErrorParamInvalid.ErrNo, "关键词长度不能超过6"}
		}
	}
	if req.GroupId == 0 && len(req.GroupIdList) == 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "组织架构为空"}
	}
	if req.GroupId > 0 {
		req.GroupIdList = utils.FilterDuplicatesInt64(append(req.GroupIdList, req.GroupId))
	}
	if len(req.GroupIdList) > defines.CHATWORD_MAX_GROUP_NUM {
		return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("话术绑定组织架构超过最大数量，最大限制数量为:%v", defines.CHATWORD_MAX_GROUP_NUM)}
	}

	category, err := cw.ChatWordCategroyRef.GetById(ctx, req.CategoryId)
	if err != nil {
		return err
	}
	if category.Id != req.CategoryId {
		return base.Error{components.ErrorParamInvalid.ErrNo, "分类错误"}
	}
	for composeIndex, compose := range req.ComposeList {
		if len(compose.MessageList) == 0 {
			return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v内容为空", composeIndex+1)}
		}
		for messageIndex, message := range compose.MessageList {
			if message.Content == nil {
				return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v,第%v条消息内容为空", composeIndex+1, messageIndex+1)}
			}
			if !supportMsgType[message.Type] {
				return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v,第%v条消息内容不支持", composeIndex+1, messageIndex+1)}
			}
			jsonContent, _, err := servicebase.GetJsonContentByMessageType(ctx, message.Type, message.Content)
			if err != nil {
				return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v,第%v条消息格式内容不正确:%v", composeIndex+1, messageIndex+1, err.Error())}
			}
			if message.Type != defines.MessageTypeWord {
				baseMessage, err := servicebase.GetMessageContent(message.Type, jsonContent)
				if err != nil {
					return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v,第%v条消息格式内容不正确:%v", composeIndex+1, messageIndex+1, err.Error())}
				}
				if len(baseMessage.GetItemList()) > 1 {
					return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v,第%v条消息仅限制上传一个", composeIndex+1, messageIndex+1)}
				}
			}
			if messageIndex > 0 && message.IntervalTime < 2 { //校验间隔时间
				return base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("组合%v,第%v条消息间隔时间必须大于等于2秒", composeIndex+1, messageIndex+1)}
			}

		}
	}
	return nil
}
