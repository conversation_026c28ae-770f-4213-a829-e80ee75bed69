package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	FEEDBACK_TYPE_LIKE           = 0 //赞
	FEEDBACK_TYPE_DIS_LIKE       = 1 //踩
	FEEDBACK_TYPE_Like_CANCEL    = 2 //踩
	FEEDBACK_TYPE_COLLECT        = 3 //收藏
	FEEDBACK_TYPE_COLLECT_CANCEL = 4 //取消收藏
	FEEDBACK_TYPE_SEND           = 5 //发送
	FEEDBACK_TYPE_EDIT_SEND      = 6 //编辑后发送
	FEEDBACK_TYPE_MOVE           = 7 //移植消息组
	FEEDBACK_TYPE_CPOY           = 8 //复制

)

var feedbackTypeMap = map[int64]string{
	FEEDBACK_TYPE_LIKE:           "赞",
	FEEDBACK_TYPE_DIS_LIKE:       "踩",
	FEEDBACK_TYPE_Like_CANCEL:    "取消赞踩",
	FEEDBACK_TYPE_COLLECT:        "收藏",
	FEEDBACK_TYPE_COLLECT_CANCEL: "取消收藏",
	FEEDBACK_TYPE_SEND:           "发送",
	FEEDBACK_TYPE_EDIT_SEND:      "编辑后发送",
	FEEDBACK_TYPE_MOVE:           "转存消息组",
	FEEDBACK_TYPE_CPOY:           "复制",
}

func Feedback(ctx *gin.Context, req dtochatword.FeedBackReq) (err error) {

	if feedbackTypeMap[req.Type] == "" {
		return base.Error{components.ErrorParamInvalid.ErrNo, "不支持的类型"}
	}
	if req.TalkId <= 0 {
		return base.Error{components.ErrorParamInvalid.ErrNo, "话术id异常"}
	}

	loginUser, err := middleware.GetWecomHelperUser(ctx)
	if err != nil {
		return err
	}

	talkInfo, err := cw.ChatWordTalkRef.GetById(ctx, req.TalkId)
	if err != nil {
		return err
	}
	if talkInfo.Id != req.TalkId || talkInfo.Hide == cw.DeletedYes {
		return base.Error{components.ErrorParamInvalid.ErrNo, "话术不存在"}
	}
	zlog.Info(ctx, "ChatWordFeed:%+v", req)
	now := time.Now().Unix()
	feedBack, err := cw.ChatWordFeedBackRef.GetByTalkIdAndPersonUid(ctx, req.TalkId, int64(loginUser.UserId))
	if err != nil {
		return err
	}
	if feedBack.Id == 0 {
		feedBack.TalkId = req.TalkId
		feedBack.PersonUid = int64(loginUser.UserId)
		feedBack.CreateTime = now
		feedBack.UpdateTime = now
	}

	update := false
	OP := ""
	switch req.Type {
	case FEEDBACK_TYPE_LIKE:
		if feedBack.Like == 1 {
			//已经点过
			return nil
		}

		if feedBack.Like == 2 { //原来是踩
			talkInfo.DislikeCount--
		}

		feedBack.Like = 1
		talkInfo.LikeCount++
		update = true
	case FEEDBACK_TYPE_DIS_LIKE:
		if feedBack.Like == 2 {
			//已经点过
			return nil
		}

		if feedBack.Like == 1 { //原来是踩
			talkInfo.LikeCount--
		}
		talkInfo.DislikeCount++
		feedBack.Like = 2
		update = true
	case FEEDBACK_TYPE_Like_CANCEL:
		if feedBack.Like == 0 {
			return nil
		}
		if feedBack.Like == 2 {
			talkInfo.DislikeCount--
		}
		if feedBack.Like == 1 {
			talkInfo.LikeCount--
		}
		feedBack.Like = 0
		update = true
	case FEEDBACK_TYPE_COLLECT:
		if feedBack.Collect == 1 {
			return nil
		}
		feedBack.Collect = 1
		talkInfo.CollectCount++
		update = true
		OP = defines.CHATWORD_TALK_COLLECT
	case FEEDBACK_TYPE_COLLECT_CANCEL:
		if feedBack.Collect != 1 {
			return
		}
		talkInfo.CollectCount--
		feedBack.Collect = 2
		update = true
		OP = defines.CHATWORD_TALK_COLLECT_CANCEL
	case FEEDBACK_TYPE_SEND:
		feedBack.SendCount += 1
		talkInfo.SendCount++
		update = true
	case FEEDBACK_TYPE_EDIT_SEND:
		feedBack.EditSendCount += 1
		talkInfo.EditSendCount += 1
		update = true
	case FEEDBACK_TYPE_MOVE:
		talkInfo.TransferGroupCount++
		feedBack.TransferGroupCount++
		update = true
	}
	if update {
		tx := helpers.MysqlClientFuDao.Begin()
		talkInfo.UpdateTime = now
		feedBack.UpdateTime = now
		if talkInfo.LikeCount < 0 {
			talkInfo.LikeCount = 0
		}
		if talkInfo.DislikeCount < 0 {
			talkInfo.DislikeCount = 0
		}
		if talkInfo.CollectCount < 0 {
			talkInfo.CollectCount = 0
		}

		err = cw.ChatWordTalkRef.Update(ctx, talkInfo, tx)
		if err != nil {
			tx.Rollback()
			return err
		}
		err = cw.ChatWordFeedBackRef.Save(ctx, feedBack)
		if err != nil {
			tx.Rollback()
			return err
		}
		if OP != "" {
			err := NotifyTalkMq(ctx, []int64{req.TalkId}, OP, loginUser.UserId, 0, []int64{})
			if err != nil {
				tx.Rollback()
				return err
			}
		}

		tx.Commit()
	}

	//oplog.AddBaseLog(ctx, utils2.ToString(req.TalkId), defines.RELATION_TYPE_TALK,
	//	defines.REFER, defines.MODULE_CHATWORD, defines.SERVICE_ATWORD_FEEDBACK,
	//	req, int64(loginUser.UserId),
	//	fmt.Sprintf("%v对话术id:%v做如下动作:%v", loginUser.UserName, req.TalkId, feedbackTypeMap[req.Type]))
	return nil
}
