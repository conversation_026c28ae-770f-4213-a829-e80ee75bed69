package chatword

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/models/chatword"
	"assistantdeskgo/utils"
	"errors"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"time"
)

const REDIS_KEY_FLUSH_DATA = "assistantdeskgo:chatword_flush_data_group"

func FlushGroup(ctx *gin.Context) error {

	lock, err := utils.LockRetry(ctx, REDIS_KEY_FLUSH_DATA, "1", 60, 3)
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("抢锁失败")
	}

	allTalk, err := chatword.ChatWordTalkRef.ListAll(ctx)
	if err != nil {
		return err
	}
	now := time.Now().Unix()
	for _, talk := range allTalk {
		talkGroupIdList, _ := utils.StringToInt64Slice(ctx, talk.GroupIdList)
		if len(talkGroupIdList) == 0 {
			talk.GroupIdList = utils.Int64ArrayToString([]int64{talk.GroupId})
			talk.UpdateTime = now
			err = chatword.ChatWordTalkRef.Update(ctx, talk, nil)
			if err != nil {
				return errors.New("更新DB失败，talkId=" + utils2.ToString(talk.Id))
			}
			err = NotifyTalkMq(ctx, []int64{talk.Id}, defines.CHATWORD_TALK_UPDATE_GROUP, int(talk.PersonUid), talk.GroupId, []int64{talk.GroupId})
			if err != nil {
				return errors.New("更新MQ失败，talkId=" + utils2.ToString(talk.Id))
			}
		}
	}

	return err
}
