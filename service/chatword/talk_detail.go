package chatword

import (
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/middleware"
	cw "assistantdeskgo/models/chatword"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"strconv"
	"strings"
)

func TalkDetail(ctx *gin.Context, req dtochatword.TalkDetailReq) (dtochatword.TalkDetailRsp, error) {

	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return dtochatword.TalkDetailRsp{}, err
	}

	detailList, err := TalkListInfo(ctx, []int64{req.TalkId}, int64(info.StaffUid))
	if err != nil {
		return dtochatword.TalkDetailRsp{}, err
	}
	rsp, ok := utils.ToMap(detailList, "TalkId")[req.TalkId]
	if ok {
		return rsp, nil
	}
	return rsp, base.Error{components.ErrorParamInvalid.ErrNo, "话术不存在"}
}

func TalkListDetail(ctx *gin.Context, req dtochatword.TalkDetailListReq) ([]dtochatword.TalkDetailRsp, error) {
	return TalkListInfo(ctx, req.TalkIdList, 0)
}

func TalkListInfo(ctx *gin.Context, talkIdList []int64, personUid int64) (resp []dtochatword.TalkDetailRsp, err error) {

	if len(talkIdList) == 0 {
		return
	}
	//查DB

	talkList, err := cw.ChatWordTalkRef.ListByIdList(ctx, talkIdList)
	if err != nil {
		return
	}
	talkMessageList, err := cw.ChatWordMessageRef.ListByTalkId(ctx, talkIdList)
	if err != nil {
		return
	}

	talkCategoryList, err := cw.ChatWordCategroyRef.ListByIdList(ctx, utils.FilterDuplicatesInt64(utils.Int64Collect(talkList, "CategoryId")))
	if err != nil {
		return
	}

	groupIdList := []int64{}
	for _, talk := range talkList {
		if talk.GroupId > 0 {
			groupIdList = append(groupIdList, talk.GroupId)
		}
		talkGroupIdList, _ := utils.StringToInt64Slice(ctx, talk.GroupIdList)
		if len(talkGroupIdList) > 0 {
			groupIdList = append(groupIdList, talkGroupIdList...)
		}

	}
	groupDetailMap, err := mesh.GetGroupDetailByIds(ctx, utils.FilterDuplicatesInt64(groupIdList))
	if err != nil {
		return nil, err
	}
	collectMap := map[int64]cw.FeedBack{}
	collectPersonMap := map[int64][]int64{}
	if personUid > 0 {
		feedbackList, err := cw.ChatWordFeedBackRef.ListByTalkIdAndPersonUid(ctx, talkIdList, personUid)
		if err != nil {
			return nil, err
		}
		for _, feedback := range feedbackList {
			collectMap[feedback.TalkId] = feedback
		}
	} else {
		//等于0说明是API接口，查询收藏的人
		feedbackList, err := cw.ChatWordFeedBackRef.ListByTalkIdList(ctx, talkIdList)
		if err != nil {
			return nil, err
		}
		for _, feedback := range feedbackList {
			collectPersonMap[feedback.TalkId] = append(collectPersonMap[feedback.TalkId], feedback.PersonUid)
		}
	}

	categoryMap := utils.ToMap(talkCategoryList, "Id")
	talkMessageMap := utils.GroupBy(talkMessageList, "TalkId")
	for indexTalk := range talkList {
		talk := talkList[indexTalk]
		subTalkMessage := talkMessageMap[talk.Id]
		if len(subTalkMessage) == 0 {
			continue
		}
		list, err := buildComposeMessageList(ctx, subTalkMessage)
		if err != nil {
			return nil, err
		}
		groupNameList := []string{}
		groupIdList, _ = utils.StringToInt64Slice(ctx, talk.GroupIdList)
		if talk.GroupId > 0 {
			groupIdList = utils.FilterInt64Duplicates(append(groupIdList, talk.GroupId))
		}
		for _, groupId := range groupIdList {
			groupNameList = append(groupNameList, groupDetailMap[utils2.ToString(groupId)].Name)
		}

		resp = append(resp, dtochatword.TalkDetailRsp{
			TalkId:               talk.Id,
			CategoryId:           talk.CategoryId,
			Hide:                 talk.Hide,
			CategoryName:         categoryMap[talk.CategoryId].Category,
			ComposeList:          list,
			Content:              talk.Content,
			Title:                talk.Title,
			GroupId:              talk.GroupId,
			GroupName:            groupDetailMap[strconv.FormatInt(talk.GroupId, 10)].Name,
			GroupIdList:          groupIdList,
			GroupNameList:        groupNameList,
			LikeCount:            talk.LikeCount,
			DislikeCount:         talk.DislikeCount,
			CollectCount:         talk.CollectCount,
			SendCount:            talk.SendCount,
			EditSendCount:        talk.EditSendCount,
			TransferGroupCount:   talk.TransferGroupCount,
			UpdateTime:           talk.UpdateTime,
			CreateTime:           talk.CreateTime,
			Keyword:              strings.Split(talk.Keyword, ","),
			PersonName:           talk.PersonName,
			PersonUid:            talk.PersonUid,
			UpdateUid:            talk.UpdateUid,
			UpdateName:           talk.UpdateName,
			CollectPersonUidList: collectPersonMap[talk.Id],
			Collect:              collectMap[talk.Id].Collect,
			Like:                 collectMap[talk.Id].Like,
		})
	}
	return
}
