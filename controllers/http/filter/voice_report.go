package filter

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtofilter"
	"assistantdeskgo/service/filter"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetVoiceReportUrl(ctx *gin.Context) {
	params := dtofilter.GetVoiceUrlReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:GetCourseAppId] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	if params.CourseId <= 0 || params.LessonId == 0 || params.StudentUid <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	url, err := filter.GetVoiceUrl(ctx, params.CourseId, params.LessonId, params.StudentUid)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, url)

}
