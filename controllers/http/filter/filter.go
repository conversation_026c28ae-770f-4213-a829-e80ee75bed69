package filter

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtofilter"
	"assistantdeskgo/service/filter"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func RemoteSelect(ctx *gin.Context) {
	params := dtofilter.GetRemoteSelectReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:GetCourseAppId] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	if params.CourseId <= 0 || len(params.Keys) == 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	remoteSelect, err := filter.RemoteSelect(ctx, params.CourseId, params.LessonId, params.Keys)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, remoteSelect)

}
