package wxmass

import (
	"assistantdeskgo/dto/dtowxmass"
	"assistantdeskgo/service/wxmass"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetGroupReceivers(ctx *gin.Context) {
	params := dtowxmass.GetGroupReceiversReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:GetGroupReceivers] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	receiverList, err := wxmass.GetGroupReceivers(ctx, params)
	if err != nil {
		zlog.Error(ctx, "[touchTask:GetGroupReceivers] search error: ", err.<PERSON>rror())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, receiverList)
	return
}
