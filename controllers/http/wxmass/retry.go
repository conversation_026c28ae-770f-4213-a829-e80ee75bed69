package wxmass

import (
	"assistantdeskgo/api/lpcmsg"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtowxmass"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func RetryMainTask(ctx *gin.Context) {
	params := dtowxmass.RetryMainTaskReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	// retry
	resp, err := lpcmsg.RetryMainTask(ctx, params.AssistantUid, params.MainTaskId)
	if err == nil && !resp.HasRetry {
		err = components.DefaultError(resp.Message)
	}

	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func RetryGroupTask(ctx *gin.Context) {
	params := dtowxmass.RetryGroupTaskReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	// retry
	resp, err := lpcmsg.RetryGroupTask(ctx, params.MainTaskId, params.GroupTaskId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
