package wxmass

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/dto/dtowxmass"
	"assistantdeskgo/service/wxmass"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func AppList(ctx *gin.Context) {
	params := dtowxmass.GetCourseAppIdReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:GetCourseAppId] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	//查询所有课程信息
	courseFields := []string{"courseId", "mainSubjectId", "mainGradeId", "source", "newCourseType"}
	lessonFields := []string{"lessonId"}
	courseList, err := dal.GetCourseLessonInfoByCourseIds(ctx, params.CourseIds, courseFields, lessonFields)

	//获取课程匹配的appId
	result, err := wxmass.GetCourseAppList(ctx, courseList)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
	return
}
