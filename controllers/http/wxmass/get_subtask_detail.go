package wxmass

import (
	"assistantdeskgo/dto/dtowxmass"
	"assistantdeskgo/service/wxmass"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetSubTaskDetail(ctx *gin.Context) {
	params := dtowxmass.GetSubTaskDetailReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:GetSubTaskDetail] params error: ", err.<PERSON>rror())
		base.RenderJsonFail(ctx, err)
		return
	}

	taskDetailList, err := wxmass.GetSubTaskDetail(ctx, params)
	if err != nil {
		zlog.Error(ctx, "[touchTask:GetSubTaskDetail] search error: ", err.<PERSON>rror())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, taskDetailList)
	return
}
