package wxmass

import (
	"assistantdeskgo/components"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
)

type Option struct {
	Label interface{} `json:"label"`
	Value int64       `json:"value"`
}

type SceneTypeReq struct {
	SceneId int64 `json:"sceneId" form:"sceneId"`
}

func GetTaskTypeOptions(ctx *gin.Context) {
	taskTypes := components.GetTaskTypeOptions()
	base.RenderJsonSucc(ctx, buildOptionList(taskTypes))
	return
}

func GetSceneTypeOptions(ctx *gin.Context) {
	params := SceneTypeReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:GetSceneTypeOptions] params error: ", err.<PERSON><PERSON>r())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, buildOptionList(components.GetSceneTypeBySceneId(params.SceneId)))
	return
}

func buildOptionList(dataMap map[int64]interface{}) []Option {
	options := make([]Option, 0, len(dataMap))
	for k, v := range dataMap {
		options = append(options, Option{
			Label: v,
			Value: k,
		})
	}

	sort.Slice(options, func(i, j int) bool {
		return options[i].Value < options[j].Value
	})
	return options
}
