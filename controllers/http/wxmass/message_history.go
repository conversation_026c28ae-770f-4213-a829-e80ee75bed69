package wxmass

import (
	"assistantdeskgo/dto/dtowxmass"
	"assistantdeskgo/service/wxmass"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func MessageHistory(ctx *gin.Context) {
	params := dtowxmass.MessageHistoryReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[touchTask:MessageHistory] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	paged, err := wxmass.MessageHistory(ctx, params)
	if err != nil {
		zlog.Error(ctx, "[touchTask:MessageHistory] search error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, paged)
	return
}
