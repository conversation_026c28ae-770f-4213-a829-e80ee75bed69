package oplog

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtooplog"
	"assistantdeskgo/service/oplog"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func AddLog(ctx *gin.Context) {
	var param dtooplog.OperateLogAddReq
	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	oplog.Add(ctx, param)

	base.RenderJsonSucc(ctx, nil)
	return
}

func QueryLog(ctx *gin.Context) {
	var param dtooplog.OperateLogQueryReq
	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	logData, err := oplog.QueryLog(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, logData)
	return
}
