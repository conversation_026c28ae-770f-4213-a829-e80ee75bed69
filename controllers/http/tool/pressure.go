package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtotool"
	"assistantdeskgo/service/tool/pressure"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CreateTask(ctx *gin.Context) {
	var param dtotool.CreateTaskReq
	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	rsp, err := pressure.CreateTask(ctx, param)
	if err != nil {
		fmt.Println(err)
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
	return
}

func UploadUidsTxt(ctx *gin.Context) {
	url, err := pressure.UploadUidsTxt(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, url)
	return
}

func DownLoadTaskCsv(ctx *gin.Context) {
	var param dtotool.DownLoadTaskCsvReq
	if err := ctx.BindQuery(&param); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	rsp, err := pressure.DownLoadTaskCsv(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
	return
}

func Fail(ctx *gin.Context) {
	var req dtotool.FailTaskCsvReq
	if err := ctx.BindQuery(&req); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := pressure.Fail(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, "success")
	return
}
