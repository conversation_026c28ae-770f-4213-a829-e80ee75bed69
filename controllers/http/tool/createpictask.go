package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtocreatepictask"
	"assistantdeskgo/service/pictask"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CreateCaptureTask(ctx *gin.Context) {
	var param dtocreatepictask.CreateCaptureTask
	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	taskId, err := pictask.CreateTask(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorDbInsert)
		return
	}
	base.RenderJsonSucc(ctx, dtocreatepictask.RetCreateCaptureTask{TaskId: taskId})
	return
}

func OfflineCallback(ctx *gin.Context) {
	var queryParam dtocreatepictask.QueryOfflineCallback
	if err := ctx.BindQuery(&queryParam); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	var bodyParam dtocreatepictask.BodyOfflineCallback
	if err := ctx.ShouldBind(&bodyParam); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	zlog.Infof(ctx, "query: %+v ; body : %+v", queryParam, bodyParam)

	if err := pictask.PreCheckChildTask(ctx, queryParam, bodyParam); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, bodyParam)
}
