package student

import (
	"assistantdeskgo/dto/dtostudent"
	"assistantdeskgo/service/student"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SetBelonger(ctx *gin.Context) {
	params := dtostudent.WXBelongerReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[SetBelonger] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	resp, err := student.SetBelonger(ctx, params)
	if err != nil {
		zlog.Error(ctx, "[SetBelonger] cancel error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func GetBelongerMap(ctx *gin.Context) {
	resp, err := student.GetBelongerMap(ctx)
	if err != nil {
		zlog.Error(ctx, "[GetBelongerMap] cancel error: ", err.<PERSON><PERSON>r())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
