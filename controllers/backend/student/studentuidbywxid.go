package student

import (
	"assistantdeskgo/dto/dtostudent"
	"assistantdeskgo/service/student"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetStudentUidByWxId(ctx *gin.Context) {
	params := dtostudent.StudentUidByWxIdReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[SetBelonger] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	resp, err := student.GetStudentUidByWxId(ctx, params)
	if err != nil {
		zlog.Error(ctx, "[SetBelonger] cancel error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
