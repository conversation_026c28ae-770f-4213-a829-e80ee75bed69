package notice

import (
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/service/notice"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func SaveOrUpdateNotice(ctx *gin.Context) {
	param := dtonotice.SystemNoticeSaveParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, checkMsg, err := notice.SaveOrUpdateNotice(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	if checkMsg != "" {
		base.RenderJson(ctx, -2, checkMsg, result)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func List(ctx *gin.Context) {
	param := dtonotice.SystemNoticeListParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.GetNoticeList(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func Get(ctx *gin.Context) {
	param := dtonotice.SystemNoticeGetParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.GetNotice(ctx, param.Id)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func Detail(ctx *gin.Context) {
	param := dtonotice.SystemNoticeGetParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.ManagerNoticeDetail(ctx, param.Id)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func Delete(ctx *gin.Context) {
	param := dtonotice.SystemNoticeDeleteParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.DeleteNotice(ctx, param.Id)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func ChangeStatus(ctx *gin.Context) {
	param := dtonotice.SystemNoticeDeleteParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.ChangeNoticeStatus(ctx, param.Id, param.Status)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetExpectedNum(ctx *gin.Context) {
	param := dtonotice.ReceiverScopeParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.GetNoticeExpectedNum(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetNoticeExpectedUserList(ctx *gin.Context) {
	param := dtonotice.ReceiverFilterParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.GetNoticeExpectedUserList(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func InformList(ctx *gin.Context) {
	param := dtonotice.ReceiverFilterParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.InformList(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func Readlist(ctx *gin.Context) {
	param := dtonotice.ReceiverFilterParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.Readlist(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func UnReadlist(ctx *gin.Context) {
	param := dtonotice.ReceiverFilterParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.UnReadlist(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func Feedbacklist(ctx *gin.Context) {
	param := dtonotice.ReceiverFilterParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.Feedbacklist(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func ExportNoticeList(ctx *gin.Context) {
	notice.ExportNoticeList(ctx)
}

func ExportNoticeUserList(ctx *gin.Context) {
	param := dtonotice.SystemNoticeGetParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	notice.ExportNoticeUserList(ctx, param.Id)
}
