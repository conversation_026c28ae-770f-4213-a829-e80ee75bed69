package notice

import (
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/service/notice"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func ReplyNoticeComment(ctx *gin.Context) {
	param := dtonotice.UserNoticeCommentParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.ReplyNoticeComment(ctx, param, true)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetNoticeListForComment(ctx *gin.Context) {
	result, err := notice.GetNoticeListForComment(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetCommentList(ctx *gin.Context) {
	param := dtonotice.CommentListParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.GetCommentList(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func SetCommentSelected(ctx *gin.Context) {
	param := dtonotice.SystemNoticeCommentParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.SetCommentSelected(ctx, param.Id, param.IsSelected)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func DeleteComment(ctx *gin.Context) {
	param := dtonotice.SystemNoticeCommentParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	result, err := notice.DeleteComment(ctx, param.Id)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}
