package notice

import (
	"assistantdeskgo/service/notice"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func GetClassDict(ctx *gin.Context) {
	result, err := notice.GetClassDict()
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}

func GetBosTmpToken(ctx *gin.Context) {
	result, err := notice.GetBosTmpToken(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}
