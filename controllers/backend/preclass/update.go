package preclass

import (
	"assistantdeskgo/components"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/service/backend/preclass"
	"git.zuoyebang.cc/fwyybase/fwyylibs/defines"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func UpdateMultiPreClassTag(ctx *gin.Context) {
	req := dtopreclass.UpdateMultiPreClassTagReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "UpdateMultiPreClassTag params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	userInfo := components.GetUserInfo(ctx)
	if userInfo != nil {
		req.AssistantUid = userInfo.SelectedBusinessUid
		req.PersonUid = int64(userInfo.UserId)
	}

	req.UpdateTime = time.Now().Unix()
	req.DataSource = defines.PreClassDataFromAssistant
	rsp, err := preclass.UpdateMultiPreClassTag(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
