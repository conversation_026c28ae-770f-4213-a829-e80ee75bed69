package preclass

import (
	"assistantdeskgo/components"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/service/backend/preclass"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetAllLessonStates(ctx *gin.Context) {
	req := dtopreclass.GetAllLessonStatesReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetAllLessonStates params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	zlog.Infof(ctx, "GetAllLessonStates params, req: %+v", fwyyutils.MarshalIgnoreError(req))
	rsp, err := preclass.GetAllLessonStates(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
