package preclass

import (
	"assistantdeskgo/components"
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/service/backend/preclass"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetPreClassDetail(ctx *gin.Context) {
	req := dtopreclass.GetPreClassDetailReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetPreClassDetail params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := preclass.GetPreClassDetail(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
