package scenetouch

import (
	"assistantdeskgo/components"
	dtoscenetouch "assistantdeskgo/dto/scenetouch"
	"assistantdeskgo/service/scenetouch"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func UseJob(ctx *gin.Context) {
	req := dtoscenetouch.UseJobReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "UseJob params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := scenetouch.UseJob(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "scenetouch.UseJob error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func JobStatus(ctx *gin.Context) {
	req := dtoscenetouch.JobStatusReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "JobStatus params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := scenetouch.JobStatus(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "scenetouch.JobStatus error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func JobResult(ctx *gin.Context) {
	req := dtoscenetouch.JobResultReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "JobResult params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := scenetouch.JobResult(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "scenetouch.JobResult error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
