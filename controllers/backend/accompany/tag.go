package accompany

import (
	"assistantdeskgo/components"
	dtoaccompany "assistantdeskgo/dto/accompany"
	"assistantdeskgo/service/backend/accompany"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetTagInfo(ctx *gin.Context) {
	req := dtoaccompany.GetTagInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetTagInfo params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := accompany.GetTagInfo(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
