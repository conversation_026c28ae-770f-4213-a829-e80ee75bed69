package groupposter

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/service/backend/template/groupposter"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func FindByPaged(ctx *gin.Context) {
	req := dtogroupposter.FindByPagedReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Warnf(ctx, "GroupPoster.FindByPaged params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := groupposter.FindByPaged(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.FindByPaged params error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
