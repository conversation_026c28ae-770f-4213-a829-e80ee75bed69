package groupposter

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/service/backend/template/groupposter"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetById(ctx *gin.Context) {
	req := dtogroupposter.GetByIdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Warnf(ctx, "GroupPoster.GetById params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	gpt, err := groupposter.GetById(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.GetById GetById error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, gpt)
}
