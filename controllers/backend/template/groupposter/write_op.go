package groupposter

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtotemplate/dtogroupposter"
	"assistantdeskgo/service/backend/template/groupposter"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func Add(ctx *gin.Context) {
	req := dtogroupposter.AddGroupPosterReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := req.ValidateParams()
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add ValidateParams error, req: %+v, err: %+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	rsp, err := groupposter.Add(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Add Add error, req: %+v, err: %+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func Update(ctx *gin.Context) {
	req := dtogroupposter.UpdateGroupPosterReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Warnf(ctx, "GroupPoster.Update params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := req.ValidateParams()
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Update ValidateParams error, req: %+v, err: %+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	err = groupposter.Update(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Update Update error, req: %+v, err: %+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, true)
}

func Delete(ctx *gin.Context) {
	req := dtogroupposter.DeleteGroupPosterReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Warnf(ctx, "GroupPoster.Delete params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := groupposter.Delete(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.Delete Update error, req: %+v, err: %+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, true)
}
