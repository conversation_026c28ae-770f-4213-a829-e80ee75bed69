package laxintagfixtool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolaxintagfixtool"
	"assistantdeskgo/service/backend/laxintagtool"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func UploadLaxinTagFile(ctx *gin.Context) {
	req := dtolaxintagfixtool.UploadLaxinTagFileRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "upload laxin tag fix data params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := laxintagtool.UploadLaxinTagFile(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, true)
}

func GetLaxinTagFileTask(ctx *gin.Context) {
	req := dtolaxintagfixtool.GetLaxinTagFileTaskRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "upload laxin tag fix data params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := laxintagtool.GetLaxinTagFileTask(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
