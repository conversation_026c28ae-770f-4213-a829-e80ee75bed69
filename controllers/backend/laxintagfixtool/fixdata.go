package laxintagfixtool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolaxintagfixtool"
	"assistantdeskgo/service/backend/laxintagtool"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetLaxinTagCase(ctx *gin.Context) {
	req := dtolaxintagfixtool.GetLaxinTagCaseRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "upload laxin tag fix data params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := laxintagtool.GetLaxinTagCase(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func TestConsume(ctx *gin.Context) {
	req := dtolaxintagfixtool.TestConsume{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "TestConsume laxin tag fix data params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := laxintagtool.HandleLaxinTagFixTask(ctx, mcpack.V2Map{"taskId": req.TaskId})
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, true)
}

func GetCaseChangeLog(ctx *gin.Context) {
	req := dtolaxintagfixtool.GetCaseChangeLogRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetCaseChangeLog params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := laxintagtool.GetCaseChangeLog(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
