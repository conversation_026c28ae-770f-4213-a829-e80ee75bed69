package touchmessagegroup

import (
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func OrganizationTree(ctx *gin.Context) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	organizationTree, err := message.GetOrganizationTree(ctx, int64(userInfo.UserId))
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, organizationTree)
}

func AllOrganizationTree(ctx *gin.Context) {
	organizationTree, err := message.GetAllOrganizationTree(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, organizationTree)
}
