package touchmessagegroup

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func HtmlBaseInfo(ctx *gin.Context) {

	req := dtomessage.HtmlBaseInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "HtmlBaseInfoReq params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.HtmlBaseInfo(ctx, &req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
