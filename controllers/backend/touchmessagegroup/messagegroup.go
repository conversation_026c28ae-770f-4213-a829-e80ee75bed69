package touchmessagegroup

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SaveMessageGroup(ctx *gin.Context) {
	req := dtomessage.SaveMessageGroupReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "SaveMessageGroup params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.SaveMessageGroup(ctx, req)
	if rsp.Success == 0 {
		rsp.Success = 1
	} else {
		rsp.Success = 0
	}
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func DeleteMessageGroup(ctx *gin.Context) {
	req := dtomessage.DeleteMessageGroupReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "DeleteMessageGroup params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.DeleteMessageGroup(ctx, req.GroupId)
	if rsp.Success == 0 {
		rsp.Success = 1
	} else {
		rsp.Success = 0
	}
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func MessageGroupDetail(ctx *gin.Context) {
	req := dtomessage.MessageGroupDetailReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "sailorScoreList params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.GetMessageGroupDetail(ctx, req.GroupId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func UpdateGroupAvailableRange(ctx *gin.Context) {
	req := dtomessage.MessageGroupUpdateAvailableRangeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "UpdateGroupAvailableRange params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := message.UpdateGroupAvailableRange(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, "success")

}
