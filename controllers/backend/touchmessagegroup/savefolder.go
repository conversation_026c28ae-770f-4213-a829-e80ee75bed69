package touchmessagegroup

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SaveFolder(ctx *gin.Context) {
	req := dtomessage.SaveFolderReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "SaveFolder params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.SaveFolder(ctx, req)
	if rsp.Success == 0 {
		rsp.Success = 1
	} else {
		rsp.Success = 0
	}
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
