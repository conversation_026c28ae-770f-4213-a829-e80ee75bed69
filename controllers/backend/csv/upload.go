package csv

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtocsv"
	"assistantdeskgo/service/backend/sailorscore"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func UploadSailorScore(ctx *gin.Context) {
	req := dtocsv.UploadSailorScoreReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "upload sailor score params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := sailorscore.UploadSailorScore(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func CanUploadSailorScore(ctx *gin.Context) {
	rsp, err := sailorscore.CanUploadSailorScore(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
