package csv

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtocsv"
	"assistantdeskgo/service/csv"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	params := dtocsv.CsvTaskListParam{}
	if err := ctx.ShouldBind(&params); err != nil {
		// 输出用户定义的特定错误码及错误信息，data为 {} 形式
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	res, err := csv.List(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, res)
}
