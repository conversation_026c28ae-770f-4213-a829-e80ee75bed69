package sailorscore

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtosailor"
	"assistantdeskgo/service/csv"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func DoExcelByTaskId(ctx *gin.Context) {
	req := dtosailor.TaskInfo{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "DoExcelByTaskId params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := csv.DoExcelByTaskId(ctx, req.TaskId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func UpdateExtraInfo(ctx *gin.Context) {
	req := dtosailor.UpdateExtraInfo{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "UpdateExtraInfo params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := csv.UpdateExtraInfo(ctx, req.TaskId, req.ExtraInfo)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, nil)
}
