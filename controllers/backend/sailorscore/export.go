package sailorscore

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtosailor"
	"assistantdeskgo/service/backend/sailorscore"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func Export(ctx *gin.Context) {
	req := dtosailor.ListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "sailorScoreList Export params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := sailorscore.Export(ctx, &req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
