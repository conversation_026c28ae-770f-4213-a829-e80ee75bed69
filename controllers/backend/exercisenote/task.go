package exercisenote

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoexercisenote"
	"assistantdeskgo/service/exerciseNote"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CourseTaskUrl(ctx *gin.Context) {
	//入参校验
	req := dtoexercisenote.CourseTaskUrlReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "get course task list params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	//查询结果
	result, err := exerciseNote.GetTaskUrl(ctx, req.CourseId, req.AppId, req.TaskId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, result)
	return
}

func ExportMistakeDetail(ctx *gin.Context) {
	//入参校验
	req := dtoexercisenote.ExportMistakeDetailReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "get course task list params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	//查询结果
	result, err := exerciseNote.GetMistakeByCourseAndTask(ctx, req.CourseId, req.TaskId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	//写入csv
	err = exerciseNote.ExportMistake(ctx, result)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	return
}
