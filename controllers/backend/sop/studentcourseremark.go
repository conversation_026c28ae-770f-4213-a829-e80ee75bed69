package sop

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtosop"
	"assistantdeskgo/service/sop"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func StudentCourseRemark(ctx *gin.Context) {
	req := dtosop.StudentCourseRemarkReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "StudentCourseRemark params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := sop.StudentCourseRemark(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
