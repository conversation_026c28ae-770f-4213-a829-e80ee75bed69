package satisfaction

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoSatisfactionCollect"
	"assistantdeskgo/service/backend/satisfaction"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SaveTplScore(ctx *gin.Context) {
	req := dtoSatisfactionCollect.SaveTplScoreRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "SaveTplScore params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := satisfaction.SaveTplScore(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, true)
}
