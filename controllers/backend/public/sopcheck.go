package public

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/dto/dtopublic"
	"assistantdeskgo/service/backend/public"
	"assistantdeskgo/service/wxmass"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func CheckSopGray(ctx *gin.Context) {
	params := dtopublic.SopGrayReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[public:CheckSopGray] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	isShow := false
	//查询所有课程信息
	courseFields := []string{"courseId", "mainSubjectId", "mainGradeId", "source", "newCourseType"}
	lessonFields := []string{"lessonId", "playType", "stopTime"}
	courseList, err := dal.GetCourseLessonInfoByCourseIds(ctx, []int64{params.CourseId}, courseFields, lessonFields)
	courseInfo, ok := courseList[params.CourseId]
	if !ok {
		base.RenderJsonFail(ctx, fmt.Errorf("获取课程信息失败"))
	}
	//获取章节信息
	lessonInfo, ok := courseInfo.LessonList[int(params.LessonId)]
	if !ok {
		base.RenderJsonFail(ctx, fmt.Errorf("获取章节信息失败"))
	}
	//过滤AI课
	if lessonInfo.PlayType == 3 || lessonInfo.PlayType == 7 {
		base.RenderJsonSucc(ctx, public.FormatCardInfo(isShow))
		return
	}
	//过滤伴学
	if int(time.Now().Unix()) > lessonInfo.StopTime+1800 {
		base.RenderJsonSucc(ctx, public.FormatCardInfo(isShow))
		return
	}

	//查询命中情况
	result, err := wxmass.GetCourseAppList(ctx, courseList)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	//获取命中数据
	if len(result.AppList) > 0 {
		for _, appObj := range result.AppList {
			if appObj.CourseId == params.CourseId && len(appObj.AppInfo) > 0 {
				isShow = true
			}
		}
	}
	//格式化输出内容
	response := public.FormatCardInfo(isShow)
	base.RenderJsonSucc(ctx, response)
	return
}
