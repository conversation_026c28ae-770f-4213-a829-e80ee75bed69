package public

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/userprofile"
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtopublic"
	"assistantdeskgo/service/gray"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CheckCallGray(ctx *gin.Context) {
	params := dtopublic.DuXueCallGrayReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[public:CheckCallGray] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}
	user, ok := ctx.Get("userInfo")
	if !ok {
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return
	}
	userInfo := user.(*userprofile.UserInfo)
	params.StaffUid = int64(userInfo.StaffUid)
	//校验课程、课程需要存
	// 获取课程信息
	courseFields := []string{"courseId", "courseName", "teacherUids", "serviceInfo"}
	lessonFields := []string{"lessonId"}
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, params.CourseId, courseFields, lessonFields)
	if err != nil {
		zlog.Warnf(ctx, "CheckCallGray 获取课程信息失败, courseId: %d, err: %+v", params.CourseId, err)
		base.RenderJsonFail(ctx, components.ErrorAPIGetCourseInfo)
		return
	}
	if courseInfo.CourseId <= 0 {
		base.RenderJsonFail(ctx, components.ErrorAPIGetCourseInfo)
		return
	}
	//在且只能是lpc服务支持
	isLpc := false
	for _, serviceInfo := range courseInfo.ServiceInfo {
		if serviceInfo.ServiceId == 1164 {
			isLpc = true
		}
	}
	if !isLpc {
		base.RenderJsonSucc(ctx, isLpc)
		return
	}
	//判断是否命中水星灰度
	result := gray.HitGray(ctx, params.StaffUid, "assistantdeskgo_call_gray")
	base.RenderJsonSucc(ctx, result)
	return
}
