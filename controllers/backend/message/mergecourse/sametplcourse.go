package mergecourse

import (
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SameTplCourse(ctx *gin.Context) {
	req := dtomessage.SameTplCourseReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "sailorScoreDetail params error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	rsp, err := message.SameTplCourse(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
