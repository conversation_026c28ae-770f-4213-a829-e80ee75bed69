package message

import (
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func UpdateTaskStatus(ctx *gin.Context) {
	params := dtomessage.UpdateTaskStatusReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Error(ctx, "[UpdateTaskStatus] params error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	resp, err := message.UpdateTaskStatus(ctx, params)
	if err != nil {
		zlog.Error(ctx, "[UpdateTaskStatus] cancel error: ", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
