package message

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	TouchmisRouteSendMessageGray = "touchmis_route_send_message_gray"
)

func SendGroupMessage(ctx *gin.Context) {
	req := dtomessage.SendGroupMessageReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "sailorScoreDetail params error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	var rsp dtomessage.SendGroupMessageRsp
	var err error
	if matchRouteToTouchmisGray(ctx) {
		rsp, err = message.RouteToTouchmisSendMessage(ctx, req)
	} else {
		rsp, err = message.SendGroupMessage(ctx, req)
	}
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func matchRouteToTouchmisGray(ctx *gin.Context) bool {
	userInfo := components.GetUserInfo(ctx)
	if userInfo == nil {
		return false
	}

	result, err := touchmisgo.GrayHit(ctx, touchmisgo.GrayHitRequest{
		PersonUid: int64(userInfo.StaffUid),
		Key:       TouchmisRouteSendMessageGray,
	})
	if err != nil {
		return false
	}
	return result
}
