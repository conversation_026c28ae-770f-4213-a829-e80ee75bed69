package deskviewdetail

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtodeskviewdetail"
	"assistantdeskgo/service/backend/deskviewdetail"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func GetTeacherQRCode(ctx *gin.Context) {
	req := dtodeskviewdetail.GetTeacherQRCodeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	res, err := deskviewdetail.GetTeacherQRCOde(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, res)
}
