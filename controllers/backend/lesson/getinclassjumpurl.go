package lesson

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtolesson"
	"assistantdeskgo/service/backend/lesson"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetInClassJumpUrl(ctx *gin.Context) {
	req := &dtolesson.GetInClassJumpUrlReq{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "GetInClassJumpUrl params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := lesson.GetInClassJumpUrl(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
