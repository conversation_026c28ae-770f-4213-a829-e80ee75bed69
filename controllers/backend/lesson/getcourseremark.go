package lesson

import (
	"assistantdeskgo/dto/dtolesson"
	"assistantdeskgo/service/backend/lesson"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetCourseRemark(ctx *gin.Context) {
	req := dtolesson.GetCourseRemarkReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetInClassJumpUrl params error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	rsp, err := lesson.GetCourseRemark(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
