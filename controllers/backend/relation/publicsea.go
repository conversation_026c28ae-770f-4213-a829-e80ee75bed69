package relation

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtopublicsea"
	"assistantdeskgo/service/relation/publicsea"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func ClueList(ctx *gin.Context) {
	req := dtopublicsea.ClueListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := publicsea.ClueList(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "ClueList.req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetPublicSeaFilterMap(ctx *gin.Context) {
	resp, err := publicsea.GetFilterMap(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GetPublicSeaFilterMap err:%+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func Retrieve(ctx *gin.Context) {
	req := dtopublicsea.RetrieveReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := publicsea.Retrieve(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "Retrieve.req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func ClueStatus(ctx *gin.Context) {
	req := dtopublicsea.ClueStatusReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	respMap, err := publicsea.ClueStatus(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "ClueStatus.req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, respMap)
}
