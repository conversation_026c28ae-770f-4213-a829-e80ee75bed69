package relation

import (
	"assistantdeskgo/dto/dtorelation/dtosource"
	"assistantdeskgo/service/relation/source"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetStudentCallRecordInfo(ctx *gin.Context) {
	req := dtosource.GetStudentCallRecordInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := source.GetStudentCallRecordInfo(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetLessonList(ctx *gin.Context) {
	req := dtosource.GetLessonListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := source.GetLessonList(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonList.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetRemarkList(ctx *gin.Context) {
	req := dtosource.GetRemarkListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	respList, err := source.GetRemarkList(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetRemarkList.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, respList)
}
