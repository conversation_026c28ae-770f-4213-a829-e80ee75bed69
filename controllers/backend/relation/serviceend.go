package relation

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtoserviceend"
	"assistantdeskgo/service/relation/serviceend"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetFilterMap(ctx *gin.Context) {
	resp, err := serviceend.GetFilterMap(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GetPublicSeaFilterMap err:%+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetRecordList(ctx *gin.Context) {
	req := dtoserviceend.RecordListReq{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	res, err := serviceend.RecordList(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, res)
}
