package relation

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtorelation/dtoclue"
	"assistantdeskgo/service/relation/clue"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func EditManualIntention(ctx *gin.Context) {
	req := &dtoclue.ManualIntentionReq{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "EditManualIntention params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := clue.EditManualIntention(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, "sucess")
}

func EditManualCallStatus(ctx *gin.Context) {
	req := &dtoclue.ManualCallStatusReq{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "EditManualCallStatus params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := clue.EditManualCallStatus(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, "sucess")
}

func EditManualRemark(ctx *gin.Context) {
	req := &dtoclue.ManualRemarkReq{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "EditManualRemark params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := clue.EditManualRemark(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, "sucess")
}
