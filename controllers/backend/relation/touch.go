package relation

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/relation/touch"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CallCheck(ctx *gin.Context) {
	req := dtotouch.CallCheckReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.ErrorAssistantUidInvalid)
		return
	}

	resp := touch.CallCheck(ctx, req, deviceInfo.DeviceUid)
	base.RenderJsonSucc(ctx, resp)
}

func Call(ctx *gin.Context) {
	req := dtotouch.CallReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := touch.Call(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "Call.req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func StudentSmsFilter(ctx *gin.Context) {
	req := &dtotouch.StudentSmsFilterReq{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "StudentSmsFilter params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if len(req.StudentUids) > defines.CreateCardTaskLimit {
		zlog.Infof(ctx, "StudentSmsFilter params studentUids error")
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	res, err := touch.StudentSmsFilter(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, res)
}

func SendAddWxMsg(ctx *gin.Context) {
	req := &dtotouch.SendAddWXMsgReq{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "EditManualCallStatus params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	if len(req.StudentList) > defines.CreateCardTaskLimit {
		zlog.Infof(ctx, "StudentSmsFilter params studentUids error")
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	res, err := touch.SendAddWxMsg(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, res)
}

func GetSmsSendConfig(ctx *gin.Context) {
	req := dtotouch.GetSmsSendConfigReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := touch.GetSmsSendConfig(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetSmsSendConfig.req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func SendSmsSingle(ctx *gin.Context) {
	req := dtotouch.SendSmsSingleReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := touch.SendSmsSingle(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "SendSmsSingle.req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
