package aimessage

import (
	"assistantdeskgo/service/stuaimessage"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

type listParam struct {
	Uid          int64 `json:"studentUid" form:"studentUid"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	Page         int   `json:"page" form:"page"`
	PageSize     int   `json:"pageSize" form:"pageSize"`
}

func List(ctx *gin.Context) {
	param := listParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	//获取列表数据
	result, err := stuaimessage.List(ctx, param.Uid, param.AssistantUid, param.Page, param.PageSize)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, result)
}
