package aimessage

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/longlinkService"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"time"
)

type ReadParam struct {
	StudentUid   int64 `json:"studentUid" form:"studentUid" binding:"required"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid" binding:"required"`
	Id           int64 `json:"id" form:"id"`
}

func Read(ctx *gin.Context) {
	param := &ReadParam{}
	if err := ctx.ShouldBind(param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	stuAIMessageModel := models.StuAiMessage{
		StudentUid:   param.StudentUid,
		AssistantUid: param.AssistantUid,
	}
	if param.Id > 0 {
		stuAIMessageModel.Id = param.Id
	}
	count, err := stuAIMessageModel.Count(ctx, param.StudentUid)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	if count <= 0 {
		base.RenderJsonFail(ctx, fmt.Errorf("there is no message to read"))
		return
	}
	//更新数据
	updateModel := stuAIMessageModel
	updateModel.Status = 1
	updateModel.UpdateTime = time.Now().Unix()
	err = stuAIMessageModel.Update(ctx, param.StudentUid, updateModel)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, true)
	return
}

func Send(ctx *gin.Context) {
	param := &ReadParam{}
	if err := ctx.ShouldBind(param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	stuAIMessageModel := models.StuAiMessage{
		StudentUid:   param.StudentUid,
		AssistantUid: param.AssistantUid,
	}
	if param.Id > 0 {
		stuAIMessageModel.Id = param.Id
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).
		Table(stuAIMessageModel.TableName(param.StudentUid)).
		Model(stuAIMessageModel).
		Find(&stuAIMessageModel).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	msgId, err := longlinkService.SendInterviewMsg(ctx, stuAIMessageModel, time.Now().Unix(), 0)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, msgId)
	return

}
