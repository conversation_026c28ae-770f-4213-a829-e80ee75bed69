package interview

import (
	"assistantdeskgo/service/interview"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

type DetailParam struct {
	CallId     int64 `json:"callId" form:"callId"`
	StudentUid int64 `json:"studentUid" form:"studentUid"`
	Type       int   `json:"type" form:"type"`
}

func Detail(ctx *gin.Context) {
	param := DetailParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	//获取列表数据
	switch param.Type {
	case 2:
		//查询电话外呼详情
		result, err := interview.GetCallInterviewDetail(ctx, param.StudentUid, param.CallId)
		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}
		base.RenderJsonSucc(ctx, result)
		return
	case 3:
		//查询wx外呼详情
		result, err := interview.GetWxCallInterviewDetail(ctx, param.StudentUid, param.CallId)
		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}
		base.RenderJsonSucc(ctx, result)
		return
	default:
		base.RenderJsonFail(ctx, fmt.Errorf("未知维系记录类型"))
		return
	}
}
