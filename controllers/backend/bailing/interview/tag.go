package interview

import (
	"assistantdeskgo/service/interview"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

type HideTagParam struct {
	CallId     int64    `json:"callId" form:"callId"`
	StudentUid int64    `json:"studentUid" form:"studentUid"`
	Type       int      `json:"type" form:"type"`
	Tags       []string `json:"tags" form:"tags"`
}

func HideTag(ctx *gin.Context) {
	param := HideTagParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	if len(param.Tags) <= 0 {
		base.RenderJsonFail(ctx, fmt.Errorf("param tags can not be empty"))
		return
	}
	err := interview.HideTag(ctx, param.CallId, param.StudentUid, param.Type, param.Tags)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, true)
	return
}
