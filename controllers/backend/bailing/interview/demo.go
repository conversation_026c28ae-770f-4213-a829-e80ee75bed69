package interview

import (
	"assistantdeskgo/controllers/mq"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func Demo1(ctx *gin.Context) {
	msgData := mq.WxCallMessageData{
		UserID:         "qw240411000002",
		CorpID:         "ww55f2721cf084c185",
		CallUserID:     "qw240411000002",
		CallCorpID:     "ww55f2721cf084c185",
		TalkMode:       0,
		MsgType:        17,
		IsSender:       1,
		OtherRole:      1,
		CallRemoteID:   "1688856897592530",
		OtherRemoteID:  "7881300920296004",
		RingTime:       1732518557,
		CallTime:       1732518561,
		EndTime:        1732518627,
		CallStatus:     1,
		Duration:       66,
		UploadProgress: 1,
		LocalFile:      "1732518561_1_7881300920296004.mp3",
		RecordFile:     "work_call_record/ceb3fddf6eaeb4116d163b844cbb58b5.mp3",
		CreateTime:     1732518627,
		UpdateTime:     1732518627,
		CallID:         846,
		CallStaffUID:   4300283172,
		OtherStaffUID:  -1,
		AudioURL:       "https://charge.zuoyebang.cc/work_call_record/ceb3fddf6eaeb4116d163b844cbb58b5.mp3?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-11-25T07%3A10%3A58Z%2F604800%2Fhost%2Fc560432690fb0f7686db5c9ec8a86f3a415878a6526e71fdf400c92baea874cb",
		CommandNo:      0,
		Role:           1,
		PressMark:      0,
	}
	err := mq.DealWxCallRecord(ctx, msgData)
	base.RenderJsonSucc(ctx, err)
	return
}

func Demo2(ctx *gin.Context) {
	msgDataStr := `{
		"result_url": "https://zyb-audio-search-1253445850.cos.ap-beijing.myqcloud.com/eb83481c-9d61-4ea4-96f9-ec90d48da6b6?q-sign-algorithm=sha1&q-ak=AKIDNWP3RYnCcnkwuhGVVFjwrV3Leoyu9CWl&q-sign-time=1732587495%3B1741227555&q-key-time=1732587495%3B1741227555&q-header-list=host&q-url-param-list=&q-signature=611d667fa119d2ac1242d0cb19b5ebf200d5e586",
		"raw_param": "{\"audio_id\": \"assistantdeskgo_ai_wx_2135228215_846\", \"ext_info\": \"\"}",
		"result": "{\"id\": \"eb83481c-9d61-4ea4-96f9-ec90d48da6b6\", \"SentenceList\": [{\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5582\u5582\u6587\u8f69\u5bf9\u6587\u8f69\u7238\u7238\u60a8\u597d\u6211\u662f\u4f5c\u4e1a\u5e2e\u82f1\u8bed\u8001\u5e08\u60a8\u597d\u3002\"}]}, \"keyword-length\": 2, \"word-num\": 22, \"segment-length\": 4.36, \"segment-start\": 0.76, \"speed\": 5.045871559633027, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u54ce\u4f60\u597d\u4f60\u597d\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 5, \"segment-length\": 0.6799999999999997, \"segment-start\": 5.92, \"speed\": 7.352941176470591, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u55ef\u6587\u8f69\u4ed6\u4e0d\u662f\u5728\u6211\u4eec\u4f5c\u4e1a\u5e2e\u8003\u4e86\u8fd9\u4e2a\u671f\u4e2d\u4e07\u4eba\u6a21\u8003\u5377\u561b\uff0c\u6211\u7ed9\u60a8\u53cd\u9988\u4e00\u4e0b\u4ed6\u8fd9\u4e2a\u6210\u7ee9\u60c5\u51b5\u54c8\u3002\"}]}, \"keyword-length\": 1, \"word-num\": 39, \"segment-length\": 6.880000000000001, \"segment-start\": 6.92, \"speed\": 5.66860465116279, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u554a\u597d\u597d\u597d\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 4, \"segment-length\": 0.7200000000000006, \"segment-start\": 14.6, \"speed\": 5.555555555555551, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u55ef\u5c31\u662f\u6211\u4e5f\u7fa4\u53d1\u4e86\uff0c\u5c31\u662f\u6211\u4e5f\u7ed9\u60a8\u53d1\u4e86\u4ed6\u8fd9\u4e2a\u9636\u6bb5\u6d4b\u7684\u62a5\u544a\u4e0d\u77e5\u9053\u4f60\u5f53\u65f6\u6709\u6ca1\u6709\u70b9\u5f00\u6765\u770b\u3002\"}]}, \"keyword-length\": 1, \"word-num\": 38, \"segment-length\": 6.879999999999999, \"segment-start\": 15.68, \"speed\": 5.523255813953489, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5443\u8fd9\u4e2a\u5443\u5c0f\u5b69\u8fd9\u4e2a\u90fd\u4ed6\u81ea\u5df1\u770b\u7684\u6211\u5929\u5929\u5fd9\u5f97\u8981\u6b7b\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 21, \"segment-length\": 4.039999999999999, \"segment-start\": 23.8, \"speed\": 5.198019801980199, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u597d\u7684\u597d\u7684\u54b1\u4eec\u8fd9\u4e2a\u6ee1\u5206\u662f\u4e00\u767e\u5206\u7684\u8fd9\u4e2a\u8bd5\u5377\uff0c\u55ef\u6587\u8f69\u4ed6\u8003\u4e86\u516d\u5341\u516b\u5206\uff0c\u8fd9\u4e2a\u5206\u6570\u5c31\u662f\uff0c\u55ef\u6211\u4eec\u5b66\u5458\u7684\u5e73\u5747\u5206\u662f\u4e03\u5341\u4e03\u5206\uff0c\u6240\u4ee5\u8fd9\u4e2a\u5206\u6570\u662f\u6709\u70b9\u7565\u4f4e\u4e8e\u5e73\u5747\u5206\u7684\uff0c\u7136\u540e\uff0c\u6211\u4eec\u8fd9\u4e2a\u6574\u7684\u8fd9\u4e2a\u8bd5\u5377\u5462\u662f\u6ca1\u6709\u4f5c\u6587\u9898\u7684\u6240\u4ee5\u7a0d\u5fae\u96be\u5ea6\u964d\u4e86\u4e00\u70b9\u56e0\u4e3a\u6709\u4f5c\u6587\u9898\u7684\u8bdd\u5b69\u5b50\u80af\u5b9a\u8fd8\u8981\u518d\u6263\u4e2a\u4e00\u4e24\u5206\u561b\uff0c\u7136\u540e\u770b\u4e86\u4e00\u4e0b\u4ed6\u8fd9\u4e2a\u9519\u9898\uff0c\u6ee1\u5206\u662f\u5341\u5206\u7684\uff0c\u8fd9\u4e2a\u5341\u5206\u7684\u542c\u529b\u9898\uff0c\u4ed6\u603b\u5171\u5c31\u5bf9\u4e86\u4e00\u534a\u4e94\u9053\u9898\uff0c\u6240\u4ee5\u6211\u5728\u60f3\u4ed6\u5f53\u65f6\uff0c\u542c\u542c\u529b\u7684\u65f6\u5019\u662f\u4e0d\u662f\u5e94\u8be5\uff0c\u5468\u56f4\u73af\u5883\u6bd4\u8f83\u5435\u554a\u6240\u4ee5\u5bfc\u81f4\u4ed6\u5f53\u65f6\u6ca1\u6709\u597d\u597d\u7684\u542c\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 192, \"segment-length\": 42.48, \"segment-start\": 28.6, \"speed\": 4.519774011299435, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u4e0d\u5435\u4e0d\u5435\u7684\u4ed6\u5728\u5bb6\u91cc\u5728\u5728\u4ed6\u5728\u4ed6\u81ea\u5df1\u7684\u975e\u5e38\u5b89\u9759\u7684\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 22, \"segment-length\": 4.280000000000001, \"segment-start\": 71.44, \"speed\": 5.140186915887849, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u54e6\u597d\u7684\u90a3\u4ed6\u6700\u8fd1\u6709\u6ca1\u6709\u5176\u4ed6\u7684\u8003\u8bd5\u554a\u82f1\u8bed\u8003\u8bd5\u8fd9\u4e2a\u5b66\u671f\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 24, \"segment-length\": 4.239999999999995, \"segment-start\": 76.08, \"speed\": 5.660377358490573, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5b66\u6821\u4f1a\u8003\u5427\u5b66\u6821\u8003\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 8, \"segment-length\": 1.2000000000000028, \"segment-start\": 81.84, \"speed\": 6.666666666666651, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u54e6\u5b66\u6821\u8003\u4ed6\u6709\u544a\u8bc9\u60a8\u4ed6\u5c31\u662f\u8fd9\u5b66\u671f\u82f1\u8bed\u6709\u8003\u591a\u5c11\u5206\u5417\uff1f\"}]}, \"keyword-length\": 1, \"word-num\": 23, \"segment-length\": 4.640000000000001, \"segment-start\": 83.92, \"speed\": 4.956896551724137, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5443\u6ca1\u8bf4\u4ee5\u524d\u6211\u4f1a\u5173\u6ce8\u8fd9\u4e2a\u540e\u6765\u6211\u4e5f\u4e0d\u5173\u6ce8\u4ed6\u4e86\u6211\u4e5f\u4e0d\u7ba1\u4ed6\u4e86\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 26, \"segment-length\": 3.6799999999999926, \"segment-start\": 89.56, \"speed\": 7.065217391304362, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u54e6\u4ed6\u73b0\u5728\u662f\u4f4f\u5bbf\u5417\u8fd8\u662f\u8d70\u8bfb\u554a\u8d70\u8d70\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 15, \"segment-length\": 4.039999999999992, \"segment-start\": 94.4, \"speed\": 3.71287128712872, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u8bfb\u8d70\u8bfb\u8d70\u8bfb\u7684\u5bf9\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 7, \"segment-length\": 1.0799999999999983, \"segment-start\": 98.56, \"speed\": 6.481481481481492, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u90a3\u4ed6\u6bcf\u5929\u90fd\u4f1a\u5230\u5bb6\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 8, \"segment-length\": 1.2800000000000011, \"segment-start\": 100.08, \"speed\": 6.249999999999995, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u662f\u5417\u5bf9\u5bf9\u5bf9\u5bf9\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 6, \"segment-length\": 1.0, \"segment-start\": 102.04, \"speed\": 6.0, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u6211\u770b\u4ed6\u8fd9\u4e2a\u4e0a\u8bfe\u7684\u8fd9\u4e2a\u72b6\u6001\u5728\u8fd8\u662f\u53ef\u4ee5\u7684\u6bcf\u8282\u8bfe\u4ed6\u90fd\u6709\u8ba4\u771f\u770b\u7136\u540e\u8fd9\u4e2a\u5de9\u56fa\u7ec3\u4e60\u4ed6\u8fd8\u90fd\u505a\u4e86\u6240\u4ee5\u8fd9\u4e2a\u6001\u5ea6\u662f\u6709\u7684\uff0c\u4f46\u662f\uff0c\u55ef\uff0c\u8fd9\u4e2a\uff0c\u8bd5\u5377\u7684\u8fd9\u4e2a\u57fa\u672c\u60c5\u51b5\u5462\u662f\u4ed6\u8fd9\u4e2a\uff0c\u9519\u9898\u786e\u5b9e\u6709\u70b9\u591a\u4e86\uff0c\u56e0\u4e3a\u4ed6\u8fd9\u4e2a\u9009\u62e9\u9898\u8fde\u7eed\u9519\u4e86\u56db\u9053\u8fd9\u4e2a\uff0c\u9009\u9879\u9898\uff0c\u8003\u5bdf\uff0c\u5f62\u5bb9\u8bcd\uff0c\u540d\u8bcd\u7684\u8fd9\u4e9b\u8fa8\u6790\u9898\u4ed6\u90fd\u9519\u4e86\uff0c\u4e0d\u77e5\u9053\u4ed6\u73b0\u5728\u5728\u4e0d\u5728\u5bb6\u6211\u53ef\u4ee5\u8bb2\u7ed9\u4ed6\u4e00\u4e9b\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 130, \"segment-length\": 28.72, \"segment-start\": 103.44, \"speed\": 4.5264623955431755, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u6211\u6211\u73b0\u5728\u6211\u73b0\u5728\u4e0d\u5728\u5bb6\u60a8\u3002\"}]}, \"keyword-length\": 1, \"word-num\": 11, \"segment-length\": 2.1200000000000045, \"segment-start\": 133.2, \"speed\": 5.188679245283008, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u73b0\u5728\u4e0d\u5728\u5bb6\u6211\u521a\u624d\u4e5f\u6253\u4e86\u4ed6\u90a3\u4e2a\u7535\u8bdd\u6211\u770b\u4e00\u4e0b\u4ed6\u90a3\u4e2a\u624b\u673a\u5c3e\u53f7\u662f\u4e0d\u662f\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 30, \"segment-length\": 5.039999999999992, \"segment-start\": 135.36, \"speed\": 5.952380952380961, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u96f6\u4e09\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 2, \"segment-length\": 0.23999999999998067, \"segment-start\": 141.36, \"speed\": 8.333333333334004, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u96f6\u4e09\u4e94\u516b\u7684\u662f\u5427\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 7, \"segment-length\": 0.9200000000000159, \"segment-start\": 142.6, \"speed\": 7.608695652173782, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5bf9\u6211\u4e00\u5f00\u59cb\u6253\u7684\u5c31\u662f\u8fd9\u4e2a\u4f46\u662f\u5f53\u65f6\u597d\u50cf\u662f\u6b63\u5728\u901a\u8bdd\u4e2d\u8fd8\u662f\u5565\uff0c\u53cd\u6b63\u6ca1\u6709\u6253\u901a\uff0c\u7136\u540e\u6211\u6211\u518d\u8ddf\u4f60\u8bf4\u4e00\u4e0b\u4ed6\u9519\u4e3b\u8981\u9519\u7684\u9898\u8fd8\u6709\uff0c\u8fd9\u4e2a\uff0c\u55ef\uff0c\u540e\u9762\u7684\u8fd9\u4e2a\uff0c\u5b8c\u5f62\u586b\u7a7a\u9519\u4e86\u4e00\u9053\uff0c\u7136\u540e\u9605\u8bfb\u7406\u89e3\u505a\u7684\u8fd8\u4e0d\u9519\u4e5f\u662f\u53ea\u9519\u9519\u4e86\u4e24\u9053\u5427\uff0c\u9519\u4e86\u4e00\u9053\u7136\u540e\u540e\u9762\u8fd8\u6709\u4e00\u4e2a\uff0c\u90a3\u4e2a\u77ed\u6587\u8fd8\u539f\u53e5\u5b50\u4ed6\u53c8\u9519\u4e86\u4e00\u9053\uff0c\u6240\u4ee5\u4e0d\u77e5\u9053\u4ed6\u5e73\u65f6\uff0c\u90a3\u4f60\u77e5\u9053\u4ed6\uff0c\u4ed6\u4ed6\u7684\u8fd9\u4e2a\u8bd5\u5377\u662f\u4e00\u767e\u4e8c\u5341\u5206\u5236\u7684\u662f\u5427\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 143, \"segment-length\": 31.75999999999999, \"segment-start\": 144.4, \"speed\": 4.502518891687659, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5443\u82f1\u8bed\u82f1\u8bed\u662f\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 6, \"segment-length\": 1.2800000000000011, \"segment-start\": 177.52, \"speed\": 4.687499999999996, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u662f\u4e00\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 2, \"segment-length\": 0.3199999999999932, \"segment-start\": 179.84, \"speed\": 6.250000000000133, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u662f\u4e00\u767e\u4e8c\u8fd8\u662f\u4e00\u767e\u4e94\u7684\u662f\u4e00\u767e\u4e8c\u4e00\u767e\u4e8c\u5bf9\u82f1\u8bed\u662f\u4e00\u767e\u4e8c\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 24, \"segment-length\": 3.9199999999999875, \"segment-start\": 180.84, \"speed\": 6.122448979591856, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u82f1\u8bed\u5e94\u8be5\u662f\u4e00\u767e\u4e8c\u5341\u5206\u5236\u7684\uff0c\u5443\u90a3\u4ed6\u521d\u4e00\u4e0a\u7684\u65f6\u5019\u6709\u6ca1\u6709\u8ddf\u60a8\u8bf4\u8fc7\u4ed6\u8fd9\u4e2a\u6210\u7ee9\u60c5\u51b5\u5440\u3002\"}]}, \"keyword-length\": 1, \"word-num\": 36, \"segment-length\": 5.639999999999986, \"segment-start\": 185.08, \"speed\": 6.382978723404271, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u55ef\uff0c\u4e0a\u5b66\u671f\u7684\u65f6\u5019\u8001\u5e08\u662f\u6709\u6bb5\u65f6\u95f4\u7ecf\u5e38\u4f1a\u627e\u8bf4\u4ed6\u6210\u7ee9\u76f4\u7ebf\u4e0b\u964d\uff0c\u518d\u5230\u540e\u6765\u518d\u5230\u540e\u6765\u8001\u5e08\u4e5f\u4e0d\u627e\u4e86\u8bc1\u660e\u4ed6\u5c31\uff0c\u6ca1\u6551\u4e86\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 47, \"segment-length\": 11.400000000000006, \"segment-start\": 192.0, \"speed\": 4.122807017543858, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u8fd8\u662f\u60f3\u548c\u4ed6\u672c\u4eba\u804a\u804a\u4ed6\u4ed6\u73b0\u5728\u662f\uff0c\u5443\u662f\u8c01\u54ea\u54ea\u54ea\u4e2a\u4eb2\u4eba\u5728\u4ed6\u8eab\u8fb9\u5440\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 28, \"segment-length\": 6.52000000000001, \"segment-start\": 204.56, \"speed\": 4.294478527607355, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5443\u53eb\u6211\u8fd8\u6709\u4ed6\u5976\u5976\u54e6\uff1f\"}]}, \"keyword-length\": 0, \"word-num\": 9, \"segment-length\": 1.960000000000008, \"segment-start\": 211.88, \"speed\": 4.591836734693859, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u4ed6\u5976\u5976\u5728\u4ed6\u8eab\u8fb9\uff0c\u55ef\u6211\u770b\u4ed6\u8fd9\u4e2a\u6001\u5ea6\u662f\u6709\u7684\u56e0\u4e3a\u6211\u4eec\u8fd9\u4e2a\u8bfe\u4e09\u8282\u8bfe\u4ed6\u6bcf\u8bfe\u6bcf\u4e00\u8282\u8bfe\u90fd\u6ca1\u6709\u843d\u4e0b\u7136\u540e\u5de9\u56fa\u7ec3\u4e60\u4e5f\u505a\u4e86\u4e92\u52a8\u9898\u662f\u505a\u7684\u867d\u7136\uff0c\u6709\u65f6\u5019\uff0c\u6b63\u786e\u90a3\u4e2a\u4e92\u52a8\u7387\u4e0d\u662f\u7279\u522b\u9ad8\u4f46\u662f\u4ed6\u8fd9\u4e2a\uff0c\u6001\u5ea6\u8fd8\u662f\u6709\u7684\u5728\u7ebf\u7684\u8fd9\u4e2a\u72b6\u6001\uff0c\u8fd8\u662f\u4e0d\u9519\u7684\u6240\u4ee5\u6211\u89c9\u5f97\u4ed6\u662f\u6709\u60f3\u60f3\u597d\u597d\u5b66\u7684\u8fd9\u4e2a\u65b0\u7684\uff0c\u6240\u4ee5\u4ed6\u8fd9\u4e2a\u82f1\u8bed\u7684\u8fd9\u4e2a\u5176\u4ed6\u7684\u60c5\u51b5\u4e0d\u592a\u4e86\u89e3\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 132, \"segment-length\": 28.120000000000005, \"segment-start\": 214.2, \"speed\": 4.694167852062588, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u4ed6\u4ed6\u82f1\u8bed\u5c31\u662f\u4ece\u5c0f\u5b66\u7684\u65f6\u5019\u82f1\u8bed\u4e00\u76f4\u90fd\u4e0d\u884c\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 19, \"segment-length\": 3.200000000000017, \"segment-start\": 243.04, \"speed\": 5.937499999999968, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u54e6\uff0c\u4ece\u5c0f\u5b66\u7684\u65f6\u5019\u5c31\u5f00\u59cb\u4e86\uff0c\u54ce\uff0c\u597d\u7684\u90a3\u6211\u5927\u6982\u4e86\u89e3\u4e86\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 21, \"segment-length\": 5.560000000000002, \"segment-start\": 246.76, \"speed\": 3.7769784172661853, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u554a\u4ed6\u82f1\u8bed\u4e00\u76f4\u90fd\u662f\u6700\u5dee\u7684\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 11, \"segment-length\": 1.759999999999991, \"segment-start\": 252.84, \"speed\": 6.250000000000032, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u90a3\u4ed6\u90a3\u4ed6\u5468\u672b\u6709\u5176\u4ed6\u7684\u5b89\u6392\u5417\u8fd8\u662f\u54b1\u4eec\u672b\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 18, \"segment-length\": 4.159999999999997, \"segment-start\": 255.92, \"speed\": 4.32692307692308, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u6ca1\u6709\u6ca1\u6709\u5c31\u662f\u5728\u90a3\u4e2a\u4f5c\u4e1a\u5e2e\u8fd8\u6709\u6570\u5b66\u8bfe\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 17, \"segment-length\": 3.1200000000000045, \"segment-start\": 261.12, \"speed\": 5.448717948717941, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u54e6\u8fd8\u6709\u4e2a\u6570\u5b66\u8bfe\u662f\u5427\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 9, \"segment-length\": 1.5600000000000023, \"segment-start\": 264.76, \"speed\": 5.769230769230761, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u5bf9\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 1, \"segment-length\": 0.040000000000020464, \"segment-start\": 266.84, \"speed\": 24.99999999998721, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u4ed6\u81ea\u89c9\u6027\u8fd8\u662f\u6709\u7684\uff0c\u4ed6\u4ed6\u8fd9\u4e2a\u8bfe\u56de\u653e\u8bfe\u90fd\u6709\u81ea\u5df1\u5728\u770b\u7684\uff0c\u6211\u770b\u4ed6\u8fd9\u4e2a\uff0c\u8fd9\u4e2a\u4e13\u6ce8\u7684\u65f6\u95f4\u4e5f\u662f\u86ee\u957f\u7684\u5c31\u662f\u4e0d\u77e5\u9053\u4ed6\u4f1a\u4e0d\u4f1a\u8bb0\u7b14\u8bb0\u554a\uff0c\u7136\u540e\u4e0d\u77e5\u9053\u4ed6\u5728\u5b66\u6821\u7684\u8fd9\u4e2a\u6210\u7ee9\u548b\u6837\uff0c\u6211\uff0c\u8fc7\u51e0\u5929\u518d\u7ed9\u4ed6\u81ea\u5df1\u672c\u4eba\u6253\u5427\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 82, \"segment-length\": 18.28000000000003, \"segment-start\": 267.64, \"speed\": 4.485776805251634, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u55ef\uff0c\u884c\u884c\u6211\uff0c\u5468\u4e94\u73b0\u5728\u4e0d\u5728\u5bb6\u4e3b\u8981\u8981\u662f\u5728\u5bb6\u7684\u8bdd\u5c31\u53ef\u4ee5\u8ddf\u4ed6\u8bf4\u4e86\u6211\u73b0\u5728\u8fd8\u6ca1\u6ca1\u6ca1\u56de\u5bb6\u4e86\u55ef\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 37, \"segment-length\": 7.319999999999993, \"segment-start\": 286.68, \"speed\": 5.054644808743174, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u597d\u7684\u597d\u7684\u90a3\u60a8\u5148\u5fd9\u4e0d\u6253\u6270\u60a8\u4e86\u54c8\u3002\"}]}, \"keyword-length\": 2, \"word-num\": 14, \"segment-length\": 3.160000000000025, \"segment-start\": 294.32, \"speed\": 4.430379746835408, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u55ef\u90a3\u597d\u7684\u597d\u7684\u597d\u7684\u597d\u7684\u9ebb\u70e6\u4f60\u4e86\u55ef\u6ca1\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 16, \"segment-length\": 2.640000000000043, \"segment-start\": 297.84, \"speed\": 6.060606060605961, \"spk_id\": 1, \"role\": \"User\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u4e8b\u597d\u62dc\u62dc\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 4, \"segment-length\": 0.7199999999999704, \"segment-start\": 300.6, \"speed\": 5.555555555555784, \"spk_id\": 2, \"role\": \"Agent\"}, {\"result\": {\"hypotheses\": [{\"confidence\": 10000000000.0, \"transcript\": \"\u55ef\u597d\u597d\u7684\u597d\u7684\u8bf6\u62dc\u62dc\u62dc\u62dc\u3002\"}]}, \"keyword-length\": 0, \"word-num\": 11, \"segment-length\": 2.0, \"segment-start\": 301.48, \"speed\": 5.5, \"spk_id\": 1, \"role\": \"User\"}], \"TotalTime\": 304.824, \"status\": 0, \"hotword_token\": \"\", \"trans_time\": 14.397, \"raw_param\": \"{\\\"audio_id\\\": \\\"assistantdeskgo_ai_wx_2135228215_846\\\", \\\"ext_info\\\": \\\"\\\"}\"}"
	}`
	msgData := mq.CallRecordAudioMsg{}
	_ = json.Unmarshal([]byte(msgDataStr), &msgData)
	err := mq.DealCallRecordTextTransform(ctx, msgData)
	base.RenderJsonSucc(ctx, err)
	return
}

func Demo3(ctx *gin.Context) {
	msgDataStr := `{"abstract":"用户购买了作业帮四年级学习礼盒。学生的语数英期中考试成绩均为90多分，语文失分主要在阅读理解部分。老师指导用户进行小测试，制定了后续材料发送计划。","callid":"qw_2135228215_846","deviceNumberIn":"2730990767","deviceNumberOut":"4421859141","tag":"callAbs-qiwei","tags":[{"tagkey":"校内考试成绩","taginfo":"语文 90多, 数学 90多, 英语 90多","tagvalue":"[{\"科目\":\"语文\",\"成绩\":90,\"满分\":100},{\"科目\":\"数学\",\"成绩\":90,\"满分\":100},{\"科目\":\"英语\",\"成绩\":90,\"满分\":100}]","tagtype":"2","sid":"2","remark":"老师:嗯美琪宝贝是吧，嗯，嗯号码发了嗯好嘞行然后我给您说一下就是我第一次带咱们宝贝嘛对他之前的情况也不是很了解，所以呢给咱植入了一个小测试，一会儿呢您让宝贝自己单独做你不要教他我看一下他现在的一个学习能力好吧嗯好好嗯嗯就是咱们那个测试你知道在哪做吗，在哪呃在那个作业帮直播课P您手机上有没有下载呀，哦没有，哦那你的手机是那个华为还是苹果还是小米哦华为华为您点开那个应用市场，您搜索那个作业帮直播课，哦好嗯，嗯作业帮直播课你看一下是不是一个绿色的软件上面有一个课本的课字，哦看到了啊看到了然后您点那个下载就行，在下载嗯在下载是吧嗯，行您先让他下载中然后我给您了解一下咱们孩子一个成绩，目前咱们这个期中考试是考过了对吧，考过了嗯咱们那个语数英大概是多少分呀，呃，九十多这样，嗯，均分都是九十多是吗，对啊哦咱们那个基础能力还挺不错的，嗯，那您有分析他那个数学或者语文他，那个失分点那个失分是在哪里哪哪个部分最，最，最扣分最多吗，哦他这次，是，语文，就，差了一点，他说，嗯那个，呃，就是那个，作文那个，哦，那个阅读理解那，阅读那一大块是吧，那那我明白了啊好的，那稍后呢我针对他阅读这一块呢语文方面我再给他针对性的找一些资料好吧，嗯，嗯，行，咱们那个软件下载好了吗，看一下嗯好嘞，如果下载好的话您用那个，呃尾号是四二三七的这个手机号直接登录啊。","hideflag":""},{"tagkey":"学科薄弱点","taginfo":"语文 阅读理解","tagvalue":"[{\"科目\":\"语文\",\"学科薄弱点\":\"阅读理解\"}]","tagtype":"2","sid":"2","remark":"老师:嗯美琪宝贝是吧，嗯，嗯号码发了嗯好嘞行然后我给您说一下就是我第一次带咱们宝贝嘛对他之前的情况也不是很了解，所以呢给咱植入了一个小测试，一会儿呢您让宝贝自己单独做你不要教他我看一下他现在的一个学习能力好吧嗯好好嗯嗯就是咱们那个测试你知道在哪做吗，在哪呃在那个作业帮直播课P您手机上有没有下载呀，哦没有，哦那你的手机是那个华为还是苹果还是小米哦华为华为您点开那个应用市场，您搜索那个作业帮直播课，哦好嗯，嗯作业帮直播课你看一下是不是一个绿色的软件上面有一个课本的课字，哦看到了啊看到了然后您点那个下载就行，在下载嗯在下载是吧嗯，行您先让他下载中然后我给您了解一下咱们孩子一个成绩，目前咱们这个期中考试是考过了对吧，考过了嗯咱们那个语数英大概是多少分呀，呃，九十多这样，嗯，均分都是九十多是吗，对啊哦咱们那个基础能力还挺不错的，嗯，那您有分析他那个数学或者语文他，那个失分点那个失分是在哪里哪哪个部分最，最，最扣分最多吗，哦他这次，是，语文，就，差了一点，他说，嗯那个，呃，就是那个，作文那个，哦，那个阅读理解那，阅读那一大块是吧，那那我明白了啊好的，那稍后呢我针对他阅读这一块呢语文方面我再给他针对性的找一些资料好吧，嗯，嗯，行，咱们那个软件下载好了吗，看一下嗯好嘞，如果下载好的话您用那个，呃尾号是四二三七的这个手机号直接登录啊。","hideflag":""},{"tagkey":"在家时间","taginfo":"六点半回家","tagvalue":"六点半回家","tagtype":"0","sid":"0","remark":"老师:喂诶您好咱是给孩子二十九元买了一个作业帮的学习礼盒对吧给您确认一下是四年级的宝贝对吧，对呀嗯呃我是咱们这个学习学习资料的辅导老师资料的话已经给咱们安排发货了中通快递您大概三天左右就可以收到哈，哦好嗯但是这个资料呢包含了四年级的整个学年的一个知识所以呢好多那个母题大招都是他都是要配套的那个课程去讲解，孩子先学会了技巧与方法再去做会更轻松的，嗯，跟您说一下就是因为现在孩子分到了我的班里以后宝贝有任何不懂不会的问题您都可以让他来问我我单独给宝贝讲解，好好嗯嗯对然后呢您现在先别挂就是我带您添加一下我的企业微信稍后呢我把所有的课程资料还有那个学习的呃学习讲义都发您微信上，嗯，嗯嗯现在，现在这个手机能加到你微信吗嗯你能加但是呢你加的是我的个人微信我们公司统一要求要用企业微信的所以您花一分钟的时间我带着您加一下我的企业微信好吧，嗯行您现在打开那个微信右上角有一个加号对不对，嗯等一下等一下嗯好嘞不着急哈，哎宝贝现在放学了吗，还没有哦，咱们咱们放学挺晚的，他六点半才回来六点放学然后他要跳舞，哦有兴趣班是吧，呃，学校选了他跳舞哦，咱们还多才多艺的，宝宝，呃就是，添加朋友对添加朋友点开之后最下面那一行是企业微信联系人对吧，微信联系对，嗯您看到那个最下面那个企业微信联系人点开之后我跟您说一下我的企业微信号，是幺九零，三六九五，幺，三七嗯对幺九零三六九五，三七八二，三七，八二，对然后您点那个添加看一下是不是作业帮的一个头像幺九零三六九五三七八二嗯对的对的宝贝妈妈，嗯，您搜一下是不是一个作业帮的头像，对对对哎是作业帮是吧那您点添加我这边给您通过一下，哦，发了嗯行好嘞我看一下啊，哎双降是咱们是吧对啊对呀哎行我给咱们通过一下，诶您把那个宝贝的那个全名，或者是小名都可以，还有您买礼盒的那个手机号给我发一下我给咱们备注好以防之后给您发资料的时候发错了，嗯嗯，名字跟电话号码对对你买礼盒的那个手机号。","hideflag":""}],"timestamp":1732011947,"todo":{"todoitem":"整理语文阅读资料并发送","remindertime":""}}`
	msgData := mq.CallRecordTextAbstractMsg{}
	_ = json.Unmarshal([]byte(msgDataStr), &msgData)
	err := mq.DealCallRecordAIResult(ctx, msgData, "callAbs-qiwei")
	base.RenderJsonSucc(ctx, err)
	return
}
