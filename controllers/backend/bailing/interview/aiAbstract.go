package interview

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/service/interview"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
)

type AiAbstractParam struct {
	WxCallParam []interview.AiAbstractParamItem `json:"wxCallParam" form:"wxCallParam"`
	CallParam   []interview.AiAbstractParamItem `json:"callParam" form:"callParam"`
}

type AiAbstractResp struct {
	WxCallData map[int64]string `json:"wxCallData" form:"wxCallData"`
	CallData   map[int64]string `json:"callData" form:"callData"`
}

func AiAbstract(ctx *gin.Context) {
	param := AiAbstractParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	if len(param.WxCallParam) > 10 || len(param.CallParam) > 10 {
		base.RenderJsonFail(ctx, fmt.Errorf("单次查询不能超过 10 条"))
		return
	}

	resp := AiAbstractResp{
		WxCallData: make(map[int64]string),
		CallData:   make(map[int64]string),
	}

	//获取 ai 摘要
	if len(param.CallParam) > 0 {
		abstract, err := interview.GetCallInterviewAiAbstract(ctx, param.CallParam)
		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}
		resp.CallData = abstract
	}

	if len(param.WxCallParam) > 0 {
		reqList := make([]aiturbo.PostSelectAbstractReq, 0)
		for _, wxCallRecord := range param.WxCallParam {
			tmpReq := aiturbo.PostSelectAbstractReq{
				CallId:    fmt.Sprintf("qw_%d_%d", wxCallRecord.StudentUid, wxCallRecord.CallId),
				TeacherId: utils2.ToString(wxCallRecord.AssistantUid),
			}
			reqList = append(reqList, tmpReq)
		}

		//去AI摘要查询摘要信息
		abstractList, err := aiturbo.BatchPostSelectAbstract(ctx, reqList)
		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}

		for _, item := range param.WxCallParam {
			callStr := fmt.Sprintf("qw_%d_%d", item.StudentUid, item.CallId)
			resp.WxCallData[item.CallId] = abstractList[callStr].AbstractContent.Abstract
		}
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
