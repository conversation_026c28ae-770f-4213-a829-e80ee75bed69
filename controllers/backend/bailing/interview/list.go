package interview

import (
	"assistantdeskgo/service/interview"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type listParam struct {
	Uid          int64 `json:"studentUid" form:"studentUid"`
	CourseId     int64 `json:"courseId" form:"courseId"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
	LastTime     int64 `json:"lastTime" form:"lastTime"`
	PageSize     int   `json:"pageSize" form:"pageSize"`
	IsMyself     int   `json:"isMyself" form:"isMyself"`
}

func List(ctx *gin.Context) {
	param := listParam{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	if param.IsMyself == 0 {
		param.AssistantUid = 0
	}
	//获取列表数据
	list, err := interview.GetAllInterviewList(ctx, param.Uid, param.CourseId, param.AssistantUid, param.LastTime, param.PageSize)
	if err != nil {
		zlog.Warn(ctx, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, struct {
		List []interview.InterviewRecord `json:"list"`
	}{
		List: list,
	})
}
