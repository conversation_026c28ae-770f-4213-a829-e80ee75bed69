package feedbackv2

import (
	"assistantdeskgo/components"
	dtofeedbackv2 "assistantdeskgo/dto/feedbackv2"
	"assistantdeskgo/service/feedbackv2"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func FeedbackData(ctx *gin.Context) {
	req := dtofeedbackv2.FeedbackDataReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "FeedbackData params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := feedbackv2.FeedbackData(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "feedbackv2.FeedbackData error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
