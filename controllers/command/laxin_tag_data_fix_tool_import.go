package command

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"os"
	"strings"
	"time"
)

const REDIS_LAXIN_TAG_DATA_FIX_TASK_NAME = "assistantdeskgo:laxin_tag_data_fix_task"
const TASK_INTERVAL_LAXIN_TAG_FIX = 300

func LaxinTagDataFixTaskFallBack(ctx *gin.Context) error {
	clusterName := os.Getenv("CLUSTER_NAME")
	zlog.Infof(ctx, "当前所在集群,env:%s", clusterName)
	if env.GetRunEnv() == env.RunEnvTips || strings.Contains(clusterName, defines.ENV_STABLE_KEY) || strings.Contains(clusterName, defines.ENV_SMALL_KEY) {
		zlog.Infof(ctx, "当前所在集群跳过,env:%s", clusterName)
		return nil
	}

	lock, err := helpers.RedisClient.SetNxByEX(ctx, REDIS_LAXIN_TAG_DATA_FIX_TASK_NAME, 1, uint64(TASK_INTERVAL_LAXIN_TAG_FIX))
	if err != nil {
		zlog.Warnf(ctx, "Get Redis Lock fail,err=%v", err)
		return nil
	}
	if !lock {
		zlog.Infof(ctx, "Not acquired lock,skip")
		return nil
	}

	zlog.Infof(ctx, "start LaxinTagDataFixTaskFallBack ...")

	processLaxinFixTask(ctx) // 处理异常任务

	return nil
}

func processLaxinFixTask(ctx *gin.Context) {
	zlog.Infof(ctx, "start processLaxinFixTask ...")
	var err error

	// 处理一小时内的异常任务
	var lastID int64
	batchSize := 500
	oneHourEarlier := time.Now().Add(-1 * time.Hour).Unix()

	for {
		var batch []*models.LaxinTagDataFixTask
		batch, err = models.LaxinTagDataFixTaskDao.GetListByCreateTime(ctx, lastID, oneHourEarlier)
		if err != nil {
			return
		}

		for _, task := range batch {
			createTime := time.Unix(task.CreateTime, 0)
			threeMinuteAgo := time.Now().Add(-3 * time.Minute)
			halfHourAgo := time.Now().Add(-1 * 30 * time.Minute)

			switch task.Status {
			case models.LaxinTagDataFixTaskStatusPending:
				if createTime.Before(threeMinuteAgo) {
					// 一分钟还没消费
					err = handleLaxinTagFixExceptionTask(ctx, task.ID)
					if err != nil {
						return
					}
				}
			case models.LaxinTagDataFixTaskStatusInProgress:
				if createTime.Before(halfHourAgo) {
					// 半小时还没处理完还没消费
					err = handleLaxinTagFixExceptionTask(ctx, task.ID)
					if err != nil {
						return
					}
				}
			default:
				continue
			}
		}

		if len(batch) < batchSize {
			break
		}

		lastID = batch[len(batch)-1].ID
	}
	return
}

func handleLaxinTagFixExceptionTask(ctx *gin.Context, taskId int64) error {
	// get
	task, err := models.LaxinTagDataFixTaskDao.GetByTaskID(ctx, taskId)
	if err != nil {
		return err
	}
	if task.ID == 0 || !utils.InArrayInt(task.Status, []int{
		models.LaxinTagDataFixTaskStatusPending, int(models.LaxinTagDataFixTaskStatusInProgress),
	}) {
		zlog.Warnf(ctx, "handleLaxinTagFixExceptionTask return,task:%v,%v,status:%v", taskId, task.ID, task.Status)
		return nil
	}

	// update status to init
	err = models.LaxinTagDataFixTaskDao.UpdateByMap(ctx, taskId, map[string]interface{}{
		"status": models.LaxinTagDataFixTaskStatusPending,
	})
	if err != nil {
		return err
	}

	// send msg
	data := map[string]interface{}{
		"taskId": taskId,
	}

	zlog.Infof(ctx, "sendLaXinTagFixTaskMQ, param:%+v", data)
	res, err := rmq.SendCmd(ctx, "self_product", 214202, "core", "zb", data, "")
	if err != nil {
		zlog.Warnf(ctx, "sendLaXinTagFixTaskMQ err, taskId:%+v,msgId:%+v,err:%v", taskId, res, err)
		return err
	}
	return nil
}
