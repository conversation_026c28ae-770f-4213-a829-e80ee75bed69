package command

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	duxuescDao "assistantdeskgo/models/duxuesc"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	utils2 "github.com/emirpasic/gods/utils"
	"github.com/gin-gonic/gin"
	"math"
	"strconv"
	"strings"
	"time"
)

type SyncConfig struct {
	Stop     int64   `json:"stop"`
	Skip     []int64 `json:"skip"`
	Interval int64   `json:"interval"`
	Batch    int64   `json:"batch"`
}

const REDIS_AI_SYNC_LAST_TIME_KEY = "assistantdeskgo:ai_sync_create_time_v1"

func AiSync(ctx *gin.Context, args ...string) error {
	var config SyncConfig
	err := duxuescDao.ConfigDao.GetConfigByKey(ctx, defines.Config_AI_SYNC_LAST_TIME_KEY, &config, nil)
	if err != nil {
		return err
	}

	zlog.Infof(ctx, "Aisync Config=%+v", config)

	if config.Stop == 1 {
		zlog.Infof(ctx, "Aisync finish return")
		return nil
	}

	start := time.Now().Unix()
	for true {
		err = process(ctx, config)
		time.Sleep(time.Duration(config.Interval) * time.Second)
		if time.Now().Unix()-start > 240 {
			return nil
		}
	}
	if err != nil {
		return err
	}

	return nil
}

func process(ctx *gin.Context, config SyncConfig) error {
	lastCreateTime := int64(math.MaxInt64)
	get, err := helpers.RedisClient.Get(ctx, REDIS_AI_SYNC_LAST_TIME_KEY)
	if err != nil {
		return err
	}
	if get != nil {
		lastCreateTime, err = strconv.ParseInt(string(get), 10, 64)
		if err != nil {
			return err
		}
	}

	taskList, err := models.AiCallRecordTaskRef.ListOrderByCreateTime(ctx, lastCreateTime, config.Batch)
	if err != nil {
		return err
	}
	newTaskList := make([][]models.AiCallRecord, 20)
	if len(taskList) != 0 {
		for _, task := range taskList {
			newTaskList[task.ToUid%20] = append(newTaskList[task.ToUid%20], models.AiCallRecordRef.From2(task))
			lastCreateTime = task.CreateTime
		}

	}
	batchCnt := 0
	for _index, newTask := range newTaskList {
		insertCall := []models.AiCallRecord{}
		callIdList := utils.Int64Collect(newTask, "CallId")
		inList, err := models.AiCallRecordRef.ListByCallIdIn(ctx, callIdList, int64(_index))
		if err != nil {
			return err
		}
		inCallIdList := utils.Int64Collect(inList, "CallId")
		for _, call := range newTask {
			if utils.InArrayInt64(call.CallId, inCallIdList) {
				continue
			}
			if strings.Trim(call.Content, " ") != "" {
				uploadContentName := fmt.Sprintf("%v_%v", defines.AI_BUSSINE_CALL_TYPE, call.CallId)
				uploadContentNameFileName := fmt.Sprintf("%v.txt", uploadContentName)
				_link, err := helpers.BaiduBucket2.UploadFileContent(ctx, call.Content, uploadContentName, "txt")
				if err != nil {
					return err
				}
				zlog.Infof(ctx, "UploadFileContent callId=%v,file=%v,link=%v", call.CallId, uploadContentNameFileName, _link)
				call.Content = uploadContentNameFileName
			}
			insertCall = append(insertCall, call)
		}
		if len(insertCall) == 0 {
			continue
		}
		err = models.AiCallRecordRef.BatchInsert(ctx, insertCall, int64(_index))
		if err != nil {
			return err
		}
		batchCnt += len(insertCall)

	}
	err = helpers.RedisClient.Set(ctx, REDIS_AI_SYNC_LAST_TIME_KEY, utils2.ToString(lastCreateTime), defines.SECOND_OF_7DAY)
	if err != nil {
		zlog.Warnf(ctx, "UPDATE Redis fail,err=%v", err)
		return err
	}
	zlog.Infof(ctx, "Sync data success,batchCount=%v,lastTime=%v", batchCnt, lastCreateTime)
	return nil
}
