package command

import (
	"assistantdeskgo/service/backend/allocate"
	"assistantdeskgo/service/csv"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func ExcelTask(ctx *gin.Context, args ...string) error {
	tickMinute(ctx, func(ctx *gin.Context) error {
		return csv.ExcelTask(ctx)
	}, 5)
	return nil
}

func NewLeadNotice(ctx *gin.Context, args ...string) error {
	tickMinute(ctx, func(ctx *gin.Context) error {
		return allocate.NewLeadMessageNotice(ctx)
	}, 5)
	return nil
}


func tickMinute(ctx *gin.Context, f func(ctx *gin.Context) error, collapse int) {
	begin := time.Now().Minute()
	for {
		current := time.Now().Minute()
		if current < begin || current-begin >= collapse {
			return
		}
		err := f(ctx)
		if err != nil {
			zlog.Warnf(ctx, "tick err:%+v", err)
		}
		time.Sleep(3000 * time.Millisecond)
	}
}

func tickMinute30(ctx *gin.Context, f func(ctx *gin.Context) error, collapse int) {
	begin := time.Now().Minute()
	for {
		current := time.Now().Minute()
		if current < begin || current-begin >= collapse {
			return
		}
		err := f(ctx)
		if err != nil {
			zlog.Warnf(ctx, "tick err:%+v", err)
		}
		time.Sleep(30 * time.Second)
	}
}

func tickHour(ctx *gin.Context, f func(ctx *gin.Context) error) {
	begin := time.Now().Hour()
	for {
		current := time.Now().Hour()
		if begin != current {
			return
		}
		err := f(ctx)
		if err != nil {
			zlog.Warnf(ctx, "tickHour err:%+v", err)
		}
		time.Sleep(1000 * time.Millisecond)
	}
}
