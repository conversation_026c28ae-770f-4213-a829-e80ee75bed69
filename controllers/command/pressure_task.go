package command

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/models"
	"assistantdeskgo/service/tool/pressure"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"os"
	"strings"
)

func PressureTask(ctx *gin.Context) error {
	zlog.Infof(ctx, "当前所在集群,env:%s", env.GetRunEnv())
	// 为防止大促导数据相关影响线上服务，只在tips跑数据
	clusterName := os.Getenv("CLUSTER_NAME")
	if strings.Contains(clusterName, defines.ENV_ONLINE_KEY) {
		zlog.Infof(ctx, "当前所在集群跳过,env:%s", env.GetRunEnv())
		return nil
	}

	tasks := GetLastRecord(ctx)
	err := ProcessTask(ctx, tasks)
	if err != nil {
		return err
	}

	return nil
}

func ProcessTask(ctx *gin.Context, tasks []models.PressureTask) error {
	var err error
	var exportCsvUrl string
	for _, task := range tasks {
		if !pressure.CanGetLock(ctx, task.TaskType) {
			continue
		}

		err = models.PressureTaskObj.Process(ctx, task.Id)
		if err != nil {
			return err
		}
		exec, err := pressure.GetExecTask(task.TaskType)
		if err != nil {
			_ = models.PressureTaskObj.Fail(ctx, task.Id, err.Error())
			pressure.DelLock(ctx, task.TaskType)
			continue
		}
		exportCsvUrl, err = exec.ExportExcelUrl(ctx, task)
		if err != nil {
			_ = models.PressureTaskObj.Fail(ctx, task.Id, err.Error())
			pressure.DelLock(ctx, task.TaskType)
			continue
		}

		err = models.PressureTaskObj.Success(ctx, task.Id, exportCsvUrl)
		if err != nil {
			return err
		}
		pressure.DelLock(ctx, task.TaskType)
	}
	return nil
}

func GetLastRecord(ctx *gin.Context) (tasks []models.PressureTask) {
	pressureTaskObj := models.PressureTask{
		TaskType: models.PressureTaskTypeCreateOrder,
		Status:   models.PressureTaskStatusCreate,
	}
	task, err := pressureTaskObj.GetLastRecord(ctx)
	if err == nil && task.Id != 0 {
		// 有，则加入
		tasks = append(tasks, task)
	}

	pressureTaskObj.TaskType = models.PressureTaskTypeWxStudentList
	task, err = pressureTaskObj.GetLastRecord(ctx)
	if err == nil && task.Id != 0 {
		// 有，则加入
		tasks = append(tasks, task)
	}

	pressureTaskObj.TaskType = models.PressureTaskTypeInClass
	task, err = pressureTaskObj.GetLastRecord(ctx)
	if err == nil && task.Id != 0 {
		// 有，则加入
		tasks = append(tasks, task)
	}

	return
}
