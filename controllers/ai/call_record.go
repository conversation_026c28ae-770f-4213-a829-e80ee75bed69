package ai

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoai"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/ai"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func StudentCallRecord(ctx *gin.Context) {
	req := dtoai.StudentCallRecordRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "StudentCallRecord params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "StudentCallRecord loginFail error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return
	}

	rsp, err := ai.StudentCallRecord(ctx, int64(loginDeviceInfo.DeviceUid), req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)

}

func AddFeedBack(ctx *gin.Context) {
	req := dtoai.AddFeedBackRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AddFeedBack params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "AddFeedBack loginFail error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return
	}

	err = ai.AddFeedBack(ctx, req, int(loginDeviceInfo.DeviceUid))
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, true)
}

func GetFeedBack(ctx *gin.Context) {
	req := dtoai.GetFeedBackRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetFeedBack params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	rsp, err := ai.GetFeedBack(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func AddCallRecord(ctx *gin.Context) {
	type params struct {
		CallId       int64 `json:"callId" form:"callId"`
		OnlyAbstract int64 `json:"onlyAbstract" form:"onlyAbstract"`
	}
	req := params{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetFeedBack params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := ai.AddCallRecord(ctx, req.CallId, req.OnlyAbstract)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, nil)
}

func AddCallRecordV2(ctx *gin.Context) {
	type params struct {
		CallId int64 `json:"callId" form:"callId"`
	}
	req := params{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetFeedBack params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := ai.AddCallRecordV2(ctx, req.CallId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, nil)
}

func ListCallId(ctx *gin.Context) {
	type params struct {
		CreateTime int64 `json:"createTime" form:"createTime"`
		Size       int   `json:"size" form:"size"`
	}
	req := params{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetFeedBack params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	callIdList, createTime, err := ai.ListCallId(ctx, req.CreateTime, req.Size)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, map[string]interface{}{
		"callIdList":     callIdList,
		"lastCreateTime": createTime,
	})
}
