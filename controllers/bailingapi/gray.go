package bailingapi

import (
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/bailingapi"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GrayHit(ctx *gin.Context) {
	var params struct {
		Key string `json:"key" form:"key"`
	}

	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warn(ctx, "param err: ", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	ipsId := middleware.GetIpsUserInfo(ctx)

	isGray := bailingapi.GrayHit(ctx, params.Key, ipsId)

	base.RenderJsonSucc(ctx, gin.H{
		"isGray": isGray,
	})
}
