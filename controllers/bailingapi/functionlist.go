package bailingapi

import (
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/bailingapi"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func FunctionList(ctx *gin.Context) {
	var params struct {
		Type         int64  `json:"Type" form:"type"`
		CorpId       string `json:"corpId" form:"corpId"`
		UserId       string `json:"userId" form:"userId"`
		AssistantUid int64  `json:"assistantUid" form:"assistantUid"`
		PersonUid    int64  `json:"personUid" form:"personUid"`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warn(ctx, "param err: ", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	list, err := bailingapi.FunctionList(ctx, params.Type, params.CorpId, params.UserId, middleware.GetIpsUserInfo(ctx), params.AssistantUid)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, list)

}
