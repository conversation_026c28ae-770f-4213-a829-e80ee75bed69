package bailingapi

import (
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/bailingapi"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SetServerMode(ctx *gin.Context) {
	var params struct {
		Mode int64 `json:"mode" form:"mode"`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warn(ctx, "param err: ", err)
		base.RenderJsonFail(ctx, err)
		return
	}
	ipsId := middleware.GetIpsUserInfo(ctx)
	ipsName := middleware.GetIpsUserName(ctx)
	err := bailingapi.SetServerMode(ctx, ipsId, ipsName, params.Mode)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, nil)

}

func GetServerMode(ctx *gin.Context) {
	ipsId := middleware.GetIpsUserInfo(ctx)
	ipsName := middleware.GetIpsUserName(ctx)
	list, err := bailingapi.GetServerMode(ctx, ipsId, ipsName)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, list)

}
