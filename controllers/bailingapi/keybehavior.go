package bailingapi

import (
	"assistantdeskgo/components"
	"assistantdeskgo/service/bailingapi"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func KeyBehavior(ctx *gin.Context) {

	var param struct {
		StudentUid int64 `json:"studentUid" form:"studentUid"`
		CourseId   int64 `json:"courseId" form:"courseId"`
	}

	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Warnf(ctx, "param err:%s", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	if param.StudentUid <= 0 || param.CourseId <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	list, err := bailingapi.KeyBehavior(ctx, param.StudentUid, param.CourseId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, list)

}
