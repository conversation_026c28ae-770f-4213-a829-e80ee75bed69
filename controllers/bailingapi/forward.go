package bailingapi

import (
	"assistantdeskgo/components"
	"assistantdeskgo/middleware"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/render"
	utils2 "gorm.io/gorm/utils"
	"net/http"
	"net/http/httputil"
	"net/url"
	"time"
)

const proxyModuleHeader = "X-PROXY-MODULE"
const proxyIpsIdHeader = "X-PROXY-IPS-UID"
const proxyIpsNameHeader = "X-PROXY-IPS-NAME"

//type entryWrapper struct {
//	domain        string
//	needCheckAuth bool
//}

//func proxy2BackendService() map[string]entryWrapper {
//	proxyMap := map[string]entryWrapper{}
//	for _, ent := range conf.Custom.ApiProxy {
//		for _, service := range ent.Entries {
//			proxyMap[service] = entryWrapper{
//				domain:        ent.Domain,
//				needCheckAuth: true,
//			}
//		}
//		for _, service := range ent.EntriesNoIps {
//			proxyMap[service] = entryWrapper{
//				domain:        ent.Domain,
//				needCheckAuth: false,
//			}
//		}
//	}
//	return proxyMap
//}

func Forward(ctx *gin.Context) {
	targetUrl := middleware.GetProxyTargetUrl(ctx)
	proxyConfWrapper, ok := middleware.Proxy2BackendService()[targetUrl]
	if !ok {
		base.RenderJsonFail(ctx, base.Error{ErrNo: components.ErrorParamInvalid.ErrNo, ErrMsg: "不支持该请求"})
		return
	}
	ur, err := url.Parse(proxyConfWrapper.Domain)
	if err != nil {
		return
	}
	start := time.Now()
	fields := []zlog.Field{
		zlog.String(zlog.TopicType, zlog.LogNameModule),
		zlog.String("prot", "http"),
		zlog.String("scheme", ur.Scheme),
		zlog.String("service", proxyConfWrapper.Service),
		zlog.String("method", ctx.Request.Method),
		zlog.String("domain", proxyConfWrapper.Domain),
		zlog.String("requestUri", ctx.Request.RequestURI),
		zlog.String("targetUri", targetUrl),
		zlog.String("requestStartTime", utils.GetFormatRequestTime(start)),
	}

	defer func() {
		zlog.InfoLogger(ctx, "proxy done", fields...)
	}()
	proxy := &httputil.ReverseProxy{
		Director: func(req *http.Request) {

			req.Host = ur.Host
			req.URL.Scheme = ur.Scheme
			req.URL.Host = ur.Host
			req.URL.Path = targetUrl

			//copy for golib/base/http.go
			req.Header.Set(base.HttpHeaderService, env.AppName)
			req.Header.Set(zlog.TraceHeaderKey, zlog.GetRequestID(ctx))
			req.Header.Set(zlog.LogIDHeaderKey, zlog.GetLogID(ctx))
			req.Header.Set(zlog.LogIDHeaderKeyLower, zlog.GetLogID(ctx))
			req.Header.Set(proxyModuleHeader, "assistantdeskgo")
			req.Header.Set(proxyIpsIdHeader, utils2.ToString(middleware.GetIpsUserInfo(ctx)))
			req.Header.Set(proxyIpsNameHeader, middleware.GetIpsUserName(ctx))

		},
		ModifyResponse: func(r *http.Response) error {
			end := time.Now()
			fields = append(fields,
				zlog.Int("httpCode", r.StatusCode),
				zlog.String("requestEndTime", utils.GetFormatRequestTime(end)),
				zlog.Float64("cost", utils.GetRequestCost(start, end)),
				zlog.Int("ralCode", calRalCode(r)),
			)
			//下游服务返回非200
			if r.StatusCode != http.StatusOK {
				return fmt.Errorf("backend response %d of %s", r.StatusCode, r.Status)
			}
			return nil
		},
		ErrorHandler: func(rw http.ResponseWriter, req *http.Request, err error) {
			zlog.WarnLogger(ctx,
				"proxy response error: "+err.Error(),
				zlog.String(zlog.TopicType, zlog.LogNameModule),
				zlog.String("domain", req.Host),
				zlog.String("requestUri", req.URL.Path),
			)
			rw.WriteHeader(http.StatusOK)
			ctx.Header("X_BD_UPS_ERR_NO", "-1")
			ctx.Header("X_BD_UPS_ERR_MSG", "backend service is not available")
			ctx.Header("Request-Id", zlog.GetLogID(ctx))
			_ = render.WriteJSON(rw, base.DefaultRender{ErrNo: -1, ErrMsg: "backend service is not available", Data: gin.H{}})
		},
	}
	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

func calRalCode(resp *http.Response) int {
	if resp == nil || resp.StatusCode >= 400 || resp.StatusCode == 0 {
		return -1
	}
	return 0
}
