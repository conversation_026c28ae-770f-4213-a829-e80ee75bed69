package bailingapi

import (
	"assistantdeskgo/service/bailingapi"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func KpOnline(ctx *gin.Context) {
	var params struct {
		WebCorpId string `json:"webCorpId" form:"webCorpId"`
		UserId    string `json:"userId" form:"userId"`
	}

	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warn(ctx, "param err: ", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	data, err := bailingapi.KpOnline(ctx, params.UserId, params.WebCorpId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, data)
}
