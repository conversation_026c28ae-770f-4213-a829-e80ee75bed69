package bailingapi

import (
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/bailingapi"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CheckUserInfo(ctx *gin.Context) {
	var params struct {
		CorpId string `json:"corpId" form:"corpId"`
		UserId string `json:"userId" form:"userId"`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warn(ctx, "param err: ", err)
		base.RenderJsonFail(ctx, err)
		return
	}
	ipsId := middleware.GetIpsUserInfo(ctx)
	ipsName := middleware.GetIpsUserName(ctx)
	isValid, err := bailingapi.CheckUserInfo(ctx, ipsId, ipsName, params.CorpId, params.UserId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, map[string]interface{}{
		"isValid": isValid,
	})

}
