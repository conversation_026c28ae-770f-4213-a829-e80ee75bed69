package mq

import (
	"assistantdeskgo/service/backend/touch"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	TouchCallTime = "271308" // 外呼时间

)

func TouchLpcMsg(ctx *gin.Context, msg rmq.Message) error {
	zlog.Info(ctx, "TouchLpcMsg got message id=", msg.GetID(), " tag=", msg.GetTag(), " header=", msg.GetHeader("Time"))
	switch msg.GetTag() {
	case TouchCallTime:
		params := touch.TouchCallTimeData{}
		err := DecodeMcpack(ctx, msg, &params)
		if err != nil {
			return err
		}
		return touch.TouchCallTime(ctx, params)
	}
	return nil
}
