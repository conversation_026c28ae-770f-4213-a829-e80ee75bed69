package mq

import (
	"assistantdeskgo/service/delayer"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	CallTimeDelayerMessage = "218108"
)

func DelayerTaskConsumer(ctx *gin.Context, msg rmq.Message) (err error) {
	zlog.Info(ctx, "got message id=", msg.GetID(), " tag=", msg.GetTag(), " time=", msg.GetHeader("Time"))

	var input delayer.DelayerTaskMessage
	if err = DecodeMcpack(ctx, msg, &input); err != nil {
		return err
	}
	inputStr, _ := jsoniter.MarshalToString(input)
	zlog.Infof(ctx, "[DelayerTaskConsumer] got delayer task message,  id: %s,  shard: %s, tag: %s, retry: %d, content: %s", msg.GetID(), msg.GetShard(), msg.GetTag(), msg.GetRetry(), inputStr)

	switch msg.GetTag() {
	case CallTimeDelayerMessage:
		return input.Consumer(ctx)
	}

	return
}
