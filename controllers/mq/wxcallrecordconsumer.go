package mq

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/transcribe"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

type WxCallMessageData struct {
	UserID         string `json:"userId" mcpack:"userId"`                 // 上报用户企业微信userId
	CorpID         string `json:"corpId" mcpack:"corpId"`                 // 上报用户组织id'
	CallUserID     string `json:"callUserId" mcpack:"callUserId"`         //录音用户企业微信userId
	CallCorpID     string `json:"callCorpId" mcpack:"callCorpId"`         //录音用户上报用户组织id
	TalkMode       int64  `json:"talkMode" mcpack:"talkMode"`             //会话模式 0单聊 1群聊
	MsgType        int64  `json:"msgType" mcpack:"msgType"`               //消息类型 16语音通话 17视频通话'
	IsSender       int64  `json:"isSender" mcpack:"isSender"`             //会话发起方 1发起方 2接收方
	OtherRole      int64  `json:"otherRole" mcpack:"otherRole"`           //聊天对象角色 1个微 2企微内部用户 3其他企业用户
	CallRemoteID   string `json:"callRemoteId" mcpack:"callRemoteId"`     //本人的remoteId
	OtherRemoteID  string `json:"otherRemoteId" mcpack:"otherRemoteId"`   //聊天对象的remoteId', 如果是群聊的话是群id
	RingTime       int64  `json:"ringTime" mcpack:"ringTime"`             //响铃时间, 时间戳
	CallTime       int64  `json:"callTime" mcpack:"callTime"`             //接通时间, 时间戳
	EndTime        int64  `json:"endTime" mcpack:"endTime"`               //结束时间, 时间戳
	CallStatus     int64  `json:"callStatus" mcpack:"callStatus"`         //通话状态 0初始化(异常) 1正常结束 2未接通 3拒接 4取消
	Duration       int64  `json:"duration" mcpack:"duration"`             //通话时长(仅供参考)
	UploadProgress int64  `json:"uploadProgress" mcpack:"uploadProgress"` //录音文件上传状态 0没有录音文件 1初始化bos文件地址 10客户端上传文件成功 11客户端上传文件失败
	LocalFile      string `json:"localFile" mcpack:"localFile"`           //客户端本地录音文件名称
	RecordFile     string `json:"recordFile" mcpack:"recordFile"`         //录音文件
	CreateTime     int64  `json:"createTime" mcpack:"createTime"`         //创建时间
	UpdateTime     int64  `json:"updateTime" mcpack:"updateTime"`         //更新时间
	CallID         int64  `json:"callId" mcpack:"callId"`                 //自增id
	CallStaffUID   int64  `json:"callStaffUid" mcpack:"callStaffUid"`     //???
	OtherStaffUID  int64  `json:"otherStaffUid" mcpack:"otherStaffUid"`   //???
	AudioURL       string `json:"audioUrl" mcpack:"-"`                    //录音url
	CommandNo      int64  `json:"command_no" mcpack:"command_no"`         //命令号
	Role           int64  `json:"role" mcpack:"role"`                     //业务线
	PressMark      int64  `json:"_press_mark" mcpack:"_press_mark"`       //业务线
}

func WxCallRecord(ctx *gin.Context, msg rmq.Message) error {
	//消费通话记录
	var msgData WxCallMessageData
	zlog.Infof(ctx, "got message id=%v,tag=%v,header=%v", msg.GetID(), msg.GetTag(), msg.GetHeader("Time"))
	if msg.GetTag() != "182007" {
		return nil
	}
	err := DecodeMcpack(ctx, msg, &msgData)
	if err != nil {
		return err
	}
	return DealWxCallRecord(ctx, msgData)
}

func DealWxCallRecord(ctx *gin.Context, msgData WxCallMessageData) error {

	zlog.Infof(ctx, "MQ_WxCall msgContent=%+v", msgData)
	if msgData.PressMark == 1 {
		zlog.Infof(ctx, "skip preessMark data,call_id=%v", msgData.CallID)
		return nil
	}
	if msgData.CallID <= 0 {
		zlog.Warnf(ctx, "WxCall callId is 0,data=%d", msgData.CallID)
		return nil
	}
	//资产老师ID
	deviceInfo, err := mesh.GetDeviceInfoListByDeviceUids(ctx, msgData.CallStaffUID)
	if err != nil {
		zlog.Warnf(ctx, "WxCall GetDeviceInfoListByDeviceUids fail,callId=%v,err=%v", msgData.CallID, err)
		return err
	}
	if deviceInfo.StaffUid <= 0 {
		zlog.Infof(ctx, "WxCall GetDeviceInfoListByDeviceUids not find,deviceUid=%v", msgData.CallID, msgData.CallStaffUID)
		return nil
	}

	studentWxInfo, err := kunpeng.GetUidByWxId(ctx, getKpReq(msgData.CallStaffUID, msgData.OtherRemoteID))
	if err != nil {
		zlog.Warnf(ctx, "WxCall GetUidByWxId fail,callId=%v,remoteId=%v,err=%v", msgData.CallID, msgData.OtherRemoteID, err)
		return err
	}
	if len(studentWxInfo) == 0 || studentWxInfo[0].StudentUid <= 0 {
		zlog.Infof(ctx, "WxCall GetUidByWxId is empty,callId=%v,remoteId=%v", msgData.CallID, msgData.OtherRemoteID)
		return err
	}
	studentUid := studentWxInfo[0].StudentUid

	helpers.WxCallRecordConsumerLimiter.Take()

	wxCallRecord, err := models.WxCallRecordRef.GetByCallId(ctx, msgData.CallID)
	if err != nil {
		zlog.Warnf(ctx, "WxCall GetByCallId fail,callId=%v,err=%v", msgData.CallID, err)
		return err
	}
	callRecordModel, update := getWxCallRecordModel(wxCallRecord, msgData, studentUid, int64(deviceInfo.StaffUid))
	if update {
		err = models.WxCallRecordRef.Update(ctx, callRecordModel)
	} else {
		err = models.WxCallRecordRef.Insert(ctx, callRecordModel)
	}
	if err != nil {
		zlog.Warnf(ctx, "WxCall Update or Insert fail,callId=%v,err=%v", msgData.CallID, err)
		return err
	}
	zlog.Infof(ctx, "WxCall add success,callId=%v", msgData.CallID)

	//过滤掉10s以内的通话记录
	if msgData.Duration <= 10 {
		return nil
	}
	//双写微信语音通话记录表新表
	//查询是否存在记录
	recordModel := &models.WxCallRecordNew{}
	recordNew, err := recordModel.GetByCallId(ctx, msgData.CallID, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "WxCallNew GetByCallId fail,callId=%d,err=%s", msgData.CallID, err.Error())
		return err
	}
	//查询例子信息
	stuLeads, err := allocate.GetLeadsByUid(ctx, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "allocate GetByCallId fail,studentUid=%d,err=%s", studentUid, err.Error())
		return err
	}
	//从学员例子信息获取课程ID
	zlog.Infof(ctx, "leadsList%+v,assistantUid%d", stuLeads, deviceInfo.DeviceId)
	courseId := allocate.GetCourseIdByLeads(stuLeads, deviceInfo.DeviceId)
	callRecordNewModel, update := getWxCallRecordNewModel(recordNew, msgData, studentUid, int64(deviceInfo.StaffUid))
	callRecordNewModel.CourseId = courseId
	if update {
		err = recordModel.Update(ctx, studentUid, callRecordNewModel)
	} else {
		err = recordModel.Insert(ctx, studentUid, callRecordNewModel)
	}
	if err != nil {
		zlog.Warnf(ctx, "WxCall Update or Insert fail,callId=%v,err=%v", msgData.CallID, err)
		return err
	}
	url, err := kunpeng.GetFileUrl(ctx, msgData.RecordFile)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio getLink Fail,callId=%v,err=%v", msgData.CallID, err)
		return err
	}
	if url == "" {
		zlog.Warnf(ctx, "processPostAudio getLink is empty,callId=%v", msgData.CallID)
		return nil
	}

	reqList := []transcribe.AudioReqModel{}
	reqList = append(reqList, transcribe.AudioReqModel{
		defines.AI_BUSSINE_WX_TYPE + "_" + strconv.FormatInt(studentUid, 10) + "_" + strconv.FormatInt(msgData.CallID, 10),
		url,
		msgData.Duration,
	})
	err = transcribe.ReqTranscribe(ctx, reqList)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio ReqTranscribe fail,callId=%v", msgData.CallID)
		return err
	}

	return nil
}

func getWxCallRecordModel(wxCallRecord models.WxCallRecord, msgData WxCallMessageData, studentUid int64, staffUid int64) (models.WxCallRecord, bool) {
	update := true
	now := time.Now().Unix()
	if wxCallRecord.CallId <= 0 {
		wxCallRecord.CallId = msgData.CallID
		wxCallRecord.CreateTime = now
		update = false
	}
	if msgData.IsSender == 1 { //老师是发起方
		wxCallRecord.FromUid = msgData.CallStaffUID
		wxCallRecord.ToUid = studentUid
		wxCallRecord.CallType = msgData.IsSender
	} else {
		wxCallRecord.FromUid = studentUid
		wxCallRecord.ToUid = msgData.CallStaffUID
		wxCallRecord.CallType = msgData.IsSender
	}
	wxCallRecord.StudentRemoteId = msgData.CallRemoteID
	wxCallRecord.StartTime = msgData.CallTime
	wxCallRecord.StopTime = msgData.EndTime
	wxCallRecord.DeviceUid = msgData.CallStaffUID
	wxCallRecord.PersonUid = staffUid
	wxCallRecord.Duration = msgData.Duration
	wxCallRecord.MsgType = msgData.MsgType
	wxCallRecord.CallResult = msgData.CallStatus
	wxCallRecord.RecordFile = msgData.RecordFile
	wxCallRecord.UpdateTime = now
	return wxCallRecord, update
}

func getWxCallRecordNewModel(wxCallRecordNew models.WxCallRecordNew, msgData WxCallMessageData, studentUid int64, staffUid int64) (models.WxCallRecordNew, bool) {
	update := true
	now := time.Now().Unix()
	if wxCallRecordNew.CallId <= 0 {
		wxCallRecordNew.CallId = msgData.CallID
		wxCallRecordNew.CreateTime = now
		update = false
	}
	if msgData.IsSender == 1 { //老师是发起方
		wxCallRecordNew.FromUid = msgData.CallStaffUID
		wxCallRecordNew.ToUid = studentUid
		wxCallRecordNew.CallType = msgData.IsSender
		wxCallRecordNew.FromRemoteId = msgData.CallRemoteID
		wxCallRecordNew.ToRemoteId = msgData.OtherRemoteID
	} else {
		wxCallRecordNew.FromUid = studentUid
		wxCallRecordNew.ToUid = msgData.CallStaffUID
		wxCallRecordNew.CallType = msgData.IsSender
		wxCallRecordNew.FromRemoteId = msgData.OtherRemoteID
		wxCallRecordNew.ToRemoteId = msgData.CallRemoteID
	}
	wxCallRecordNew.StudentUid = studentUid
	wxCallRecordNew.StartTime = msgData.CallTime
	wxCallRecordNew.StopTime = msgData.EndTime
	wxCallRecordNew.DeviceUid = msgData.CallStaffUID
	wxCallRecordNew.PersonUid = staffUid
	wxCallRecordNew.Duration = msgData.Duration * 1000
	wxCallRecordNew.MsgType = msgData.MsgType
	wxCallRecordNew.CallResult = msgData.CallStatus
	wxCallRecordNew.RecordFile = msgData.RecordFile
	wxCallRecordNew.Status = 1 //语音上传成功信令默认语音转文字中状态
	wxCallRecordNew.UpdateTime = now

	return wxCallRecordNew, update
}

func getKpReq(deviceUid int64, remoteId string) kunpeng.GetUidByWxIdReq {
	req := kunpeng.GetUidByWxIdReq{}
	req.StaffUid = deviceUid
	req.WxIds = []string{remoteId}
	req.AppId = 2
	return req
}

func getCourseIdByLeads(leadsList []allocate.StuLeadsInfo, assistantUid int64) int64 {
	var courseId int64
	if len(leadsList) <= 0 {
		return courseId
	}
	for _, leadsInfo := range leadsList {
		if leadsInfo.UserId == assistantUid {
			courseId = leadsInfo.CourseId
			break
		}
	}
	return courseId
}
