package mq

import (
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/muse"
	"assistantdeskgo/api/plum"
	"assistantdeskgo/api/touchmis"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/backend/touch"
	commonservice "assistantdeskgo/service/common"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	DbRetryLimit        = 3             // 未查询到任务时重试次数
	RetryWaitTime       = 300           // 未查询到任务时等待时长 单位ms
	VirtualPhone        = "***********" // 虚拟手机号
	EnableCallTimeLimit = 600           // 10分钟内可外呼

	AutoCallTouchInfoCacheKey    = "assistantdeskgo_autocall_info_"
	AutoCallTouchInfoCacheExpire = 5 * 60

	GenKeTouchmisIvrSwitchConf = "genke_touchmis_ivr_switch_conf"
)

type AutoCallMsg struct {
	UniqueId int64 `json:"uniqueId"`
}

type TouchInfo struct {
	BusinessLine  int64 `json:"businessLine"`
	DepartmentId  int64 `json:"departmentId"`
	TemplateId    int64 `json:"templateId"`
	MainSubjectId int64 `json:"mainSubjectId"`
}

func AutoCall(ctx *gin.Context, msg rmq.Message) error {
	zlog.Info(ctx, "got message id=", msg.GetID(), " tag=", msg.GetTag(), " header=", msg.GetHeader("Time"))

	var input AutoCallMsg
	if err := DecodeMcpack(ctx, msg, &input); err != nil {
		return err
	}
	zlog.Infof(ctx, "[autoCallConsumer] got sms message id: %s, shard: %s, tag: %s, retry: %d, content: %+v", msg.GetID(), msg.GetShard(), msg.GetTag(), msg.GetRetry(), input)

	if err := AutoCallSend(ctx, input.UniqueId, msg.GetRetry()); err != nil {
		return err
	}
	return nil
}

func AutoCallSend(ctx *gin.Context, uniqueId int64, retry int) (err error) {
	var (
		autoCallRecord *models.AutoCallRecord // 跟课外呼记录
		locked         bool                   // 是否成功加锁
		callRet        *muse.CallOutRsq       // 外呼结果
		touchInfo      *TouchInfo             // 外呼模板、学部等信息
		toPhone        string                 // 呼叫手机号
	)
	if autoCallRecord, err = GetAutoCallRecord(ctx, uniqueId); err != nil {
		return err
	}

	// 如果命中灰度则关闭消费，走 touchmisgo 的消费
	staffId, err := commonservice.GetStaffUid(ctx, autoCallRecord.AssistantUid)
	if err != nil {
		return err
	}
	matchGenKeTouchIvrSwitch, _ := touchmisgo.GrayHit(ctx, touchmisgo.GrayHitRequest{
		PersonUid: staffId,
		Key:       GenKeTouchmisIvrSwitchConf,
	})
	if matchGenKeTouchIvrSwitch {
		zlog.Infof(ctx, "命中灰度，消费逻辑迁移至 touchmisgo 服务")
		return nil
	}

	if autoCallRecord.ID == 0 {
		if retry > 3 { // 重试次数大于3时触发报警
			zlog.Warnf(ctx, "get_auto_call_record_failed, uniqueId: %d", uniqueId)
		}
		return errors.New("自动外呼任务查询失败")
	}

	// 超过10分钟后停止外呼
	if time.Now().Unix()-autoCallRecord.CreateTime > EnableCallTimeLimit {
		zlog.Warnf(ctx, "auto_call_delay, uniqueId: %d", uniqueId)
		return nil
	}

	// 流控
	helpers.AutoCallConsumerLimiter.Take()

	//抢占自动外呼任务
	if locked, err = lockRecord(ctx, autoCallRecord); err != nil {
		zlog.Warnf(ctx, "auto_call_add_lock_failed 自动外呼任务抢占失败, uniqueId: %d, err: %+v", uniqueId, err)
		return err
	}

	if !locked { // 抢占失败时, 已被其他任务消费, 无需重试
		zlog.Warnf(ctx, "auto_call_add_lock_failed 自动外呼任务抢占失败, uniqueId: %d", uniqueId)
		return nil
	}

	// 获取触达基础信息
	if touchInfo, err = getTouchInfo(ctx, autoCallRecord); err != nil { // 避免单条任务阻塞整个队列
		zlog.Warnf(ctx, "auto_call_get_tplId_failed 获取外呼模板失败, uniqueId: %d, err: %+v", uniqueId, err)
		return nil
	}
	// 未获取到外呼模板时, 不下发外呼任务
	if touchInfo.TemplateId == 0 {
		zlog.Warnf(ctx, "auto_call_get_tplId_failed 获取外呼模板失败, uniqueId: %d", uniqueId)
		return nil
	}

	if toPhone, err = helpers.Kms.Decrypt(ctx, autoCallRecord.KmsToPhone); err != nil || len(toPhone) == 0 {
		zlog.Warnf(ctx, "kms_decrypt_toPhone failed 解析外呼手机号失败, autoCallRecord: %+v, phone:%s, toPhone: %s", autoCallRecord, autoCallRecord.KmsToPhone, toPhone)
		if toPhone, err = touchmis.Decrypt(ctx, autoCallRecord.KmsToPhone); err != nil || len(toPhone) == 0 {
			zlog.Warnf(ctx, "touchmis_decrypt_toPhone failed 解析外呼手机号失败, toPhone: %s, err: %+v", toPhone, err)
		}
	}

	if len(toPhone) == 0 {
		return nil
	}
	params := muse.CallOutReq{
		CallID:       uniqueId,
		FromPhone:    VirtualPhone,
		ToPhone:      toPhone,
		CourseID:     autoCallRecord.CourseId,
		DepartmentID: touchInfo.DepartmentId,
		DeviceUID:    autoCallRecord.AssistantUid,
		SubjectID:    touchInfo.MainSubjectId,
		CallMode:     defines.CallOutModeAUTO,
		TemplateID:   touchInfo.TemplateId,
		Line:         touchInfo.BusinessLine,
		ExtData:      "[]", // 下游此字段不能为空
	}

	callRet, err = muse.CallOut(ctx, params)
	sendStatus := models.AutoCallSucceedCalling
	if err != nil {
		zlog.Warnf(ctx, "auto_call_failed, request muse callOut failed, params: %+v, callRet: %+v, err: %+v", params, callRet, err)
		sendStatus = models.AutoCallSucceedSend
	}
	if affected, dbErr := models.AutoCallRecordRef.UpdateRecordSucceed(ctx, autoCallRecord.ID, int64(sendStatus)); dbErr != nil || affected == 0 {
		zlog.Warnf(ctx, "auto_call_updateStatus_failed, sendStatus: %d, params: %+v, callRet: %+v, affected: %d, err: %+v", sendStatus, params, callRet, affected, err)
	}
	return nil
}

func lockRecord(ctx *gin.Context, autoCallRecord *models.AutoCallRecord) (bool, error) {
	if autoCallRecord.Succeed != models.AutoCallSucceedWaiting {
		return false, nil
	}

	affected, err := models.AutoCallRecordRef.UpdateRecordSucceed(ctx, autoCallRecord.ID, models.AutoCallSucceedGet)
	if err != nil {
		return false, err
	}

	return affected > 0, nil
}

func getTouchInfo(ctx *gin.Context, autoCallRecord *models.AutoCallRecord) (info *TouchInfo, err error) {
	// 从缓存中获取模板信息
	var data []byte
	info = &TouchInfo{
		BusinessLine: defines.BusinessLineForFuDao, // 默认辅导
	}
	cacheKey := fmt.Sprintf("%s%d", AutoCallTouchInfoCacheKey, autoCallRecord.UniqueId)
	if data, err = helpers.RedisClient.Get(ctx, cacheKey); err == nil {
		if len(data) > 0 {
			err = json.Unmarshal(data, info)
			if err != nil || info.MainSubjectId == 0 || info.TemplateId == 0 || info.DepartmentId == 0 {
				zlog.Warnf(ctx, "[getTouchInfo] get cache data failed, data: %s, info: %+v err: %+v", string(data), info, err)
			} else {
				return info, nil
			}
		}
	}

	var (
		courseInfo     dal.CourseLessonInfo            // 课程信息
		touchInfo      *plum.GetTouchInfoByCourseIdRsp // 触达学部等信息
		coursePriceTag int64                           // 服务模式
		courseBaseInfo *tower.GetCourseInfoRsp         // 课程基础信息
		templateList   []touch.TemplateInfo            // 模板列表
		templateId     int64                           // 模板ID
	)
	courseFields := []string{"courseId", "courseName", "mainSubjectId", "isOwnPackage", "source", "mainGradeId"}
	lessonFields := []string{"lessonId"}
	// 获取课程信息
	if courseInfo, err = dal.GetCourseLessonInfoByCourseId(ctx, autoCallRecord.CourseId, courseFields, lessonFields); err != nil {
		zlog.Warnf(ctx, "auto_call_dal_getCourseInfo_failed 获取课程信息失败, courseId: %d, err: %+v", autoCallRecord.CourseId, err)
		return nil, err
	}

	if courseInfo.CourseId == 0 {
		zlog.Warnf(ctx, "auto_call_getCourseInfo_failed 获取课程信息失败, courseId: %d", autoCallRecord.CourseId)
		return nil, nil
	}
	info.MainSubjectId = courseInfo.MainSubjectId

	// 权益包业务线及学部固定(历史逻辑)
	if courseInfo.IsOwnPackage == 1 {
		info.DepartmentId = 90 // 低幼
		coursePriceTag = 160   // 合约服务模式默认为160
	} else {
		if touchInfo, err = plum.GetTouchInfoByCourseId(ctx, autoCallRecord.CourseId); err != nil {
			return nil, err
		}
		info.DepartmentId = touchInfo.DepartmentId
		info.BusinessLine = touchInfo.BusinessLine

		if courseBaseInfo, err = tower.GetCourseInfo(ctx, autoCallRecord.CourseId); err != nil || courseBaseInfo.CourseID == 0 {
			zlog.Warnf(ctx, "auto_call_getCourseInfo_failed 获取课程性质失败, courseId: %d, courseBaseInfo:%+v", autoCallRecord.CourseId, courseBaseInfo)
			return nil, err
		}
		coursePriceTag = courseBaseInfo.CoursePriceTag
	}

	// 获取外呼模板
	if templateList, err = touch.GetTouchTemplateList(ctx, defines.TouchConfActionIvr, courseInfo.Source, courseInfo.MainGradeId, courseInfo.MainSubjectId, coursePriceTag); err != nil {
		zlog.Warnf(ctx, "auto_call_GetTouchTemplateList_failed 获取外呼模板配置, courseId: %d, coursePriceTag:%d", autoCallRecord.CourseId, coursePriceTag)
		return nil, err
	}

	for _, templateInfo := range templateList {
		if templateInfo.Id > 0 {
			templateId = templateInfo.Id
			break // 返回第一个模板
		}
	}
	if templateId == 0 {
		zlog.Warnf(ctx, "GetTouchTemplateList_failed 未匹配到外呼模板, courseId: %d, coursePriceTag:%d", autoCallRecord.CourseId, coursePriceTag)
		return
	}
	info.TemplateId = templateId

	// 写入缓存
	data, err = json.Marshal(info)
	if err != nil {
		zlog.Warnf(ctx, "[getTouchInfo] marshal cache data err, info: %+v, err: %+v", info, err)
	} else {
		_ = helpers.RedisClient.Set(ctx, cacheKey, string(data), AutoCallTouchInfoCacheExpire)
	}
	return info, nil
}

func GetAutoCallRecord(ctx *gin.Context, uniqueId int64) (autoCallRecord *models.AutoCallRecord, err error) {
	autoCallRecord, err = models.AutoCallRecordRef.GetRecordByUniqueId(ctx, uniqueId)
	if err != nil {
		return nil, err
	}

	// 主从延迟
	if autoCallRecord.ID == 0 {
		for i := 0; i < DbRetryLimit; i++ {
			time.Sleep(time.Millisecond * RetryWaitTime)
			autoCallRecord, err = models.AutoCallRecordRef.GetRecordByUniqueId(ctx, uniqueId)
			if err != nil {
				return nil, err
			}

			if autoCallRecord.ID > 0 {
				break
			}
		}
	}
	return autoCallRecord, nil
}
