package mq

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/api/dal"
	"assistantdeskgo/api/dau"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/api/tower"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/ai"
	"assistantdeskgo/service/gray"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"
)

//接受语音转写结果

type CallRecordAudioMsg struct {
	AudioId   string `json:"audio_id" mapstructure:"audio_id"`
	ResultUrl string `json:"result_url" mapstructure:"result_url"`
	Result    string `json:"result" mapstructure:"result"`
	RawParam  string `json:"raw_param" mapstructure:"raw_param"`
}

type CallRecordAudioResultDataMsg struct {
	Id           string `json:"id"`
	SentenceList []struct {
		Result struct {
			Hypotheses []struct {
				Confidence float64 `json:"confidence"`
				Transcript string  `json:"transcript"`
			} `json:"hypotheses"`
		} `json:"result"`
		KeywordLength int     `json:"keyword-length"`
		WordNum       int     `json:"word-num"`
		SegmentLength float64 `json:"segment-length"`
		SegmentStart  float64 `json:"segment-start"`
		Speed         float64 `json:"speed"`
		SpkId         int     `json:"spk_id"`
		Role          string  `json:"role"`
	} `json:"SentenceList"`
	TotalTime float64 `json:"TotalTime"`
	Status    int     `json:"status"`
	TransTime float64 `json:"trans_time"`
}

type RawParam struct {
	AudioId string `json:"audio_id"`
}

type Sentence struct {
	SentenceId int     `json:"sentence_id"`
	StartTime  float64 `json:"start_time"`
	EndTime    float64 `json:"end_time"`
	Role       int     `json:"role"`
	Content    string  `json:"content"`
}

func CallRecordTextTransform(ctx *gin.Context, msg rmq.Message) error {
	var msgData CallRecordAudioMsg
	zlog.Infof(ctx, "got message id=%v,tag=%v,header=%v,msg=%v", msg.GetID(), msg.GetTag(), msg.GetHeader("Time"), string(msg.GetContent()))
	err := json.Unmarshal(msg.GetContent(), &msgData)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordAudioResult Unmarshal fail,err=%v,data=%v", err, string(msg.GetContent()))
		return err
	}
	return DealCallRecordTextTransform(ctx, msgData)
}

func DealCallRecordTextTransform(ctx *gin.Context, msgData CallRecordAudioMsg) error {

	zlog.Infof(ctx, "MQ_CallRecordAudioMsg=%+v", msgData)
	var rawParam RawParam
	err := json.Unmarshal([]byte(msgData.RawParam), &rawParam)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordAudioResult Param Unmarshal fail,data=%+v", msgData)
		return err
	}

	helpers.AiCallAudioConsumerLimiter.Take()

	//获取callId
	split := strings.Split(rawParam.AudioId, "_")
	if len(split) <= 0 {
		zlog.Warnf(ctx, "consume AudioResult audio_id err ,audioId=%s", rawParam.AudioId)
		return nil
	}

	//判断是否存在wx部分，如果有，更新微信语音通话记录表，如果没有，更新电话通话记录
	if split[2] == "wx" {
		err = dealWxCallAiAbstract(ctx, rawParam.AudioId, msgData)
	} else {
		err = dealCallAiAbstract(ctx, rawParam.AudioId, msgData)
	}
	return err
}

func dealWxCallAiAbstract(ctx *gin.Context, audioId string, msgData CallRecordAudioMsg) error {
	split := strings.Split(audioId, "_")
	callIdStr := split[len(split)-1]
	studentUidStr := split[len(split)-2]
	callId, _ := strconv.ParseInt(callIdStr, 10, 64)
	studentUid, _ := strconv.ParseInt(studentUidStr, 10, 64)

	wxCallRecordModel := &models.WxCallRecordNew{}
	wxCallRecord, err := wxCallRecordModel.GetByCallId(ctx, callId, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "wxCallRecordTextTransform GetByCallId fail,callId=%v,msg=%v,err=%v", callId, msgData, err)
		return err
	}
	if wxCallRecord.CallId == 0 {
		zlog.Warnf(ctx, "wxCallRecordTextTransform GetByCallId is empty,callId=%v,msg=%v", callId, msgData)
		return nil
	}
	//获取语音结果
	msgResultData, err := getAudioData(ctx, msgData, callIdStr)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform getAudioData fail,callId=%v,err=%v", callId, err)
		return err
	}
	//语音结果转文本存数据库
	sentenceList, sentenceListStr := getAudioContent(msgResultData)
	wxCallRecord.Content = string(sentenceListStr)
	wxCallRecord.Status = models.STATUS_CALL_AUDIO_FINISH
	if wxCallRecord.Duration <= 10*time.Second.Milliseconds() {
		//小于10s标已结束
		wxCallRecord.Status = models.STATUS_FINISH
	}
	if len(sentenceList) == 0 {
		wxCallRecord.Status = models.STATUS_EXPIRE
	}

	//查询学员信息
	//var studentInfo dau.StudentInfo
	studentMap, err := dau.GetStudents(ctx, []int64{studentUid}, []string{"studentName"})
	if err != nil {
		zlog.Warnf(ctx, "WxCallRecordTextTransform getStudentInfo fail,studentUid=%d,err=%s", studentUid, err.Error())
		return err
	}
	studentInfo, ok := studentMap[studentUid]
	if !ok {
		return nil
	}

	//更新通话记录状态
	err = wxCallRecordModel.Update(ctx, studentUid, wxCallRecord)
	if err != nil {
		zlog.Warnf(ctx, "WxCallRecordTextTransform Updates fail,studentUid=%d,err=%s", studentUid, err.Error())
		return err
	}
	if wxCallRecord.Status != models.STATUS_CALL_AUDIO_FINISH {
		return nil
	}
	//查询课程信息
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, wxCallRecord.CourseId, []string{}, []string{})
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform GetCourseLessonInfoByCourseId fail,courseId=%v,err=%v", wxCallRecord.CourseId, err)
		return err
	}
	coursePriceTag, err := tower.GetCourseInfo(ctx, courseInfo.CourseId)
	if err != nil {
		return err
	}

	if env.GetRunEnv() == env.RunEnvTips || gray.SwitchAbstractText(ctx, coursePriceTag.CoursePriceTag, defines.Config_AI_TEXT_ABSTRACT_GRAY) { //todo服务模式判断
		err = aiturbo.PostTextAbstractV3(ctx, getQwTextAbstractRequest(wxCallRecord, courseInfo, sentenceList, studentInfo, coursePriceTag.CoursePriceTag))
	} else {
		err = aiturbo.PostTextAbstractV2(ctx, getQwTextAbstractRequest(wxCallRecord, courseInfo, sentenceList, studentInfo, 0))
	}
	return nil
}

func dealCallAiAbstract(ctx *gin.Context, audioId string, msgData CallRecordAudioMsg) error {
	now := time.Now().Unix()

	split := strings.Split(audioId, "_")
	if strings.HasPrefix(audioId, defines.AI_BUSSINE_CALL_TYPE) {
		callIdStr := split[len(split)-1]
		studentUidStr := split[len(split)-2]
		callId, err := strconv.ParseInt(callIdStr, 10, 64)
		if err != nil {
			return errors.New("callId:" + callIdStr)
		}
		studentUid, err := strconv.ParseInt(studentUidStr, 10, 64)
		if err != nil {
			return errors.New("studentUid:" + studentUidStr)
		}

		return dealCallAiAbstractV2(ctx, callId, studentUid, now, msgData)
	}
	callId := split[len(split)-1]
	task, err := models.AiCallRecordTaskRef.GetByCallId(ctx, callId)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform GetByCallId fail,callId=%v,msg=%v,err=%v", callId, msgData, err)
		return err
	}
	if task.CallId == "" {
		zlog.Warnf(ctx, "CallRecordTextTransform GetByCallId is empty,callId=%v,msg=%v", callId, msgData)
		return nil
	}
	//获取语音结果
	msgResultData, err := getAudioData(ctx, msgData, callId)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform getAudioData fail,callId=%v,err=%v", callId, err)
		return err
	}
	zlog.Infof(ctx, "AiCallRecord_Delay_Text_Transform:%v", now-task.UpdateTime)
	//语音结果转文本存数据库
	sentenceList, sentenceListStr := getAudioContent(msgResultData)
	task.Content = string(sentenceListStr)
	task.Status = models.STATUS_CALL_AUDIO_FINISH
	task.TransformTime = now
	task.UpdateTime = now
	if task.Duration <= 10*time.Second.Milliseconds() {
		//小于10s标已结束
		task.Status = models.STATUS_FINISH
	}
	if len(sentenceList) == 0 {
		task.Status = models.STATUS_EXPIRE
	}

	err = models.AiCallRecordTaskRef.Updates(ctx, []models.AiCallRecordTask{task})
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform Updates fail,task=%v,err=%v", task, err)
		return err
	}

	if task.Status != models.STATUS_CALL_AUDIO_FINISH {
		zlog.Infof(ctx, "CallRecordTextTransform skip not valid call,callId=%v,status=%v", callId, task.Status)
		return nil
	}

	info, err := mesh.GetDeviceInfoListByDeviceUids(ctx, task.DeviceUid)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform GetDeviceInfoListByDeviceUids fail,deviceUid=%v,err=%v", task.DeviceUid, err)
		return err
	}

	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, task.CourseId, []string{}, []string{})
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform GetCourseLessonInfoByCourseId fail,courseId=%v,err=%v", task.CourseId, err)
		return err
	}

	coursePriceTag, err := tower.GetCourseInfo(ctx, courseInfo.CourseId)
	if err != nil {
		return err
	}

	if env.GetRunEnv() == env.RunEnvTips || gray.SwitchAbstractText(ctx, coursePriceTag.CoursePriceTag, defines.Config_AI_TEXT_ABSTRACT_GRAY) {
		err = aiturbo.PostTextAbstractV3(ctx, getTextAbstractRequest(task, info, courseInfo, sentenceList, coursePriceTag.CoursePriceTag))
	} else {
		err = aiturbo.PostTextAbstractV2(ctx, getTextAbstractRequest(task, info, courseInfo, sentenceList, 0))
	}
	if err != nil {
		return err
	}

	zlog.Infof(ctx, "CallRecordTextTransform PostTextAbstract Success callId=%v", task.CallId)
	err = models.AiCallRecordTaskRef.Updates(ctx, []models.AiCallRecordTask{getAiCallTask(callId)})
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform Update fail,callId=%v,err=%v", task.CallId, err)
		return err
	}
	return nil
}

func dealCallAiAbstractV2(ctx *gin.Context, callId int64, uid int64, now int64, msgData CallRecordAudioMsg) error {
	callInfo, err := models.AiCallRecordRef.GetByCallId(ctx, callId, uid)
	if err != nil {
		return err
	}
	if callInfo.CallId <= 0 {
		zlog.Warnf(ctx, "dealCallAiAbstractV2 GetByCallId is empty,callId=%v,msg=%v", callId, msgData)
		return nil
	}
	//获取语音结果
	msgResultData, err := getAudioData(ctx, msgData, utils2.ToString(callId))
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform getAudioData fail,callId=%v,err=%v", callId, err)
		return err
	}

	//语音结果转文本存数据库
	sentenceList, sentenceListStr := getAudioContent(msgResultData)
	//上传bos
	uploadContentName := fmt.Sprintf("%v_%v", defines.AI_BUSSINE_CALL_TYPE, callId)
	uploadContentNameFileName := fmt.Sprintf("%v.txt", uploadContentName)
	uploadFileLink, err := helpers.BaiduBucket2.UploadFileContent(ctx, string(sentenceListStr), uploadContentName, "txt")
	if err != nil {
		return err
	}
	zlog.Infof(ctx, "UploadFileContent success,link=%v", uploadFileLink)
	callInfo.Content = uploadContentNameFileName
	callInfo.Status = models.STATUS_CALL_AUDIO_FINISH
	callInfo.UpdateTime = now
	if callInfo.Duration <= 10*time.Second.Milliseconds() {
		//小于10s标已结束
		callInfo.Status = models.STATUS_FINISH
	}
	if len(sentenceList) == 0 {
		callInfo.Status = models.STATUS_EXPIRE
	}

	err = models.AiCallRecordRef.Updates(ctx, []models.AiCallRecord{callInfo}, uid)
	if err != nil {
		zlog.Warnf(ctx, "dealCallAiAbstractV2 Updates fail,task=%v,err=%v", callInfo, err)
		return err
	}

	oldCallInfo, err := models.AiCallRecordTaskRef.GetByCallId(ctx, utils2.ToString(callId))
	if err != nil {
		return err
	}
	if oldCallInfo.CallId != "" {
		oldCallInfo.Content = string(sentenceListStr)
		oldCallInfo.Status = callInfo.Status
		oldCallInfo.UpdateTime = now
		err = models.AiCallRecordTaskRef.Updates(ctx, []models.AiCallRecordTask{oldCallInfo})
		if err != nil {
			zlog.Warnf(ctx, "dealCallAiAbstractV2 Updates old fail,task=%v,err=%v", callInfo, err)
			return err
		}
	}

	if callInfo.Status != models.STATUS_CALL_AUDIO_FINISH {
		zlog.Infof(ctx, "dealCallAiAbstractV2 skip not valid call,callId=%v,status=%v", callId, callInfo.Status)
		return nil
	}

	info, err := mesh.GetDeviceInfoListByDeviceUids(ctx, callInfo.DeviceUid)
	if err != nil {
		zlog.Warnf(ctx, "dealCallAiAbstractV2 GetDeviceInfoListByDeviceUids fail,deviceUid=%v,err=%v", callInfo.DeviceUid, err)
		return err
	}

	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, callInfo.CourseId, []string{}, []string{})
	if err != nil {
		zlog.Warnf(ctx, "dealCallAiAbstractV2 GetCourseLessonInfoByCourseId fail,courseId=%v,err=%v", callInfo.CourseId, err)
		return err
	}
	coursePriceTag, err := tower.GetCourseInfo(ctx, courseInfo.CourseId)
	if err != nil {
		return err
	}
	if env.GetRunEnv() == env.RunEnvTips || gray.SwitchAbstractText(ctx, coursePriceTag.CoursePriceTag, defines.Config_AI_TEXT_ABSTRACT_GRAY) {

		err = aiturbo.PostTextAbstractV3(ctx, getTextAbstractRequestNew(callInfo, info, courseInfo, sentenceList, coursePriceTag.CoursePriceTag))
	} else {
		err = aiturbo.PostTextAbstractV2(ctx, getTextAbstractRequestNew(callInfo, info, courseInfo, sentenceList, 0))

	}
	if err != nil {
		return err
	}

	zlog.Infof(ctx, "dealCallAiAbstractV2 PostTextAbstract Success callId=%v", callInfo.CallId)
	err = models.AiCallRecordRef.UpdateStatus(ctx, []int64{callInfo.CallId}, models.STATUS_TEXT_ABSTRACT, callInfo.StudentUid)
	if err != nil {
		zlog.Warnf(ctx, "dealCallAiAbstractV2 Update fail,callId=%v,err=%v", callInfo.CallId, err)
		return err
	}
	return nil

}

func getAiCallTask(callId string) models.AiCallRecordTask {
	now := time.Now().Unix()
	updateTask := models.AiCallRecordTask{}
	updateTask.CallId = callId
	updateTask.Status = models.STATUS_TEXT_ABSTRACT
	updateTask.UpdateTime = now
	updateTask.AiCallTime = now
	return updateTask
}

func getAudioContent(msgResultData CallRecordAudioResultDataMsg) ([]Sentence, []byte) {
	sentenceList := []Sentence{}
	order := 0
	for _, data := range msgResultData.SentenceList {
		if len(data.Result.Hypotheses) == 0 {
			continue
		}
		sentence := Sentence{}
		sentence.SentenceId = order
		if data.Role == defines.CALL_RECORD_ROLE_TEACHER {
			sentence.Role = defines.CALL_RECORD_ROLE_TEACHER_VALUE
		} else {
			sentence.Role = defines.CALL_RECORD_ROLE_USER_VALUE
		}
		sentence.Content = data.Result.Hypotheses[0].Transcript
		sentence.StartTime = data.SegmentStart
		sentence.EndTime = data.SegmentStart + data.SegmentLength
		sentenceList = append(sentenceList, sentence)
		order = order + 1
	}
	sentenceListStr, _ := json.Marshal(sentenceList)
	return sentenceList, sentenceListStr
}

func getAudioData(ctx *gin.Context, msgData CallRecordAudioMsg, callId string) (CallRecordAudioResultDataMsg, error) {
	var audioResult []byte

	if msgData.Result == "" {
		//从cos拿数据
		resp, err := http.Get(msgData.ResultUrl)
		if err != nil {
			zlog.Warnf(ctx, "CallRecordTextTransform getResult For Url fail,callId=%v,err=%v", callId, err)
			return CallRecordAudioResultDataMsg{}, err
		}
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				zlog.Warnf(ctx, "Clod file fail,callId=%v,err=%v", callId, err)
			}
		}(resp.Body)

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			zlog.Warnf(ctx, "CallRecordTextTransform ReadAll For Url fail,callId=%v,err=%v", callId, err)
			return CallRecordAudioResultDataMsg{}, err
		}
		audioResult = body
	} else {
		audioResult = []byte(msgData.Result)
	}

	var msgResultData CallRecordAudioResultDataMsg
	err := json.Unmarshal(audioResult, &msgResultData)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordTextTransform Unmarshal result fail,result=%v,err=%v", msgData.Result, err)
		return CallRecordAudioResultDataMsg{}, err
	}
	return msgResultData, nil
}

func getQwTextAbstractRequest(task models.WxCallRecordNew, courseInfo dal.CourseLessonInfo, sentenceList []Sentence, studentInfo dau.StudentInfo, priceTag int64) aiturbo.PostTextAbstractReq {
	req := aiturbo.PostTextAbstractReq{}
	studentRemoteId := ""
	if task.CallType == 1 {
		studentRemoteId = task.ToRemoteId
	} else {
		studentRemoteId = task.FromRemoteId
	}
	req.Calltype = task.MsgType
	req.CallId = fmt.Sprintf("qw_%d_%d", task.StudentUid, task.CallId)
	req.Source = defines.AiTokenQw
	req.Token = defines.AiTokenQw
	req.DeviceNumber = []aiturbo.DeviceNumber{{Number: utils2.ToString(task.FromRemoteId), Type: "in"}, {Number: utils2.ToString(task.ToRemoteId), Type: "out"}}

	req.Xuebu = defines.Grade2DepartMap[courseInfo.MainGradeId]
	req.ConversationStartTime = strconv.FormatInt(task.StartTime, 10)
	req.Classname = courseInfo.CourseName
	req.StudentName = studentInfo.StudentName
	req.TeacherId = utils2.ToString(task.DeviceUid)
	req.StudentUid = utils2.ToString(task.StudentUid)
	req.StudentWeChatId = studentRemoteId
	req.CourseId = strconv.FormatInt(courseInfo.CourseId, 10)
	var contentReq []aiturbo.PostTextAbstractReqContent
	for _, sentence := range sentenceList {
		contentReq = append(contentReq, aiturbo.PostTextAbstractReqContent{
			Sentenceid: sentence.SentenceId,
			Content:    sentence.Content,
			Role:       sentence.Role,
		})

	}
	req.Contents = contentReq
	req.PriceTag = priceTag
	return req
}

func getTextAbstractRequest(task models.AiCallRecordTask, info mesh.DeviceInfo, courseInfo dal.CourseLessonInfo, sentenceList []Sentence, priceTag int64) aiturbo.PostTextAbstractReq {
	req := aiturbo.PostTextAbstractReq{}
	req.Calltype = task.CallMode
	req.CallId = task.CallId
	req.Source = defines.AI_TOKEN
	req.Token = defines.AI_TOKEN
	req.Topic = ai.GetTopic(task.SourceType)
	req.TeacherId = utils2.ToString(task.DeviceUid)
	req.StudentUid = utils2.ToString(task.ToUid)
	req.DeviceNumber = []aiturbo.DeviceNumber{{Number: utils2.ToString(task.FromUid), Type: "in"}, {Number: utils2.ToString(task.ToUid), Type: "out"}}
	if utils.InArrayInt(defines.ROLE_TEACHER_DUXUE, info.KpAscriptionList) { //督学给AI是1
		req.Teacherjs = defines.AI_TUBOR_TEACHER_DUXUE
	}

	req.Xuebu = defines.Grade2DepartMap[courseInfo.MainGradeId]
	req.Classname = courseInfo.CourseName
	req.ConversationStartTime = strconv.FormatInt(task.StartTime/1000, 10)
	req.CourseId = strconv.FormatInt(courseInfo.CourseId, 10)
	var contentReq []aiturbo.PostTextAbstractReqContent
	for _, sentence := range sentenceList {
		contentReq = append(contentReq, aiturbo.PostTextAbstractReqContent{
			Sentenceid: sentence.SentenceId,
			Content:    sentence.Content,
			Role:       sentence.Role,
		})

	}
	req.Contents = contentReq
	req.Token = defines.AI_TOKEN
	req.PriceTag = priceTag
	return req
}

func getTextAbstractRequestNew(task models.AiCallRecord, info mesh.DeviceInfo, courseInfo dal.CourseLessonInfo, sentenceList []Sentence, priceTag int64) aiturbo.PostTextAbstractReq {
	req := aiturbo.PostTextAbstractReq{}
	req.Calltype = task.CallMode
	req.CallId = utils2.ToString(task.CallId)
	req.Source = defines.AI_TOKEN
	req.Token = defines.AI_TOKEN
	req.Topic = ai.GetTopic(task.SourceType)
	req.TeacherId = utils2.ToString(task.DeviceUid)
	req.StudentUid = utils2.ToString(task.StudentUid)
	req.DeviceNumber = []aiturbo.DeviceNumber{{Number: utils2.ToString(task.DeviceUid), Type: "in"}, {Number: utils2.ToString(task.StudentUid), Type: "out"}}
	if utils.InArrayInt(defines.ROLE_TEACHER_DUXUE, info.KpAscriptionList) { //督学给AI是1
		req.Teacherjs = defines.AI_TUBOR_TEACHER_DUXUE
	}

	req.Xuebu = defines.Grade2DepartMap[courseInfo.MainGradeId]
	req.Classname = courseInfo.CourseName
	req.ConversationStartTime = strconv.FormatInt(task.StartTime/1000, 10)
	req.CourseId = strconv.FormatInt(courseInfo.CourseId, 10)
	var contentReq []aiturbo.PostTextAbstractReqContent
	for _, sentence := range sentenceList {
		contentReq = append(contentReq, aiturbo.PostTextAbstractReqContent{
			Sentenceid: sentence.SentenceId,
			Content:    sentence.Content,
			Role:       sentence.Role,
		})

	}
	req.Contents = contentReq
	req.Token = defines.AI_TOKEN
	req.PriceTag = priceTag
	return req
}
