package mq

import (
	"assistantdeskgo/service/scenetouch"
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SceneTouchPrepare(ctx *gin.Context, msg rmq.Message) (err error) {
	zlog.Info(ctx, "got message id=", msg.GetID(), " tag=", msg.GetTag(), " header=", msg.GetHeader("Time"))
	content := msg.GetContent()

	msgData, err := mcpack.Decode(msg.GetContent())
	if err != nil {
		zlog.Errorf(ctx, "can't decode mcpack msg; msgid: %s", msg.GetID())
		return nil
	}

	tag := msgData.GetString("_cmd", "")
	zlog.Info(ctx, "getTag:", tag)

	switch tag {
	case "214204":
		return scenetouch.HandleSceneTouchPrepare(ctx, msgData)
	}

	zlog.Debug(ctx, "SceneTouchPrepare content is ", string(content))

	return nil
}
