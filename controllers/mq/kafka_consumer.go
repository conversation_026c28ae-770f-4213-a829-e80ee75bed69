package mq

import (
	"errors"

	"git.zuoyebang.cc/pkg/golib/v2/kafka"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// 处理方法
func PrintOneHandler(ctx *gin.Context) error {
	msg, ok := kafka.GetKafkaMsg(ctx)
	if !ok {
		return errors.New("get empty msg")
	}
	zlog.Debug(ctx, "[PrintOneHandler] GetKafkaMsg: ", msg)

	// do something

	return nil
}

func PrintTwoHandler(ctx *gin.Context) error {
	msg, ok := kafka.GetKafkaMsg(ctx)
	if !ok {
		return errors.New("get empty msg")
	}

	zlog.Debugf(ctx, "[PrintTwoHandler] GetKafkaMsg: ", msg)

	// do something

	return nil
}
