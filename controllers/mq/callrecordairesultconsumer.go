package mq

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/longlinkService"
	"encoding/json"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
	"time"
)

type CallRecordTextAbstractMsg struct {
	CallId    string `json:"callid"`
	Tag       string `json:"tag"`
	Abstract  string `json:"abstract"`
	Tags      []Tags `json:"tags"`
	TimeStamp int64  `json:"timeStamp"`
	Todo      Todo   `json:"todo"`
}

type Todo struct {
	TodoItem     string `json:"todoitem"`
	ReminderTime string `json:"remindertime"`
}

type Tags struct {
	Label  string `json:"tagkey"`
	Value  string `json:"taginfo"`
	Remark string `json:"remark"`
}

func CallRecordAIResult(ctx *gin.Context, msg rmq.Message) error {
	var msgData CallRecordTextAbstractMsg
	zlog.Infof(ctx, "got message id=%v,tag=%v,header=%v,msg=%v", msg.GetID(), msg.GetTag(), msg.GetHeader("Time"), string(msg.GetContent()))
	err := json.Unmarshal(msg.GetContent(), &msgData)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordAIResult Unmarshal fail,err=%v,data=%v", err, string(msg.GetContent()))
		return err
	}
	zlog.Infof(ctx, "MQ_CallRecordAIResult msgContent=%+v", msgData)
	if msg.GetTag() != defines.AI_CALL_ABS_TAG && msg.GetTag() != defines.AI_CALL_ABS_QW_TAG {
		zlog.Infof(ctx, "MQ_CallRecordAIResult filter support:%v、%s,but is %v", defines.AI_CALL_ABS_TAG, defines.AI_CALL_ABS_QW_TAG, msgData.Tag)
		return nil
	}
	return DealCallRecordAIResult(ctx, msgData, msg.GetTag())
}

func DealCallRecordAIResult(ctx *gin.Context, msgData CallRecordTextAbstractMsg, msgTag string) error {

	helpers.AiCallAbstractConsumerLimiter.Take()

	if msgTag == defines.AI_CALL_ABS_TAG {
		return dealCallResult(ctx, msgData)
	} else if msgTag == defines.AI_CALL_ABS_QW_TAG {
		return dealQwCallResult(ctx, msgData)
	} else {
		zlog.Infof(ctx, "MQ_CallRecordAIResult filter support:%v、%s,but is %v", defines.AI_CALL_ABS_TAG, defines.AI_CALL_ABS_QW_TAG, msgData.Tag)
		return nil
	}
}

func dealQwCallResult(ctx *gin.Context, msgData CallRecordTextAbstractMsg) error {
	//查询通话记录
	msgId := msgData.CallId
	split := strings.Split(msgId, "_")
	callIdStr := split[len(split)-1]
	studentUidStr := split[len(split)-2]
	callId, _ := strconv.ParseInt(callIdStr, 10, 64)
	studentUid, _ := strconv.ParseInt(studentUidStr, 10, 64)
	model := models.WxCallRecordNew{}
	task, err := model.GetByCallId(ctx, callId, studentUid)
	if err != nil {
		return err
	}
	if task.CallId <= 0 {
		return nil
	}
	//记录待办
	var remindId int64
	/** 2025-03-19 由于待办数量过多导致线上问题，暂时下线待办的创建*/
	//if len(msgData.Todo.TodoItem) > 0 && task.CourseId > 0 {
	//	addReq := &muse.AddRemindReq{
	//		Uid:        task.PersonUid,
	//		AppType:    muse.RemindTypeAssistant,
	//		FromType:   muse.FromTypeAi,
	//		Content:    msgData.Todo.TodoItem,
	//		RemindTime: msgData.Todo.ReminderTime,
	//		RemindId:   0,
	//		Students: []muse.StudentInfo{
	//			{
	//				CourseId:   task.CourseId,
	//				StudentUid: task.StudentUid,
	//			},
	//		},
	//	}
	//	addRsp, err := muse.AddRemind(ctx, addReq)
	//	zlog.Infof(ctx, "[dealQwCallResult] addRemindSuccess-remindId=%d", addRsp.RemindId)
	//	if err != nil {
	//		zlog.Warnf(ctx, "[dealQwCallResult] AddRemindErr: %+v, addReq: %s", err, fwyyutils.MarshalIgnoreError(addReq))
	//		return err
	//	}
	//	remindId = addRsp.RemindId
	//}
	//更新通话记录
	updateData := models.WxCallRecordNew{
		Status:     4,
		UpdateTime: time.Now().Unix(),
		RemindId:   remindId,
	}
	model.Id = task.Id
	err = model.Update(ctx, studentUid, updateData)
	if err != nil {
		return err
	}

	//记录消息历史
	stuAiMessageModel := models.StuAiMessage{
		MsgType:      models.StuAiMessageType3,
		StudentUid:   studentUid,
		AssistantUid: task.DeviceUid,
		PersonUid:    task.PersonUid,
	}
	//查询下未读消息数
	count, err := stuAiMessageModel.Count(ctx, studentUid)
	if err != nil {
		return err
	}

	stuAiMessageModel.MsgId = callId
	stuAiMessageModel.Content = msgData.Abstract
	stuAiMessageModel.CreateTime = time.Now().Unix()
	err = stuAiMessageModel.Insert(ctx, studentUid)
	if err != nil {
		return err
	}

	//发送长链接消息通知
	resultId, err := longlinkService.SendInterviewMsg(ctx, stuAiMessageModel, task.StartTime, count+1)
	if err != nil {
		return err
	}
	zlog.Infof(ctx, "[dealQwCallResult] sendInterviewMsg-msgId=%d", resultId)

	return nil
}

func dealCallResult(ctx *gin.Context, msgData CallRecordTextAbstractMsg) error {
	callId, _ := strconv.ParseInt(msgData.CallId, 10, 64)
	if callId <= 0 {
		zlog.Warnf(ctx, "CallRecordAIResult CallId not support,callId=%v,msg=%+v", msgData.CallId, msgData)
		return errors.New("CallID异常")
	}
	touchmisCallInfo, err := getCallInfo(ctx, callId)
	if err != nil {
		zlog.Warnf(ctx, "CallRecordAIResult getCallInfo err,callId=%v,msg=%+v,err=%v", msgData.CallId, msgData, err)
		return err
	}

	newCallRecord, err := models.AiCallRecordRef.GetByCallId(ctx, touchmisCallInfo.CallId, touchmisCallInfo.ToUid)
	if err != nil {
		return err
	}

	oldCallRecord, err := models.AiCallRecordTaskRef.GetByCallId(ctx, msgData.CallId)
	if err != nil {
		return err
	}

	if newCallRecord.CallId != touchmisCallInfo.CallId && oldCallRecord.CallId != msgData.CallId {
		zlog.Warnf(ctx, "CallRecordAIResult getCallInfo not find,callId=%v,msg=%+v,err=%v", msgData.CallId, msgData, err)
		return errors.New("CallID未找到")
	}

	var studentUid int64
	if touchmisCallInfo.CallType == 1 {
		studentUid = touchmisCallInfo.ToUid
	} else {
		studentUid = touchmisCallInfo.FromUid
	}
	//创建待办
	var remindId int64
	/** 2025-03-19 由于待办数量过多导致线上问题，暂时下线待办的创建*/
	//if len(msgData.Todo.TodoItem) > 0 {
	//	addReq := &muse.AddRemindReq{
	//		Uid:        touchmisCallInfo.PersonUid,
	//		AppType:    muse.RemindTypeAssistant,
	//		Content:    msgData.Todo.TodoItem,
	//		FromType:   1,
	//		RemindTime: msgData.Todo.ReminderTime,
	//		RemindId:   0,
	//		Students: []muse.StudentInfo{
	//			{
	//				CourseId:   touchmisCallInfo.CourseId,
	//				StudentUid: studentUid,
	//			},
	//		},
	//	}
	//	addRsp, err := muse.AddRemind(ctx, addReq)
	//	zlog.Infof(ctx, "[dealCallResult] addRemindSuccess-remindId=%d", addRsp.RemindId)
	//	if err != nil {
	//		zlog.Warnf(ctx, "[dealCallResult] AddRemindErr: %+v, addReq: %s", err, fwyyutils.MarshalIgnoreError(addReq))
	//		return err
	//	}
	//	remindId = addRsp.RemindId
	//}

	if newCallRecord.CallId == callId {
		v2 := getUpdateTaskV2(ctx, callId, msgData)
		v2.RemindId = remindId
		err = models.AiCallRecordRef.Updates(ctx, []models.AiCallRecord{v2}, newCallRecord.StudentUid)
		if err != nil {
			zlog.Warnf(ctx, "CallRecordAIResultV2 Updates fail,task=%v,err=%v", newCallRecord, err)
			return err
		}
	}

	if oldCallRecord.CallId == msgData.CallId {
		v1 := getUpdateTask(ctx, msgData.CallId, msgData)
		v1.RemindId = remindId
		err = models.AiCallRecordTaskRef.Updates(ctx, []models.AiCallRecordTask{v1})
		if err != nil {
			zlog.Warnf(ctx, "CallRecordAIResultV1 Updates fail,task=%v,err=%v", newCallRecord, err)
			return err
		}
	}

	stopTime := touchmisCallInfo.StartTime/1000 + touchmisCallInfo.Duration/1000
	updateTime := newCallRecord.UpdateTime
	if updateTime == 0 {
		updateTime = oldCallRecord.UpdateTime
	}
	zlog.Infof(ctx, "AiCallRecord_Delay_AI_Result:%v", time.Now().Unix()-updateTime)
	zlog.Infof(ctx, "AiCallRecord_Delay_Total:%v", time.Now().Unix()-stopTime)

	//记录消息历史
	stuAiMessageModel := models.StuAiMessage{
		MsgType:      models.StuAiMessageType2,
		StudentUid:   studentUid,
		AssistantUid: touchmisCallInfo.DeviceUid,
		PersonUid:    touchmisCallInfo.PersonUid,
	}
	//查询下未读消息数
	count, err := stuAiMessageModel.Count(ctx, studentUid)
	if err != nil {
		return err
	}

	stuAiMessageModel.MsgId = callId
	stuAiMessageModel.Content = msgData.Abstract
	stuAiMessageModel.CreateTime = time.Now().Unix()
	err = stuAiMessageModel.Insert(ctx, studentUid)
	if err != nil {
		return err
	}

	//发送长链接消息通知
	msgId, err := longlinkService.SendInterviewMsg(ctx, stuAiMessageModel, touchmisCallInfo.StartTime/1000, count+1)
	if err != nil {
		return err
	}
	zlog.Infof(ctx, "[dealCallResult] sendInterviewMsg-msgId=%d", msgId)
	return nil
}

func getUpdateTask(ctx *gin.Context, callId string, msgData CallRecordTextAbstractMsg) models.AiCallRecordTask {
	now := time.Now().Unix()
	updateTask := models.AiCallRecordTask{}
	updateTask.CallId = callId
	if strings.Trim(msgData.Abstract, " ") == "" {
		//如果摘要为空
		zlog.Warnf(ctx, "AIResult_DATA_IS_UNVALID tag or abstract is empty,callId=%v,data=%+v", msgData.CallId, msgData)
		updateTask.Status = models.STATUS_CALL_AUDIO_FINISH
		updateTask.UpdateTime = now
	} else {
		tags, _ := json.Marshal(msgData.Tags)
		updateTask.AiResultTime = msgData.TimeStamp
		updateTask.Status = models.STATUS_FINISH
		updateTask.Abstract = msgData.Abstract
		updateTask.Tags = string(tags)
		updateTask.UpdateTime = now
	}
	return updateTask
}

func getUpdateTaskV2(ctx *gin.Context, callId int64, msgData CallRecordTextAbstractMsg) models.AiCallRecord {
	now := time.Now().Unix()
	updateTask := models.AiCallRecord{}
	updateTask.CallId = callId
	if strings.Trim(msgData.Abstract, " ") == "" {
		//如果摘要为空
		zlog.Warnf(ctx, "AIResult_DATA_IS_UNVALID tag or abstract is empty,callId=%v,data=%+v", msgData.CallId, msgData)
		updateTask.Status = models.STATUS_CALL_AUDIO_FINISH
		updateTask.UpdateTime = now
	} else {
		tags, _ := json.Marshal(msgData.Tags)
		updateTask.Status = models.STATUS_FINISH
		updateTask.Abstract = msgData.Abstract
		updateTask.Tags = string(tags)
		updateTask.UpdateTime = now
	}
	return updateTask
}
