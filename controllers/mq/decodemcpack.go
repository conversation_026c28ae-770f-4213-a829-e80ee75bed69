package mq

import (
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/mitchellh/mapstructure"
)

func DecodeMcpack(ctx *gin.Context, msg rmq.Message, data interface{}) error {
	msgContent, err := mcpack.Decode(msg.GetContent())
	if err != nil {
		zlog.Errorf(ctx, "can't decode mcpack msg; tag: %s , msgid: %s", msg.GetTag(), msg.GetID())
		return err
	}
	if err = mapstructure.Decode(msgContent, data); err != nil {
		zlog.Warnf(ctx, "mapstructure Decode 失败，tag: %s , msgid: %s , msg：%+v ,err:%+v", msg.GetTag(), msg.GetID(), msg<PERSON>ontent, err.Error())
		return err
	}
	return nil
}

func DecodeMsg(ctx *gin.Context, bytes []byte, data interface{}) error {
	err := jsoniter.Unmarshal(bytes, data)
	if err != nil {
		return err
	}
	return nil
}
