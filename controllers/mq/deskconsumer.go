package mq

import (
	"assistantdeskgo/service/pictask"
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func DeskConsumer(ctx *gin.Context, msg rmq.Message) error {
	// do something.
	zlog.Info(ctx, "got message id=", msg.GetID(), " tag=", msg.GetTag(), " header=", msg.GetHeader("Time"))
	content := msg.GetContent()

	msgData, err := mcpack.Decode(msg.GetContent())
	if err != nil {
		zlog.Errorf(ctx, "can't decode mcpack msg; msgid: %s", msg.GetID())
		return nil
	}

	tag := msgData.GetString("_cmd", "")
	zlog.Info(ctx, "getTag:", tag)

	switch tag {
	case "214200":
		zlog.Info(ctx, "getTag:", tag)
		return pictask.CreateChildTask(ctx, msgData)
	}

	zlog.Debug(ctx, "content is ", string(content))

	// 消费成功需要return nil, 消费失败需要return err. 失败消息后续会重试消费
	return nil
}
