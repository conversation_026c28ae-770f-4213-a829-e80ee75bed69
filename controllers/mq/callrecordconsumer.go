package mq

import (
	"assistantdeskgo/api/muse"
	"assistantdeskgo/api/transcribe"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"assistantdeskgo/service/gray"
	"errors"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	utils2 "gorm.io/gorm/utils"
	"strings"
	"time"
)

type CallRecordMsg struct {
	CallId       int64  `json:"call_id" mapstructure:"call_id"`
	BusinessType string `json:"business_type" mapstructure:"business_type"`
	BusinessKey  string `json:"business_key" mapstructure:"business_key"`
	Duration     int64  `json:"duration" mapstructure:"duration"`
	PressMark    int    `json:"_press_mark" mapstructure:"_press_mark"`
}

const GARY_AI_CALL_RECORD_KEY = "assistantdeskgo_gray_ai_call_record"
const RedisMqLockTime = 5
const RedisMqCallLock = "assistantdeskgo:ai_mq_call_lock:%v"

func CallRecord(ctx *gin.Context, msg rmq.Message) error {
	//消费通话记录
	var msgData CallRecordMsg
	zlog.Infof(ctx, "got message id=%v,tag=%v,header=%v", msg.GetID(), msg.GetTag(), msg.GetHeader("Time"))
	err := DecodeMcpack(ctx, msg, &msgData)
	if err != nil {
		return err
	}
	zlog.Infof(ctx, "MQ_CallRecord msgContent=%+v", msgData)
	if msgData.PressMark == 1 {
		zlog.Infof(ctx, "skip preessMark data,call_id=%v", msgData.CallId)
		return nil
	}

	if msgData.BusinessKey == touchmis.BusinessTypeForPublicSea {
		zlog.Infof(ctx, "skip publicSea call record,call_id=%v", msgData.CallId)
		return nil
	}

	if msgData.Duration <= 0 {
		//没有时间的不统计
		zlog.Infof(ctx, "AiCallRecord Duration lesson than 0,callId=%v,data=%v", msgData.CallId, msgData)
		return nil
	}

	helpers.AiCallConsumerLimiter.Take()
	//查询通话记录
	callRecord, err := getCallInfo(ctx, msgData.CallId)
	if err != nil {
		return err
	}
	if strings.Trim(callRecord.RecordFile, " ") == "" || callRecord.CourseId == 0 {
		zlog.Infof(ctx, "AiCallRecord recordFile is empty or courseId is 0,callId=%v", msgData.CallId)
		return nil
	}
	if !gray.HitGray(ctx, callRecord.PersonUid, GARY_AI_CALL_RECORD_KEY) {
		zlog.Infof(ctx, "AiCallRecord hit gray false,callId=%v,personUid=%v", msgData.CallId, callRecord.PersonUid)
		return nil
	}

	if callRecord.CallMode == defines.CALLOUT_MODE_AUTO {
		zlog.Infof(ctx, "AiCallRecord filter auto record,callId=%v", msgData.CallId)
		return nil
	}

	aiTask, err := models.AiCallRecordRef.GetByCallId(ctx, msgData.CallId, callRecord.ToUid)
	if err != nil {
		zlog.Warnf(ctx, "AiCallRecord GetByCallId error,callId=%v", msgData.CallId)
		return err
	}

	lockKey := fmt.Sprintf(RedisMqCallLock, msgData.CallId)
	lock, lockErr := helpers.RedisClient.SetNxByEX(ctx, lockKey, 1, uint64(RedisMqLockTime))
	if !lock {
		zlog.Infof(ctx, "Get Redis Lock fail,key = %v,err=%v", lockKey, lockErr)
		return lockErr
	}

	task := models.AiCallRecordRef.From(callRecord)
	if aiTask.CallId <= 0 {
		err = models.AiCallRecordRef.Insert(ctx, task)
		if gray.DoubleWrite(ctx, defines.Config_AI_SYNC_LAST_TIME_KEY) { //双写
			oldCall, _ := models.AiCallRecordTaskRef.GetByCallId(ctx, utils2.ToString(msgData.CallId))
			if oldCall.CallId != utils2.ToString(msgData.CallId) {
				err = models.AiCallRecordTaskRef.Insert(ctx, models.AiCallRecordTaskRef.From2(callRecord))
			}

		}
	} else if aiTask.Status >= models.STATUS_CALL_AUDIO {
		zlog.Infof(ctx, "AiCallRecord is submit filter,callId=%v", msgData.CallId)
		return nil
	}
	if err != nil {
		zlog.Warnf(ctx, "AiCallRecord Insert fail data=%v,err=%v", task, err)
		return err
	}
	stopTime := task.StartTime/1000 + task.Duration/1000
	zlog.Infof(ctx, "AiCallRecord_Delay_Call_time:%v", time.Now().Unix()-stopTime)
	reqList := []transcribe.AudioReqModel{}

	link, err := muse.GetLink(ctx, muse.GetRecordLinkReq{
		Path:    task.RecordFile,
		Type:    task.ResourceType,
		Day:     1,
		IsInner: 1,
	})
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio getLink Fail,callId=%v,err=%v", task.CallId, err)
		return err
	}
	if link.Link == "" {
		zlog.Warnf(ctx, "processPostAudio getLink is empty,callId=%v", task.CallId)
		return nil
	}

	reqList = append(reqList, transcribe.AudioReqModel{
		defines.AI_BUSSINE_CALL_TYPE + "_" + utils2.ToString(task.StudentUid) + "_" + utils2.ToString(task.CallId),
		link.Link,
		task.Duration,
	})
	err = transcribe.ReqTranscribe(ctx, reqList)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio ReqTranscribe fail,callId=%v", task.CallId)
		return nil
	}
	err = models.AiCallRecordRef.UpdateStatus(ctx, []int64{task.CallId}, models.STATUS_CALL_AUDIO, task.StudentUid)
	if err != nil {
		zlog.Warnf(ctx, "processPostAudio Updates fail,callId=%v", task.CallId)
		return nil
	}

	return nil
}

func getCallInfo(ctx *gin.Context, callId int64) (*touchmisgo.CallRecordInfo, error) {
	callRecordMap, err := touchmisgo.GetCallRecordInfo(ctx, &touchmisgo.GetCallRecordInfoReq{CallIds: []int64{callId}})
	if err != nil {
		zlog.Warnf(ctx, "AiCallRecord GetByCallId fail,callId=%v,err=%v", callId, err)
		return nil, err
	}
	if len(callRecordMap) == 0 {
		zlog.Warnf(ctx, "AiCallRecord GetByCallId not find,callId=%v", callId)
		return nil, errors.New("通话记录未找到")
	}
	callRecord := callRecordMap[callId]
	if callRecord.CallId <= 0 {
		zlog.Warnf(ctx, "AiCallRecord GetByCallId not find,callId=%v", callId)
		return nil, errors.New("通话记录未找到")
	}
	return callRecord, nil
}
