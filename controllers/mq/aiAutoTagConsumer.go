package mq

import (
	dtopreclass "assistantdeskgo/dto/preclass"
	"assistantdeskgo/helpers"
	"assistantdeskgo/service/backend/preclass"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func AIAutoTagConsumer(ctx *gin.Context, msg rmq.Message) error {
	// do something.
	content := msg.GetContent()
	zlog.Infof(ctx, "[AIAutoTagConsumer] got message id: %s, tag: %s, header: %s, content: %s",
		msg.GetID(), msg.GetTag(), msg.GetHeader("Time"), string(content))

	var aiAutoTag dtopreclass.AIAutoTag
	if err := DecodeMsg(ctx, msg.GetContent(), &aiAutoTag); err != nil {
		zlog.Errorf(ctx, "[AIAutoTagConsumer] can't decode msg, msgid: %s, err: %+v", msg.GetID(), err)
		return err
	}

	// 这里控制下限流（如果需要调整需要考虑获取 leadsId 的接口能不能承受）
	helpers.AIAutoTagConsumerLimiter.Take()

	aiAutoTag.MsgID = msg.GetID()
	if err := preclass.AIAutoUpdatePreClassState(ctx, aiAutoTag); err != nil {
		zlog.Warnf(ctx, "[AIAutoTagConsumer] AIAutoUpdatePreClassState failed, msgid: %s, err: %+v", msg.GetID(), err)
		return err
	}
	return nil
}
