package mq

import (
	dtopreclass "assistantdeskgo/dto/preclass"
	"testing"
)

func Test_AIAutoTagConsumer(t *testing.T) {
	// do something.

	val := `{"sceneType":97,"courseId":537807,"studentUid":2135423798,"assistantUid":4300412528,"createTime":1724318874,"lessonPreClassTagInfos":[{"lessonId":510511,"tagType":5,"extInfo":{"banXueTag":"","contentTime":0,"firstLeaveReason":"","leaveSeason":""}},{"lessonId":510512,"tagType":1,"extInfo":{"banXueTag":"","contentTime":0,"firstLeaveReason":"","leaveSeason":"我不想去上课"}},{"lessonId":510513,"tagType":3,"extInfo":{"banXueTag":"","contentTime":1724865316,"firstLeaveReason":"","leaveSeason":""}}]}`

	var aiAutoTag dtopreclass.AIAutoTag
	err := DecodeMsg(nil, []byte(val), &aiAutoTag)
	if err != nil {
		return
	}
	t.Log(aiAutoTag)
}
