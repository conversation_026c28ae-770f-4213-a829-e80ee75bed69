package mq

import (
	"assistantdeskgo/api/allocate"
	"assistantdeskgo/api/kunpeng"
	"assistantdeskgo/api/mesh"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"
	"git.zuoyebang.cc/pkg/golib/v2/gomcpack/mcpack"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"strconv"
	"time"
)

// -----鲲鹏消息定义---
type KpMsg struct {
	UniqueId  string          `json:"uniqueId" mcpack:"uniqueId"`
	EventType string          `json:"eventType" mcpack:"eventType"`
	Pt        string          `json:"pt" mcpack:"pt"`
	UserId    string          `json:"userId" mcpack:"userId"`
	CorpId    string          `json:"corpId" mcpack:"corpId"`
	StaffRole int64           `json:"staffRole" mcpack:"staffRole"`
	TimeStamp int64           `json:"timeStamp" mcpack:"timeStamp"`
	Data      json.RawMessage `json:"data" mcpack:"data"`
}

type WxMessageRecordData struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	MsgSvrId  string `json:"msgSvrId"`
	MsgSvrKey string `json:"msgSvrKey"`
	//学生的remoteID
	RemoteId       string      `json:"remoteId"`
	ExternalUserId string      `json:"externalUserId"`
	MsgKpId        int64       `json:"msgKpId"`
	CreateTime     string      `json:"createTime"`
	MsgType        int         `json:"msgType"`
	MsgContent     interface{} `json:"msgContent"`
	Trench         int         `json:"trench"`
	SendMethod     int         `json:"sendMethod"`
	StaffRole      int         `json:"staffRole"`
	BmErrCode      int         `json:"bmErrCode"`
	BmError        string      `json:"bmError"`
	Recordwording  string      `json:"recordwording"`
}

type MessageContent struct {
	introduction string
	Duration     int64
}

func WxMessageRecord(ctx *gin.Context, msg rmq.Message) error {
	helpers.Consumer182001Limiter.Take()
	//无效数据太多，没有记录意义
	return nil
	var msgData KpMsg
	zlog.Infof(ctx, "got message id=%v,tag=%v,header=%v,message=%s", msg.GetID(), msg.GetTag(), msg.GetHeader("Time"), string(msg.GetContent()))
	if msg.GetTag() != "182001" {
		return nil
	}
	msgContent, err := mcpack.Decode(msg.GetContent())
	if err != nil {
		zlog.Errorf(ctx, "can't decode mcpack msg; tag: %s , msgid: %s", msg.GetTag(), msg.GetID())
		return err
	}
	msgData.UniqueId = msgContent.GetString("uniqueId", "")
	msgData.EventType = msgContent.GetString("eventType", "")
	msgData.Pt = msgContent.GetString("pt", "")
	msgData.UserId = msgContent.GetString("userId", "")
	msgData.CorpId = msgContent.GetString("corpId", "")
	msgData.Data = []byte(msgContent.GetString("data", ""))

	//err := DecodeMcpack(ctx, msg, &msgData)
	//if err := DecodeMsg(ctx, msg.GetContent(), &msgData); err != nil {
	//	zlog.Errorf(ctx, "[AIAutoTagConsumer] can't decode msg, msgid: %s, err: %+v", msg.GetID(), err)
	//	return err
	//}

	recordData := WxMessageRecordData{}
	err = json.Unmarshal(msgData.Data, &recordData)
	if err != nil {
		zlog.Errorf(ctx, "kp data decode kpData:[%s] err:%s", msgContent.GetString("data", ""), err.Error())
		return err
	}
	zlog.Infof(ctx, "MQ_WxMessage msgContent=%+v, recordData=%+v", msgData, recordData)
	//过滤完全失败消息
	if recordData.Code != 0 {
		zlog.Infof(ctx, "skip preessMark data,call_id=%v", recordData.Code)
		return nil
	}
	//过滤除语音视频外数据
	if recordData.MsgType != 16 && recordData.MsgType != 17 {
		return nil
	}
	//过滤正常数据，只保留通话异常数据
	content, ok := recordData.MsgContent.(map[string]interface{})
	if !ok {
		zlog.Infof(ctx, "get content result,content=%+v", content)
		return nil
	}
	if duration, ok := content["duration"]; ok {
		if duration != nil && len(duration.(string)) > 0 && duration != "0" {
			zlog.Infof(ctx, "get duration result,content=%+v", content)
			return nil
		}
	}
	//通过userId+corpId获取资产uid
	deviceInfo, err := mesh.GetDeviceInfoByWxUserId(ctx, msgData.UserId, msgData.CorpId)
	if err != nil {
		return err
	}
	if deviceInfo.DeviceId <= 0 {
		zlog.Infof(ctx, "kp182001,can not find device info userId=%s,corpId=%s", msgData.UserId, msgData.CorpId)
		return nil
	}
	//获取学生uid
	studentWxInfo, err := kunpeng.GetUidByWxId(ctx, getKpReq(deviceInfo.DeviceId, recordData.RemoteId))
	if err != nil {
		zlog.Warnf(ctx, "182001 GetUidByWxId fail,remoteId=%v,err=%v", recordData.RemoteId, err)
		return err
	}
	if len(studentWxInfo) == 0 || studentWxInfo[0].StudentUid <= 0 {
		zlog.Infof(ctx, "WxCall GetUidByWxId is empty,remoteId=%v", recordData.RemoteId)
		return err
	}
	studentUid := studentWxInfo[0].StudentUid
	//查询例子信息
	stuLeads, err := allocate.GetLeadsByUid(ctx, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "allocate GetByCallId fail,studentUid=%d,err=%s", studentUid, err.Error())
		return err
	}
	//从学员例子信息获取课程ID
	zlog.Infof(ctx, "leadsList%+v,assistantUid%d", stuLeads, deviceInfo.DeviceId)
	courseId := allocate.GetCourseIdByLeads(stuLeads, deviceInfo.DeviceId)

	//查询是否存在记录
	oriRecordModel := &models.WxCallRecordNew{}
	oriRecord, err := oriRecordModel.GetByCallId(ctx, recordData.MsgKpId, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "WxCallNew GetByCallId fail,callId=%d,err=%s", recordData.MsgKpId, err.Error())
		return err
	}
	zlog.Infof(ctx, "kp182001 consumer msgData=%+v, recordData=%+v", msgData, recordData)
	createTime, _ := strconv.Atoi(recordData.CreateTime)
	//异常记录入库
	recordModel := models.WxCallRecordNew{
		CallId:     recordData.MsgKpId,
		CourseId:   courseId,
		StudentUid: studentUid,
		StartTime:  int64(createTime),
		StopTime:   int64(createTime),
		DeviceUid:  deviceInfo.DeviceId,
		PersonUid:  int64(deviceInfo.StaffUid),
		MsgType:    int64(recordData.MsgType),
		CallResult: 2,
		Status:     0,
		CreateTime: time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
	}
	//发送到人
	if msgData.EventType == "WorkSendMessage" {
		recordModel.FromUid = deviceInfo.DeviceId
		recordModel.ToUid = studentUid
		recordModel.CallType = 1
		recordModel.ToRemoteId = recordData.RemoteId
	} else if msgData.EventType == "WorkReceiveMessage" {
		recordModel.ToUid = deviceInfo.DeviceId
		recordModel.FromUid = studentUid
		recordModel.CallType = 2
		recordModel.FromRemoteId = recordData.RemoteId
	}
	if oriRecord.Id > 0 {
		err = recordModel.Update(ctx, studentUid, recordModel)
	} else {
		err = recordModel.Insert(ctx, studentUid, recordModel)
	}

	return nil
}
