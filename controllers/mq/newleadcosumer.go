package mq

import (
	"assistantdeskgo/service/backend/allocate"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	AllAllocate    = "280022" // 统一分配
	AllocateChange = "280023" // 统一调配

)

type LeadMsgStruct struct {
	GroupKey  int64 `json:"groupKey"`
	CustomUid int64 `json:"customUid"`
	CourseId  int64 `json:"courseId"`
	ScUid     int64 `json:"scUid"`
	KpUid     int64 `json:"kpUid"`
}

func NewLead(ctx *gin.Context, msg rmq.Message) error {
	zlog.Info(ctx, "got message id=", msg.GetID(), " tag=", msg.GetTag(), " header=", msg.GetHeader("Time"))
	var params = LeadMsgStruct{}
	switch msg.GetTag() {
	case AllAllocate:
		err := DecodeMcpack(ctx, msg, &params)
		if err != nil {
			return err
		}
		return allocate.NewLeadAdd(ctx, params.CourseId, params.CustomUid, params.KpUid)
	case AllocateChange:
		err := DecodeMcpack(ctx, msg, &params)
		if err != nil {
			return err
		}
		return allocate.NewLeadAdd(ctx, params.CourseId, params.CustomUid, params.KpUid)
	}

	return nil
}
