package apitemplate

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoapitemplate"
	"assistantdeskgo/service/backend/template/groupposter"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetPosterTemplateByGroupIds(ctx *gin.Context) {
	req := dtoapitemplate.GetPosterTemplateByGroupIdsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Warnf(ctx, "GroupPoster.GetPosterTemplateByGroupIds params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	resp, err := groupposter.FindByTypeGroupIds(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GroupPoster.FindByTypeGroupIds params error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
