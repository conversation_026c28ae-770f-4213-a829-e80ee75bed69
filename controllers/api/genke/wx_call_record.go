package genke

import (
	"assistantdeskgo/components"
	"assistantdeskgo/service/genke"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func WxCallRecord(ctx *gin.Context) {
	type params struct {
		StudentUid int64 `json:"studentUid" form:"studentUid"`
	}
	req := params{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetUserDeviceListByCourse params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if req.StudentUid <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	list, err := genke.WxCallRecord(ctx, req.StudentUid)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, list)
}
