package relation

import (
	"assistantdeskgo/dto/dtorelation/dtotouch"
	"assistantdeskgo/service/relation/touch"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetTouchCallDataBySingleAssistant(ctx *gin.Context) {
	req := dtotouch.ApiTouchCallReq{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := touch.GetTouchCallDataBySingleAssistant(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonList.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetLastAccessTimeBySingleAssistant(ctx *gin.Context) {
	req := dtotouch.ApiTouchCallReq{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := touch.GetLastAccessTimeBySingleAssistant(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonList.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetTouchCallDataByAllAssistant(ctx *gin.Context) {
	req := dtotouch.ApiTouchCallAllReq{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := touch.GetTouchCallDataByAllAssistant(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonList.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
