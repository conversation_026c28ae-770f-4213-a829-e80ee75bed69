package relation

import (
	"assistantdeskgo/dto/dtorelation/dtoclue"
	"assistantdeskgo/service/relation/clue"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetOpLogByClueDeviceId(ctx *gin.Context) {
	req := dtoclue.ApiOpLogReq{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := clue.GetOpLogByClueDeviceId(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetOpLogByClueDeviceId.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

func ResetTimeOpLogByClueDeviceId(ctx *gin.Context) {
	req := dtoclue.ResetTimeOpLogByClueDeviceIdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	resp, err := clue.ResetTimeOpLogByClueDeviceId(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "ResetTimeOpLogByClueDeviceId.failed,req:%+v,err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
