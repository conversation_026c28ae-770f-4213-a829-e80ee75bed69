package relation

import (
	"assistantdeskgo/dto/dtorelation/dtosource"
	"assistantdeskgo/service/relation/datalayer"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func GetLearnReportByClueIds(ctx *gin.Context) {
	req := dtosource.GetLearnReportByClueIdsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.New(fmt.Sprintf("参数错误:%+v", err)))
		return
	}

	respMap := datalayer.GetClueIdLearnReportInfoMap(ctx, req.ClueIdGradeIdMap)
	base.RenderJsonSucc(ctx, respMap)
}
