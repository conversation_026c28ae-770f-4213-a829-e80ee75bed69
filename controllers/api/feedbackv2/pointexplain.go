package feedbackv2

import (
	"assistantdeskgo/components"
	dtofeedback "assistantdeskgo/dto/feedbackv2"
	svcfeedback "assistantdeskgo/service/feedbackv2"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	req := dtofeedback.PointExplainListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "feedbackv2 List params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := svcfeedback.List(ctx, req)
	if err != nil {
		zlog.Infof(ctx, "feedbackv2 List error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func Upsert(ctx *gin.Context) {
	req := dtofeedback.PointExplainUpsertReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "feedbackv2 Upsert params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := svcfeedback.Upsert(ctx, req)
	if err != nil {
		zlog.Infof(ctx, "feedbackv2 Upsert error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func Delete(ctx *gin.Context) {
	req := dtofeedback.PointExplainDeleteReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "feedbackv2 Upsert params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := svcfeedback.Delete(ctx, req)
	if err != nil {
		zlog.Infof(ctx, "feedbackv2 Delete error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
