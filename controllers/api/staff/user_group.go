package staff

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtostaff"
	commonservice "assistantdeskgo/service/common"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetUserProductLineGroupIds(ctx *gin.Context) {
	req := dtostaff.GetUserProductLineGroupIdsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetUserDeviceListByCourse params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	ret, err := commonservice.GetUserProductLineGroupIds(ctx, req.PeronUid)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, ret)
}
