package data

import (
	"assistantdeskgo/dto/dtometricsdata"
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const fwyyDataAssistantMetricsCommon = "fwyy-data-assistant-metricscommon"

func SyncMetricsCommon(ctx *gin.Context) {
	type params struct {
		MessageList []dtometricsdata.MetricsData `json:"messageList" form:"messageList" binding:"required"`
	}
	p := &params{}
	if err := ctx.ShouldBind(p); err != nil {
		zlog.Warn(ctx, "SyncMetricsCommon.param err", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	zlog.Info(ctx, fmt.Sprintf("SyncMetricsCommon.send messageList %+v", p.MessageList))

	for _, message := range p.MessageList {
		err := helpers.MetricsPubClient.Pub(ctx, fwyyDataAssistantMetricsCommon, message)
		if err != nil {
			zlog.Warn(ctx, fmt.Sprintf("send message %+v to kafka err", message), err)
			base.RenderJsonFail(ctx, err)
			return
		}
	}

	base.RenderJsonSucc(ctx, true)
	return
}
