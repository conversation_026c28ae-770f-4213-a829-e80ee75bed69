package gray

import (
	"assistantdeskgo/components"
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/gray"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type HitGrayRequest struct {
	PersonUid int64  `json:"personUid" form:"personUid"`
	Key       string `json:"key" form:"key"`
}

func Hit(ctx *gin.Context) {
	req := HitGrayRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AutoCall params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	if req.PersonUid <= 0 || req.Key == "" {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	base.RenderJsonSucc(ctx, gray.HitGray(ctx, req.PersonUid, req.Key))

}

func HitUseCookie(ctx *gin.Context) {
	req := HitGrayRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AutoCall params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	info, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return
	}

	base.RenderJsonSucc(ctx, gray.HitGray(ctx, int64(info.StaffUid), req.Key))

}

type UserGroupRequest struct {
	PersonUid    int64  `json:"personUid" form:"personUid"`
	Key          string `json:"key" form:"key"`
	ConfigSource string `json:"configSource" form:"configSource"`
}

func UserGroup(ctx *gin.Context) {
	req := UserGroupRequest{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AutoCall params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	if req.PersonUid <= 0 || req.Key == "" {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	isGray := gray.UserGroupGray(ctx, req.PersonUid, req.Key, req.ConfigSource)
	base.RenderJsonSucc(ctx, gin.H{
		"isGray": isGray,
	})
}
