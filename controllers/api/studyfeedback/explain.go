package studyfeedback

import (
	"assistantdeskgo/components"
	dtostudyfeedback "assistantdeskgo/dto/studyfeedback"
	"assistantdeskgo/service/studyfeedback"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetExplain(ctx *gin.Context) {
	req := dtostudyfeedback.GetExplainReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetExplain params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := studyfeedback.GetExplain(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func AddPointExplains(ctx *gin.Context) {
	req := dtostudyfeedback.AddPointExplainsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AddPointExplains params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := studyfeedback.AddPointExplains(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
