package studyfeedback

import (
	"assistantdeskgo/components"
	dtostudyfeedback "assistantdeskgo/dto/studyfeedback"
	"assistantdeskgo/service/studyfeedback"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetPointByTid(ctx *gin.Context) {
	req := dtostudyfeedback.GetPointByTidReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetPreClassDetail params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := studyfeedback.GetPointByTid(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func AddPointTids(ctx *gin.Context) {
	req := dtostudyfeedback.AddPointTidsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AddPointTids params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := studyfeedback.AddPointTids(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}

func RemovePoint(ctx *gin.Context) {
	req := dtostudyfeedback.RemovePointReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "RemovePoint params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := studyfeedback.RemovePoint(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
