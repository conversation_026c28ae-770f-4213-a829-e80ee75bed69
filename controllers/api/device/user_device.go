package device

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtodevice"
	"assistantdeskgo/service/device"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetUserDeviceListByCourse(ctx *gin.Context) {
	req := dtodevice.GetUserDeviceListByCourseReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetUserDeviceListByCourse params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	courseBind, err := device.GetUserDeviceListByCourse(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, courseBind)

}
