package options

import (
	"assistantdeskgo/dto/dtooptions"
	"assistantdeskgo/models/grouppostertemplate"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetSecondGroupOptions(ctx *gin.Context) {
	groupNodeList, err := getSecondGroupNodeList(ctx)
	if err != nil {
		zlog.Warnf(ctx, "Options.GetSecondGroupOptions error, err: %+v", err)
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, groupNodeList)
	return
}

func GetTplStyleOptions(ctx *gin.Context) {
	base.RenderJsonSucc(ctx, FormatKvMapToLabelValue(grouppostertemplate.GetTplStyleOptions()))
	return
}

func getSecondGroupNodeList(ctx *gin.Context) (nodeList []dtooptions.NodeOption, err error) {
	groupTrees, fErr := message.FormatOrganizationTreeForGlobal(ctx)
	if fErr != nil {
		err = fErr
		return
	}

	nodeList = make([]dtooptions.NodeOption, 0, len(groupTrees))
	// 仅保留二级团队
	for _, groupTree := range groupTrees {
		if groupTree == nil || groupTree.Children == nil || len(groupTree.Children) == 0 {
			continue
		}

		childList := make([]dtooptions.NodeOption, 0, len(groupTree.Children))
		for _, child := range groupTree.Children {
			childList = append(childList, dtooptions.NodeOption{
				Label: child.Name,
				Value: child.ID,
			})
		}

		nodeList = append(nodeList, dtooptions.NodeOption{
			Label:    groupTree.Name,
			Value:    groupTree.ID,
			Children: childList,
		})
	}
	return
}
