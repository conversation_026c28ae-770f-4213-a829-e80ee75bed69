package scenetouch

import (
	"assistantdeskgo/components"
	dtoscenetouch "assistantdeskgo/dto/scenetouch"
	"assistantdeskgo/service/scenetouch"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func ContextOptions(ctx *gin.Context) {
	req := dtoscenetouch.ContextOptionsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "ContextOptions params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := scenetouch.ContextOptions(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "scenetouch.ContextOptions error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
