package scenetouch

import (
	"assistantdeskgo/components"
	dtoscenetouch "assistantdeskgo/dto/scenetouch"
	"assistantdeskgo/service/scenetouch"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GroupFilterGroupBind(ctx *gin.Context) {
	req := dtoscenetouch.GroupFilterGroupBindReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GroupFilterGroupBind params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := scenetouch.GroupFilterGroupBind(ctx, req)
	if err != nil {
		zlog.Infof(ctx, "GroupFilterGroupBind error! req:%+v, err:%+v", req, err)
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
