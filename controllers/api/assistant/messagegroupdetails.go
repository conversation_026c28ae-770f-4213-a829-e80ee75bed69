package assistant

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func MessageGroupDetails(ctx *gin.Context) {
	req := dtomessage.MessageGroupDetailsReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "MessageGroupDetails params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.GetMessageGroupDetails(ctx, req.GroupIds)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
