package assistant

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func CheckMessageGroupPermission(ctx *gin.Context) {
	req := dtomessage.CheckMessageGroupPermissionReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "CheckMessageGroupPermission params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.CheckMessageGroupPermission(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
