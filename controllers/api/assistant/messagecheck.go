package assistant

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoassistant"
	"assistantdeskgo/service/assistant"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func MessageCheck(ctx *gin.Context) {
	req := dtoassistant.MessageCheckReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "SendGroupMessage params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := assistant.MessageCheck(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
