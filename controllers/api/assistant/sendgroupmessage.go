package assistant

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtoassistant"
	"assistantdeskgo/service/assistant"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SendGroupMessage(ctx *gin.Context) {
	req := dtoassistant.SendGroupMessageReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	zlog.Infof(ctx, "SendGroupMessage_params: %+v", req)

	rsp, err := assistant.SendGroupMessage(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
