package assistant

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtomessage"
	"assistantdeskgo/service/backend/message"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func MessageGroupDetail(ctx *gin.Context) {
	req := dtomessage.MessageGroupDetailReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "MessageGroupDetail params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := message.GetMessageGroupDetail(ctx, req.GroupId)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
