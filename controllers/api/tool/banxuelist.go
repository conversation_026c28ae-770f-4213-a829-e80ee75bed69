package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/service/tool"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func BanXueList(ctx *gin.Context) {
	req := tool.BanXueListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := tool.GetBanXueList(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
