package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/controllers/mq"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type AutoCallReq struct {
	UniqueId int64 `json:"uniqueId" form:"uniqueId"`
}

func AutoCall(ctx *gin.Context) {
	req := AutoCallReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AutoCall params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := mq.AutoCallSend(ctx, req.UniqueId, 0)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, "success")
}
