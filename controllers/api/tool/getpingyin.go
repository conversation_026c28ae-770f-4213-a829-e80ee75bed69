package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtopublic"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetPingYin(ctx *gin.Context) {
	type params struct {
		ChineseName []string `json:"chineseName" form:"chineseName" binding:"required"`
	}
	req := &params{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetPingYin params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	zlog.Infof(ctx, "GetPingYin params: %+v", req.ChineseName)

	data := make([]dtopublic.GetPingYinItem, 0)
	for _, name := range req.ChineseName {
		pinyin := utils.GetPinyin(name)
		data = append(data, dtopublic.GetPingYinItem{
			Name:    name,
			PingYin: pinyin,
		})
	}

	resp := dtopublic.GetPingYinResp{PingYin: data}
	base.RenderJsonSucc(ctx, resp)
}
