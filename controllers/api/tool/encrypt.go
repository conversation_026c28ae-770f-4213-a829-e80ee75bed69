package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type EncryptReq struct {
	SourceStr string `json:"sourceStr" form:"sourceStr"`
}

type EncryptRsp struct {
	EncryptStr string `json:"encryptStr" form:"encryptStr"`
}

func Encrypt(ctx *gin.Context) {
	req := EncryptReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "Encrypt params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	ret, err := helpers.Kms.Encrypt(ctx, req.SourceStr)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	rsp := EncryptRsp{EncryptStr: ret}

	base.RenderJsonSucc(ctx, rsp)
}
