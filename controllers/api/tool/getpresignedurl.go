package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

func GetPreSignedURL(ctx *gin.Context) {
	type Params struct {
		Method    string `json:"method" form:"method"`
		ObjectKey string `json:"objectKey" form:"objectKey"`
		Expired   int64  `json:"expired" form:"expired"`
	}
	req := &Params{}
	if err := ctx.ShouldBind(req); err != nil {
		zlog.Infof(ctx, "GetPingYin params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	url, err := helpers.BosBucket.GetPresignedURL(ctx, req.Method, req.Object<PERSON>ey, time.Duration(req.Expired), map[string]string{}, map[string]string{})
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, map[string]string{
		"url": url,
	})
}
