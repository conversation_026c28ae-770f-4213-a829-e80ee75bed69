package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type DecryptReq struct {
	EncryptStr string `json:"encryptStr" form:"encryptStr"`
}

type DecryptRsp struct {
	SourceStr string `json:"sourceStr" form:"sourceStr"`
}

func Decrypt(ctx *gin.Context) {
	req := DecryptReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "Decrypt params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	ret, err := helpers.Kms.Decrypt(ctx, req.EncryptStr)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	rsp := DecryptRsp{SourceStr: ret}

	base.RenderJsonSucc(ctx, rsp)
}
