package tool

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtodeskviewdetail"
	"assistantdeskgo/service/interview"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type AddInterViewRequest struct {
	StudentUid     int64  `json:"studentUid" form:"studentUid" binding:"required"`
	Content        string `json:"content" form:"content" binding:"required"`
	Uid            int64  `json:"uid" form:"uid" binding:"required"` // 操作人
	CourseId       string `json:"courseId" form:"courseId"`          // 批量课程写入
	InterviewId    string `json:"interviewId" form:"interviewId"`    // 传了就是更新
	CourseIdArr    []int64
	InterviewIdArr []int64
}

func (a *AddInterViewRequest) Validate(ctx *gin.Context) error {

	runes := []rune(a.Content)
	if len(runes) < 1 || len(runes) > 300 {
		return components.ErrorParamInvalidFormat.Sprintf("记录内容为空或过长")
	}

	if a.StudentUid == 0 || a.Uid == 0 {
		return components.ErrorParamInvalid
	}

	courseArr := make([]int64, 0)
	InterviewIdArr := make([]int64, 0)
	if a.CourseId != "" {
		err := json.Unmarshal([]byte(a.CourseId), &courseArr)
		if err != nil {
			return components.ErrorParamInvalidFormat.Sprintf("课程 id 格式错误")
		}
	}

	if len(courseArr) == 0 || len(courseArr) > 10 {
		return components.ErrorParamInvalidFormat.Sprintf("课程 id 数大于 10")
	}

	if a.InterviewId != "" {
		err := json.Unmarshal([]byte(a.InterviewId), &InterviewIdArr)
		if err != nil {
			return components.ErrorParamInvalidFormat.Sprintf("维系 id 格式错误")
		}
	}

	if len(InterviewIdArr) > 20 {
		return components.ErrorParamInvalidFormat.Sprintf("维系 id 数大于 20")
	}

	a.CourseIdArr = courseArr
	a.InterviewIdArr = InterviewIdArr

	return nil
}

func AddJSQInterView(ctx *gin.Context) {
	param := &AddInterViewRequest{}
	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Infof(ctx, "AddInterView params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	zlog.Infof(ctx, "AddInterView params: %+v", param)

	ids := make([]int64, 0)
	courseIds := make([]int64, 0)
	// 更新
	if len(param.InterviewIdArr) > 0 {
		ids, courseIds, err = interview.UpdateJSQInterview(ctx, param.StudentUid, param.Content, param.InterviewIdArr, param.Uid)
		if err != nil {
			return
		}
	} else {
		ids, courseIds, err = interview.AddJSQInterview(ctx, param.StudentUid, param.CourseIdArr, param.Content, param.Uid)
		if err != nil {
			return
		}
	}

	resp := make([]dtodeskviewdetail.AddInterViewItem, 0)
	for i, id := range ids {
		resp = append(resp, dtodeskviewdetail.AddInterViewItem{
			CourseId: courseIds[i],
			RecordId: id,
		})
	}

	base.RenderJsonSucc(ctx, resp)
}

func GetJSQInterView(ctx *gin.Context) {
	type params struct {
		Ids string `json:"ids" form:"ids" binding:"required"`
	}
	req := &params{}

	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetJSQInterView params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	ids := make([]int64, 0)
	err := json.Unmarshal([]byte(req.Ids), &ids)
	if err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	zlog.Infof(ctx, "GetJSQInterView params: %+v", ids)

	resp, err := interview.GetJSQInterviewByIds(ctx, ids)
	if err != nil {
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
