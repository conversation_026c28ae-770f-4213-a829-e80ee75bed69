package accompany

import (
	"assistantdeskgo/components"
	dtoaccompany "assistantdeskgo/dto/accompany"
	"assistantdeskgo/service/backend/accompany"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetAccompanyDetail(ctx *gin.Context) {
	req := dtoaccompany.GetAccompanyDetailReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetAccompanyDetail params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := accompany.GetAccompanyDetail(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, rsp)
}
