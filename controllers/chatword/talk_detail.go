package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func TalkDetail(ctx *gin.Context) {
	req := dtochatword.TalkDetailReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	data, err := chatword.TalkDetail(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}

func ListTalkDetail(ctx *gin.Context) {
	req := dtochatword.TalkDetailListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	data, err := chatword.TalkListDetail(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}
