package chatword

import (
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func AllCategory(ctx *gin.Context) {

	data, err := chatword.AllCategory(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}

func ListCategory(ctx *gin.Context) {

	data, err := chatword.ListCategory(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}
