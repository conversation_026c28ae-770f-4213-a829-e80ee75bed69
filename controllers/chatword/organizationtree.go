package chatword

import (
	"assistantdeskgo/middleware"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func OrganizationTree(ctx *gin.Context) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	organizationTree, err := chatword.GetOrganizationTree(ctx, int64(userInfo.UserId))
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, organizationTree)
}
