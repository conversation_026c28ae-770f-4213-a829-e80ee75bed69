package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func DeleteCategory(ctx *gin.Context) {
	req := dtochatword.DeleteCategoryReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "FolderList params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if req.CategoryId <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := chatword.CategoryDelete(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, nil)
}
