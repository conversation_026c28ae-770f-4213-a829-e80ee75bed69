package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func AssistantListTalk(ctx *gin.Context) {
	req := dtochatword.AssistantListTalkReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	data, err := chatword.AssistantTalkList(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}

func WeworkListTalk(ctx *gin.Context) {
	req := dtochatword.WeworkListTalkReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "WeworkListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	data, err := chatword.WeworkTalkList(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}
