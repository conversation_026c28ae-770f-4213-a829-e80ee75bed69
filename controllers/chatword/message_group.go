package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func ToMessageGroup(ctx *gin.Context) {
	req := dtochatword.ToMessageGroupReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := chatword.ToMessageGroup(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, base.Error{components.ErrorParamInvalid.ErrNo, err.<PERSON><PERSON><PERSON>()})
		return
	}
	base.RenderJsonSucc(ctx, nil)
}
