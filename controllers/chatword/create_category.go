package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

func CreateCategory(ctx *gin.Context) {
	req := dtochatword.CreateCategoryReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "FolderList params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if strings.Trim(req.CategoryName, " ") == "" {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := chatword.CategoryCreate(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, nil)
}
