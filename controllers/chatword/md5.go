package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SaveMd5(ctx *gin.Context) {
	req := dtochatword.SaveMd5Req{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := chatword.SaveMd5(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, nil)
}

func GetMd5(ctx *gin.Context) {
	req := dtochatword.GetMd5Req{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	data, err := chatword.GetMd5(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, data)
}
