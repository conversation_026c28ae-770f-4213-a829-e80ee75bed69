package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func SendMq(ctx *gin.Context) {
	req := dtochatword.SendMqReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "SendMq params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if len(req.TalkIdList) == 0 || req.Op == "" || req.PersonUid <= 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := chatword.NotifyTalkMq(ctx, req.TalkIdList, req.Op, req.PersonUid, req.GroupId, req.GroupIdList)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, nil)
}
