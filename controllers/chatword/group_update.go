package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtochatword"
	"assistantdeskgo/service/chatword"
	"assistantdeskgo/utils"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func UpdateGroup(ctx *gin.Context) {
	req := dtochatword.UpdateGroupReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "UpdateGroup params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if len(req.TalkIdList) == 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if req.GroupId <= 0 && len(req.GroupIdList) == 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if req.GroupId > 0 {
		req.GroupIdList = utils.FilterInt64Duplicates(append(req.GroupIdList, req.GroupId))
	}
	if len(req.GroupIdList) > defines.CHATWORD_MAX_GROUP_NUM {
		base.RenderJsonFail(ctx, base.Error{components.ErrorParamInvalid.ErrNo, fmt.Sprintf("话术绑定组织架构超过最大数量，最大限制数量为:%v", defines.CHATWORD_MAX_GROUP_NUM)})
		return
	}

	err := chatword.GroupUpdate(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, nil)

}
