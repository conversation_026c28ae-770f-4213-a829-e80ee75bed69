package chatword

import (
	"assistantdeskgo/components"
	"assistantdeskgo/service/backend/message"
	"assistantdeskgo/service/chatword"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func ListResource(ctx *gin.Context) {
	type params struct {
		Type int64 `json:"type" form:"type"`
	}
	req := params{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "AssistantListTalk params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if req.Type == 0 {
		data, err := chatword.ListResource(ctx)
		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}
		base.RenderJsonSucc(ctx, data)
	} else {
		data, err := message.ListResource(ctx)
		if err != nil {
			base.RenderJsonFail(ctx, err)
			return
		}
		base.RenderJsonSucc(ctx, data)
	}

}
