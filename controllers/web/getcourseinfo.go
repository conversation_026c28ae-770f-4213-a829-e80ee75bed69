package web

import (
	"assistantdeskgo/components"
	"assistantdeskgo/dto/dtocoursetimetable"
	"assistantdeskgo/service/web/coursetimetable"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func GetTeacherInfo(ctx *gin.Context) {
	req := dtocoursetimetable.GetTeacherInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		zlog.Infof(ctx, "GetTeacherInfoReq params error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	rsp, err := coursetimetable.GetTeacherInfo(ctx, req)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
