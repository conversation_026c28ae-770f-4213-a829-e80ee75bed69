package models

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"gorm.io/gorm"
	"strings"
	"time"
)

// 一级团队
const (
	LaxinTagDataFixFirstLineJXLpc     = 1   //进校LPC
	LaxinTagDataFixFirstLineDuXueLpc  = 2   //督学LPC
	LaxinTagDataFixFirstLineAssistant = 3   //辅导老师
	LaxinTagDataFixFirstLineOther     = 999 //其他

	LaxinTagDataFixFirstLineJXLpcStr     = "进校LPC" //进校LPC
	LaxinTagDataFixFirstLineDuXueLpcStr  = "督学LPC" //督学LPC
	LaxinTagDataFixFirstLineAssistantStr = "辅导老师"  //辅导老师
	LaxinTagDataFixFirstLineOtherStr     = "其他"    //其他
)

// 二级团队
const (
	LaxinTagDataFixSecondLinePrimarySchool = 1   // 小学
	LaxinTagDataFixSecondLineMiddleSchool  = 2   // 初中
	LaxinTagDataFixSecondLineHighSchool    = 3   // 高中
	LaxinTagDataFixSecondLineProgramming   = 4   // 编程
	LaxinTagDataFixSecondLineWriting       = 5   // 写字
	LaxinTagDataFixSecondLineSports        = 6   // 运动
	LaxinTagDataFixSecondLineJZL           = 7   // 鲸准练
	LaxinTagDataFixSecondLineOthers        = 999 // 其他

	LaxinTagDataFixSecondLinePrimarySchoolStr = "小学"  // 小学
	LaxinTagDataFixSecondLineMiddleSchoolStr  = "初中"  // 初中
	LaxinTagDataFixSecondLineHighSchoolStr    = "高中"  // 高中
	LaxinTagDataFixSecondLineProgrammingStr   = "编程"  // 编程
	LaxinTagDataFixSecondLineWritingStr       = "写字"  // 写字
	LaxinTagDataFixSecondLineSportsStr        = "运动"  // 运动
	LaxinTagDataFixSecondLineJZLStr           = "鲸准练" // 鲸准练
	LaxinTagDataFixSecondLineOthersStr        = "其他"  // 其他
)

func GetLaxinTagDataFixFirstLine(s string) (int, error) {
	s = strings.TrimSpace(s)
	switch s {
	case LaxinTagDataFixFirstLineJXLpcStr:
		return LaxinTagDataFixFirstLineJXLpc, nil
	case LaxinTagDataFixFirstLineDuXueLpcStr:
		return LaxinTagDataFixFirstLineDuXueLpc, nil
	case LaxinTagDataFixFirstLineAssistantStr:
		return LaxinTagDataFixFirstLineAssistant, nil
	case LaxinTagDataFixFirstLineOtherStr:
		return LaxinTagDataFixFirstLineOther, nil
	default:
		return 0, errors.New("first line params error")
	}
}

func GetLaxinTagDataFixSecondLine(s string) (int, error) {
	s = strings.TrimSpace(s)
	switch s {
	case LaxinTagDataFixSecondLinePrimarySchoolStr:
		return LaxinTagDataFixSecondLinePrimarySchool, nil
	case LaxinTagDataFixSecondLineMiddleSchoolStr:
		return LaxinTagDataFixSecondLineMiddleSchool, nil
	case LaxinTagDataFixSecondLineHighSchoolStr:
		return LaxinTagDataFixSecondLineHighSchool, nil
	case LaxinTagDataFixSecondLineProgrammingStr:
		return LaxinTagDataFixSecondLineProgramming, nil
	case LaxinTagDataFixSecondLineWritingStr:
		return LaxinTagDataFixSecondLineWriting, nil
	case LaxinTagDataFixSecondLineSportsStr:
		return LaxinTagDataFixSecondLineSports, nil
	case LaxinTagDataFixSecondLineJZLStr:
		return LaxinTagDataFixSecondLineJZL, nil
	case LaxinTagDataFixSecondLineOthersStr:
		return LaxinTagDataFixSecondLineOthers, nil
	default:
		return 0, errors.New("second line params error")
	}
}

func GetLaxinTagDataFixFirstLineStr(i int) (string, error) {
	switch i {
	case LaxinTagDataFixFirstLineJXLpc:
		return LaxinTagDataFixFirstLineJXLpcStr, nil
	case LaxinTagDataFixFirstLineDuXueLpc:
		return LaxinTagDataFixFirstLineDuXueLpcStr, nil
	case LaxinTagDataFixFirstLineAssistant:
		return LaxinTagDataFixFirstLineAssistantStr, nil
	case LaxinTagDataFixFirstLineOther:
		return LaxinTagDataFixFirstLineOtherStr, nil
	default:
		return "", errors.New("invalid first line params")
	}
}

func GetLaxinTagDataFixSecondLineStr(i int) (string, error) {
	switch i {
	case LaxinTagDataFixSecondLinePrimarySchool:
		return LaxinTagDataFixSecondLinePrimarySchoolStr, nil
	case LaxinTagDataFixSecondLineMiddleSchool:
		return LaxinTagDataFixSecondLineMiddleSchoolStr, nil
	case LaxinTagDataFixSecondLineHighSchool:
		return LaxinTagDataFixSecondLineHighSchoolStr, nil
	case LaxinTagDataFixSecondLineProgramming:
		return LaxinTagDataFixSecondLineProgrammingStr, nil
	case LaxinTagDataFixSecondLineWriting:
		return LaxinTagDataFixSecondLineWritingStr, nil
	case LaxinTagDataFixSecondLineSports:
		return LaxinTagDataFixSecondLineSportsStr, nil
	case LaxinTagDataFixSecondLineJZL:
		return LaxinTagDataFixSecondLineJZLStr, nil
	case LaxinTagDataFixSecondLineOthers:
		return LaxinTagDataFixSecondLineOthersStr, nil
	default:
		return "", errors.New("invalid second line params")
	}
}

var LaxinTagDataFixDao laxinTagDataFixDao

type laxinTagDataFixDao struct {
}

type LaxinTagDataFix struct {
	ID                int64  `gorm:"primary_key;column:id" json:"id"`
	CourseID          int    `gorm:"column:course_id" json:"courseId"`
	AssistantPhone    string `gorm:"column:assistant_phone" json:"assistantPhone"`        // 资产手机号
	AssistantUID      int64  `gorm:"column:assistant_uid" json:"assistantUid"`            // 资产 id
	PersonEmailPrefix string `gorm:"column:person_email_prefix" json:"personEmailPrefix"` // 真人 email
	PersonUID         int64  `gorm:"column:person_uid" json:"personUid"`                  // 真人 id
	FirstLineTeam     int    `gorm:"column:first_line_team" json:"firstLineTeam"`         // 一级团队
	SecondLineTeam    int    `gorm:"column:second_line_team" json:"secondLineTeam"`       // 二级团队
	OperatorUID       int64  `gorm:"column:operator_uid" json:"operatorUid"`              // 操作人Uid
	Operator          string `gorm:"column:operator" json:"operator"`                     // 操作人
	IsDeleted         bool   `gorm:"column:is_deleted" json:"isDeleted"`                  // 有效/已删除
	DeletedID         int64  `gorm:"column:deleted_id" json:"deleted_id"`                 // 已删除的记录 id
	CreateTime        int64  `gorm:"column:create_time" json:"createTime"`                // 创建时间
	UpdateTime        int64  `gorm:"column:update_time" json:"updateTime"`                // 上传完成时间
}

func (l *LaxinTagDataFix) TableName() string {
	return "tblLaxinTagDataFix"
}

func (l laxinTagDataFixDao) BatchCreate(ctx *gin.Context, data []*LaxinTagDataFix, tx *gorm.DB) error {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		db := tx.WithContext(ctx).Model(&LaxinTagDataFix{})
		return db.CreateInBatches(data, 100).Error
	}

	return helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFix{}).CreateInBatches(data, components.CreateBatchSize).Error // Clauses(clause.Insert{Modifier: "IGNORE"})
}

func (l laxinTagDataFixDao) BatchDelete(ctx *gin.Context, ids []int64, tx *gorm.DB) error {
	if len(ids) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"is_deleted":  true,
		"update_time": time.Now().Unix(),
		"deleted_id":  gorm.Expr("id"),
	}

	if tx != nil {
		db := tx.WithContext(ctx).Model(&LaxinTagDataFix{})
		return db.Where("id in (?) and is_deleted = ? ", ids, false).Updates(updates).Error
	}

	return helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFix{}).Where("id in (?) and is_deleted = ? ", ids, false).Updates(updates).Error
}

func (l laxinTagDataFixDao) GetListByCond(ctx *gin.Context, cond string) (res []*LaxinTagDataFix, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFix{}).Where(cond).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (l laxinTagDataFixDao) GetListByPage(ctx *gin.Context, page, size int, courseID []int, email, phone string) (res []*LaxinTagDataFix, count int64, err error) {
	offset := (page - 1) * size

	conditions := make(map[string]interface{}, 0)

	if len(courseID) != 0 {
		conditions["course_id"] = courseID
	}

	if email != "" {
		conditions["person_email_prefix"] = email
	}

	if phone != "" {
		conditions["assistant_phone"] = phone
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFix{}).Where(conditions).Count(&count).Error
	if err != nil {
		return nil, count, err
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFix{}).Where(conditions).Order("id DESC").Offset(offset).Limit(size).Find(&res).Error
	if err != nil {
		return nil, count, err
	}
	return res, count, nil
}

func IsDuplicateEntryErrorForLaxinTagDataFix(err error) bool {
	var errNew *mysql.MySQLError
	if errors.As(err, &errNew) && errNew.Number == 1062 && strings.Contains(errNew.Message, "idx_course_id_phone_number_email_deleted_id") {
		return true
	}
	return false
}
