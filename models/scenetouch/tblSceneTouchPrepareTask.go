package scenetouch

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
)

const (
	TaskStatusInit    = 0
	TaskStatusRunning = 1
	TaskStatusSuccess = 2
	TaskStatusFail    = 3
	TaskStatusCancel  = 4
)

// TblSceneTouchPrepareTask 场景化群发数据准备任务
type TblSceneTouchPrepareTask struct {
	Id         int64  `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键id'"`
	JobId      int64  `gorm:"column:job_id;default:0;NOT NULL;comment:'作业ID'"`
	GroupName  string `gorm:"column:group_name;default:;NOT NULL;comment:'分组'"`
	StudentUid int64  `gorm:"column:student_uid;default:0;NOT NULL;comment:'学员UID'"`
	Variables  string `gorm:"column:variables;comment:'变量值'"`
	Extra      string `gorm:"column:extra;comment:'额外数据'"`
	Status     int    `gorm:"column:status;default:0;NOT NULL;comment:'状态 0:待分配执行 1:已分配执行 2:执行成功 3:执行失败 4:取消执行'"`
	Retry      int    `gorm:"column:retry;default:0;NOT NULL;comment:'重试次数'"`
	CreateTime int64  `gorm:"column:create_time;default:0;NOT NULL;comment:'创建时间'"`
	UpdateTime int64  `gorm:"column:update_time;default:0;NOT NULL;comment:'修改时间'"`
}

// TableName 表名
func (t *TblSceneTouchPrepareTask) TableName() string {
	return "tblSceneTouchPrepareTask"
}

func (t *TblSceneTouchPrepareTask) GetTaskListByJobId(ctx *gin.Context, jobId int64, onlyFail bool) ([]*TblSceneTouchPrepareTask, error) {
	var taskList []*TblSceneTouchPrepareTask
	tx := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("job_id = ?", jobId)
	if onlyFail {
		tx = tx.Where("status = ?", TaskStatusFail)
	}
	err := tx.Find(&taskList).Error
	if err != nil {
		return nil, err
	}
	return taskList, nil
}

func (t *TblSceneTouchPrepareTask) GetSuccessTaskListByJobIdStudentUids(ctx *gin.Context, jobId int64, studentUids []int64) ([]*TblSceneTouchPrepareTask, error) {
	if len(studentUids) == 0 {
		return nil, nil
	}
	var taskList []*TblSceneTouchPrepareTask
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("job_id = ? AND student_uid IN (?) AND status = ?", jobId, studentUids, TaskStatusSuccess).Find(&taskList).Error
	if err != nil {
		return nil, err
	}
	return taskList, nil
}

func (t *TblSceneTouchPrepareTask) GetTaskListByIdList(ctx *gin.Context, idList []int64) ([]*TblSceneTouchPrepareTask, error) {
	if len(idList) == 0 {
		return nil, nil
	}
	var taskList []*TblSceneTouchPrepareTask
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("id IN (?)", idList).Find(&taskList).Error
	if err != nil {
		return nil, err
	}
	return taskList, nil
}
