package scenetouch

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/hints"

	"github.com/gin-gonic/gin"
)

const (
	JobStatusInit    = 0
	JobStatusRunning = 1
	JobStatusDone    = 2
	JobStatusCancel  = 3
)

const (
	HintsReadWrite = "/*#mode=READWRITE*/"
	HintsReadOnly  = "/*#mode=READONLY*/"
)

// TblSceneTouchPrepareJob 场景化群发数据准备作业
type TblSceneTouchPrepareJob struct {
	Id           int64  `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键id'"`
	AssistantUid int64  `gorm:"column:assistant_uid;default:0;NOT NULL;comment:'辅导UID'"`
	CourseId     int64  `gorm:"column:course_id;default:0;NOT NULL;comment:'课程ID'"`
	LessonId     int64  `gorm:"column:lesson_id;default:0;NOT NULL;comment:'章节ID'"`
	SceneType    int    `gorm:"column:scene_type;default:0;NOT NULL;comment:'场景类型'"`
	GroupList    string `gorm:"column:group_list;comment:'分组列表'"`
	SceneContext string `gorm:"column:scene_context;comment:'场景上下文'"`
	ParamKey     string `gorm:"column:param_key;default:;NOT NULL;comment:'参数区分键'"`
	SuccessNum   int    `gorm:"column:success_num;default:0;NOT NULL;comment:'成功任务数'"`
	FailNum      int    `gorm:"column:fail_num;default:0;NOT NULL;comment:'失败任务数'"`
	TotalNum     int    `gorm:"column:total_num;default:0;NOT NULL;comment:'总任务数'"`
	Status       int    `gorm:"column:status;default:0;NOT NULL;comment:'状态 0:未启动 1:执行中 2:执行完成 3:取消执行'"`
	StartTime    int64  `gorm:"column:start_time;default:0;NOT NULL;comment:'启动时间'"`
	EndTime      int64  `gorm:"column:end_time;default:0;NOT NULL;comment:'结束时间'"`
	ExpireTime   int64  `gorm:"column:expire_time;default:0;NOT NULL;comment:'失效时间'"`
	Creator      int64  `gorm:"column:creator;default:0;NOT NULL;comment:'创建者'"`
	Updater      int64  `gorm:"column:updater;default:0;NOT NULL;comment:'更新者'"`
	CreateTime   int64  `gorm:"column:create_time;default:0;NOT NULL;comment:'创建时间'"`
	UpdateTime   int64  `gorm:"column:update_time;default:0;NOT NULL;comment:'修改时间'"`
}

// TableName 表名
func (t *TblSceneTouchPrepareJob) TableName() string {
	return "tblSceneTouchPrepareJob"
}

func (t *TblSceneTouchPrepareJob) GetLatestValidJobByParamKey(ctx *gin.Context, paramKey string, expireTime int64) (*TblSceneTouchPrepareJob, error) {
	var job TblSceneTouchPrepareJob
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("param_key = ? AND expire_time > ? AND status != ?", paramKey, expireTime, JobStatusCancel).Order("create_time DESC").First(&job).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

func (t *TblSceneTouchPrepareJob) GetJobById(ctx *gin.Context, jobId int64, hitMaster bool) (*TblSceneTouchPrepareJob, error) {
	var job TblSceneTouchPrepareJob

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("id = ?", jobId)
	if hitMaster {
		db.Clauses(hints.NewHint(HintsReadWrite))
	}
	err := db.First(&job).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}
