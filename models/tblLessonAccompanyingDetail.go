package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
)

const (
	StatusExist   = 0
	StatusDeleted = 1
)

type LessonAccompanyingDetail struct {
	ID         int64 `gorm:"id" json:"id"` // 自增id
	CourseId   int64 `gorm:"course_id" json:"courseId"`
	LessonId   int64 `gorm:"lesson_id" json:"lessonId"`
	StartTime  int64 `gorm:"start_time" json:"startTime"`
	EndTime    int64 `gorm:"end_time" json:"endTime"`
	BanxueId   int64 `gorm:"banxue_id" json:"banxueId"`
	Deleted    int   `gorm:"deleted" json:"deleted"`
	CreateTime int64 `gorm:"create_time" json:"createTime"`
	UpdateTime int64 `gorm:"update_time" json:"updateTime"`
}

func (l *LessonAccompanyingDetail) TableName() string {
	tableName := "tblLessonAccompanyingDetail"
	return tableName
}

func (l *LessonAccompanyingDetail) GetAllLessonDetail(ctx *gin.Context, courseID, lessonID int64) ([]*LessonAccompanyingDetail, error) {
	var res []*LessonAccompanyingDetail
	err := helpers.MysqlClientFuDao.Table(l.TableName()).WithContext(ctx).
		Where("course_id = ? and lesson_id = ? and deleted = ?", courseID, lessonID, StatusExist).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
