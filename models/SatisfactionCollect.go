package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	SatisfactionCollectSourceType = 1 // 方舟模板评价
)

const (
	SatisfactionCollectScoreType = 1 // 五分制打分
)

type SatisfactionCollect struct {
	ID           int64  `gorm:"primary_key;column:id;auto_increment" json:"id"`
	SourceType   int64  `gorm:"column:source_type" json:"sourceType"`
	BusinessKey  string `gorm:"column:business_key" json:"businessKey"`
	ScoreType    int64  `gorm:"column:score_type" json:"scoreType"`
	Score        int64  `gorm:"column:score" json:"score"`
	UserComment  string `gorm:"column:user_comment" json:"userComment"`
	ExtraID      int64  `gorm:"column:extra_id" json:"extraId"`
	ExtraInfo    string `gorm:"column:extra_info" json:"extraInfo"`
	AssistantUID int64  `gorm:"column:assistant_uid" json:"assistantUid"`
	IsDeleted    bool   `gorm:"column:is_deleted" json:"isDeleted"`
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime   int64  `gorm:"column:update_time" json:"updateTime"`
}

func (l *SatisfactionCollect) TableName() string {
	return "tblSatisfactionCollect"
}

func (l *SatisfactionCollect) Create(ctx *gin.Context, comment *SatisfactionCollect) (int64, error) {
	now := time.Now().Unix()
	comment.CreateTime = now
	comment.UpdateTime = now

	err := helpers.MysqlClientFuDao.WithContext(ctx).Create(comment).Error
	if err != nil {
		return 0, err
	}
	return comment.ID, nil
}
