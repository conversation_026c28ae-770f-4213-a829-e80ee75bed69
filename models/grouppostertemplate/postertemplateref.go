package grouppostertemplate

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

const (
	refTableName = "tblGroupPosterTemplateRef"

	batchSize = 100
)

type PosterTemplateRef struct {
	Id               int64 `gorm:"primary_key;column:id" json:"id"`
	PosterTemplateId int64 `gorm:"column:poster_template_id" json:"posterTemplateId"`
	GroupId          int64 `gorm:"column:group_id" json:"groupId"`
	Deleted          int64 `gorm:"column:deleted" json:"deleted"`
}

func (ptf *PosterTemplateRef) TableName() string {
	return refTableName
}
func getRefTableName(ctx *gin.Context) string {
	return refTableName
}

func saveRefList(ctx *gin.Context, tx *gorm.DB, gptId int64, ptfList []*PosterTemplateRef) (err error) {
	var oldList []*PosterTemplateRef
	err = tx.Table(getRefTableName(ctx)).
		Where("poster_template_id = ?", gptId).
		Find(&oldList).Error
	if err != nil {
		return
	}

	// init
	oldGroupIdMap := make(map[int64]*PosterTemplateRef)
	for _, ref := range oldList {
		oldGroupIdMap[ref.GroupId] = ref
	}
	newGroupIdMap := make(map[int64]*PosterTemplateRef)
	for _, ref := range ptfList {
		ref.PosterTemplateId = gptId
		newGroupIdMap[ref.GroupId] = ref
	}

	// add update
	addRefs := make([]*PosterTemplateRef, 0)
	updateIds := make([]int64, 0)
	for _, ref := range ptfList {
		old, ok := oldGroupIdMap[ref.GroupId]
		if !ok {
			addRefs = append(addRefs, ref)
			continue
		}

		if old.Deleted == DeletedNo {
			// skip
			continue
		}

		updateIds = append(updateIds, old.Id)
	}
	if len(addRefs) > 0 {
		err = tx.Table(getRefTableName(ctx)).
			CreateInBatches(addRefs, batchSize).Error
		if err != nil {
			return
		}
	}
	if len(updateIds) > 0 {
		err = tx.Table(getRefTableName(ctx)).
			Where("id in (?)", updateIds).
			Update("deleted", DeletedNo).Error
		if err != nil {
			return
		}
	}

	// delete
	deletedIds := make([]int64, 0)
	for _, old := range oldList {
		if _, ok := newGroupIdMap[old.GroupId]; !ok {
			deletedIds = append(deletedIds, old.Id)
		}
	}
	if len(deletedIds) > 0 {
		err = tx.Table(getRefTableName(ctx)).
			Where("id in (?)", deletedIds).
			Update("deleted", DeletedYes).Error
		if err != nil {
			return
		}
	}

	return
}

func deleteRefByGPTId(ctx *gin.Context, tx *gorm.DB, gptId int64) (err error) {
	err = tx.Table(getRefTableName(ctx)).
		Where("poster_template_id = ?", gptId).
		Update("deleted", DeletedYes).Error
	return
}

func findRefByGPTId(ctx *gin.Context, tx *gorm.DB, gptId int64) (ptfList []*PosterTemplateRef, err error) {
	err = tx.Table(getRefTableName(ctx)).
		Where("poster_template_id = ?", gptId).
		Where("deleted = ?", DeletedNo).
		Find(&ptfList).Error
	return
}

func findRefByGPTIds(ctx *gin.Context, tx *gorm.DB, gptIds []int64) (ptfListMap map[int64][]*PosterTemplateRef, err error) {
	var ptfList []*PosterTemplateRef
	err = tx.Table(getRefTableName(ctx)).
		Where("poster_template_id in (?)", gptIds).
		Where("deleted = ?", DeletedNo).
		Find(&ptfList).Error
	if err != nil {
		return
	}

	ptfListMap = make(map[int64][]*PosterTemplateRef)
	for _, ref := range ptfList {
		list, exist := ptfListMap[ref.PosterTemplateId]
		if !exist {
			list = make([]*PosterTemplateRef, 0)
		}
		list = append(list, ref)
		ptfListMap[ref.PosterTemplateId] = list
	}
	return
}

func findRefByGroupIds(ctx *gin.Context, tx *gorm.DB, groupIds []int64) (ptfList []*PosterTemplateRef, err error) {
	err = tx.Table(getRefTableName(ctx)).
		Where("group_id in (?)", groupIds).
		Where("deleted = ?", DeletedNo).
		Find(&ptfList).Error
	return
}
