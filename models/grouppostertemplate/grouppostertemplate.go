package grouppostertemplate

import (
	"assistantdeskgo/helpers"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"strings"
	"time"
	"unicode/utf8"
)

const (
	TableName = "tblGroupPosterTemplate"

	TplTypePayQrCode = 1 // 支付二维码海报

	TplStyleHorizon  = 1 // 横版
	TplStyleVertical = 2 // 竖版

	DeletedYes = 1 // 已删除
	DeletedNo  = 0 // 未删除
)

type GroupPosterTemplate struct {
	Id         int64  `gorm:"primary_key;column:id" json:"id"`
	CreateTime int64  `gorm:"column:create_time" json:"createTime"`
	Creator    string `gorm:"column:creator" json:"creator"`
	UpdateTime int64  `gorm:"column:update_time" json:"updateTime"`
	Updater    string `gorm:"column:updater" json:"updater"`
	TplName    string `gorm:"column:tpl_name" json:"tplName"`
	TplType    int64  `gorm:"column:tpl_type" json:"tplType"`
	TplStyle   int64  `gorm:"column:tpl_style" json:"tplStyle"`
	TplDesc    string `gorm:"column:tpl_desc" json:"tplDesc"`
	TplContent string `gorm:"column:tpl_content" json:"tplContent"`
	Deleted    int64  `gorm:"column:deleted" json:"deleted"`

	TplGroupIds          []*PosterTemplateRef `gorm:"-"`
	notUpdateTplGroupIds bool                 `gorm:"-"`
}

func GetTplTypeOptions() map[int64]string {
	return map[int64]string{
		TplTypePayQrCode: "支付二维码海报",
	}
}

func GetTplStyleOptions() map[int64]string {
	return map[int64]string{
		TplStyleHorizon:  "横版",
		TplStyleVertical: "竖版",
	}
}

func ApplyFields(gpt *GroupPosterTemplate, tplName string, tplType, tplStyle int64, tplGroupIds []int64, tplDesc, tplContent string) (err error) {
	if gpt == nil {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, gpt empty"))
		return
	}

	if len(tplName) == 0 {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板名称 不能为空"))
		return
	}
	if utf8.RuneCountInString(tplName) > 50 {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板名称[%s] 长度不能大于50字符", tplName))
		return
	}
	gpt.TplName = tplName

	if _, ok := GetTplTypeOptions()[tplType]; !ok {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板类型[%d] 不合法", tplType))
		return
	}
	gpt.TplType = tplType

	if _, ok := GetTplStyleOptions()[tplStyle]; !ok {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板样式[%d] 不合法", tplStyle))
		return
	}
	gpt.TplStyle = tplStyle

	if len(tplGroupIds) == 0 {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板归属二级团队 不能为空"))
		return
	}
	// distinct
	groupIdMap := make(map[int64]int64)
	groupIds := make([]*PosterTemplateRef, 0)
	for _, groupId := range tplGroupIds {
		if _, ok := groupIdMap[groupId]; ok {
			continue
		}
		groupIdMap[groupId] = groupId
		groupIds = append(groupIds, &PosterTemplateRef{
			GroupId: groupId,
			Deleted: DeletedNo,
		})
	}
	if gpt.TplGroupIds != nil && len(gpt.TplGroupIds) == len(groupIds) {
		rep := true
		for _, ref := range gpt.TplGroupIds {
			if _, ok := groupIdMap[ref.GroupId]; !ok {
				rep = false
				break
			}
		}
		if rep {
			gpt.notUpdateTplGroupIds = true
		} else {
			gpt.TplGroupIds = groupIds
		}
	} else {
		gpt.TplGroupIds = groupIds
	}

	if utf8.RuneCountInString(tplDesc) > 200 {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板描述[%s] 长度不能大于200字符", tplDesc))
		return
	}
	gpt.TplDesc = tplDesc

	if len(tplContent) == 0 {
		err = errors.New(fmt.Sprintf("GroupPosterTemplate.ApplyFields error, 模板内容 不能为空"))
		return
	}
	gpt.TplContent = tplContent
	return
}

func (gpt *GroupPosterTemplate) GetGroupIds(ctx *gin.Context) []int64 {
	if gpt.TplGroupIds == nil {
		zlog.Infof(ctx, "GroupPosterTemplate.GetGroupIds not init gpt: %+v", gpt)
	}

	groupIds := make([]int64, 0, len(gpt.TplGroupIds))
	for _, templateRef := range gpt.TplGroupIds {
		groupIds = append(groupIds, templateRef.GroupId)
	}
	return groupIds
}

func (gpt *GroupPosterTemplate) GetUpdaterInfo() (userId int64, userName string) {
	if len(gpt.Updater) == 0 {
		return
	}

	splits := strings.Split(gpt.Updater, "_")
	if len(splits) == 0 {
		return
	}
	userId = cast.ToInt64(splits[0])

	if len(splits) <= 1 {
		return
	}
	userName = splits[1]
	return
}

// 以上是model相关方法，以下是持久层交互方法

func (gpt *GroupPosterTemplate) TableName() string {
	return TableName
}
func getDB(ctx *gin.Context) *gorm.DB {
	return helpers.MysqlClientFuDao.WithContext(ctx)
}
func getTableName(ctx *gin.Context) string {
	return TableName
}

func (gpt *GroupPosterTemplate) Save(ctx *gin.Context, userId int, userName string) (id int64, err error) {
	now := time.Now().Unix()
	updater := fmt.Sprintf("%d_%s", userId, userName)
	gpt.UpdateTime = now
	gpt.Updater = updater

	tx := getDB(ctx).Begin()
	if gpt.Id > 0 {
		// update
		up := tx.Table(getTableName(ctx)).
			Where("id = ?", gpt.Id).
			Updates(gpt)
		err = up.Error
		if err == nil && up.RowsAffected == 0 {
			err = errors.New(fmt.Sprintf("GroupPosterTemplate.Save Updates no affected"))
		}
		if err != nil {
			tx.Rollback()
			zlog.Warnf(ctx, "GroupPosterTemplate.Save Updates error, gpt: %+v, err: %+v", gpt, err)
			return
		}

		// update ref
		if !gpt.notUpdateTplGroupIds {
			err = saveRefList(ctx, tx, gpt.Id, gpt.TplGroupIds)
		}
	} else {
		// add
		gpt.CreateTime = now
		gpt.Creator = updater
		err = tx.Table(getTableName(ctx)).Create(&gpt).Error
		if err != nil {
			tx.Rollback()
			zlog.Warnf(ctx, "GroupPosterTemplate.Save Create error, gpt: %+v, err: %+v", gpt, err)
			return
		}

		// add ref
		err = saveRefList(ctx, tx, gpt.Id, gpt.TplGroupIds)
	}
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.Save saveRefList error, refList: %+v, err: %+v", gpt.TplGroupIds, err)
		return
	}
	id = gpt.Id
	tx.Commit()
	return
}

func (gpt *GroupPosterTemplate) Delete(ctx *gin.Context, userId int, userName string) (err error) {
	now := time.Now().Unix()
	gpt.UpdateTime = now
	gpt.Updater = fmt.Sprintf("%d_%s", userId, userName)
	gpt.Deleted = DeletedYes

	tx := getDB(ctx).Begin()
	// delete
	err = tx.Table(getTableName(ctx)).
		Where("id = ?", gpt.Id).
		Select([]string{"update_time", "updater", "deleted"}).
		Updates(gpt).Error
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.Delete error, gpt: %+v, err: %+v", gpt, err)
		return
	}

	// delete ref
	err = deleteRefByGPTId(ctx, tx, gpt.Id)
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.Delete deleteRefByGPTId error, gpt: %+v, err: %+v", gpt, err)
		return
	}

	tx.Commit()
	return
}

// 上面 写接口 下面 读接口；Get 查单条，Find 查多条

func GetById(ctx *gin.Context, id int64) (gpt *GroupPosterTemplate, err error) {
	tx := getDB(ctx).Begin()
	// get
	err = tx.Table(getTableName(ctx)).
		Where("id = ?", id).
		Take(&gpt).Error
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.GetById error, id: %d, err: %+v", id, err)
		return
	}

	// get ref
	templateRefs, fErr := findRefByGPTId(ctx, tx, gpt.Id)
	if fErr != nil {
		tx.Rollback()
		err = fErr
		zlog.Warnf(ctx, "GroupPosterTemplate.GetById findRefByGPTId error, id: %d, err: %+v", id, err)
		return
	}
	gpt.TplGroupIds = templateRefs

	tx.Commit()
	return
}

func FindByPaged(ctx *gin.Context, id, tplType, tplStyle int64, tplGroupIds []int64, tplName string, pn, rn int64) (gptList []*GroupPosterTemplate, total int64, err error) {
	var gptIdListMap map[int64][]*PosterTemplateRef
	tx := getDB(ctx).Begin()
	tx1 := tx.Table(getTableName(ctx))
	tx1.Where("deleted = ?", DeletedNo)
	if id > 0 {
		// 有id即精确查询
		tx1 = tx1.Where("id = ?", id)
		total = 1
	} else {
		if len(tplGroupIds) > 0 {
			var tplIds []int64
			tplIds, gptIdListMap, err = findTplIdsByGroupIds(ctx, tx, tplGroupIds)
			if err != nil {
				tx.Rollback()
				return
			}

			if len(tplIds) == 0 {
				tx.Rollback()
				return
			}

			tx1.Where("id in (?)", tplIds)
		}
		// 其他条件
		if tplType > 0 {
			tx1 = tx1.Where("tpl_type = ?", tplType)
		}
		if tplStyle > 0 {
			tx1 = tx1.Where("tpl_style = ?", tplStyle)
		}
		if len(tplName) > 0 {
			tx1 = tx1.Where("tpl_name like ?", "%"+tplName+"%")
		}
		err = tx1.Count(&total).Error
		if err != nil {
			tx.Rollback()
			zlog.Warnf(ctx, "GroupPosterTemplate.FindByPaged count error, err: %+v", err)
			return
		}
		if rn > 0 {
			tx1 = tx1.Offset(int(pn * rn)).Limit(int(rn))
		}
	}
	if total == 0 {
		tx.Rollback()
		return
	}

	err = tx1.Order("id desc").Find(&gptList).Error
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.FindByPaged find error, err: %+v", err)
		return
	}

	// find ref
	gptList, err = applyTemplateRef(ctx, tx, gptList, gptIdListMap)
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.FindByPaged applyTemplateRef error, err: %+v", err)
		return
	}

	tx.Commit()
	return
}

func findTplIdsByGroupIds(ctx *gin.Context, tx *gorm.DB, tplGroupIds []int64) (tplIds []int64, gptIdListMap map[int64][]*PosterTemplateRef, err error) {
	var templateRefs []*PosterTemplateRef
	templateRefs, err = findRefByGroupIds(ctx, tx, tplGroupIds)
	if err != nil {
		zlog.Warnf(ctx, "GroupPosterTemplate.findTplIdsByGroupIds findRefByGroupIds error, tplGroupIds: %+v, err: %+v", tplGroupIds, err)
		return
	}
	// 子表无数据
	if len(templateRefs) == 0 {
		return
	}

	gptIdListMap = make(map[int64][]*PosterTemplateRef)
	for _, templateRef := range templateRefs {
		list, exist := gptIdListMap[templateRef.PosterTemplateId]
		if !exist {
			list = make([]*PosterTemplateRef, 0)
		}
		list = append(list, templateRef)
		gptIdListMap[templateRef.PosterTemplateId] = list
	}

	tplIds = make([]int64, 0, len(gptIdListMap))
	for k, _ := range gptIdListMap {
		tplIds = append(tplIds, k)
	}
	return
}

func applyTemplateRef(ctx *gin.Context, tx *gorm.DB, gptList []*GroupPosterTemplate, gptIdListMap map[int64][]*PosterTemplateRef) (newGptList []*GroupPosterTemplate, err error) {
	if len(gptList) == 0 {
		newGptList = gptList
		return
	}
	ids := make([]int64, 0, len(gptList))
	for _, template := range gptList {
		ids = append(ids, template.Id)
	}
	if gptIdListMap == nil {
		gptIdListMap, err = findRefByGPTIds(ctx, tx, ids)
		if err != nil {
			zlog.Warnf(ctx, "GroupPosterTemplate.applyTemplateRef findRefByGPTIds error, err: %+v", err)
			return
		}
	}
	for _, template := range gptList {
		template.TplGroupIds = gptIdListMap[template.Id]
	}

	newGptList = gptList
	return
}

func FindByTypeGroupIds(ctx *gin.Context, tplType int64, tplGroupIds []int64) (gptList []*GroupPosterTemplate, err error) {
	var gptIdListMap map[int64][]*PosterTemplateRef
	tx := getDB(ctx).Begin()
	tx1 := tx.Table(getTableName(ctx))
	tx1.Where("deleted = ?", DeletedNo)
	if tplType > 0 {
		tx1.Where("tpl_type = ?", tplType)
	}
	if len(tplGroupIds) > 0 {
		var tplIds []int64
		tplIds, gptIdListMap, err = findTplIdsByGroupIds(ctx, tx, tplGroupIds)
		if err != nil {
			tx.Rollback()
			return
		}

		if len(tplIds) == 0 {
			tx.Rollback()
			return
		}

		tx1.Where("id in (?)", tplIds)
	}
	err = tx1.Order("id desc").Find(&gptList).Error
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.FindByTypeGroupIds find error, err: %+v", err)
		return
	}

	// find ref
	gptList, err = applyTemplateRef(ctx, tx, gptList, gptIdListMap)
	if err != nil {
		tx.Rollback()
		zlog.Warnf(ctx, "GroupPosterTemplate.FindByPaged applyTemplateRef error, err: %+v", err)
		return
	}
	tx.Commit()

	return
}
