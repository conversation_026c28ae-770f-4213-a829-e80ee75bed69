package models

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	"time"
)

// 上传任务执行状态
const (
	LaxinTagDataFixTaskStatusPending     = 1 // 待启动
	LaxinTagDataFixTaskStatusInProgress  = 2 // 执行中
	LaxinTagDataFixTaskStatusSuccess     = 3 // 执行成功
	LaxinTagDataFixTaskStatusPartialFail = 4 // 执行失败
)

// 上传操作类型
const (
	LaxinTagDataFixTaskOperaTypeCreate = 1 // 新建数据
	LaxinTagDataFixTaskOperaTypeDelete = 2 // 删除数据
)

var LaxinTagDataFixTaskDao laxinTagDataFixTaskDao

type laxinTagDataFixTaskDao struct {
}

type LaxinTagDataFixTask struct {
	ID             int64  `gorm:"primary_key;column:id" json:"id"`               // 任务 id
	OriginFileName string `gorm:"column:origin_file_name" json:"originFileName"` // 原始文件名称
	CommentInfo    string `gorm:"column:comment_info" json:"comment_info"`       // 备忘
	FilePath       string `gorm:"column:file_path" json:"filePath"`              // 远程文件名称
	RetFile        string `gorm:"column:ret_file" json:"retFile"`                // 结果文件名称
	Status         int    `gorm:"column:status" json:"status"`                   // 上传状态
	FailReason     string `gorm:"column:fail_reason" json:"failReason"`          // 失败原因
	OperatorType   int    `gorm:"column:operator_type" json:"operatorType"`      // 操作类型：创建/删除
	OperatorUid    int64  `gorm:"column:operator_uid" json:"operatorUid"`        // 操作人 uid userInfo 获取
	OperatorName   string `gorm:"column:operator_name" json:"operatorName"`      // 操作人姓名 userInfo 获取
	CreateTime     int64  `gorm:"column:create_time" json:"createTime"`          // 创建时间
	UpdateTime     int64  `gorm:"column:update_time" json:"updateTime"`          // 上传完成时间
	IsDeleted      bool   `gorm:"column:is_deleted" json:"isDeleted"`
}

func (l *LaxinTagDataFixTask) TableName() string {
	return "tblLaxinTagDataFixTask"
}

func (l *laxinTagDataFixTaskDao) Create(ctx *gin.Context, task *LaxinTagDataFixTask) (int64, error) {
	err := helpers.MysqlClientFuDao.WithContext(ctx).Create(task).Error
	if err != nil {
		return 0, err
	}
	return task.ID, nil
}

func (l *laxinTagDataFixTaskDao) UpdateByMap(ctx *gin.Context, taskID int64, updates map[string]interface{}) error {
	updates["update_time"] = time.Now().Unix()
	return helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFixTask{}).Where("id = ? and is_deleted = ? ", taskID, false).Updates(updates).Error
}

func (l *laxinTagDataFixTaskDao) GetByTaskID(ctx *gin.Context, taskID int64) (res *LaxinTagDataFixTask, err error) {
	conditions := map[string]interface{}{
		"id":         taskID,
		"is_deleted": false,
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFixTask{}).Where(conditions).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (l *laxinTagDataFixTaskDao) GetByStatusAndOperator(ctx *gin.Context, status, page, size int, operatorName string) (res []*LaxinTagDataFixTask, count int64, err error) {
	offset := (page - 1) * size
	conditions := map[string]interface{}{
		"is_deleted": false,
	}

	if status != 0 {
		conditions["status"] = status
	}

	if operatorName != "" {
		conditions["operator_name"] = operatorName
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFixTask{}).Where(conditions).Count(&count).Error
	if err != nil {
		return nil, count, err
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFixTask{}).Where(conditions).Order("id DESC").Offset(offset).Limit(size).Find(&res).Error
	if err != nil {
		return nil, count, err
	}
	return res, count, nil
}

func (l *laxinTagDataFixTaskDao) GetList(ctx *gin.Context, lastID int64) (res []*LaxinTagDataFixTask, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("id > ? and is_deleted = ?", lastID, false).Order("id ASC").Limit(components.GetBatchSize).Find(&res).Error
	if err != nil {
		return res, err
	}
	return res, nil
}

func (l *laxinTagDataFixTaskDao) GetListByCreateTime(ctx *gin.Context, lastID int64, createTime int64) (res []*LaxinTagDataFixTask, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("id > ? and create_time > ? and is_deleted = ?", lastID, createTime, false).Order("id ASC").Limit(components.GetBatchSize).Find(&res).Error
	if err != nil {
		return res, err
	}
	return res, nil
}
