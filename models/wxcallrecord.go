package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type WxCallRecord struct {
	CallId          int64  `gorm:"primary_key;column:call_id" json:"call_id"`         //  CallId
	FromUid         int64  `gorm:"column:from_uid" json:"from_uid"`                   //  主叫方的uid
	ToUid           int64  `gorm:"column:to_uid" json:"to_uid"`                       //  被叫方的uid
	StartTime       int64  `gorm:"column:start_time" json:"start_time"`               //  开始接听时间
	StopTime        int64  `gorm:"column:stop_time" json:"stop_time"`                 //  结束接听时间
	Duration        int64  `gorm:"column:duration" json:"duration"`                   //  通话时长
	CallType        int64  `gorm:"column:call_type" json:"call_type"`                 //  '通话类型 1-呼出 2-呼入
	MsgType         int64  `gorm:"column:msg_type" json:"msg_type"`                   //  通话类型16语音通话 17视频通话
	CallResult      int64  `gorm:"column:call_result" json:"call_result"`             //  通话状态 0初始化(异常) 1正常结束 2未接通 3拒接 4取消
	RecordFile      string `gorm:"column:record_file" json:"record_file"`             //  文件
	DeviceUid       int64  `gorm:"column:device_uid" json:"device_uid"`               //  呼叫班主任uid
	PersonUid       int64  `gorm:"column:person_uid" json:"person_uid"`               //  呼叫班主任真人uid
	StudentRemoteId string `gorm:"column:student_remote_id" json:"student_remote_id"` //  呼叫班主任真人uid
	Deleted         int64  `gorm:"column:deleted" json:"deleted"`                     //  是否删除
	CreateTime      int64  `gorm:"column:create_time" json:"create_time"`             //  创建时间
	UpdateTime      int64  `gorm:"column:update_time" json:"update_time"`             //  更新时间
}

func (a *WxCallRecord) TableName() string {
	return "tblWxCallRecord"
}

func (a *WxCallRecord) GetByCallId(ctx *gin.Context, callId int64) (response WxCallRecord, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByCallId List failed, callId=%v", callId, err)
	}

	return
}

func (a *WxCallRecord) ListByToUidAndFromUidAndTimeRange(ctx *gin.Context, toUid int64, fromUid int64, time int64) (response []WxCallRecord, err error) {
	conditions := map[string]interface{}{
		"to_uid":    toUid,
		"from_uid":  fromUid,
		"call_type": 1,
		"deleted":   0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName()).
		Where(conditions).
		Where("create_time > ?", time).
		Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByCallId List failed, toUid=%v", toUid, err)
	}

	return
}

func (a *WxCallRecord) Insert(ctx *gin.Context, wxCallRecord WxCallRecord) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Create(wxCallRecord).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", wxCallRecord, err)
	}
	return
}

func (a *WxCallRecord) Update(ctx *gin.Context, wxCallRecord WxCallRecord) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Updates(wxCallRecord).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", wxCallRecord, err)
	}
	return
}
