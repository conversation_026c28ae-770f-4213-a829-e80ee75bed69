package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	IsOpenYes = 1
	IsOpenNo  = 0
)

type ExplainConfItem struct {
	Tag  string `json:"tag"`
	Text string `json:"text"`
}

type FeedbackPointTargetExplain struct {
	Id          int64  `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键id'" json:"id"`
	CpuId       int64  `gorm:"column:cpu_id;default:0;NOT NULL;comment:'课程cpuId'" json:"cpuId"`
	PointId     int64  `gorm:"column:point_id;default:0;NOT NULL;comment:'知识目标id'" json:"pointId"`
	MasterLevel int    `gorm:"column:master_level;default:0;NOT NULL;comment:'正确率等级 1:S 2:A 3:B 4:C'" json:"masterLevel"`
	LowerLimit  string `gorm:"column:lower_limit;default:0;NOT NULL;comment:'下限'" json:"lowerLimit"`
	LowerIsOpen int    `gorm:"column:lower_is_open;default:0;NOT NULL;comment:'下限是否为开区间 1:是 0:否'" json:"lowerIsOpen"`
	UpperLimit  string `gorm:"column:upper_limit;default:0;NOT NULL;comment:'上限'" json:"upperLimit"`
	UpperIsOpen int    `gorm:"column:uppper_is_open;default:0;NOT NULL;comment:'上限是否为开区间 1:是 0:否'" json:"uppperIsOpen"`
	ExplainConf string `gorm:"column:explain_conf;default:;NOT NULL;comment:'解读话术配置'" json:"explainConf"`
	Creator     int64  `gorm:"column:creator;default:0;NOT NULL;comment:'创建者'" json:"creator"`
	Updater     int64  `gorm:"column:updater;default:0;NOT NULL;comment:'更新者'" json:"updater"`
	CreateTime  int64  `gorm:"column:create_time;default:0;NOT NULL;comment:'创建时间'" json:"createTime"`
	UpdateTime  int64  `gorm:"column:update_time;default:0;NOT NULL;comment:'更新时间'" json:"updateTime"`
}

func (t *FeedbackPointTargetExplain) TableName() string {
	return "tblFeedbackPointTargetExplain"
}

func (t *FeedbackPointTargetExplain) GetExplainByPointIdList(ctx *gin.Context, cpuId int64, idList []int64) (list []FeedbackPointTargetExplain, err error) {
	if cpuId == 0 || len(idList) == 0 {
		return
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("cpu_id = ? and point_id in ?", cpuId, idList).Find(&list).Error
	if err != nil {
		return
	}
	return
}

func (t *FeedbackPointTargetExplain) GetExplainList(ctx *gin.Context, cpuId, pointId int64, pn, rn int) (list []FeedbackPointTargetExplain, count int64, err error) {
	offset := pn * rn
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName())
	if cpuId > 0 {
		db = db.Where("cpu_id = ?", cpuId)
	}
	if pointId > 0 {
		db = db.Where("point_id = ?", pointId)
	}
	err = db.Order("id desc").Offset(offset).Limit(rn).Find(&list).Count(&count).Error
	if err != nil {
		return
	}
	return
}

func (t *FeedbackPointTargetExplain) DeleteByIdList(ctx *gin.Context, idList []int64, tx *gorm.DB) (err error) {
	if len(idList) == 0 {
		return
	}
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.Where("id in ?", idList).Delete(FeedbackPointTargetExplain{}).Error
	if err != nil {
		return
	}
	return
}

func (t *FeedbackPointTargetExplain) BatchInsert(ctx *gin.Context, list []FeedbackPointTargetExplain, tx *gorm.DB) (err error) {
	if len(list) == 0 {
		return nil
	}
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.CreateInBatches(list, 100).Error
	if err != nil {
		return
	}
	return
}

func (t *FeedbackPointTargetExplain) UpdateByPrimaryId(ctx *gin.Context, id int64, rec FeedbackPointTargetExplain) (err error) {
	data := map[string]interface{}{
		"cpu_id":        rec.CpuId,
		"point_id":      rec.PointId,
		"master_level":  rec.MasterLevel,
		"lower_limit":   rec.LowerLimit,
		"lower_is_open": rec.LowerIsOpen,
		"upper_limit":   rec.UpperLimit,
		"upper_is_open": rec.UpperIsOpen,
		"explain_conf":  rec.ExplainConf,
		"updater":       rec.Updater,
	}
	data["update_time"] = time.Now().Unix()
	err = helpers.MysqlClientFuDao.Table(t.TableName()).WithContext(ctx).Where("id = ?", id).Updates(data).Error
	if err != nil {
		return
	}
	return err
}
