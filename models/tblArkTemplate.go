package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
)

type ArkTemplate struct {
	TplId        int64  `gorm:"column:tpl_id" json:"tplId"`
	SourceId     int64  `gorm:"column:source_id" json:"sourceId"`
	BusinessLine int64  `gorm:"column:business_line" json:"businessLine"` //来源 1=lpc，2=辅导
	Name         string `gorm:"column:name" json:"name"`                  //服务配置模板名
	Type         int64  `gorm:"column:type" json:"type"`                  //模板课程类型
	Description  string `gorm:"column:description" json:"description"`    //模板描述
	Status       int64  `gorm:"column:status" json:"status"`              //当前模板状态:未上线、已上线、已下线
	OperatorUid  int64  `gorm:"column:operator_uid" json:"operatorUid"`   //操作人UID
	Operator     string `gorm:"column:operator" json:"operator"`
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime   int64  `gorm:"column:update_time" json:"updateTime"`
	Version      int64  `gorm:"column:version" json:"version"`         //版本号
	BizLine      int64  `gorm:"column:biz_line" json:"bizLine"`        //业务线
	PriceTagId   int64  `gorm:"column:price_tag_id" json:"priceTagId"` //课程性质
}

func (l *ArkTemplate) TableName() string {
	return "tblArkTemplate"
}

func (l *ArkTemplate) GetById(ctx *gin.Context, tplId int64) (*ArkTemplate, error) {
	var res *ArkTemplate
	err := helpers.MysqlClientFuDao.Table(l.TableName()).WithContext(ctx).
		Where("tpl_id = ?", tplId).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
