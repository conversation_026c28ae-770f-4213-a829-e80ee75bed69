package models

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"gorm.io/gorm"
	"time"
)

const (
	IsDelete    = 1 // 删除
	IsNotDelete = 0 // 未删
)

type NormalPage struct {
	No   int // 当前第几页
	Size int // 每页大小
}

// 传统分页示例
func NormalPaginate(page *NormalPage) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		pageNo := 1
		if page.No > 0 {
			pageNo = page.No
		}

		pageSize := page.Size
		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (pageNo - 1) * pageSize
		return db.Order("id asc").Offset(offset).Limit(pageSize)
	}
}

// 瀑布流分页示例
type ScrollPage struct {
	Start int // 当前页开始标示
	Size  int // 每页大小
}

func ScrollingPaginate(page *ScrollPage) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		start := -1
		if page.Start > 0 {
			start = page.Start
		}

		pageSize := page.Size
		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}

		return db.Where("id > ?", start).Order("id asc").Limit(pageSize)
	}
}

// 对外以秒级时间戳展示
type UnixTime struct {
	Time time.Time
}

// 记录json marshal 时用
func (t UnixTime) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%v\"", t.Time.Unix())), nil
}

func (t *UnixTime) String() (driver.Value, error) {
	return t.Time.Unix(), nil
}

// 写入数据库之前，对数据做类型转换
func (t UnixTime) Value() (driver.Value, error) {
	return t.Time, nil
}

// 将数据库中取出的数据，赋值给目标类型
func (t *UnixTime) Scan(v interface{}) error {
	switch vt := v.(type) {
	case time.Time:
		t.Time = vt
	default:
		return errors.New("format error")
	}

	return nil
}

type PhoneNumber string

func (t PhoneNumber) Value() (driver.Value, error) {
	return env.EncodeDBSensitiveField(string(t)), nil
}

func (t *PhoneNumber) Scan(v interface{}) error {
	switch vt := v.(type) {
	case []byte:

		*t = PhoneNumber(env.DecodeDBSensitiveField(string(vt)))
	case string:
		*t = PhoneNumber(env.DecodeDBSensitiveField(vt))
	default:
		return errors.New("format error")
	}
	return nil
}
