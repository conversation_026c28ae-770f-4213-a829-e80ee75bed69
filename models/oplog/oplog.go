package oplog

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var OperateLogRef = OperateLog{}

type OperateLog struct {
	ID           int64  `gorm:"column:id;primary_key" json:"id"`
	Refer        string `gorm:"column:refer" json:"refer"`
	Module       string `gorm:"column:module" json:"module"`
	Service      string `gorm:"column:service" json:"service"`
	Content      string `gorm:"column:content" json:"content"`
	Before       string `gorm:"column:before" json:"before"`
	Remark       string `gorm:"column:remark" json:"remark"`
	RelationId   string `gorm:"column:relation_id" json:"relation_id"`
	RelationType string `gorm:"column:relation_type" json:"relation_type"`
	LogId        string `gorm:"column:log_id" json:"log_id"`
	RequestId    string `gorm:"column:request_id" json:"request_id"`
	AssistantUid int64  `gorm:"column:assistant_uid" json:"assistant_uid"`
	PersonUid    int64  `gorm:"column:person_uid" json:"person_uid"`
	OperateTime  int64  `gorm:"column:operate_time" json:"operate_time"`
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`
}

func (OperateLog) TableName() string {
	return "tblOperateLog"
}

func (o OperateLog) PageList(ctx *gin.Context, relationId string, relationType string, personUid int64, page int, pageSize int) (result []OperateLog, err error) {

	param := map[string]interface{}{}

	if relationId != "" {
		param["relation_id"] = relationId
	}

	if relationType != "" {
		param["relation_type"] = relationType
	}

	if personUid > 0 {
		param["person_uid"] = personUid
	}

	err = helpers.MysqlClientFuDao.
		Table(o.TableName()).
		Select(param).
		WithContext(ctx).
		Offset(pageSize * page).
		Limit(pageSize).
		Order("create_time desc").
		Find(&result).
		Error
	if err != nil {
		zlog.Warnf(ctx, "GetByRelationId fail,param=%+v,err=%v", param, err)
	}
	return
}

func (o OperateLog) Insert(ctx *gin.Context, operateLog OperateLog) (err error) {
	err = helpers.MysqlClientFuDao.Table(o.TableName()).Create(&operateLog).WithContext(ctx).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert OperateLog fail,data=%+v,err=%v", operateLog, err)
	}
	return
}

func (o OperateLog) Query(ctx *gin.Context, relationId, relationType string, personUid int64, page, pageSize int) (res []OperateLog, err error) {
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(o.TableName())
	if relationId != "" {
		db = db.Where("relation_id = ?", relationId)
		if relationType != "" {
			db = db.Where("relation_type = ? ", relationType)
		}
	}
	if personUid > 0 {
		db = db.Where("person_uid = ? ", personUid)
	}
	db = db.Offset(pageSize * (page - 1)).Limit(pageSize)
	err = db.Find(&res).Error
	if err != nil {
		zlog.Warnf(ctx, "Query OperateLog fail,relationId=%+v,relationType=%v,personUid=%v,err=%v", relationId, relationType, personUid, err)
	}
	return
}
