package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MessageGroupFolder 消息组文件夹表
type MessageGroupFolder struct {
	ID         int64  `json:"id" gorm:"id"`                   // 自增id
	Name       string `json:"name" gorm:"name"`               // 名字（文件夹名或者消息组名）
	Order      int64  `json:"order" gorm:"order"`             // 顺序
	IsDeleted  int8   `json:"is_deleted" gorm:"is_deleted"`   // 是否删除：0-未删除、1-删除
	CreateTime int64  `json:"create_time" gorm:"create_time"` // 创建时间
	CreateUid  int64  `json:"create_uid" gorm:"create_uid"`   // 创建人uid
	CreateName string `json:"create_name" gorm:"create_name"` // 创建者
	UpdateTime int64  `json:"update_time" gorm:"update_time"` // 更新时间
	UpdateUid  int64  `json:"update_uid" gorm:"update_uid"`   // 更新人uid
	UpdateName string `json:"update_name" gorm:"update_name"` // 更新者
}

func (m *MessageGroupFolder) TableName() string {
	return "tblMessageGroupFolder"
}

func (m *MessageGroupFolder) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (m *MessageGroupFolder) Tx(ctx *gin.Context) *gorm.DB {
	return m.SchemaClient().WithContext(ctx).Begin()
}

func (m *MessageGroupFolder) BatchInsertWithTx(ctx *gin.Context, data []*MessageGroupFolder, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		db := tx.WithContext(ctx).Table(m.TableName())
		err = db.CreateInBatches(data, 100).Error
		if err != nil {
			zlog.Warnf(ctx, "MessageGroupFolder BatchInsertWithTx err, err:%+v", err)
		}
		return
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName())
	err = db.CreateInBatches(data, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder BatchInsert err, err:%+v", err)
	}
	return
}

func (m *MessageGroupFolder) BatchUpdateWithTx(ctx *gin.Context, data []MessageGroupFolder, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		for _, messageGroupFolder := range data {
			_err := tx.WithContext(ctx).Table(m.TableName()).Updates(&messageGroupFolder).Error
			if _err != nil {
				err = _err
				if err != nil {
					zlog.Warnf(ctx, "MessageGroupFolder BatchUpdateWithTx err, data:%+v, err:%+v", messageGroupFolder, err)
				}
				return
			}
		}
		return
	}

	for _, messageGroupFolder := range data {
		_err := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName()).Updates(&messageGroupFolder).Error
		if _err != nil {
			err = _err
			if err != nil {
				zlog.Warnf(ctx, "MessageGroupFolder BatchUpdate err, data:%+v, err:%+v", messageGroupFolder, err)
			}
			return
		}
	}
	return
}

// List 获取有效的文件夹列表
func (m *MessageGroupFolder) List(ctx *gin.Context, uid int, tx *gorm.DB) (folderList []MessageGroupFolder, err error) {
	conditions := map[string]interface{}{
		"create_uid": uid,
		"is_deleted": 0,
	}
	if tx != nil {
		err = tx.WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&folderList).Error
	} else {
		err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&folderList).Error
	}
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder List err, uid:%+v, err:%+v", uid, err)
	}
	return
}

func (m *MessageGroupFolder) PageListById(ctx *gin.Context, idList []int64, pn int, rn int) (folderList []MessageGroupFolder, total int64, err error) {
	conditions := map[string]interface{}{
		"is_deleted": 0,
		"id":         idList,
	}

	err = m.SchemaClient().WithContext(ctx).
		Table(m.TableName()).
		Where(conditions).
		Order("create_time desc").
		Offset(pn * rn).
		Limit(rn).
		Find(&folderList).Error

	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder PageListById err, uid:%+v, err:%+v", idList, err)
	}

	err = m.SchemaClient().WithContext(ctx).
		Table(m.TableName()).
		Where(conditions).
		Count(&total).
		Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder total err, uid:%+v, err:%+v", idList, err)
	}
	return
}

func (m *MessageGroupFolder) PageListByUser(ctx *gin.Context, userId int64, pn int, rn int) (folderList []MessageGroupFolder, total int64, err error) {
	conditions := map[string]interface{}{
		"is_deleted": 0,
		"create_uid": userId,
	}

	err = m.SchemaClient().WithContext(ctx).
		Table(m.TableName()).
		Where(conditions).
		Order("`order` desc").
		Offset(pn * rn).
		Limit(rn).
		Find(&folderList).Error

	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder PageListByUser err, userId:%+v, err:%+v", userId, err)
	}

	err = m.SchemaClient().WithContext(ctx).
		Table(m.TableName()).
		Where(conditions).
		Count(&total).
		Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder total err, uid:%+v, err:%+v", userId, err)
	}
	return
}

func (m *MessageGroupFolder) Count(ctx *gin.Context, uid int, idList []int64) (total int64, err error) {
	conditions := map[string]interface{}{
		"is_deleted": 0,
	}
	if uid > 0 {
		conditions["create_uid"] = uid
	}
	if len(idList) > 0 {
		conditions["id"] = idList
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Count(&total).Error

	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder List err, uid:%+v, err:%+v", uid, err)
	}
	return total, err
}

// GetById 删除或者不删除都可以查出来
func (m *MessageGroupFolder) GetById(ctx *gin.Context, id int64) (folderList []MessageGroupFolder, err error) {
	conditions := map[string]interface{}{
		"id": id,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&folderList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder GetById err, id:%+v, err:%+v", id, err)
	}
	return
}

func (m *MessageGroupFolder) GetFoldersByIds(ctx *gin.Context, folderIds []int64) (folderList []MessageGroupFolder, err error) {
	conditions := map[string]interface{}{
		"id": folderIds,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Order("`order` desc").Find(&folderList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder List err, folderIds:%+v, err:%+v", folderIds, err)
	}
	return
}

func (m *MessageGroupFolder) GetFoldersByUid(ctx *gin.Context, createUid int64) (folderList []MessageGroupFolder, err error) {
	conditions := map[string]interface{}{
		"create_uid": createUid,
		"is_deleted": 0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Order("`order` desc").Find(&folderList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroupFolder List err, createUid:%+v, err:%+v", createUid, err)
	}
	return
}
