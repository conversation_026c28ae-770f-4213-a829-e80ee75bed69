package models

import (
	"assistantdeskgo/helpers"
	"errors"
	"git.zuoyebang.cc/pkg/hints"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	DoCreating    = 1
	CreateSuccess = 2
	CreateFailed  = 3
	CreateTimeout = 4

	UseTypeSendSig   = 1
	UseTypeSendGroup = 2

	HintsReadWrite = "/*#mode=READWRITE*/"
	HintsReadOnly  = "/*#mode=READONLY*/"
)

type CreateCaptureTask struct {
	Id           int    `gorm:"primary_key;column:id" json:"id"`
	AssistantUid int    `gorm:"column:assistant_uid" json:"assistantUid"`
	ChildNum     int    `gorm:"column:child_num" json:"childNum"`
	UseType      int    `gorm:"column:use_type" json:"useType"`
	Status       int    `gorm:"column:status" json:"status"`
	UnionId      string `gorm:"column:union_id" json:"unionId"`
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime   int64  `gorm:"column:update_time" json:"updateTime"`
	ExtData      string `gorm:"column:ext_data" json:"extData"`
}

func (p *CreateCaptureTask) TableName() string {
	return "tblCreateCaptureTask"
}

func (p *CreateCaptureTask) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (p *CreateCaptureTask) CreateTask(ctx *gin.Context, task *CreateCaptureTask) error {
	nowTime := time.Now().Unix()
	if task.ChildNum <= 0 {
		return errors.New("子任务数量为0")
	}
	if task.Status == 0 {
		task.Status = DoCreating
	}
	task.CreateTime = nowTime
	task.UpdateTime = nowTime
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(p.TableName()).Create(task).Error
	return err
}

func (p *CreateCaptureTask) ChangeStatus(ctx *gin.Context) error {
	if p.Status == DoCreating || p.Id <= 0 {
		return errors.New("参数错误")
	}
	value := map[string]interface{}{
		"status":      p.Status,
		"update_time": time.Now().Unix(),
	}
	return helpers.MysqlClientFuDao.WithContext(ctx).Table(p.TableName()).Where("id = ?", p.Id).Updates(value).Error
}

func (p *CreateCaptureTask) GetTaskInfoById(ctx *gin.Context, hitMaster bool) (task CreateCaptureTask, err error) {
	if p.Id <= 0 {
		return task, errors.New("获取生成图片任务失败")
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(p.TableName()).Where("id = ?", p.Id)
	if hitMaster {
		db.Clauses(hints.NewHint(HintsReadWrite))
	}
	err = db.First(&task).Error
	return
}
