package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	ExcelTaskStatusInit        = 1
	ExcelTaskStatusExecuting   = 2
	ExcelTaskStatusFail        = 3
	ExcelTaskStatusSuccess     = 4
	ExcelTaskStatusPartSuccess = 5
)

var ExcelTaskStatusMap = map[int]string{
	ExcelTaskStatusInit:        "待执行",
	ExcelTaskStatusExecuting:   "执行中",
	ExcelTaskStatusFail:        "执行失败:%s",
	ExcelTaskStatusSuccess:     "已完成",
	ExcelTaskStatusPartSuccess: "部分成功",
}

const (
	ExcelTaskStatusWaitProcess = iota
	ExcelTaskStatusProcessing
	ExcelTaskStatusFinish
)

var ExcelTaskStatusFEDisplayMap = map[int]int{
	ExcelTaskStatusInit:        ExcelTaskStatusWaitProcess,
	ExcelTaskStatusExecuting:   ExcelTaskStatusProcessing,
	ExcelTaskStatusFail:        ExcelTaskStatusFinish,
	ExcelTaskStatusSuccess:     ExcelTaskStatusFinish,
	ExcelTaskStatusPartSuccess: ExcelTaskStatusFinish,
}

var ExcelTaskStatusFEDisplayStrMap = map[int]string{
	ExcelTaskStatusInit:        "待处理",
	ExcelTaskStatusExecuting:   "进行中",
	ExcelTaskStatusFail:        "已完成",
	ExcelTaskStatusSuccess:     "已完成",
	ExcelTaskStatusPartSuccess: "已完成",
}

type ExcelTask struct {
	ID            int    `gorm:"primary_key;column:id" json:"id"`
	FileName      string `gorm:"column:file_name" json:"file_name"`
	FilePath      string `gorm:"column:file_path" json:"file_path"`
	RetFile       string `gorm:"column:ret_file" json:"ret_file"`
	Status        int    `gorm:"column:status" json:"status"`
	FailReason    string `gorm:"column:fail_reason" json:"fail_reason"`
	Type          int    `gorm:"column:type" json:"type"`
	OperatorUid   int64  `gorm:"column:operator_uid" json:"operator_uid"`
	OperatorUname string `gorm:"column:operator_uname" json:"operator_uname"`
	CreateTime    int64  `gorm:"column:create_time" json:"create_time"`
	StartTime     int64  `gorm:"column:start_time" json:"start_time"`
	EndTime       int64  `gorm:"column:end_time" json:"end_time"`
	ExtraInfo     string `gorm:"column:extra_info" json:"extra_info"`
}

type SailorScoreExtraInfo struct {
	SuccessCount int64 `json:"successCount"`
	FailCount    int64 `json:"failCount"`
}

func (e *ExcelTask) TableName() string {
	return "tblExcelTask"
}

func (e *ExcelTask) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (e *ExcelTask) Tx(ctx *gin.Context) *gorm.DB {
	return e.SchemaClient().WithContext(ctx).Begin()
}

func (e *ExcelTask) Insert(ctx *gin.Context) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Create(e).Error
	if err != nil {
		return err
	}

	return nil
}

func (e *ExcelTask) GetList(ctx *gin.Context, conds map[string]interface{}, page, size int) ([]*ExcelTask, int64, error) {
	var count int64
	helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Count(&count)
	var res []*ExcelTask
	offset := (page - 1) * size
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Order("id DESC").Offset(offset).Limit(size).Find(&res).Error

	if err != nil {
		return nil, count, err
	}

	return res, count, err
}

func (e *ExcelTask) GetInitTaskList(ctx *gin.Context, limit int) ([]*ExcelTask, error) {
	var res []*ExcelTask
	conds := map[string]interface{}{
		"status": ExcelTaskStatusInit,
		"type":   []int{1, 2, 3},
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Offset(0).Limit(limit).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (e *ExcelTask) UpdateInitToExecuting(ctx *gin.Context, id int) (bool, error) {
	conds := map[string]interface{}{
		"id":     id,
		"status": ExcelTaskStatusInit,
	}
	updateData := map[string]interface{}{
		"status":     ExcelTaskStatusExecuting,
		"start_time": time.Now().UnixNano() / 1e6,
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Updates(updateData)
	err := db.Error
	if err != nil {
		return false, err
	}
	if db.RowsAffected > 0 {
		return true, nil
	}
	return false, nil
}

func (e *ExcelTask) UpdateTaskFail(ctx *gin.Context, id int, failReason string) (int64, error) {
	conds := map[string]interface{}{
		"id":     id,
		"status": ExcelTaskStatusExecuting,
	}
	updateData := map[string]interface{}{
		"status":      ExcelTaskStatusFail,
		"end_time":    time.Now().UnixNano() / 1e6,
		"fail_reason": failReason,
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Updates(updateData)
	err := db.Error
	if err != nil {
		return 0, err
	}
	return db.RowsAffected, nil
}

func (e *ExcelTask) UpdateTaskSucc(ctx *gin.Context, id int) (int64, error) {
	conds := map[string]interface{}{
		"id":     id,
		"status": ExcelTaskStatusExecuting,
	}
	updateData := map[string]interface{}{
		"status":   ExcelTaskStatusSuccess,
		"end_time": time.Now().UnixNano() / 1e6,
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Updates(updateData)
	err := db.Error
	if err != nil {
		return 0, err
	}
	return db.RowsAffected, nil
}

func (e *ExcelTask) UpdateRetFile(ctx *gin.Context, id int, retFile string, allSucc bool) error {
	conds := map[string]interface{}{
		"id":     id,
		"status": ExcelTaskStatusExecuting,
	}
	updateData := map[string]interface{}{
		"ret_file": retFile,
		"end_time": time.Now().UnixNano() / 1e6,
		"status":   ExcelTaskStatusSuccess,
	}
	if allSucc == false {
		updateData["status"] = ExcelTaskStatusPartSuccess
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Updates(updateData).Error
	if err != nil {
		return err
	}
	return nil
}

func (e *ExcelTask) UpdateTaskExtraInfo(ctx *gin.Context, taskId int, extraInfo string) (err error) {
	var (
		conditions = map[string]interface{}{
			"id": taskId,
		}
		values = map[string]interface{}{
			"extra_info": extraInfo,
		}
	)
	err = e.SchemaClient().WithContext(ctx).Table(e.TableName()).Where(conditions).Updates(values).Error
	return
}

func (e *ExcelTask) GetByUid(ctx *gin.Context, uid int) (res []ExcelTask, err error) {
	condition := map[string]interface{}{
		"operator_uid": uid,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(condition).Find(&res).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByUid err, err:%+v", err)
	}
	return
}

func (e *ExcelTask) GetById(ctx *gin.Context, taskId int64) (res ExcelTask, err error) {
	condition := map[string]interface{}{
		"id": taskId,
	}
	excelTaskList := make([]ExcelTask, 0)
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(condition).Find(&excelTaskList).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByUid err, err:%+v", err)
	}
	if len(excelTaskList) > 0 {
		res = excelTaskList[0]
	}
	return
}

func (e *ExcelTask) UpdateTaskToInit(ctx *gin.Context, id int64) (bool, error) {
	conds := map[string]interface{}{
		"id": id,
	}
	updateData := map[string]interface{}{
		"status":      ExcelTaskStatusInit,
		"fail_reason": "",
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Updates(updateData)
	err := db.Error
	if err != nil {
		return false, err
	}
	if db.RowsAffected > 0 {
		return true, nil
	}
	return false, nil
}

func (e *ExcelTask) UpdateExtraInfo(ctx *gin.Context, id int64, extraInfo string) (err error) {
	conds := map[string]interface{}{
		"id": id,
	}
	updateData := map[string]interface{}{
		"extra_info": extraInfo,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(e.TableName()).Where(conds).Updates(updateData).Error
	return
}
