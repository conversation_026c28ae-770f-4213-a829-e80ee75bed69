package models

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var LaxinTagDataFixLogDao laxinTagDataFixLogDao

type laxinTagDataFixLogDao struct {
}

type LaxinTagDataFixLog struct {
	ID                int64  `gorm:"primary_key;column:id" json:"id"`
	RecordID          int64  `gorm:"column:record_id" json:"recordId"`         // 数据表 id
	OperatorType      int    `gorm:"column:operator_type" json:"operatorType"` // 操作类型：创建/删除
	CourseID          int    `gorm:"column:course_id" json:"courseId"`
	AssistantPhone    string `gorm:"column:assistant_phone" json:"assistantPhone"`        // 资产手机号
	AssistantUID      int64  `gorm:"column:assistant_uid" json:"assistantUid"`            // 资产 id
	PersonEmailPrefix string `gorm:"column:person_email_prefix" json:"personEmailPrefix"` // 真人 email
	PersonUID         int64  `gorm:"column:person_uid" json:"personUid"`                  // 真人 id
	OperatorUID       int64  `gorm:"column:operator_uid" json:"operatorUid"`              // 操作人Uid
	Operator          string `gorm:"column:operator" json:"operator"`                     // 操作人
	IsDeleted         bool   `gorm:"column:is_deleted" json:"isDeleted"`
	CreateTime        int64  `gorm:"column:create_time" json:"createTime"` // 创建时间
	UpdateTime        int64  `gorm:"column:update_time" json:"updateTime"`
}

func (e *LaxinTagDataFixLog) TableName() string {
	return "tblLaxinTagDataFixLog"
}

func (l laxinTagDataFixLogDao) BatchCreate(ctx *gin.Context, data []*LaxinTagDataFixLog, tx *gorm.DB) error {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		db := tx.WithContext(ctx).Model(&LaxinTagDataFixLog{})
		return db.CreateInBatches(data, 100).Error
	}

	return helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFixLog{}).CreateInBatches(data, components.CreateBatchSize).Error
}

func (l laxinTagDataFixLogDao) GetByCond(ctx *gin.Context, courseID []int, email, phone []string) (res []*LaxinTagDataFixLog, err error) {

	conditions := make(map[string]interface{}, 0)

	if len(courseID) != 0 {
		conditions["course_id"] = courseID
	}

	if len(email) != 0 {
		conditions["person_email_prefix"] = email
	}

	if len(phone) != 0 {
		conditions["assistant_phone"] = phone
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&LaxinTagDataFixLog{}).Where(conditions).Order("create_time DESC").Limit(1000).Find(&res).Error
	if err != nil {
		return
	}
	return
}
