package notice

import (
	"assistantdeskgo/helpers"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var SystemNoticeUserLikeDao systemNoticeUserLikeDao

type systemNoticeUserLikeDao struct{}

type SystemNoticeUserLike struct {
	Id              int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`             // id
	NoticeId        int64  `gorm:"column:notice_id;not null" json:"notice_id"`               // 通知id
	Uid             int64  `gorm:"column:uid;default:0" json:"uid"`                          // 用户Uid
	Type            int8   `gorm:"column:type;default:0" json:"type"`                        // 点踩类型：1-图文点踩，2-视频点踩
	FeedbackType    int8   `gorm:"column:feedback_type;not null" json:"feedback_type"`       // 内容反馈类型：1-点赞，2-点踩
	FeedbackContent string `gorm:"column:feedback_content;not null" json:"feedback_content"` // 内容反馈
	IsDeleted       bool   `gorm:"column:is_deleted;default:0" json:"is_deleted"`            // 是否删除：0-未删，1-已删
	FeedbackTime    int64  `gorm:"column:feedback_time;default:0" json:"feedback_time"`      // 内容反馈时间
	CreateTime      int64  `gorm:"column:create_time;default:0" json:"create_time"`          // 创建时间
	UpdateTime      int64  `gorm:"column:update_time;default:0" json:"update_time"`          // 更新时间
}

func (s *SystemNoticeUserLike) TableName() string {
	return "tblSystemNoticeUserLike"
}

func (dao *systemNoticeUserLikeDao) Insert(ctx *gin.Context, data SystemNoticeUserLike, tx *gorm.DB) (id int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Create(&data).Error
	id = data.Id
	return
}

func (dao *systemNoticeUserLikeDao) Update(ctx *gin.Context, noticeId int64, uid int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
		Where("notice_id = ? AND uid = ?", noticeId, uid).
		Updates(data).
		Error
	return
}

func (dao *systemNoticeUserLikeDao) UpdateById(ctx *gin.Context, id int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
		Where("id = ?", id).
		Updates(data).
		Error
	return
}

func (dao *systemNoticeUserLikeDao) BatchUpdateDeleted(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	if len(uids) == 0 {
		return nil
	}

	// 将 uids 切片拆分为多个小切片
	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(map[string]interface{}{
				"is_deleted":  0,
				"update_time": time.Now().Unix(),
			}).
			Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeUserLikeDao) Delete(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
		Where(conds).
		Updates(map[string]interface{}{
			"is_deleted":  1,
			"update_time": time.Now().Unix(),
		}).
		Error
	return
}

func (dao *systemNoticeUserLikeDao) BatchDeleteByUids(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	if len(uids) == 0 {
		return nil
	}

	var updateData = map[string]interface{}{
		"is_deleted":  1,
		"update_time": time.Now().Unix(),
	}

	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(updateData).
			Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeUserLikeDao) Count(ctx *gin.Context, noticeId int64, tp int, fdtp int, tx *gorm.DB) (count int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
		Where("notice_id = ? AND type = ? AND feedback_type = ?", noticeId, tp, fdtp).
		Scopes(FilterDeleted).
		Count(&count).
		Error
	return
}

func (dao *systemNoticeUserLikeDao) List(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (list *[]SystemNoticeUserLike, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
		Where(conds).
		Scopes(FilterDeleted).
		Find(&list).
		Error
	return
}

func (dao *systemNoticeUserLikeDao) ListByUids(ctx *gin.Context, conds map[string]interface{}, uids []int64, tx *gorm.DB) (list []SystemNoticeUserLike, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	list = make([]SystemNoticeUserLike, 0)
	batchSize := 1000
	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		var batchResult []SystemNoticeUserLike
		err = tx.WithContext(ctx).Model(&SystemNoticeUserLike{}).
			Where(conds).
			Where("uid IN ?", batch).
			Scopes(FilterDeleted).
			Find(&batchResult).
			Error
		if err != nil {
			return nil, err
		}

		list = append(list, batchResult...)
	}

	return
}
