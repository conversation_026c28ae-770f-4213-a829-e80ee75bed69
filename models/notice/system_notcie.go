package notice

import (
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var SystemNoticeDao systemNoticeDao

type systemNoticeDao struct{}

type SystemNotice struct {
	Id                    int64  `gorm:"primaryKey;column:id" json:"id"`                               // id
	Title                 string `gorm:"column:title" json:"title"`                                    // 通知标题
	CreatorUID            int64  `gorm:"column:creator_uid" json:"creatorUid"`                         // 创建人uid
	PublisherUID          int64  `gorm:"column:publisher_uid" json:"publisherUid"`                     // 发布人uid
	ClassID               int8   `gorm:"column:class_id" json:"classId"`                               // 所属栏目
	Status                int8   `gorm:"column:status" json:"status"`                                  // 状态：1-编辑中；2-已发布；3-已结束
	Scope                 int8   `gorm:"column:scope" json:"scope"`                                    // 发送范围：1-筛选员工；2-全体员工
	ScopeGroup            string `gorm:"column:scope_group" json:"scopeGroup"`                         // 目标组织
	ScopeUID              string `gorm:"column:scope_uid" json:"scopeUid"`                             // 目标uid
	ProfilePhoto          string `gorm:"column:profile_photo" json:"profilePhoto"`                     // 通知头像
	Content               string `gorm:"column:content" json:"content"`                                // 通知内容
	VideoAddr             string `gorm:"column:video_addr" json:"videoAddr"`                           // 视频cos地址
	Abstract              string `gorm:"column:abstract" json:"abstract"`                              // 通知摘要
	CommentFlag           bool   `gorm:"column:comment_flag" json:"commentFlag"`                       // 评论设置：0-不允许评论；1-允许评论
	MockReadNum           int64  `gorm:"column:mock_read_num" json:"mockReadNum"`                      // 模拟阅读数
	MockContentLikeNum    int64  `gorm:"column:mock_content_like_num" json:"mockContentLikeNum"`       // 模拟图文点赞数
	MockVideoLikeNum      int64  `gorm:"column:mock_video_like_num" json:"mockVideoLikeNum"`           // 模拟视频点赞数
	MockContentDisLikeNum int64  `gorm:"column:mock_content_dislike_num" json:"mockContentDisLikeNum"` // 模拟图文点踩数
	MockVideoDisLikeNum   int64  `gorm:"column:mock_video_dislike_num"  json:"mockVideoDisLikeNum"`    // 模拟视频点踩数
	MockCommentNum        int64  `gorm:"column:mock_comment_num" json:"mockCommentNum"`                // 模拟评论数
	IsDeleted             bool   `gorm:"column:is_deleted" json:"isDeleted"`                           // 是否删除：0-未删，1-已删
	CreateTime            int64  `gorm:"column:create_time" json:"createTime"`                         // 创建时间
	PublishTime           int64  `gorm:"column:publish_time" json:"publishTime"`                       // 发布时间
	UpdateTime            int64  `gorm:"column:update_time" json:"updateTime"`                         // 更新时间
}

func (s *SystemNotice) TableName() string {
	return "tblSystemNotice"
}

func FilterDeleted(db *gorm.DB) *gorm.DB {
	return db.Where("is_deleted = ?", 0)
}

func (dao *systemNoticeDao) Insert(ctx *gin.Context, data SystemNotice, tx *gorm.DB) (insertId int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Create(&data).Error
	if err != nil {
		return
	}
	insertId = data.Id
	return
}

func (dao *systemNoticeDao) UpdateById(ctx *gin.Context, id int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNotice{}).
		Where("id = ?", id).
		Updates(data).Error
	return
}

func (dao *systemNoticeDao) Delete(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNotice{}).
		Where(conds).
		Updates(map[string]interface{}{
			"is_deleted":  1,
			"update_time": time.Now().Unix(),
		}).
		Error
	return
}

func (dao *systemNoticeDao) GetById(ctx *gin.Context, id int64, tx *gorm.DB) (res *SystemNotice, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNotice{}).
		Where("id = ?", id).
		Scopes(FilterDeleted).
		First(&res).
		Error
	return
}

func (dao *systemNoticeDao) GetByIdsAndClassIds(ctx *gin.Context, ids []int64, classids []int, tx *gorm.DB) (list []SystemNotice, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	db := tx.WithContext(ctx).Model(&SystemNotice{})

	if len(classids) > 0 {
		db.Where("class_id IN (?)", classids)
	}

	err = db.Where("id IN (?)", ids).
		Scopes(FilterDeleted).
		Find(&list).
		Error
	return
}

func (dao *systemNoticeDao) Page(ctx *gin.Context, param dtonotice.SystemNoticeListParam, tx *gorm.DB) (list []SystemNotice, count int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	db := tx.WithContext(ctx).Model(&SystemNotice{})
	pageNum := param.Page
	if pageNum <= 0 {
		pageNum = 1
	}
	pageSize := param.Size
	if pageSize <= 0 {
		pageSize = 10
	}
	startTime := param.StartTime
	endTime := param.EndTime
	classId := param.ClassID
	stauts := param.Status
	if startTime > 0 {
		db.Where("update_time >= ?", startTime)
	}
	if endTime > 0 {
		db.Where("update_time <= ?", endTime)
	}
	if classId > 0 {
		db.Where("class_id =?", classId)
	}
	if stauts > 0 {
		db.Where("status =?", stauts)
	}

	db.Scopes(FilterDeleted)
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("update_time DESC").
		Offset((pageNum - 1) * pageSize).
		Limit(pageSize).
		Find(&list).
		Error
	return
}

func (dao *systemNoticeDao) List(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (list *[]SystemNotice, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNotice{}).
		Where(conds).
		Scopes(FilterDeleted).
		Find(&list).
		Error
	return
}
