package notice

import (
	"assistantdeskgo/helpers"
	"errors"
	"fmt"
	"git.zuoyebang.cc/infra/pkg/navigator"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	TableName       = "tblAssistantNotice"
	TableNameShadow = "tblAssistantNoticeShadow"

	StatusUnread = 0 // 未读
	StatusRead   = 1 // 已读

	TypeNotice               = 3  // 公告
	TypeCheck                = 4  // 质检
	TypeSignUp               = 11 // 报名
	TypeIn                   = 12 // 转入
	TypeOut                  = 13 // 转出
	TypeRetreat              = 14 // 退课
	TypeContinue             = 15 // 续报
	TypeRefundContinue       = 16 // 退续报
	TypeCloud                = 17 // 云学堂通知
	TypeChangeClass          = 18 // 调小班通知
	TypeAudit                = 19 // 课件审核通知
	TypeRefund               = 20 // 挽单通知
	TypeSeasonContinue       = 21 // 隔季续报
	TypeSeasonRefundContinue = 22 // 隔季退续报
	TypeTouchTaskNotify      = 23 // 触达任务通知
	TypeRemind               = 30 // 提醒

	DeletedYes = 1
	DeletedNo  = 0
)

type AssistantNotice struct {
	Id           int64  `gorm:"primary_key;column:id" json:"id"`
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime   int64  `gorm:"column:update_time" json:"updateTime"`
	AssistantUid int64  `gorm:"column:assistant_uid" json:"assistantUid"`
	PersonUid    int64  `gorm:"column:person_uid" json:"personUid"`
	Type         int64  `gorm:"column:type" json:"type"`
	Status       int64  `gorm:"column:status" json:"status"`
	DetailType   int64  `gorm:"column:detail_type" json:"detailType"`
	Content      string `gorm:"column:content" json:"content"`
	SourceId     int64  `gorm:"column:source_id" json:"sourceId"`
	Deleted      int64  `gorm:"column:deleted" json:"deleted"`
	ExtData      string `gorm:"column:ext_data" json:"extData"`
}

func GetTypeOptions() map[int64]string {
	return map[int64]string{
		TypeNotice:               "公告",
		TypeCheck:                "质检",
		TypeSignUp:               "报名",
		TypeIn:                   "转入",
		TypeOut:                  "转出",
		TypeRetreat:              "退课",
		TypeContinue:             "续报",
		TypeRefundContinue:       "退续报",
		TypeCloud:                "云学堂通知",
		TypeChangeClass:          "调小班通知",
		TypeAudit:                "课件审核通知",
		TypeRefund:               "挽单通知",
		TypeSeasonContinue:       "隔季续报",
		TypeSeasonRefundContinue: "隔季退续报",
		TypeTouchTaskNotify:      "触达任务通知",
		TypeRemind:               "提醒",
	}
}

func ApplyFields(notice *AssistantNotice, assistantUid, personUid, noticeType, detailType, sourceId int64, content, extData string) (err error) {
	if notice == nil {
		err = errors.New("AssistantNotice.ApplyFields error, nil access")
		return
	}
	notice.DetailType = detailType
	notice.SourceId = sourceId
	notice.ExtData = extData
	notice.Content = content

	if assistantUid == 0 && personUid == 0 {
		err = errors.New("AssistantNotice.ApplyFields error, assistantUid or personUid empty")
		return
	}
	notice.AssistantUid = assistantUid
	notice.PersonUid = personUid

	if _, ok := GetTypeOptions()[noticeType]; !ok {
		err = errors.New(fmt.Sprintf("AssistantNotice.ApplyFields error, unsupported noticeType[%d]", noticeType))
		return
	}
	notice.Type = noticeType
	return
}

func (an *AssistantNotice) TableName() string {
	return TableName
}
func getTableName(ctx *gin.Context) string {
	if navigator.IsPressure(ctx) {
		return TableNameShadow
	}
	return TableName
}
func getDB(ctx *gin.Context) *gorm.DB {
	return helpers.MysqlClientFuDao.WithContext(ctx).Table(getTableName(ctx))
}

func (an *AssistantNotice) Add(ctx *gin.Context) (id int64, err error) {
	now := time.Now().Unix()
	an.CreateTime = now
	an.UpdateTime = now
	an.Status = StatusUnread
	an.Deleted = DeletedNo

	err = getDB(ctx).Create(an).Error
	if err != nil {
		return
	}
	id = an.Id
	return
}
