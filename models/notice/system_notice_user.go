package notice

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var SystemNoticeUserDao systemNoticeUserDao

type systemNoticeUserDao struct{}

type SystemNoticeUser struct {
	Id              int64 `gorm:"primaryKey;column:id" json:"id"`                  // id
	NoticeId        int64 `gorm:"column:notice_id" json:"noticeId"`                // 通知id
	Uid             int64 `gorm:"column:uid" json:"uid"`                           // 发布人uid
	IsContentRead   bool  `gorm:"column:is_content_read" json:"isContentRead"`     // 图文是否已读
	IsVideoRead     bool  `gorm:"column:is_video_read" json:"isVideoRead"`         // 视频是否已读
	ContentReadTime int64 `gorm:"column:content_read_time" json:"contentReadTime"` // 图文浏览时长
	VideoReadTime   int64 `gorm:"column:video_read_time" json:"videoReadTime"`     // 视频浏览时长
	LastReadTime    int64 `gorm:"column:last_read_time" json:"lastReadTime"`       // 最新浏览时间
	IsDeleted       bool  `gorm:"column:is_deleted" json:"isDeleted"`              // 是否删除：0-未删，1-已删
	CreateTime      int64 `gorm:"column:create_time" json:"createTime"`            // 创建时间
	UpdateTime      int64 `gorm:"column:update_time" json:"updateTime"`            // 更新时间
}

func (s *SystemNoticeUser) TableName() string {
	return "tblSystemNoticeUser"
}

func (dao *systemNoticeUserDao) Insert(ctx *gin.Context, data SystemNoticeUser, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns:   []clause.Column{{Name: "notice_id"}, {Name: "uid"}},
				DoUpdates: clause.AssignmentColumns([]string{"is_deleted", "update_time"}),
			},
		).
		Create(&data).
		Error
	return
}

func (dao *systemNoticeUserDao) BatchInsert(ctx *gin.Context, value interface{}, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns:   []clause.Column{{Name: "notice_id"}, {Name: "uid"}},
				DoUpdates: clause.AssignmentColumns([]string{"is_deleted", "update_time"}),
			},
		).
		CreateInBatches(value, batchSize).
		Error
	return
}

func (dao *systemNoticeUserDao) Update(ctx *gin.Context, noticeId int64, uid int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where("notice_id = ? AND uid = ?", noticeId, uid).
		Updates(data).
		Error
	return
}

func (dao *systemNoticeUserDao) Delete(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where(conds).
		Updates(map[string]interface{}{
			"is_deleted":  1,
			"update_time": time.Now().Unix(),
		}).
		Error
	return
}

func (dao *systemNoticeUserDao) BatchDeleteByUids(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	var updateData = map[string]interface{}{
		"is_deleted":  1,
		"update_time": time.Now().Unix(),
	}

	// 将 uids 切片拆分为多个小切片
	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(updateData).
			Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeUserDao) List(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where(conds).
		Scopes(FilterDeleted).
		Find(&list).
		Error
	return
}

func (dao *systemNoticeUserDao) ListLatest90DaysNotice(ctx *gin.Context, userId int, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	// 计算90天前的时间戳
	ninetydaysago := time.Now().AddDate(0, 0, -90).Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where("uid = ? AND create_time >= ?", userId, ninetydaysago).
		Scopes(FilterDeleted).
		Find(&list).
		Error
	return
}

func (dao *systemNoticeUserDao) ListTopUnReadNotice(ctx *gin.Context, userId int, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Joins("JOIN tblSystemNotice ON tblSystemNoticeUser.notice_id = tblSystemNotice.id").
		Where("tblSystemNoticeUser.uid = ? AND tblSystemNotice.status = ? AND tblSystemNotice.is_deleted = 0 AND tblSystemNoticeUser.is_deleted = 0 AND is_content_read = 0",
			userId,
			defines.SystemNoticeStatusPublish).
		Order("tblSystemNotice.publish_time desc").
		Limit(6).
		Find(&list).
		Error

	return
}

func (dao *systemNoticeUserDao) ListTopNotice(ctx *gin.Context, userId int, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Joins("JOIN tblSystemNotice ON tblSystemNoticeUser.notice_id = tblSystemNotice.id").
		Where("tblSystemNoticeUser.uid = ? AND tblSystemNotice.status = ? AND tblSystemNotice.is_deleted = 0 AND tblSystemNoticeUser.is_deleted = 0",
			userId,
			defines.SystemNoticeStatusPublish).
		Order("tblSystemNotice.publish_time desc").
		Limit(6).
		Find(&list).
		Error

	return
}

func (dao *systemNoticeUserDao) GetLastReadUser(ctx *gin.Context, noticeId int64, limit int, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where("notice_id = ? AND is_content_read = 1", noticeId).
		Scopes(FilterDeleted).
		Limit(int(limit)).
		Order("last_read_time desc").
		Find(&list).
		Error
	return
}

func (dao *systemNoticeUserDao) ListByUids(ctx *gin.Context, conds map[string]interface{}, uids []int64, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	list = make([]SystemNoticeUser, 0)
	batchSize := 1000
	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		var batchResult []SystemNoticeUser
		err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
			Where(conds).
			Where("uid IN ?", batch).
			Scopes(FilterDeleted).
			Find(&batchResult).
			Error
		if err != nil {
			return nil, err
		}

		list = append(list, batchResult...)
	}

	return list, nil
}

func (dao *systemNoticeUserDao) GetSendUids(ctx *gin.Context, param dtonotice.ReceiverFilterParam, tx *gorm.DB) (uids []int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	noticeId := param.NoticeId
	startTime := param.StartTime
	endTime := param.EndTime
	db := tx.WithContext(ctx).Model(&SystemNoticeUser{})
	if startTime > 0 {
		db.Where("create_time >= ?", startTime)
	}
	if endTime > 0 {
		db.Where("create_time <= ?", endTime)
	}
	err = db.
		Where("notice_id = ?", noticeId).
		Scopes(FilterDeleted).
		Pluck("uid", &uids).
		Error
	return
}

func (dao *systemNoticeUserDao) GetReadUids(ctx *gin.Context, param dtonotice.ReceiverFilterParam, tx *gorm.DB) (uids []int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	noticeId := param.NoticeId
	startTime := param.StartTime
	endTime := param.EndTime
	readType := param.Type
	db := tx.WithContext(ctx).Model(&SystemNoticeUser{})
	if startTime > 0 {
		db.Where("last_read_time >= ?", startTime)
	}
	if endTime > 0 {
		db.Where("last_read_time <= ?", endTime)
	}
	if readType == defines.NoticeContentType {
		db.Where("is_content_read = 1")
	}
	if readType == defines.NoticeVideoType {
		db.Where("is_video_read = 1")
	}
	err = db.
		Where("notice_id = ?", noticeId).
		Scopes(FilterDeleted).
		Pluck("uid", &uids).
		Error
	return
}

func (dao *systemNoticeUserDao) GetUnReadUids(ctx *gin.Context, param dtonotice.ReceiverFilterParam, tx *gorm.DB) (uids []int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	noticeId := param.NoticeId
	startTime := param.StartTime
	endTime := param.EndTime
	readType := param.Type
	db := tx.WithContext(ctx).Model(&SystemNoticeUser{})
	if startTime > 0 {
		db.Where("create_time >= ?", startTime)
	}
	if endTime > 0 {
		db.Where("create_time <= ?", endTime)
	}
	if readType == defines.NoticeContentType {
		db.Where("is_content_read = 0")
	}
	if readType == defines.NoticeVideoType {
		db.Where("is_video_read = 0")
	}
	err = db.
		Where("notice_id = ?", noticeId).
		Scopes(FilterDeleted).
		Pluck("uid", &uids).
		Error
	return
}

func (dao *systemNoticeUserDao) GetFeedbackUids(ctx *gin.Context, param dtonotice.ReceiverFilterParam, tx *gorm.DB) (uids []int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	noticeId := param.NoticeId
	startTime := param.StartTime
	endTime := param.EndTime
	likeType := param.Type
	feedbackType := param.FeedbackType
	db := tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Joins("INNER JOIN tblSystemNoticeUserLike ON tblSystemNoticeUser.notice_id = tblSystemNoticeUserLike.notice_id AND tblSystemNoticeUser.uid = tblSystemNoticeUserLike.uid").
		Where("tblSystemNoticeUser.notice_id = ? AND tblSystemNoticeUserLike.type = ? AND tblSystemNoticeUserLike.feedback_type = ? AND tblSystemNoticeUser.is_deleted = 0",
			noticeId,
			likeType,
			feedbackType)

	if startTime > 0 {
		db = db.Where("tblSystemNoticeUserLike.create_time >= ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("tblSystemNoticeUserLike.create_time <= ?", endTime)
	}

	err = db.Pluck("tblSystemNoticeUser.uid", &uids).Error
	return
}

func (dao *systemNoticeUserDao) GetUids(ctx *gin.Context, noticeId int64, tx *gorm.DB) (uids []int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where("notice_id = ?", noticeId).
		Scopes(FilterDeleted).
		Pluck("uid", &uids).
		Error
	return
}

func (dao *systemNoticeUserDao) Count(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (count int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where(conds).
		Scopes(FilterDeleted).
		Count(&count).
		Error
	return
}

func (dao *systemNoticeUserDao) GetOne(ctx *gin.Context, noticeId int64, uid int64, tx *gorm.DB) (res *SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where("notice_id = ? AND uid = ?", noticeId, uid).
		Scopes(FilterDeleted).
		First(&res).
		Error
	return
}

func (dao *systemNoticeUserDao) GetByNoticeIdAndUids(ctx *gin.Context, noticeId int64, uids []int64, tx *gorm.DB) (list []SystemNoticeUser, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeUser{}).
		Where("notice_id = ? AND uid IN (?)", noticeId, uids).
		Scopes(FilterDeleted).
		Find(&list).
		Error
	return
}
