package notice

import (
	"assistantdeskgo/helpers"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var SystemNoticeCommentDao systemNoticeCommentDao

type systemNoticeCommentDao struct{}

type SystemNoticeComment struct {
	Id         int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`    // id
	ParentId   int64  `gorm:"column:parent_id;default:0" json:"parent_id"`     // 父评论id
	TopId      int64  `gorm:"column:top_id;default:0" json:"top_id"`           // 所属一级评论id
	NoticeId   int64  `gorm:"column:notice_id;not null" json:"notice_id"`      // 通知id
	Uid        int64  `gorm:"column:uid;default:0" json:"uid"`                 // 用户Uid
	IsSelected bool   `gorm:"column:is_selected;default:0" json:"is_selected"` // 是否精选评论：0-否，1-是
	Content    string `gorm:"column:content;not null" json:"content"`          // 内容
	IsDeleted  bool   `gorm:"column:is_deleted;default:0" json:"is_deleted"`   // 是否删除：0-未删，1-已删
	CreateTime int64  `gorm:"column:create_time;default:0" json:"create_time"` // 创建时间
	UpdateTime int64  `gorm:"column:update_time;default:0" json:"update_time"` // 更新时间
}

func (s *SystemNoticeComment) TableName() string {
	return "tblSystemNoticeComment"
}

func (dao *systemNoticeCommentDao) Insert(ctx *gin.Context, data SystemNoticeComment, tx *gorm.DB) (id int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Create(&data).Error
	id = data.Id
	return
}

func (dao *systemNoticeCommentDao) Update(ctx *gin.Context, noticeId int64, uid int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where("notice_id = ? AND uid = ?", noticeId, uid).
		Updates(data).Error
	return
}

func (dao *systemNoticeCommentDao) UpdateById(ctx *gin.Context, id int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where("id = ?", id).
		Updates(data).Error
	return
}

func (dao *systemNoticeCommentDao) BatchUpdateDeleted(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	if len(uids) == 0 {
		return nil
	}

	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(map[string]interface{}{
				"is_deleted":  0,
				"update_time": time.Now().Unix(),
			}).Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeCommentDao) Delete(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where(conds).
		Updates(map[string]interface{}{
			"is_deleted":  1,
			"update_time": time.Now().Unix(),
		}).Error
	return
}

func (dao *systemNoticeCommentDao) BatchDeleteByUids(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	if len(uids) == 0 {
		return nil
	}

	var updateData = map[string]interface{}{
		"is_deleted":  1,
		"update_time": time.Now().Unix(),
	}

	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(updateData).Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeCommentDao) Count(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (count int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where(conds).
		Scopes(FilterDeleted).
		Count(&count).Error
	return
}

func (dao *systemNoticeCommentDao) List(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (list []SystemNoticeComment, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where(conds).
		Scopes(FilterDeleted).
		Find(&list).Error
	return
}

func (dao *systemNoticeCommentDao) ListByCreateTime(ctx *gin.Context, conds map[string]interface{}, startTime int64, endTime int64, tx *gorm.DB) (list []SystemNoticeComment, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	db := tx.WithContext(ctx).Model(&SystemNoticeComment{})

	if startTime > 0 {
		db.Where("create_time >= ?", startTime)
	}

	if endTime > 0 {
		db.Where("create_time <= ?", endTime)
	}

	err = db.Where(conds).
		Scopes(FilterDeleted).
		Find(&list).Error
	return
}

func (dao *systemNoticeCommentDao) ListByTopIds(ctx *gin.Context, noticeId int64, topids []int64, tx *gorm.DB) (list []SystemNoticeComment, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where("notice_id = ? AND top_id IN ?", noticeId, topids).
		Scopes(FilterDeleted).
		Find(&list).Error
	return
}

func (dao *systemNoticeCommentDao) GetById(ctx *gin.Context, id int64, tx *gorm.DB) (res *SystemNoticeComment, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where("id = ?", id).
		Scopes(FilterDeleted).
		First(&res).Error
	return
}

func (dao *systemNoticeCommentDao) FindLastTopComment(ctx *gin.Context, noticeId int64, tx *gorm.DB) (res *SystemNoticeComment, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where("notice_id = ? AND parent_id = 0", noticeId).
		Scopes(FilterDeleted).
		Order("create_time DESC").
		Limit(1).
		Find(&res).Error
	return
}

// GetVisibleComments 获取用户可见的评论列表
// 1. 先查询用户可见的一级评论ID列表（自己的或精选的）
// 2. 查询可见的评论（包括一级评论本身，以及下面精选的或自己的回复）
func (dao *systemNoticeCommentDao) GetVisibleComments(ctx *gin.Context, noticeId int64, uid int64, tx *gorm.DB) (list []SystemNoticeComment, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	// 1. 先查询用户可见的一级评论ID列表（自己的或精选的）
	var topCommentIds []int64
	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Select("id").
		Where("notice_id = ? AND parent_id = 0 AND (uid = ? OR is_selected = ?)", noticeId, uid, true).
		Scopes(FilterDeleted).
		Pluck("id", &topCommentIds).Error
	if err != nil {
		return
	}

	if len(topCommentIds) == 0 {
		return
	}

	// 2. 查询可见的评论（包括一级评论本身，以及下面精选的或自己的回复）
	list = []SystemNoticeComment{}
	err = tx.WithContext(ctx).Model(&SystemNoticeComment{}).
		Where("notice_id = ?", noticeId).
		Where("(id IN ? OR (top_id IN ? AND (is_selected = ? OR uid = ?)))",
			topCommentIds, topCommentIds, true, uid).
		Scopes(FilterDeleted).
		Find(&list).Error
	return
}
