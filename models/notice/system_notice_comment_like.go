package notice

import (
	"assistantdeskgo/helpers"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var SystemNoticeCommentLikeDao systemNoticeCommentLikeDao

type systemNoticeCommentLikeDao struct{}

type SystemNoticeCommentLike struct {
	Id         int64 `gorm:"column:id;primaryKey;autoIncrement" json:"id"`    // id
	NoticeId   int64 `gorm:"column:notice_id;not null" json:"notice_id"`      // 通知id
	CommentId  int64 `gorm:"column:comment_id;not null" json:"comment_id"`    // 评论id
	Uid        int64 `gorm:"column:uid;default:0" json:"uid"`                 // 用户Uid
	IsDeleted  bool  `gorm:"column:is_deleted;default:0" json:"is_deleted"`   // 是否删除：0-未删，1-已删
	CreateTime int64 `gorm:"column:create_time;default:0" json:"create_time"` // 创建时间
	UpdateTime int64 `gorm:"column:update_time;default:0" json:"update_time"` // 更新时间
}

func (s *SystemNoticeCommentLike) TableName() string {
	return "tblSystemNoticeCommentLike"
}

func (dao *systemNoticeCommentLikeDao) Insert(ctx *gin.Context, data SystemNoticeCommentLike, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Create(&data).Error
	return
}

func (dao *systemNoticeCommentLikeDao) Update(ctx *gin.Context, noticeId int64, commentId int64, uid int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
		Where("notice_id = ? AND comment_id = ? AND uid = ?", noticeId, commentId, uid).
		Updates(data).Error
	return
}

func (dao *systemNoticeCommentLikeDao) UpdateById(ctx *gin.Context, id int64, data map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	data["update_time"] = time.Now().Unix()
	err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
		Where("id = ?", id).
		Updates(data).Error
	return
}

func (dao *systemNoticeCommentLikeDao) UpdateDeleteByUids(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	if len(uids) == 0 {
		return nil
	}

	updateData := map[string]interface{}{
		"is_deleted":  0,
		"update_time": time.Now().Unix(),
	}

	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(updateData).Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeCommentLikeDao) Delete(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
		Where(conds).
		Updates(map[string]interface{}{
			"is_deleted":  1,
			"update_time": time.Now().Unix(),
		}).Error
	return
}

func (dao *systemNoticeCommentLikeDao) BatchDeleteByUids(ctx *gin.Context, noticeId int64, uids []int64, batchSize int, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	if len(uids) == 0 {
		return nil
	}

	updateData := map[string]interface{}{
		"is_deleted":  1,
		"update_time": time.Now().Unix(),
	}

	for i := 0; i < len(uids); i += batchSize {
		end := i + batchSize
		if end > len(uids) {
			end = len(uids)
		}

		batch := uids[i:end]
		err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
			Where("notice_id = ? AND uid IN ?", noticeId, batch).
			Updates(updateData).Error
		if err != nil {
			return
		}
	}
	return
}

func (dao *systemNoticeCommentLikeDao) Count(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (count int64, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
		Where(conds).
		Scopes(FilterDeleted).
		Count(&count).Error
	return
}

func (dao *systemNoticeCommentLikeDao) List(ctx *gin.Context, conds map[string]interface{}, tx *gorm.DB) (list *[]SystemNoticeCommentLike, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
		Where(conds).
		Scopes(FilterDeleted).
		Find(&list).Error
	return
}

func (dao *systemNoticeCommentLikeDao) GetById(ctx *gin.Context, id int64, tx *gorm.DB) (res *SystemNoticeCommentLike, err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Model(&SystemNoticeCommentLike{}).
		Where("id = ?", id).
		Scopes(FilterDeleted).
		First(&res).Error
	return
}
