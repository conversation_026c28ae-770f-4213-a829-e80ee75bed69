package studyfeedback

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

type TblStudyFeedbackQuestionPointMapping struct {
	Id         int64 `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键id'" json:"id,omitempty"`
	Tid        int64 `gorm:"column:tid;default:0;NOT NULL;comment:'题目id'" json:"tid,omitempty"`
	PointId    int64 `gorm:"column:point_id;default:0;NOT NULL;comment:'知识点id'" json:"pointId,omitempty"`
	Creator    int64 `gorm:"column:creator;default:0;NOT NULL;comment:'创建者'" json:"creator,omitempty"`
	Updater    int64 `gorm:"column:updater;default:0;NOT NULL;comment:'更新者'" json:"updater,omitempty"`
	CreateTime int64 `gorm:"column:create_time;default:0;NOT NULL;comment:'创建时间'" json:"createTime,omitempty"`
	UpdateTime int64 `gorm:"column:update_time;default:0;NOT NULL;comment:'更新时间'" json:"updateTime,omitempty"`
	Deleted    int   `gorm:"column:deleted" json:"deleted"`
}

func (t *TblStudyFeedbackQuestionPointMapping) TableName() string {
	return "tblStudyFeedbackQuestionPointMapping"
}

func (t *TblStudyFeedbackQuestionPointMapping) GetPointMappingByTid(ctx *gin.Context, idList []int64) (list []TblStudyFeedbackQuestionPointMapping, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("tid in ?", idList).Where("deleted = ?", NotDeleted).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "GetPointMappingByTid fail,err=%v", err)
		return
	}
	return
}

func (t *TblStudyFeedbackQuestionPointMapping) DeleteMappingByPointId(ctx *gin.Context, pointId int64, assistantUid int64, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	updates := map[string]interface{}{
		"deleted":     IsDeleted,
		"updater":     assistantUid,
		"update_time": time.Now().Unix(),
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.Where("point_id = ?", pointId).Where("deleted = ?", NotDeleted).Updates(updates).Error
	if err != nil {
		zlog.Warnf(ctx, "DeleteMappingByPointId failed, pointId=%v,err:%+v", pointId, err)
		return
	}
	return
}

func (t *TblStudyFeedbackQuestionPointMapping) BatchInsert(ctx *gin.Context, list []TblStudyFeedbackQuestionPointMapping, tx *gorm.DB) (err error) {
	if len(list) == 0 {
		return nil
	}
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.CreateInBatches(list, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "BatchInsert failed, list=%v,err:%+v", list, err)
		return
	}
	return
}
