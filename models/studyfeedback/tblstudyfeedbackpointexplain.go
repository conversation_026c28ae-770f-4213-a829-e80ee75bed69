package studyfeedback

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

type TblStudyFeedbackPointExplain struct {
	Id           int64  `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键id'" json:"id,omitempty"`
	PointId      int64  `gorm:"column:point_id;default:0;NOT NULL;comment:'知识点id'" json:"pointId,omitempty"`
	ExplainIndex int    `gorm:"column:explain_index;default:0;NOT NULL;comment:'解读话术序号'" json:"explainIndex,omitempty"`
	Explain      string `gorm:"column:explain;default:;NOT NULL;comment:'解读话术'" json:"explain,omitempty"`
	Creator      int64  `gorm:"column:creator;default:0;NOT NULL;comment:'创建者'" json:"creator,omitempty"`
	Updater      int64  `gorm:"column:updater;default:0;NOT NULL;comment:'更新者'" json:"updater,omitempty"`
	CreateTime   int64  `gorm:"column:create_time;default:0;NOT NULL;comment:'创建时间'" json:"createTime,omitempty"`
	UpdateTime   int64  `gorm:"column:update_time;default:0;NOT NULL;comment:'更新时间'" json:"updateTime,omitempty"`
	Deleted      int    `gorm:"column:deleted" json:"deleted"`
}

func (t *TblStudyFeedbackPointExplain) TableName() string {
	return "tblStudyFeedbackPointExplain"
}

func (t *TblStudyFeedbackPointExplain) GetExplainByPointIdList(ctx *gin.Context, idList []int64) (list []TblStudyFeedbackPointExplain, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("point_id in ?", idList).Where("deleted = ?", NotDeleted).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "GetExplainByPointIdList fail,err=%v", err)
		return
	}
	return
}

func (t *TblStudyFeedbackPointExplain) DeleteMappingByPointId(ctx *gin.Context, pointId int64, assistantUid int64, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	updates := map[string]interface{}{
		"deleted":     IsDeleted,
		"updater":     assistantUid,
		"update_time": time.Now().Unix(),
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.Where("point_id = ?", pointId).Where("deleted = ?", NotDeleted).Updates(updates).Error
	if err != nil {
		zlog.Warnf(ctx, "DeleteMappingByPointId failed, pointId=%v,err:%+v", pointId, err)
		return
	}
	return
}

func (t *TblStudyFeedbackPointExplain) BatchInsert(ctx *gin.Context, list []TblStudyFeedbackPointExplain, tx *gorm.DB) (err error) {
	if len(list) == 0 {
		return nil
	}
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.CreateInBatches(list, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "BatchInsert failed, list=%v,err:%+v", list, err)
		return
	}
	return
}
