package studyfeedback

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"git.zuoyebang.cc/pkg/hints"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	HintsReadWrite = "/*#mode=READWRITE*/"
	HintsReadOnly  = "/*#mode=READONLY*/"
)

type TblStudyFeedbackPoint struct {
	Id         int64  `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键ID'" json:"id"`
	Point      string `gorm:"column:point;default:;NOT NULL;comment:'知识点'" json:"point"`
	Creator    int64  `gorm:"column:creator;default:0;NOT NULL;comment:'创建者'" json:"creator"`
	Updater    int64  `gorm:"column:updater;default:0;NOT NULL;comment:'更新着'" json:"updater"`
	CreateTime int64  `gorm:"column:create_time;default:0;NOT NULL;comment:'创建时间'" json:"createTime"`
	UpdateTime int64  `gorm:"column:update_time;default:0;NOT NULL;comment:'更新时间'" json:"updateTime"`
	Deleted    int    `gorm:"column:deleted" json:"deleted"`
}

func (t *TblStudyFeedbackPoint) TableName() string {
	return "tblStudyFeedbackPoint"
}

func (t *TblStudyFeedbackPoint) GetPointByIdList(ctx *gin.Context, idList []int64) (list []TblStudyFeedbackPoint, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("id in ?", idList).Where("deleted = ?", NotDeleted).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "GetPointByIdList fail,err=%v", err)
		return
	}
	return
}

func (t *TblStudyFeedbackPoint) GetPointByName(ctx *gin.Context, point string, hitMaster bool) (ret TblStudyFeedbackPoint, err error) {
	if point == "" {
		return
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(t.TableName()).Where("point = ?", point).Where("deleted = ?", NotDeleted)
	if hitMaster {
		db.Clauses(hints.NewHint(HintsReadWrite))
	}
	err = db.First(&ret).Error
	return
}

func (t *TblStudyFeedbackPoint) Insert(ctx *gin.Context, point *TblStudyFeedbackPoint, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.Create(point).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", point, err)
		return
	}
	return
}

func (t *TblStudyFeedbackPoint) DeleteMappingByPointId(ctx *gin.Context, pointId int64, assistantUid int64, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	updates := map[string]interface{}{
		"deleted":     IsDeleted,
		"updater":     assistantUid,
		"update_time": time.Now().Unix(),
	}
	db := tx.WithContext(ctx).Table(t.TableName())
	err = db.Where("id = ?", pointId).Where("deleted = ?", NotDeleted).Updates(updates).Error
	if err != nil {
		zlog.Warnf(ctx, "DeleteMappingByPointId failed, pointId=%v,err:%+v", pointId, err)
		return
	}
	return
}
