package duxuescDao

import (
	"assistantdeskgo/helpers"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var Config<PERSON>ao configDao

type configDao struct {
}

type Config struct {
	ID             int64  `gorm:"column:id" json:"id"`
	Cfg<PERSON>ey         string `gorm:"column:cfg_key" json:"cfgKey"`
	CfgValue       string `gorm:"column:cfg_value" json:"cfgValue"`
	KeyType        string `gorm:"column:key_type" json:"keyType"`
	ValueType      string `gorm:"column:value_type" json:"valueType"`
	Description    string `gorm:"column:description" json:"description"`
	Status         int64  `gorm:"column:status" json:"status"`
	CreateStaffUid int64  `gorm:"column:create_staff_uid" json:"createStaffUid"`
	UpdateStaffUid int64  `gorm:"column:update_staff_uid" json:"updateStaffUid"`
	CreateTime     int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime     int64  `gorm:"column:update_time" json:"updateTime"`
}

func (Config) TableName() string {
	return TblConfig
}

func (dao configDao) GetConfigByKey(ctx *gin.Context, key string, val interface{}, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientDuxuesc
	}

	config := &Config{}
	err = tx.WithContext(ctx).WithContext(ctx).
		Where("cfg_key = ?", key).
		First(config).Error
	if err != nil {
		return
	}

	if err = json.Unmarshal([]byte(config.CfgValue), val); err != nil {
		return err
	}
	return
}
