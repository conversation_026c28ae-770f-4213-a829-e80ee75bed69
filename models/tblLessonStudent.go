package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

const (
	StatusOK     = 0
	StatusDelete = 1

	FocusOK  = 1
	FocusNot = 0
)

type LessonStudent struct {
	ID           int64  `gorm:"id" json:"id"` // 自增id
	StudentUid   int64  `gorm:"student_uid" json:"studentUid"`
	CourseId     int64  `gorm:"course_id" json:"courseId"`
	LessonId     int64  `gorm:"lesson_id" json:"lessonId"`
	AssistantUid int64  `gorm:"assistant_uid" json:"assistantUid"`
	ClassId      int64  `gorm:"class_id" json:"classId"`
	PreAttend    int    `gorm:"pre_attend" json:"preAttend"`
	Status       int    `gorm:"status" json:"status"`
	Focus        int    `gorm:"focus" json:"focus"`
	ExtData      string `gorm:"ext_data" json:"extData"`
	CreateTime   int64  `gorm:"create_time" json:"createTime"`
	UpdateTime   int64  `gorm:"update_time" json:"updateTime"`
}

func (l *LessonStudent) TableName(courseID int64) string {
	tableName := "tblLessonStudent"
	p := courseID % 20
	return tableName + strconv.Itoa(int(p))
}

func (l *LessonStudent) GetByLessonAndStudent(ctx *gin.Context, courseID, lessonID, studentID int64) (*LessonStudent, error) {
	var res LessonStudent
	err := helpers.MysqlClientFuDao.Table(l.TableName(courseID)).WithContext(ctx).
		Where("lesson_id = ? and student_uid = ?", lessonID, studentID).First(&res).Error
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (l *LessonStudent) GetByLessonsAndStudent(ctx *gin.Context, courseID, studentID int64, lessonIDs []int64) ([]*LessonStudent, error) {
	var res []*LessonStudent
	err := helpers.MysqlClientFuDao.Table(l.TableName(courseID)).WithContext(ctx).
		Where("lesson_id in (?) and student_uid = ?", lessonIDs, studentID).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (l *LessonStudent) Create(ctx *gin.Context, courseID int64, ls *LessonStudent) error {
	ls.CreateTime = time.Now().Unix()
	ls.UpdateTime = time.Now().Unix()
	return helpers.MysqlClientFuDao.Table(l.TableName(courseID)).WithContext(ctx).Create(ls).Error
}

func (l *LessonStudent) UpdateByUniqueKey(ctx *gin.Context, courseID, lessonID, studentID int64, ls *LessonStudent) error {
	ls.UpdateTime = time.Now().Unix()
	data := map[string]interface{}{
		"update_time":   ls.UpdateTime,
		"ext_data":      ls.ExtData,
		"assistant_uid": ls.AssistantUid,
		"pre_attend":    ls.PreAttend,
		"status":        ls.Status,
		"class_id":      ls.ClassId,
		"focus":         ls.Focus,
	}
	err := helpers.MysqlClientFuDao.Table(l.TableName(courseID)).WithContext(ctx).
		Where("lesson_id = ? and student_uid = ?", lessonID, studentID).Updates(data).Error
	return err
}

func (l *LessonStudent) GetByStudentIds(ctx *gin.Context, courseID, lessonID int64, studentIDs []int64) ([]*LessonStudent, error) {
	var res []*LessonStudent
	err := helpers.MysqlClientFuDao.Table(l.TableName(courseID)).WithContext(ctx).
		Where("lesson_id = ? and student_uid in (?) and status = ?", lessonID, studentIDs, StatusOK).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}
