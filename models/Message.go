package models

import (
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/api/muse/message"
	"assistantdeskgo/components"
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"encoding/json"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"strings"
	"time"
	"unicode/utf8"
)

type Message struct {
	ID             int64  `json:"id" gorm:"id"`                             // 自增id
	MessageGroupId int64  `json:"message_group_id" gorm:"message_group_id"` // 消息组ID
	Order          int64  `json:"order" gorm:"order"`                       // 顺序
	MessageType    int64  `json:"message_type" gorm:"message_type"`         // 消息类型：0-文字、1-图片、2-语音、8-文件、20-视频号、4-卡片链接
	Content        string `json:"content" gorm:"content"`                   // 消息内容
	PreMessageId   int64  `json:"pre_message_id" gorm:"pre_message_id"`     // 上一条消息信息，为0时不存在
	IntervalTime   int64  `json:"interval_time" gorm:"interval_time"`       // 相对上一条时间的延迟发送时间，单位s
	IsDeleted      int8   `json:"is_deleted" gorm:"is_deleted"`             // 是否删除：0-未删除、1-删除
	CreateTime     int64  `json:"create_time" gorm:"create_time"`           // 创建时间
	CreateUid      int64  `json:"create_uid" gorm:"create_uid"`             // 创建人uid
	UpdateTime     int64  `json:"update_time" gorm:"update_time"`           // 更新时间
	UpdateUid      int64  `json:"update_uid" gorm:"update_uid"`             // 更新人uid
}

type MsgCheckParams struct {
	IsAtMembers bool
}

type BaseMessage interface {
	GetMessageInfo() (ret interface{})
	GetMsgType() int64
	GetItemList() []interface{}
	Check(params *MsgCheckParams) error
	FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error)
	SetContent(content string) error
	GetAvailableRange() string
	Init() BaseMessage
}

var ContentEntityMap = map[int64]BaseMessage{
	defines.MessageTypeWord:          &Word{},
	defines.MessageTypeVoice:         &Voice{},
	defines.MessageTypePicture:       &Img{},
	defines.MessageTypeVideo:         &Video{},
	defines.MessageTypeCardLink:      &Card{},
	defines.MessageTypeFile:          &File{},
	defines.MessageTypeVideoMaterial: &VideoMaterial{},
}

var DefaultMsgCheckParams = &MsgCheckParams{IsAtMembers: false}

func (m *Message) GetMessageContent() (BaseMessage, error) {
	if _, ok := ContentEntityMap[m.MessageType]; !ok {
		return nil, errors.New("不支持的消息类型")
	}

	ret := ContentEntityMap[m.MessageType]
	ret = ret.Init()
	err := ret.SetContent(m.Content)

	return ret, err
}

func (m *Message) Format(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	content, err := m.GetMessageContent()
	if err != nil {
		return nil, err
	}

	isAtMembers := false
	if len(atMembers.StudentUids) > 0 || len(atMembers.RemoteIds) > 0 {
		isAtMembers = true
	}
	checkParams := &MsgCheckParams{IsAtMembers: isAtMembers}

	err = content.Check(checkParams)
	if err != nil {
		return nil, err
	}
	return content.FormatMessageInfo(ctx, delayTime, sendTime, atMembers)
}

func DecodeContent(content string, ret interface{}) error {
	if err := json.Unmarshal([]byte(content), ret); err != nil {
		return err
	}
	return nil
}

type Word struct {
	Word           []string `json:"word"`
	AvailableRange string   `json:"-"`
	IsAtMember     bool     `json:"isAtMember"`
}

func (w *Word) SetContent(content string) (err error) {
	err = DecodeContent(content, &w)
	if err != nil {
		return err
	}

	w.AvailableRange = defines.GetAvailableRange(w.Word)
	words := make([]string, 0)
	for _, item := range w.Word {
		words = append(words, strings.Trim(item, " \t"))
	}
	w.Word = words
	return
}

func (w *Word) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	m := message.MessageInfo{
		DelayTime:  delayTime,
		MsgType:    w.GetMsgType(),
		MsgContent: w.Word,
		AtMembers:  atMembers,
	}
	return []message.MessageInfo{m}, nil
}

func (w *Word) Check(params *MsgCheckParams) error {
	wordCount := 0
	for _, item := range w.Word {
		wordCount += utf8.RuneCountInString(item)
	}

	if !params.IsAtMembers && wordCount == 0 { // @人 文本内容可以为空
		return errors.New("文本消息内容不能为空")
	}

	if wordCount > 1000 { // 标签会占用多个字符, 后端放开
		return components.DefaultError("文字消息体内容字数不能超过1000")
	}
	return nil
}

func (w *Word) GetMessageInfo() (ret interface{}) {
	return w.Word
}
func (w *Word) GetMsgType() int64 {
	return defines.MessageTypeWord
}

func (w *Word) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(w.Word))
	for _, item := range w.Word {
		ret = append(ret, item)
	}
	return ret
}

func (w *Word) GetAvailableRange() string {
	return w.AvailableRange
}

func (w *Word) Init() BaseMessage {
	return &Word{}
}

type Img struct {
	ImgList []ImgInfo `json:"imgList"`
}

func (i *Img) SetContent(content string) error {
	return DecodeContent(content, &i)
}

func (i *Img) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	var messageInfos []message.MessageInfo
	defaultDelayTime, err := GetDefaultDelayTime(ctx)
	if err != nil {
		return messageInfos, err
	}
	for idx, img := range i.ImgList {
		// 默认1秒
		delay := defaultDelayTime
		if idx == 0 {
			// 第0个为传入的delayTime
			delay = delayTime
		}
		messageInfos = append(messageInfos, message.MessageInfo{
			DelayTime:  delay,
			MsgType:    i.GetMsgType(),
			MsgContent: img,
			AtMembers:  atMembers,
		})
	}
	return messageInfos, nil
}

func (i *Img) Check(params *MsgCheckParams) error {
	if len(i.ImgList) <= 0 {
		return errors.New("图片消息为空")
	}
	for _, img := range i.ImgList {
		if img.Url == "" {
			return errors.New("图片链接不能为空")
		}
		if img.Name == "" {
			return errors.New("图片名称不能为空")
		}
	}
	return nil
}

func (i *Img) GetMessageInfo() (ret interface{}) {
	return i
}
func (i *Img) GetMsgType() int64 {
	return defines.MessageTypePicture
}
func (i *Img) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(i.ImgList))
	for _, item := range i.ImgList {
		ret = append(ret, item)
	}
	return ret
}

func (i *Img) GetAvailableRange() string {
	return defines.AvailableRangeForGeneral
}

func (i *Img) Init() BaseMessage {
	return &Img{}
}

type ImgInfo struct {
	Ori    string `json:"ori"`    // 文件名
	Name   string `json:"name"`   // 文件名
	Url    string `json:"url"`    // 文件地址
	Width  int64  `json:"width"`  // 宽
	Height int64  `json:"height"` // 高
	Md5    string `json:"md5"`    // 高
}

type Video struct {
	VideoList []VideoInfo `json:"videoList"`
}

func (v *Video) SetContent(content string) error {
	return DecodeContent(content, &v)
}

func (v *Video) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	var messageInfos []message.MessageInfo
	defaultDelayTime, err := GetDefaultDelayTime(ctx)
	if err != nil {
		return messageInfos, err
	}
	for idx, video := range v.VideoList {
		// 默认1秒
		delay := defaultDelayTime
		if idx == 0 {
			// 第0个为传入的delayTime
			delay = delayTime
		}
		messageInfos = append(messageInfos, message.MessageInfo{
			DelayTime:  delay,
			MsgType:    v.GetMsgType(),
			MsgContent: video,
			AtMembers:  atMembers,
		})
	}
	return messageInfos, nil
}

func (v *Video) Check(params *MsgCheckParams) error {
	if len(v.VideoList) <= 0 {
		return errors.New("视频消息为空")
	}
	for _, img := range v.VideoList {
		if img.Url == "" {
			return errors.New("视频链接不能为空")
		}
		if img.Name == "" {
			return errors.New("视频名称不能为空")
		}
	}
	return nil
}

func (v *Video) GetMessageInfo() (ret interface{}) {
	return v
}
func (v *Video) GetMsgType() int64 {
	return defines.MessageTypeVideo
}
func (v *Video) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(v.VideoList))
	for _, item := range v.VideoList {
		ret = append(ret, item)
	}
	return ret
}

func (v *Video) GetAvailableRange() string {
	return defines.AvailableRangeForGeneral
}

func (v *Video) Init() BaseMessage {
	return &Video{}
}

type VideoInfo struct {
	Ori  string `json:"ori"`  // 文件名
	Name string `json:"name"` // 文件名
	Url  string `json:"url"`  // 文件地址
	Md5  string `json:"md5"`  // 文件地址
}

type Voice struct {
	VoiceList []VoiceInfo `json:"voiceList"`
}

type VoiceInfo struct {
	Url           string `json:"url,omitempty"` // 前端使用
	VoiceName     string `json:"voiceName"`
	VoiceType     int64  `json:"voiceType"`
	SilkVoiceLink string `json:"silkVoiceLink"`
	Duration      int64  `json:"duration"`
	Md5           string `json:"md5"`
}

func (v *Voice) SetContent(content string) error {
	return DecodeContent(content, &v)
}

func (v *Voice) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	var msgList []message.MessageInfo
	for _, voice := range v.VoiceList {
		if voice.SilkVoiceLink == "" {
			silkName, err := utils.GetSilkName(ctx, voice.VoiceName, voice.VoiceType)
			if err != nil {
				return nil, errors.New("silk文件转化失败")
			}
			voice.SilkVoiceLink = silkName
		}
		msgList = append(msgList, message.MessageInfo{
			DelayTime:  delayTime,
			MsgType:    v.GetMsgType(),
			MsgContent: voice,
			AtMembers:  atMembers,
		})
	}
	return msgList, nil
}

func (v *Voice) Check(params *MsgCheckParams) error {
	if len(v.VoiceList) <= 0 {
		return errors.New("录音消息为空")
	}
	for _, voice := range v.VoiceList {
		if voice.Duration <= 0 {
			return errors.New("录音文件时间缺失")
		}
		if voice.Duration > 60 {
			return errors.New("语音消息体不能超过60s")
		}
		if !defines.CanConvertVoice(voice.VoiceType) {
			return errors.New("语音消息体类型不支持转换")
		}
		if voice.VoiceName == "" {
			return errors.New("录音文件名不能为空")
		}
	}
	return nil
}

func (v *Voice) GetAvailableRange() string {
	return defines.AvailableRangeForGeneral
}

func (v *Voice) GetMessageInfo() (ret interface{}) {
	return v
}
func (v *Voice) GetMsgType() int64 {
	return defines.MessageTypeVoice
}

func (v *Voice) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(v.VoiceList))
	for _, item := range v.VoiceList {
		ret = append(ret, item)
	}
	return ret
}

func (v *Voice) Init() BaseMessage {
	return &Voice{}
}

type File struct {
	FileList []FileInfo `json:"fileList"`
}
type FileInfo struct {
	Url      string `json:"url,omitempty"` // 前端使用
	FileName string `json:"fileName"`
	Ori      string `json:"ori"`  // 文件名
	Size     int64  `json:"size"` // 文件大小
	Md5      string `json:"md5"`  // 文件大小
}

func (f *File) SetContent(content string) error {
	return DecodeContent(content, &f)
}

func (f *File) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	var msgList []message.MessageInfo
	for _, file := range f.FileList {
		msgList = append(msgList, message.MessageInfo{
			DelayTime:  delayTime,
			MsgType:    f.GetMsgType(),
			MsgContent: file,
			AtMembers:  atMembers,
		})
	}
	return msgList, nil
}

func (f *File) Check(params *MsgCheckParams) error {
	if len(f.FileList) <= 0 {
		return errors.New("文件消息不能为空")
	}
	for _, file := range f.FileList {
		if file.FileName == "" {
			return errors.New("文件名不能为空")
		}
		if file.Ori == "" {
			return errors.New("文件标题不能为空")
		}
		if file.Size <= 0 {
			return errors.New("文件大小异常")
		}
	}
	return nil
}

func (f *File) GetAvailableRange() string {
	return defines.AvailableRangeForGeneral
}

func (f *File) GetMessageInfo() (ret interface{}) {
	return f
}
func (f *File) GetMsgType() int64 {
	return defines.MessageTypeFile
}

func (f *File) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(f.FileList))
	for _, item := range f.FileList {
		ret = append(ret, item)
	}
	return ret
}

func (f *File) Init() BaseMessage {
	return &File{}
}

type Card struct {
	CardList       []CardInfo `json:"cardList"`
	AvailableRange string     `json:"-"`
}
type CardInfo struct {
	Link         string   `json:"link"`
	Icon         string   `json:"icon"`
	Title        []string `json:"title"`
	Introduction []string `json:"introduction"`
}

func (c *Card) SetContent(content string) (err error) {
	err = DecodeContent(content, &c)

	if len(c.CardList) == 0 {
		return
	}

	c.AvailableRange = defines.AvailableRangeForGeneral
	for _, cardInfo := range c.CardList {
		if len(cardInfo.Title) > 0 {
			tag := defines.GetAvailableRange(cardInfo.Title)
			if tag == defines.AvailableRangeForStudentLevel {
				c.AvailableRange = defines.AvailableRangeForStudentLevel
				return
			}
		}

		if len(cardInfo.Introduction) > 0 {
			tag := defines.GetAvailableRange(cardInfo.Introduction)
			if tag == defines.AvailableRangeForStudentLevel {
				c.AvailableRange = defines.AvailableRangeForStudentLevel
				return
			}
		}
	}

	return err
}

func (c *Card) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	var msgList []message.MessageInfo
	for _, card := range c.CardList {
		msgList = append(msgList, message.MessageInfo{
			DelayTime:  delayTime,
			MsgType:    c.GetMsgType(),
			MsgContent: card,
			AtMembers:  atMembers,
		})
	}
	return msgList, nil
}

func (c *Card) Check(params *MsgCheckParams) error {
	if len(c.CardList) <= 0 {
		return errors.New("卡片消息为空")
	}
	for _, card := range c.CardList {
		if card.Link == "" {
			return errors.New("卡片链接不能为空")
		}
		if card.Icon == "" {
			return errors.New("卡片封面不能为空")
		}
		if len(card.Title) == 0 {
			return errors.New("卡片标题不能为空")
		}
		if len(card.Introduction) == 0 {
			return errors.New("卡片简介不能为空")
		}
	}
	return nil
}

func (c *Card) GetAvailableRange() string {
	return c.AvailableRange
}

func (c *Card) GetMessageInfo() (ret interface{}) {
	return c
}
func (c *Card) GetMsgType() int64 {
	return defines.MessageTypeCardLink
}

func (c *Card) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(c.CardList))
	for _, item := range c.CardList {
		ret = append(ret, item)
	}
	return ret
}

func (c *Card) Init() BaseMessage {
	return &Card{}
}

type VideoMaterial struct {
	MaterialList []VideoMaterialInfo `json:"materialList"`
}

type VideoMaterialInfo struct {
	Avatar       string `json:"avatar"`
	CoverURL     string `json:"coverUrl"`
	Desc         string `json:"desc"`
	Extras       string `json:"extras"`
	FeedType     int    `json:"feedType"`
	Nickname     string `json:"nickname"`
	ThumbURL     string `json:"thumbUrl"`
	URL          string `json:"url"`
	Eid          string `json:"eid"`
	ExpireTime   string `json:"expireTime"`
	ThumbPid     string `json:"thumbPid"`
	ShowThumbURL string `json:"showThumbUrl"`
}

func (v *VideoMaterial) SetContent(content string) error {
	return DecodeContent(content, &v)
}

func (v *VideoMaterial) FormatMessageInfo(ctx *gin.Context, delayTime, sendTime int64, atMembers message.AtMembers) ([]message.MessageInfo, error) {
	var msgList []message.MessageInfo
	for _, material := range v.MaterialList {
		material.ThumbURL = material.ShowThumbURL
		timeInfo := time.Now()
		if sendTime > 0 {
			time.Unix(sendTime, 0)
		}
		material.ExpireTime = cast.ToString(timeInfo.Add(time.Hour * 24 * 2).Unix())
		msgList = append(msgList, message.MessageInfo{
			DelayTime:  delayTime,
			MsgType:    v.GetMsgType(),
			MsgContent: material,
			AtMembers:  atMembers,
		})
	}
	return msgList, nil
}

func (v *VideoMaterial) Check(params *MsgCheckParams) error {
	if len(v.MaterialList) <= 0 {
		return errors.New("视频号消息为空")
	}
	for _, material := range v.MaterialList {
		if material.Avatar == "" {
			return errors.New("作者头像不能为空")
		}
		if material.CoverURL == "" {
			return errors.New("封面url不能为空")
		}
		if material.FeedType == 0 {
			return errors.New("视频号类型不能为空")
		}
		if material.Nickname == "" {
			return errors.New("作者昵称不能为空")
		}
		if material.ThumbPid == "" {
			return errors.New("封面缩略图不能为空")
		}
		if material.ShowThumbURL == "" {
			return errors.New("封面缩略图不能为空")
		}
		if material.URL == "" {
			return errors.New("视频号链接不能为空")
		}
	}
	return nil
}

func (v *VideoMaterial) GetAvailableRange() string {
	return defines.AvailableRangeForGeneral
}

func (v *VideoMaterial) GetMessageInfo() (ret interface{}) {
	return v
}
func (v *VideoMaterial) GetMsgType() int64 {
	return defines.MessageTypeVideoMaterial
}

func (v *VideoMaterial) GetItemList() []interface{} {
	ret := make([]interface{}, 0, len(v.MaterialList))
	for _, item := range v.MaterialList {
		item.ThumbURL = item.ShowThumbURL
		item.ExpireTime = cast.ToString(time.Now().Add(time.Hour * 24 * 7).Unix()) // 过期时间设置为一周后
		ret = append(ret, item)
	}
	return ret
}

func (v *VideoMaterial) Init() BaseMessage {
	return &VideoMaterial{}
}

func (m *Message) TableName(groupId int64) string {
	pos := groupId % 10
	return fmt.Sprintf("tblMessage%+v", pos)
}

func (m *Message) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (m *Message) Tx(ctx *gin.Context) *gorm.DB {
	return m.SchemaClient().WithContext(ctx).Begin()
}

func (m *Message) BatchInsertWithTx(ctx *gin.Context, data []*Message, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	for _, item := range data {
		if item.MessageGroupId == 0 {
			err = components.DefaultError("BatchInsertWithTx, 分片键messageGroupId不能为空")
			zlog.Warnf(ctx, "BatchInsertWithTx, 分片键messageGroupId不能为空,data:%+v", data)
			return
		}
	}
	groupId := data[0].MessageGroupId
	if tx != nil {
		db := tx.WithContext(ctx).Table(m.TableName(groupId))
		err = db.CreateInBatches(data, 100).Error
		if err != nil {
			zlog.Warnf(ctx, "Message BatchInsertWithTx err, err:%+v", err)
		}
		return
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName(groupId))
	err = db.CreateInBatches(data, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "Message BatchInsert err, err:%+v", err)
	}
	return
}

func (m *Message) BatchUpdateWithTx(ctx *gin.Context, data []*Message, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		for _, message := range data {
			if message.MessageGroupId == 0 {
				err = components.DefaultError("BatchUpdateWithTx，分片键messageGroupId不能为空")
				zlog.Warnf(ctx, "BatchUpdateWithTx, 分片键messageGroupId不能为空,data:%+v", data)
				return
			}
			_err := tx.WithContext(ctx).Table(m.TableName(message.MessageGroupId)).Updates(message).Error
			if _err != nil {
				err = _err
				if err != nil {
					zlog.Warnf(ctx, "Message BatchUpdateWithTx err, data:%+v, err:%+v", message, err)
				}
				return
			}
		}
		return
	}

	for _, message := range data {
		if message.MessageGroupId == 0 {
			err = components.DefaultError("更新message时，分片键messageGroupId不能为空")
			zlog.Warnf(ctx, "BatchUpdateWithTx, 分片键messageGroupId不能为空,data:%+v", data)
			return
		}
		_err := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName(message.MessageGroupId)).Updates(message).Error
		if _err != nil {
			err = _err
			if err != nil {
				zlog.Warnf(ctx, "Message BatchUpdate err, data:%+v, err:%+v", message, err)
			}
			return
		}
	}
	return
}

// List 获取有效的消息列表
func (m *Message) List(ctx *gin.Context, groupId int64) (messageList []Message, err error) {
	if groupId == 0 {
		err = components.DefaultError("List，分片键messageGroupId不能为空")
		zlog.Warnf(ctx, "List, 分片键messageGroupId不能为空")
		return
	}
	conditions := map[string]interface{}{
		"message_group_id": groupId,
		"is_deleted":       0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName(groupId)).Where(conditions).Find(&messageList).Error
	if err != nil {
		zlog.Warnf(ctx, "Message List err, groupId:%+v, err:%+v", groupId, err)
	}
	return
}

func (m *Message) UpdateByIds(ctx *gin.Context, groupId int64, ids []int64, values map[string]interface{}, tx *gorm.DB) (err error) {
	if groupId == 0 {
		err = components.DefaultError("UpdateByIds，分片键messageGroupId不能为空")
		zlog.Warnf(ctx, "UpdateByIds, 分片键messageGroupId不能为空")
		return
	}
	if len(ids) == 0 {
		err = components.DefaultError("UpdateByIds，id不能为空")
		zlog.Warnf(ctx, "UpdateByIds，id不能为空")
		return
	}
	conditions := map[string]interface{}{
		"id": ids,
	}
	if tx != nil {
		err = tx.WithContext(ctx).Table(m.TableName(groupId)).Where(conditions).Updates(values).Error
	} else {
		err = m.SchemaClient().WithContext(ctx).Table(m.TableName(groupId)).Where(conditions).Updates(values).Error
	}
	if err != nil {
		zlog.Warnf(ctx, "Message UpdateByIds err, ids:%+v, err:%+v", ids, err)
	}
	return
}

func (m *Message) UpdateByGroupId(ctx *gin.Context, groupId int64, values map[string]interface{}, tx *gorm.DB) (err error) {
	if groupId == 0 {
		err = components.DefaultError("UpdateByGroupId，分片键messageGroupId不能为空")
		zlog.Warnf(ctx, "UpdateByGroupId, 分片键messageGroupId不能为空")
		return
	}

	conditions := map[string]interface{}{
		"message_group_id": groupId,
	}

	if tx != nil {
		err = tx.WithContext(ctx).Table(m.TableName(groupId)).Where(conditions).Updates(values).Error
	} else {
		err = m.SchemaClient().WithContext(ctx).Table(m.TableName(groupId)).Where(conditions).Updates(values).Error
	}
	if err != nil {
		zlog.Warnf(ctx, "Message UpdateByGroupId err, groupId:%+v, err:%+v", groupId, err)
	}
	return
}

func GetDefaultDelayTime(ctx *gin.Context) (delayTime int64, err error) {
	var config mercury.SendWxMessage
	if err = mercury.GetConfigForJson(ctx, mercury.ConfigKeyForWxSend, mercury.DefaultExpireTime, &config); err != nil {
		zlog.Warnf(ctx, "[GetDefaultDelayTime] get mercury config failed, err: %+v", err)
		return
	}

	delayTime = config.DefaultDelayTime
	if delayTime == 0 {
		delayTime = 1 // 未配置默认延迟时间时, 延迟1s
	}
	return
}

func (m *Message) ListByGroupIdList(ctx *gin.Context, groupIdList []int64, lastId int64) (messageList []Message, err error) {
	if len(groupIdList) == 0 {
		err = components.DefaultError("List，分片键messageGroupId不能为空")
		zlog.Warnf(ctx, "List, 分片键messageGroupId不能为空")
		return
	}

	err = m.SchemaClient().WithContext(ctx).Table(m.TableName(groupIdList[0])).
		Where("message_group_id in ?", groupIdList).
		Where("is_deleted = ?", 0).
		Where("id > ?", lastId).
		Limit(1000).
		Order("id asc").
		Find(&messageList).Error
	if err != nil {
		zlog.Warnf(ctx, "Message List err, groupId:%+v, err:%+v", groupIdList, err)
	}
	return
}
