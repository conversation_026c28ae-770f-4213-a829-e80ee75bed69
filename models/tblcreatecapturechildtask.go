package models

import (
	"assistantdeskgo/helpers"
	"errors"
	"git.zuoyebang.cc/pkg/hints"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

const (
	modNum = 20

	CreateCaptureChildTaskStatusDoing   = 1
	CreateCaptureChildTaskStatusSuccess = 2
	CreateCaptureChildTaskStatusFailed  = 3
)

type CreateCaptureChildTask struct {
	Id             int    `gorm:"primary_key;column:id" json:"id"`
	ParentTaskId   int    `gorm:"column:parent_task_id" json:"parentTaskId"`
	GenerateTaskId string `gorm:"column:generate_task_id" json:"generateTaskId"`
	PicUrl         string `gorm:"column:pic_url" json:"picUrl"`
	SendId         string `gorm:"column:send_id" json:"sendId"`
	Type           int    `gorm:"column:type" json:"type"`
	Status         int    `gorm:"column:status" json:"status"`
	CreateTime     int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime     int64  `gorm:"column:update_time" json:"updateTime"`
	ExtData        string `gorm:"column:ext_data" json:"extData"`
}

func (c *CreateCaptureChildTask) TableName(parentTaskId int) string {
	return "tblCreateCaptureChildTask" + strconv.Itoa(parentTaskId%modNum)
}

func (c *CreateCaptureChildTask) Create(ctx *gin.Context, taskInfo CreateCaptureChildTask) error {
	if taskInfo.ParentTaskId <= 0 || taskInfo.GenerateTaskId == "" || taskInfo.PicUrl == "" || taskInfo.SendId == "" {
		return errors.New("参数错误")
	}
	nowTime := time.Now().Unix()
	taskInfo.CreateTime = nowTime
	taskInfo.UpdateTime = nowTime
	if taskInfo.Status == 0 {
		taskInfo.Status = CreateCaptureChildTaskStatusDoing
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(taskInfo.ParentTaskId)).Create(&taskInfo).Error
	return err
}

func (c *CreateCaptureChildTask) ChangeStatus(ctx *gin.Context) error {
	if c.GenerateTaskId == "" || c.ParentTaskId <= 0 || c.PicUrl == "" {
		return errors.New("参数错误")
	}
	value := map[string]interface{}{
		"status":      c.Status,
		"update_time": time.Now().Unix(),
		"pic_url":     c.PicUrl,
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(c.ParentTaskId)).
		Where("generate_task_id = ?", c.GenerateTaskId).Updates(value).Error
	return err
}

func (c *CreateCaptureChildTask) GetChildTaskByPId(ctx *gin.Context, hitMaster bool) (list []CreateCaptureChildTask, err error) {
	if c.ParentTaskId <= 0 {
		err = errors.New("参数错误")
		return
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(c.ParentTaskId)).
		Where("parent_task_id = ?", c.ParentTaskId)
	if hitMaster {
		db.Clauses(hints.NewHint(HintsReadWrite))
	}
	err = db.Find(&list).Error
	return
}
