package remark

import (
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"assistantdeskgo/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TblAssistantCourseStudent1History struct {
	ID                    int64  `gorm:"id" json:"id"` // 自增id
	StudentUid            int64  `json:"student_uid" gorm:"column:student_uid;default:0"`
	CourseId              int64  `json:"course_id" gorm:"column:course_id;default:0"`
	AssistantUid          int64  `json:"assistant_uid" gorm:"column:assistant_uid;default:0"`
	OldAssistantUid       int64  `json:"old_assistant_uid" gorm:"column:old_assistant_uid;default:0"`
	OldIntention          int64  `json:"old_intention" gorm:"column:old_intention;default:0"`
	Intention             int64  `json:"intention" gorm:"column:intention;default:0"`
	PreContinue           int    `json:"pre_continue" gorm:"column:pre_continue;default:0"`
	OldPreContinue        string `json:"old_pre_continue" gorm:"column:old_pre_continue"`
	ExtData               string `json:"ext_data" gorm:"column:ext_data"`
	Status                int64  `json:"status" gorm:"column:status;default:0"`
	MachinePreContinue    int64  `json:"machine_pre_continue" gorm:"column:machine_pre_continue;default:0"`
	OldMachinePreContinue int64  `json:"old_machine_pre_continue" gorm:"column:old_machine_pre_continue;default:0"`
	CreateTime            int64  `json:"create_time" gorm:"column:create_time;default:0"`
	UpdateTime            int64  `json:"update_time" gorm:"column:update_time;default:0"`
}
type FieldsOfHistory struct {
	CourseId   int64 `json:"course_id"`
	StudentUid int64 `json:"student_uid"`
	Page       int   `json:"page"`
	Size       int   `json:"size"`
}

func (l *TblAssistantCourseStudent1History) TableName() string {
	tableName := "tblAssistantCourseStudent1History"
	return tableName
}
func filterByHistoryFields(cond FieldsOfHistory) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if cond.CourseId > 0 {
			db = db.Where("course_id = ?", cond.CourseId)
		}

		if cond.StudentUid > 0 {
			db = db.Where("student_uid = ?", cond.StudentUid)
		}

		if cond.Page > 0 && cond.Size > 0 {
			if cond.Page > 0 && cond.Size > 0 {
				normalPage := &models.NormalPage{
					No:   cond.Page,
					Size: cond.Size,
				}
				db.Scopes(models.NormalPaginate(normalPage))
			}
		}
		return db.Order("update_time desc")
	}
}

func (t *TblAssistantCourseStudent1History) GetCourseStudentList(ctx *gin.Context, cond FieldsOfHistory) (remarks []TblAssistantCourseStudent1History, err error) {
	conn := helpers.MysqlClientFuDao.Table(t.TableName()).WithContext(ctx)
	result := conn.Scopes(filterByHistoryFields(cond)).Find(&remarks)
	err = result.Error
	if err != nil {
		return remarks, components.ErrorDbSelect.WrapPrintf(err, "TblAssistantCourseStudent1History.GetCourseStudentList courseId=%v", cond.CourseId, cond.StudentUid)
	}

	return remarks, nil
}

func (m *TblAssistantCourseStudent1History) GetCntByCond(ctx *gin.Context, conds map[string]interface{}) (int64, error) {
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName())
	var total int64
	err := db.Where(conds).Count(&total).Error
	if err != nil {
		return 0, err
	}
	return total, err
}
