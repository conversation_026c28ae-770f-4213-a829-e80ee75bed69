package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SailorScoreOperateLog  老师水手分操作表
type SailorScoreOperateLog struct {
	ID                    int64   `gorm:"column:id" json:"id"`                                         //  自增id
	StaffUid              int64   `gorm:"column:staff_uid" json:"staff_uid"`                           //  真人uid
	TeachingQualification float64 `gorm:"column:teaching_qualification" json:"teaching_qualification"` //  教资分
	WorkingAge            float64 `gorm:"column:working_age" json:"working_age"`                       //  司龄分
	Qualification         float64 `gorm:"column:qualification" json:"qualification"`                   //  学历分
	UserPraise            float64 `gorm:"column:user_praise" json:"user_praise"`                       //  用户表扬分
	Praise                float64 `gorm:"column:praise" json:"praise"`                                 //  评优分
	Scale                 float64 `gorm:"column:scale" json:"scale"`                                   //  等级分
	Other                 float64 `gorm:"column:other" json:"other"`                                   //  其他分
	CostLevel             string  `gorm:"column:cost_level" json:"cost_level"`                         //  辅导费等级
	Satisfaction          float64 `gorm:"column:satisfaction" json:"satisfaction"`                     //  用户满意度
	Punishment            float64 `gorm:"column:punishment" json:"punishment"`                         //  品质惩处
	Performance           float64 `gorm:"column:performance" json:"performance"`                       //  业绩达成
	Exam                  float64 `gorm:"column:exam" json:"exam"`                                     //  功底考试
	DataType              string  `gorm:"column:data_type" json:"data_type"`                           //  数据类型
	OperateTime           int64   `gorm:"column:operate_time" json:"operate_time"`                     //  操作时间
	OperateUid            int64   `gorm:"column:operate_uid" json:"operate_uid"`                       //  操作人uid
	OperateName           string  `gorm:"column:operate_name" json:"operate_name"`                     //  操作人姓名
}

func (s *SailorScoreOperateLog) TableName() string {
	return "tblSailorScoreOperateLog"
}

func (s *SailorScoreOperateLog) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (s *SailorScoreOperateLog) Tx(ctx *gin.Context) *gorm.DB {
	return s.SchemaClient().WithContext(ctx).Begin()
}

func (s *SailorScoreOperateLog) BatchInsertWithTx(ctx *gin.Context, data []SailorScoreOperateLog, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		db := tx.WithContext(ctx).Table(s.TableName())
		err = db.CreateInBatches(data, 100).Error
		return
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(s.TableName())
	err = db.CreateInBatches(data, 100).Error
	return
}

func (s *SailorScoreOperateLog) GetByStaffUid(ctx *gin.Context, staffUid int64) (logList []SailorScoreOperateLog, err error) {
	conditions := map[string]interface{}{
		"staff_uid": staffUid,
	}
	err = s.SchemaClient().WithContext(ctx).Table(s.TableName()).Where(conditions).Order("id desc").Find(&logList).Error
	if err != nil {
		zlog.Warnf(ctx, "GetScoreLogByStaffUid fail, staffUid:%d, err:%+v", staffUid, err)
	}
	return
}
