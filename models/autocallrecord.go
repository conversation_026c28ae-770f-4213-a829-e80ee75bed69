package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	AutoCallSucceedWaiting  = 0 //等待处理中
	AutoCallSucceedGet      = 1 //推送前获取成功, 准备推送
	AutoCallSucceedSend     = 2 //推送给三方成功，三方处理失败
	AutoCallSucceedCalling  = 3 //推送给三方成功，三方处理成功
	AutoCallSucceedCallback = 4 //推送给三方成功，三方回传成功
)

type AutoCallRecord struct {
	ID           int64  `gorm:"column:id" json:"id"`                       //  主键id
	UniqueId     int64  `gorm:"column:unique_id" json:"unique_id"`         //  和第三方交互唯一id
	CourseId     int64  `gorm:"column:course_id" json:"course_id"`         //  课程id
	LessonId     int64  `gorm:"column:lesson_id" json:"lesson_id"`         //  章节id
	Origin       int64  `gorm:"column:origin" json:"origin"`               //  来源
	AssistantUid int64  `gorm:"column:assistant_uid" json:"assistant_uid"` //  呼叫班主任uid
	StudentUid   int64  `gorm:"column:student_uid" json:"student_uid"`     //  学生uid
	ToPhone      string `gorm:"column:to_phone" json:"to_phone"`           //  呼叫手机号
	KmsToPhone   string `gorm:"column:kms_to_phone" json:"kms_to_phone"`   //  被叫方的电话
	HashToPhone  string `gorm:"column:hash_to_phone" json:"hash_to_phone"` //  被叫方的电话
	Succeed      int64  `gorm:"column:succeed" json:"succeed"`             //  是否成功传给三方
	StartTime    int64  `gorm:"column:start_time" json:"start_time"`       //  开始接听时间
	StopTime     int64  `gorm:"column:stop_time" json:"stop_time"`         //  结束接听时间
	Duration     int64  `gorm:"column:duration" json:"duration"`           //  通话时长
	Message      int64  `gorm:"column:message" json:"message"`             //  异步回执结果
	State        int64  `gorm:"column:state" json:"state"`                 //  通话回传结果
	Callback     string `gorm:"column:callback" json:"callback"`           //  回传数据
	RecordFile   string `gorm:"column:record_file" json:"record_file"`     //  录音文件
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`     //  创建时间
	UpdateTime   int64  `gorm:"column:update_time" json:"update_time"`     //  更新时间
	Deleted      int64  `gorm:"column:deleted" json:"deleted"`             //  是否删除
}

func (a *AutoCallRecord) TableName() string {
	return "tblAutoCallRecord"
}

func (a *AutoCallRecord) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (a *AutoCallRecord) Tx(ctx *gin.Context) *gorm.DB {
	return a.SchemaClient().WithContext(ctx).Begin()
}

// GetRecordByUniqueId 根据唯一ID查询外呼记录
func (a *AutoCallRecord) GetRecordByUniqueId(ctx *gin.Context, uniqueId int64) (record *AutoCallRecord, err error) {
	if uniqueId == 0 {
		return
	}
	conditions := map[string]interface{}{
		"unique_id": uniqueId,
	}
	record = &AutoCallRecord{}
	err = a.SchemaClient().WithContext(ctx).Table(a.TableName()).Where(conditions).Find(record).Error
	if err != nil {
		zlog.Warnf(ctx, "GetRecordByUniqueId failed, uniqueId:%d, err:%+v", uniqueId, err)
	}
	return
}

func getPreStatus(succeed int64) int64 {
	switch succeed {
	case AutoCallSucceedGet:
		return AutoCallSucceedWaiting
	case AutoCallSucceedSend, AutoCallSucceedCalling, AutoCallSucceedWaiting:
		return AutoCallSucceedGet
	case AutoCallSucceedCallback:
		return AutoCallSucceedCalling
	}
	return -1
}

func (a *AutoCallRecord) UpdateRecordSucceed(ctx *gin.Context, id, succeed int64) (affected int64, err error) {
	conditions := map[string]interface{}{
		"id":      id,
		"succeed": getPreStatus(succeed),
	}

	params := map[string]interface{}{
		"succeed":     succeed,
		"update_time": time.Now().Unix(),
	}

	db := a.SchemaClient().WithContext(ctx).Table(a.TableName()).Where(conditions).Updates(params)
	if err = db.Error; err != nil {
		zlog.Warnf(ctx, "UpdateRecordSucceed failed, id:%d, err:%+v", id, err)
		return 0, err
	}

	return db.RowsAffected, nil
}
