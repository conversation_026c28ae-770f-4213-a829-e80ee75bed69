package models

import (
	"assistantdeskgo/helpers"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

var PressureTaskObj *PressureTask

const (
	PressureTaskTypeCreateOrder   = 1
	PressureTaskTypeWxStudentList = 2
	PressureTaskTypeInClass       = 3

	PressureTaskStatusCreate  = 0
	PressureTaskStatusProcess = 1
	PressureTaskStatusFailed  = 2
	PressureTaskStatusSuccess = 3
)

var SupportTaskType = []int64{PressureTaskTypeCreateOrder, PressureTaskTypeWxStudentList, PressureTaskTypeInClass}

type PressureTask struct {
	Id         int64  `json:"id" gorm:"id"`
	TaskType   int64  `json:"taskType" gorm:"task_type"`
	Status     int64  `json:"status" gorm:"status"`
	FailReason string `json:"failReason" gorm:"fail_reason"`
	Operator   string `json:"operator" gorm:"operator"`
	CreateTime int64  `json:"createTime" gorm:"createTime"`
	UpdateTime int64  `json:"updateTime" gorm:"updateTime"`
	DwnUrl     string `json:"dwn_url" gorm:"dwn_url"`
	TaskParam  string `json:"taskParam" gorm:"task_param"`
}

func (p *PressureTask) TableName() string {
	return "tblPressureTask"
}

func (p *PressureTask) CountByStatus(ctx *gin.Context, status int64, taskType int64) (count int64, err error) {
	if taskType == 0 {
		return count, errors.New("参数错误")
	}
	err = helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).
		Where("status = ?", status).Where("task_type = ?", taskType).Count(&count).Error
	if err != nil {
		zlog.Warnf(ctx, "PressureTask_CountByStatus_error:%s", err.Error())
	}
	return
}

func (p *PressureTask) Create(ctx *gin.Context) error {
	if p.TaskType == 0 || p.Operator == "" {
		return errors.New("参数错误")
	}
	nowTime := time.Now().Unix()
	p.CreateTime = nowTime
	p.UpdateTime = nowTime
	err := helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).Create(p).Error
	return err
}

func (p *PressureTask) GetByTaskId(ctx *gin.Context) (task PressureTask, err error) {
	if p.Id == 0 || p.TaskType == 0 {
		return task, errors.New("参数错误")
	}
	err = helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).
		Where("id = ?", p.Id).
		Where("task_type = ?", p.TaskType).
		Where("status = ?", p.Status).
		Find(&task).Error
	return
}

func (p *PressureTask) GetLastRecord(ctx *gin.Context) (task PressureTask, err error) {
	err = helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).
		Where("task_type = ?", p.TaskType).
		Where("status = ?", p.Status).
		Order("id desc").
		First(&task).Error
	return
}

func (p *PressureTask) Process(ctx *gin.Context, id int64) error {
	updateColumn := map[string]interface{}{
		"status": PressureTaskStatusProcess,
	}
	err := helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).
		Where("id = ?", id).
		Updates(updateColumn).Error
	return err
}

func (p *PressureTask) Success(ctx *gin.Context, id int64, dwnUrl string) error {
	updateColumn := map[string]interface{}{
		"dwn_url": dwnUrl,
		"status":  PressureTaskStatusSuccess,
	}
	err := helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).
		Where("id = ?", id).
		Updates(updateColumn).Error
	return err
}

func (p *PressureTask) Fail(ctx *gin.Context, id int64, failReason string) error {
	updateColumn := map[string]interface{}{
		"fail_reason": failReason,
		"status":      PressureTaskStatusFailed,
	}
	err := helpers.MysqlClientDuxuesc.WithContext(ctx).Table(p.TableName()).
		Where("id = ?", id).
		Updates(updateColumn).Error
	return err
}
