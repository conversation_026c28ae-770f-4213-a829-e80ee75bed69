package models

import (
	"assistantdeskgo/helpers"
	"errors"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
)

const (
	oplogModNum = 20
)

type TblPublicSeaOpLog struct {
	Id               int64  `gorm:"primary_key;column:id" json:"id"`
	CourseId         int64  `gorm:"column:course_id" json:"course_id"`
	StudentUid       int64  `gorm:"column:student_uid" json:"student_uid"`
	DeviceUid        int64  `gorm:"column:device_uid" json:"device_uid"`
	ClueId           string `gorm:"column:clue_id" json:"clue_id"`
	ManualIntention  int64  `gorm:"column:manual_intention" json:"manual_intention"`
	ManualCallStatus int64  `gorm:"column:manual_call_status" json:"manual_call_status"`
	ManualRemark     string `gorm:"column:manual_remark" json:"manual_remark"`
	LastAccessTime   int64  `gorm:"column:last_access_time" json:"last_access_time"`
	LastCallTime     int64  `gorm:"column:last_call_time" json:"last_call_time"`
	CreateTime       int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime       int64  `gorm:"column:update_time" json:"updateTime"`
}
type Clue struct {
	CourseId   int64
	StudentUid int64
	DeviceUid  int64
}

func (c *TblPublicSeaOpLog) TableName(deviceUid int64) string {
	return "tblPublicSeaOpLog" + strconv.FormatInt(deviceUid%oplogModNum, 10)
}

func (c *TblPublicSeaOpLog) Insert(ctx *gin.Context, publicseaoplog TblPublicSeaOpLog) error {
	if publicseaoplog.CourseId <= 0 || publicseaoplog.StudentUid <= 0 || publicseaoplog.DeviceUid < 0 {
		return errors.New("参数错误")
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(publicseaoplog.DeviceUid)).Create(&publicseaoplog).Error
	return err
}

func (c *TblPublicSeaOpLog) UpdateByCondition(ctx *gin.Context, conds, data map[string]interface{}) error {
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(c.DeviceUid))
	return db.Where(conds).Updates(data).Error
}

func (c *TblPublicSeaOpLog) GetListByConds(ctx *gin.Context, conditions []Clue) ([]*TblPublicSeaOpLog, error) {
	data := make([]*TblPublicSeaOpLog, 0)
	if len(conditions) == 0 {
		return data, errors.New("参数错误")
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(c.DeviceUid))
	if len(conditions) > 0 {
		// 构造查询条件
		var queries []string
		var args []interface{}
		for _, condition := range conditions {
			queries = append(queries, "(device_uid = ? AND student_uid = ? AND  course_id= ?)")
			args = append(args, condition.DeviceUid, condition.StudentUid, condition.CourseId)
		}

		// 拼接查询语句
		query := strings.Join(queries, " OR ")
		err := db.Where(query, args...).Find(&data).Error
		if err != nil {
			zlog.Warnf(ctx, "TblPublicSeaOpLog GetListByCond err : %v", err)
		}
		return data, err
	}
	return data, nil
}

func (c *TblPublicSeaOpLog) GetListByByCond(ctx *gin.Context, conditions map[string]interface{}, offset int, limit int, order string) ([]*TblPublicSeaOpLog, error) {
	data := make([]*TblPublicSeaOpLog, 0)

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(c.DeviceUid))
	db = db.Limit(limit).Offset(offset)
	if len(order) > 0 {
		db.Order(order)
	}
	err := db.Where(conditions).Find(&data).Error
	if err != nil {
		zlog.Warnf(ctx, "TblPublicSeaOpLog GetListByCond err : %v", err)
	}
	return data, err
}

func (c *TblPublicSeaOpLog) GetListFirstByCond(ctx *gin.Context, cond map[string]interface{}, offset int, limit int, order string) ([]*TblPublicSeaOpLog, error) {
	var data []*TblPublicSeaOpLog
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(c.DeviceUid))
	db = db.Limit(limit).Offset(offset)
	if len(order) > 0 {
		db.Order(order)
	}
	err := db.Where(cond).First(&data).Error
	if err != nil {
		zlog.Warnf(ctx, "TblPublicSeaOpLog GetListByCond err : %v", err)
	}
	return data, err
}

func (c *TblPublicSeaOpLog) GetListByAccessTime(ctx *gin.Context, uid int64, query string) ([]*TblPublicSeaOpLog, error) {
	data := make([]*TblPublicSeaOpLog, 0)

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(c.TableName(uid))
	err := db.Where(query).Find(&data).Error
	if err != nil {
		zlog.Warnf(ctx, "TblPublicSeaOpLog GetListByAccessTime err : %v", err)
	}
	return data, err
}
