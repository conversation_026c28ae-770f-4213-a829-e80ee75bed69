package models

var (
	SailorScoreRef                = &SailorScore{}
	SailorScoreOperateLogRef      = &SailorScoreOperateLog{}
	ExcelTaskRef                  = &ExcelTask{}
	AutoCallRecordRef             = &AutoCallRecord{}
	TouchConfigRef                = &TouchConf{}
	MessageRef                    = &Message{}
	MessageGroupRef               = &MessageGroup{}
	MessageGroupFolderRef         = &MessageGroupFolder{}
	AiCallRecordTaskRef           = &AiCallRecordTask{}
	AiCallRecordRef               = &AiCallRecord{}
	AssistantCallRecordRef        = &AssistantCallRecord{}
	TblNewLeadDingDingNoticeRef   = &TblNewLeadDingDingNotice{}
	WxCallRecordRef               = &WxCallRecord{}
	LessonStudentRef              = &LessonStudent{}
	LessonAccompanyingDetailRef   = &LessonAccompanyingDetail{}
	SatisfactionCollectRef        = &SatisfactionCollect{}
	ArkTemplateRef                = &ArkTemplate{}
	FeedbackPointTargetExplainRef = &FeedbackPointTargetExplain{}
	StuInterviewRef               = &StudentInterview{}
)
