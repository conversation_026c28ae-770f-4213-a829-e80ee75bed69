package chatword

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type FeedBack struct {
	Id                 int64 `gorm:"primary_key;column:id" json:"id"`                         //  逐渐
	TalkId             int64 `gorm:"column:talk_id" json:"talk_id"`                           //  话术id
	Collect            int64 `gorm:"column:collect" json:"collect"`                           //  收藏
	Like               int64 `gorm:"column:like" json:"like"`                                 //  喜欢
	SendCount          int64 `gorm:"column:send_count" json:"send_count"`                     //  发送数量
	EditSendCount      int64 `gorm:"column:edit_send_count" json:"edit_send_count"`           //  编辑后发送数量
	TransferGroupCount int64 `gorm:"column:transfer_group_count" json:"transfer_group_count"` //  转存消息组消息组
	PersonUid          int64 `gorm:"column:person_uid" json:"person_uid"`                     //  真人id
	CreateTime         int64 `gorm:"column:create_time" json:"create_time"`                   //  创建时间
	UpdateTime         int64 `gorm:"column:update_time" json:"update_time"`                   //  更新时间
	Deleted            int64 `gorm:"column:deleted" json:"deleted"`                           //  是否删除
}

func (a *FeedBack) TableName() string {
	return "tblChatWordFeedBack"
}

func (a *FeedBack) GetByTalkIdAndPersonUid(ctx *gin.Context, talkId int64, personUid int64) (list FeedBack, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Where("talk_id = ?", talkId).
		Where("person_uid = ?", personUid).
		Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByTalkIdAndPersonUid fail,err=%v", err)
	}
	return
}

func (a *FeedBack) ListByTalkIdAndPersonUid(ctx *gin.Context, talkId []int64, personUid int64) (list []FeedBack, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Where("talk_id in ?", talkId).
		Where("person_uid = ?", personUid).
		Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByTalkIdAndPersonUid fail,err=%v", err)
	}
	return
}

func (a *FeedBack) ListByTalkIdList(ctx *gin.Context, talkId []int64) (list []FeedBack, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Where("talk_id in ?", talkId).
		Where("deleted = ?", DeletedNo).
		Select("person_uid").
		Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByTalkIdAndPersonUid fail,err=%v", err)
	}
	return
}

func (a *FeedBack) Save(ctx *gin.Context, feedback FeedBack) (err error) {
	if feedback.Id == 0 {
		//新增
		err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
			Create(&feedback).Error
	} else {
		err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where("id=?", feedback.Id).
			Updates(utils.StructToMap(&feedback, "json")).Error
		if err != nil {
			zlog.Warnf(ctx, "Save fail,err=%v", err)
		}
	}
	return
}
