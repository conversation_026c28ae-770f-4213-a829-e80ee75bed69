package chatword

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Category struct {
	Id         int64  `gorm:"primary_key;column:id" json:"id"`      //  逐渐
	Category   string `gorm:"column:category" json:"category"`      //  分类
	PersonUid  int64  `gorm:"column:person_uid" json:"personUid"`   //  真人id
	CreateTime int64  `gorm:"column:create_time" json:"createTime"` //  创建时间
	UpdateTime int64  `gorm:"column:update_time" json:"updateTime"` //  更新时间
	Deleted    int64  `gorm:"column:deleted" json:"deleted"`        //  是否删除
}

func (a *Category) TableName() string {
	return "tblChatWordCategory"
}

func (a *Category) ListAll(ctx *gin.Context) (list []Category, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAll fail,err=%v", err)
	}
	return
}

func (a *Category) GetById(ctx *gin.Context, id int64) (list Category, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where("id = ?", id).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAll fail,err=%v", err)
	}
	return
}

func (a *Category) ListByIdList(ctx *gin.Context, id []int64) (list []Category, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where("id in ?", id).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAll fail,err=%v", err)
	}
	return
}

func (a *Category) Insert(ctx *gin.Context, category *Category) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Create(category).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert fail,data=%+v,err=%v", category, err)
	}
	return
}

func (a *Category) Update(ctx *gin.Context, category Category) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Updates(category).Error
	if err != nil {
		zlog.Warnf(ctx, "Update fail,data=%+v,err=%v", category, err)
	}
	return
}
