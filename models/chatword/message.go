package chatword

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Message struct {
	Id           int64  `gorm:"primary_key;column:id" json:"id"`           //  逐渐
	TalkId       int64  `gorm:"column:talk_id" json:"talk_id"`             //  分类
	MessageType  int64  `gorm:"column:message_type" json:"message_type"`   //  分类
	Content      string `gorm:"column:content" json:"content"`             //  分类
	ComposeId    int64  `gorm:"column:compose_id" json:"composeId"`        //  组合ID
	Order        int64  `gorm:"column:order" json:"order"`                 //  排序
	IntervalTime int64  `gorm:"column:interval_time" json:"interval_time"` //  延时
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`      //  创建时间
	UpdateTime   int64  `gorm:"column:update_time" json:"updateTime"`      //  更新时间
	Deleted      int64  `gorm:"column:deleted" json:"deleted"`             //  是否删除
}

func (a *Message) TableName() string {
	return "tblChatWordMessage"
}

func (a *Message) BatchInsert(ctx *gin.Context, messageList []Message, tx *gorm.DB) error {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	err := tx.WithContext(ctx).Table(a.TableName()).CreateInBatches(messageList, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "BatchInsert fail,data=%+v,err=%v", messageList, err)
	}
	return err
}

func (a *Message) BatchUpdates(ctx *gin.Context, messageList []Message, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	for index := range messageList {
		err = tx.WithContext(ctx).Table(a.TableName()).Updates(messageList[index]).Error
		if err != nil {
			zlog.Warnf(ctx, "BatchInsert fail,data=%+v,err=%v", messageList, err)
		}

	}
	return
}

func (a *Message) ListByTalkId(ctx *gin.Context, talkId []int64) (list []Message, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("talk_id in ?", talkId).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTalkId fail,err=%v", err)
	}
	return
}

func (a *Message) ListByTalkIdResource(ctx *gin.Context, talkId []int64) (list []Message, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Where("talk_id in ?", talkId).
		Where("deleted = ?", DeletedNo).
		Where("message_type in ?", []int64{defines.MessageTypePicture, defines.MessageTypeFile, defines.MessageTypeVoice, defines.MessageTypeVideo}).
		Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTalkId fail,err=%v", err)
	}
	return
}
