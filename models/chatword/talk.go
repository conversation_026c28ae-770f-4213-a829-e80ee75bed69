package chatword

import (
	"assistantdeskgo/helpers"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Talk struct {
	Id                 int64  `gorm:"primary_key;column:id" json:"id"`                         //  逐渐
	CategoryId         int64  `gorm:"column:category_id" json:"category_id"`                   //  分类
	Hide               int64  `gorm:"column:hide" json:"hide"`                                 //  分类
	Keyword            string `gorm:"column:keyword" json:"keyword"`                           //  分类
	GroupId            int64  `gorm:"column:group_id" json:"group_id"`                         //  组织架构ID
	GroupIdList        string `gorm:"column:group_id_list" json:"group_id_list"`               //  组织架构ID
	Title              string `gorm:"column:title" json:"title"`                               //  标题
	Content            string `gorm:"column:content" json:"content"`                           //  内容
	LikeCount          int64  `gorm:"column:like_count" json:"like_count"`                     //  喜欢数量
	DislikeCount       int64  `gorm:"column:dis_like_count" json:"dis_like_count"`             //  不喜欢数量
	CollectCount       int64  `gorm:"column:collect_count" json:"collect_count"`               //  收藏数量
	SendCount          int64  `gorm:"column:send_count" json:"send_count"`                     //  发送数量
	EditSendCount      int64  `gorm:"column:edit_send_count" json:"edit_send_count"`           //  编辑发送数量
	TransferGroupCount int64  `gorm:"column:transfer_group_count" json:"transfer_group_count"` //  转存消息组消息组
	PersonUid          int64  `gorm:"column:person_uid" json:"person_uid"`                     //  真人id
	PersonName         string `gorm:"column:person_name" json:"person_name"`                   //  真人id
	UpdateUid          int64  `gorm:"column:update_uid" json:"update_uid"`                     //  真人id
	UpdateName         string `gorm:"column:update_name" json:"update_name"`                   //  真人id
	CreateTime         int64  `gorm:"column:create_time" json:"create_time"`                   //  创建时间
	UpdateTime         int64  `gorm:"column:update_time" json:"update_time"`                   //  更新时间
	Deleted            int64  `gorm:"column:deleted" json:"deleted"`                           //  是否删除
}

func (a *Talk) TableName() string {
	return "tblChatWordTalk"
}

func (a *Talk) Insert(ctx *gin.Context, talk *Talk, tx *gorm.DB) error {
	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}
	err := tx.WithContext(ctx).Table(a.TableName()).Create(talk).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert fail,data=%+v,err=%v", talk, err)
	}
	return err
}

func (a *Talk) GetById(ctx *gin.Context, id int64) (list Talk, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("id = ?", id).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAll fail,err=%v", err)
	}
	return
}

func (a *Talk) ListByIdList(ctx *gin.Context, idList []int64) (list []Talk, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("id in ?", idList).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAll fail,err=%v", err)
	}
	return
}

func (a *Talk) PageByGroupId(ctx *gin.Context, groupIdList []int64, categoryId int64, page, pageSize int) (list []Talk, total int64, err error) {
	offset := 0
	limit := 10
	if page > 0 && pageSize > 0 {
		offset = (page - 1) * pageSize
		limit = pageSize
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName())
	if len(groupIdList) > 0 {
		db = db.Where("group_id in ?", groupIdList)
	}
	if categoryId > 0 {
		db = db.Where("category_id = ?", categoryId)
	}
	db = db.Where("deleted = ?", DeletedNo)
	err = db.Count(&total).Error
	if err != nil {
		zlog.Warnf(ctx, "PageByGroupId get total fail,err=%v", err)
	}
	err = db.Select("id").Order("id desc").Offset(offset).Limit(limit).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "PageByGroupId list fail,err=%v", err)
	}

	return
}

func (a *Talk) Update(ctx *gin.Context, talk Talk, tx *gorm.DB) (err error) {

	if tx == nil {
		tx = helpers.MysqlClientFuDao
	}

	err = tx.WithContext(ctx).Table(a.TableName()).Where("id=?", talk.Id).Updates(utils.StructToMap(&talk, "json")).Error
	if err != nil {
		zlog.Warnf(ctx, "Update fail,err=%v", err)
	}
	return
}

func (a *Talk) ListAll(ctx *gin.Context) (list []Talk, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Where("deleted = ?", DeletedNo).Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAll fail,err=%v", err)
	}
	return
}
