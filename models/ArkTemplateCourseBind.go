package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var CourseBind = ArkTemplateCourseBind{}

type ArkTemplateCourseBind struct {
	Id          int64  `gorm:"column:id" json:"id"`
	TplId       int64  `gorm:"column:tpl_id" json:"tplId"`
	CourseId    int64  `gorm:"column:course_id" json:"courseId"`
	Status      int64  `gorm:"column:status" json:"status"`
	OperatorUid int64  `gorm:"column:operator_uid" json:"operatorUid"`
	Operator    string `gorm:"column:operator" json:"operator"`
	CreateTime  int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime  int64  `gorm:"column:update_time" json:"updateTime"`
}

func (acb *ArkTemplateCourseBind) TableName() string {
	return "tblArkTemplateCourseBind"
}

func (acb *ArkTemplateCourseBind) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (acb *ArkTemplateCourseBind) GetTplIdByCourseIds(ctx *gin.Context, courseIds []int64) (tplBind []ArkTemplateCourseBind, err error) {
	if len(courseIds) <= 0 {
		return
	}
	err = acb.SchemaClient().WithContext(ctx).Table(acb.TableName()).Where("course_id in ?", courseIds).Find(&tplBind).Error
	if err != nil {
		zlog.Warnf(ctx, "GetTplIdByCourseIds err:%s", err.Error())
	}
	return
}
