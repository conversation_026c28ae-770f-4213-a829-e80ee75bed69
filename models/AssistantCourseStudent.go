package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

var ACS AssistantCourseStudent

type AssistantCourseStudent struct {
	Id                 int64  `json:"id" gorm:"id"`
	StudentUid         int64  `json:"studentUid" gorm:"studentUid"`
	CourseId           int64  `json:"courseId" gorm:"courseId"`
	AssistantUid       int64  `json:"assistantUid" gorm:"assistantUid"`
	Intention          int64  `json:"intention" gorm:"intention"`
	PreContinue        int64  `json:"preContinue" gorm:"preContinue"`
	MachinePreContinue int64  `json:"machinePreContinue" gorm:"machinePreContinue"`
	Status             int64  `json:"status" gorm:"status"`
	CreateTime         int64  `json:"createTime" gorm:"createTime"`
	UpdateTime         int64  `json:"updateTime" gorm:"updateTime"`
	ExtData            string `json:"extData" gorm:"extData"`
}

type ExtData struct {
	ScRemark string `json:"scRemark"`
}

func (a *AssistantCourseStudent) TableName() string {
	return "tblAssistantCourseStudentV1"
}

func (a *AssistantCourseStudent) GetExtData() (ext ExtData) {
	err := jsoniter.Unmarshal([]byte(a.ExtData), &ext)
	if err != nil {
		return ext
	}
	return
}

func (a *AssistantCourseStudent) GetList(ctx *gin.Context, courseIds []int64, assistantUid int64, studentUids []int64) (list []AssistantCourseStudent, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
		Where("course_id in (?)", courseIds).
		Where("assistant_uid = ?", assistantUid).
		Where("student_uid in (?)", studentUids).Find(&list).Error
	return
}
