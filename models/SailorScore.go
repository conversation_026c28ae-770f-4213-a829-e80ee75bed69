package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SailorScore 老师水手分记录表
type SailorScore struct {
	ID                    int64   `gorm:"column:id" json:"id"`                                         //  自增id
	StaffUid              int64   `gorm:"column:staff_uid" json:"staff_uid"`                           //  真人uid
	TeachingQualification float64 `gorm:"column:teaching_qualification" json:"teaching_qualification"` //  教资分
	WorkingAge            float64 `gorm:"column:working_age" json:"working_age"`                       //  司龄分
	Qualification         float64 `gorm:"column:qualification" json:"qualification"`                   //  学历分
	UserPraise            float64 `gorm:"column:user_praise" json:"user_praise"`                       //  用户表扬分
	Praise                float64 `gorm:"column:praise" json:"praise"`                                 //  评优分
	Scale                 float64 `gorm:"column:scale" json:"scale"`                                   //  等级分
	Other                 float64 `gorm:"column:other" json:"other"`                                   //  其他分
	CostLevel             string  `gorm:"column:cost_level" json:"cost_level"`                         //  辅导费等级
	Satisfaction          float64 `gorm:"column:satisfaction" json:"satisfaction"`                     //  用户满意度
	Punishment            float64 `gorm:"column:punishment" json:"punishment"`                         //  品质惩处
	Performance           float64 `gorm:"column:performance" json:"performance"`                       //  业绩达成
	Exam                  float64 `gorm:"column:exam" json:"exam"`                                     //  功底考试
	DataType              string  `gorm:"column:data_type" json:"data_type"`                           //  数据类型
	CreateTime            int64   `gorm:"column:create_time" json:"create_time"`                       //  创建时间
	CreateUid             int64   `gorm:"column:create_uid" json:"create_uid"`                         //  创建人uid
	CreateName            string  `gorm:"column:create_name" json:"create_name"`                       //  创建者
	UpdateTime            int64   `gorm:"column:update_time" json:"update_time"`                       //  更新时间
	UpdateUid             int64   `gorm:"column:update_uid" json:"update_uid"`                         //  更新人uid
	UpdateName            string  `gorm:"column:update_name" json:"update_name"`                       //  更新者
}

func (s *SailorScore) TableName() string {
	return "tblSailorScore"
}

func (s *SailorScore) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (s *SailorScore) Tx(ctx *gin.Context) *gorm.DB {
	return s.SchemaClient().WithContext(ctx).Begin()
}

func (s *SailorScore) BatchInsertWithTx(ctx *gin.Context, data []SailorScore, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		db := tx.WithContext(ctx).Table(s.TableName())
		err = db.CreateInBatches(data, 100).Error
		return
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(s.TableName())
	err = db.CreateInBatches(data, 100).Error
	return
}

func (s *SailorScore) BatchUpdateWithTx(ctx *gin.Context, data []SailorScore, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		for _, sailorScore := range data {
			_err := tx.WithContext(ctx).Table(s.TableName()).Save(&sailorScore).Error
			if _err != nil {
				err = _err
				return
			}
		}
		return
	}

	for _, sailorScore := range data {
		_err := helpers.MysqlClientFuDao.WithContext(ctx).Table(s.TableName()).Save(&sailorScore).Error
		if _err != nil {
			err = _err
			return
		}
	}
	return
}

func (s *SailorScore) GetByStaffUids(ctx *gin.Context, staffUids []int64) (sailorScoreList []SailorScore, err error) {
	if len(staffUids) == 0 {
		return
	}
	conditions := map[string]interface{}{
		"staff_uid": staffUids,
	}
	err = s.SchemaClient().WithContext(ctx).Table(s.TableName()).Where(conditions).Find(&sailorScoreList).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByStaffUids fail, staffUids:%+v, err:%+v", staffUids, err)
	}
	return
}

// GetQualificationScore 资质分 = 教资分 + 司龄分 + 学历分
func (s *SailorScore) GetQualificationScore() float64 {
	return s.TeachingQualification + s.WorkingAge + s.Qualification
}

// GetContributeScore 贡献分 = 用户表扬分 + 评优分
func (s *SailorScore) GetContributeScore() float64 {
	return s.UserPraise + s.Praise
}

// GetQualityScore 品质分 = 用户满意度 + 品质惩处
func (s *SailorScore) GetQualityScore() float64 {
	return s.Satisfaction + s.Punishment
}

// GetPerformanceScore 业务分 = 业绩达成 + 功底考试
func (s *SailorScore) GetPerformanceScore() float64 {
	return s.Performance + s.Exam
}
