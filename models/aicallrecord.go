package models

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"encoding/json"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

const ()

type AiCallRecord struct {
	CallId       int64  `gorm:"primary_key;column:call_id" json:"call_id"` //  CallId
	SourceType   int64  `gorm:"column:source_type" json:"source_type"`     //  通话来源类型
	CallMode     int64  `gorm:"column:call_mode" json:"call_mode"`         //  外呼类型 9 帮帮盾
	StudentUid   int64  `gorm:"column:student_uid" json:"student_uid"`     //  学生uid
	DeviceUid    int64  `gorm:"column:device_uid" json:"device_uid"`       //  班主任uid
	PersonUid    int64  `gorm:"column:person_uid" json:"person_uid"`       //  呼叫班主任uid
	StartTime    int64  `gorm:"column:start_time" json:"start_time"`       //  开始接听时间
	StopTime     int64  `gorm:"column:stop_time" json:"stop_time"`         //  结束接听时间
	Duration     int64  `gorm:"column:duration" json:"duration"`           //  通话时长
	CallType     int64  `gorm:"column:call_type" json:"call_type"`         //  '通话类型 1-呼出 2-呼入
	CourseId     int64  `gorm:"column:course_id" json:"course_id"`         //  课程id
	RemindId     int64  `gorm:"remind_id" json:"remind_id"`                //  待办ID
	Status       int64  `gorm:"column:status" json:"status"`               //  状态
	Content      string `gorm:"column:content" json:"content"`             //  状态
	RecordFile   string `gorm:"column:record_file" json:"record_file"`     //  文件
	ResourceType string `gorm:"column:resource_type" json:"resource_type"` //  状态
	Abstract     string `gorm:"column:abstract" json:"abstract"`           //  摘要
	Tags         string `gorm:"column:tags" json:"tags"`                   //  标签
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`     //  创建时间
	UpdateTime   int64  `gorm:"column:update_time" json:"update_time"`     //  更新时间
	Deleted      int64  `gorm:"column:deleted" json:"deleted"`             //  是否删除
}

func (a *AiCallRecord) TableName(studentUid int64) string {
	return "tblAiCallRecord" + strconv.FormatInt(studentUid%20, 10)
}

func (a *AiCallRecord) Insert(ctx *gin.Context, callRecord AiCallRecord) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(callRecord.StudentUid)).Create(callRecord).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%+v,err:%+v", callRecord, err)
	}
	return
}

func (a *AiCallRecord) BatchInsert(ctx *gin.Context, callRecord []AiCallRecord, toUid int64) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(toUid)).CreateInBatches(callRecord, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%+v,err:%+v", callRecord, err)
	}
	return
}

func (a *AiCallRecord) PageByByCond(ctx *gin.Context, toUid int64, fromUid int64, callType int64, courseId int64, page int, pageSize int) (response []AiCallRecord, count int64, err error) {
	if toUid <= 0 {
		return
	}
	conditions := map[string]interface{}{
		"student_uid": toUid,
		"call_type":   callType,
		"deleted":     0,
	}
	if fromUid > 0 {
		conditions["device_uid"] = fromUid
	}
	if courseId > 0 {
		conditions["course_id"] = courseId
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName(toUid)).
		Where(conditions).
		Order("create_time desc").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListAiCallRecordByCond List failed, toUid:%d,fromUid=%d,courseId=%v err:%+v", toUid, fromUid, courseId, err)
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(toUid)).Where(conditions).Count(&count).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAiCallRecordByCond Count failed, toUid:%d,fromUid=%d,courseId=%v err:%+v", toUid, fromUid, courseId, err)
	}
	return
}

func (a *AiCallRecord) GetByCallId(ctx *gin.Context, callId int64, toUid int64) (response AiCallRecord, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(toUid)).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetAiTaskByCallId Get failed, callId:%s err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecord) ListByCallIdIn(ctx *gin.Context, callId []int64, toUid int64) (response []AiCallRecord, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(toUid)).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetAiTaskByCallId Get failed, callId:%s err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecord) UpdateStatus(ctx *gin.Context, callId []int64, status int64, toUid int64) (err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}

	updates := map[string]interface{}{
		"status":      status,
		"update_time": time.Now().Unix(),
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(toUid)).Where(conditions).Updates(updates).Error
	if err != nil {
		zlog.Warnf(ctx, "UpdateFeedBack  failed, task:%v err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecord) Updates(ctx *gin.Context, taskList []AiCallRecord, toUid int64) (err error) {

	tx := helpers.MysqlClientFuDao.Begin()
	for _, task := range taskList {
		err = tx.WithContext(ctx).Table(a.TableName(toUid)).Updates(task).Error
		if err != nil {
			tx.Rollback()
			return components.ErrorDbInsert.WrapPrintf(err, "Update tblAiCallRecord, err=%v", err)
		}
	}
	tx.Commit()
	if tx.Error != nil {
		zlog.Warnf(ctx, "提交事务失败, 详情: %s", tx.Error)
		return err
	}
	return nil
}

func (a *AiCallRecord) From(callRecord *touchmisgo.CallRecordInfo) AiCallRecord {
	now := time.Now().Unix()
	task := AiCallRecord{}
	task.CallId = callRecord.CallId
	task.SourceType = callRecord.SourceType
	task.CallMode = callRecord.CallMode
	task.DeviceUid = callRecord.FromUid
	task.StudentUid = callRecord.ToUid
	task.PersonUid = callRecord.PersonUid
	task.StartTime = callRecord.StartTime
	task.StopTime = callRecord.StopTime
	task.Duration = callRecord.Duration
	task.CallType = callRecord.CallType
	task.CourseId = callRecord.CourseId
	task.RecordFile = callRecord.RecordFile
	task.ResourceType = callRecord.ResourceType
	task.CreateTime = now
	task.UpdateTime = now
	return task
}

func (a *AiCallRecord) From2(callRecord AiCallRecordTask) AiCallRecord {
	now := time.Now().Unix()
	task := AiCallRecord{}
	callId, _ := strconv.ParseInt(callRecord.CallId, 10, 64)
	task.CallId = callId
	task.SourceType = callRecord.SourceType
	task.CallMode = callRecord.CallMode
	task.DeviceUid = callRecord.FromUid
	task.StudentUid = callRecord.ToUid
	task.PersonUid = callRecord.PersonUid
	task.Content = callRecord.Content
	task.Status = callRecord.Status
	task.StartTime = callRecord.StartTime
	task.StopTime = callRecord.StopTime
	task.Duration = callRecord.Duration
	task.CallType = callRecord.CallType
	task.CourseId = callRecord.CourseId
	task.RecordFile = callRecord.RecordFile
	task.ResourceType = callRecord.ResourseType
	task.Abstract = callRecord.Abstract
	task.Tags = callRecord.Tags
	task.CreateTime = callRecord.CreateTime
	task.UpdateTime = now
	task.RemindId = callRecord.RemindId
	return task
}

func (a *AiCallRecord) GetAbstractTags() []TagStruct {
	result := make([]TagStruct, 0)
	if len(a.Tags) <= 0 {
		return result
	}
	_ = json.Unmarshal([]byte(a.Tags), &result)
	return result
}

func (a *AiCallRecord) GetCommonAbstractTags() []aiturbo.AbstractTag {
	result := make([]aiturbo.AbstractTag, 0)
	if len(a.Tags) <= 0 {
		return result
	}
	oriTag := make([]TagStruct, 0)
	_ = json.Unmarshal([]byte(a.Tags), &oriTag)
	for _, tag := range oriTag {
		if tag.HideFlag == "1" {
			continue
		}
		tagKey := tag.Label
		tagInfo := tag.Value
		if len(tag.Label) <= 0 {
			tagKey = tag.TagKey
		}
		if len(tag.Value) <= 0 {
			tagInfo = tag.TagInfo
		}
		tmp := aiturbo.AbstractTag{
			TagKey:   tagKey,
			TagInfo:  tagInfo,
			HideFlag: tag.HideFlag,
		}
		result = append(result, tmp)
	}
	return result
}

func (a *AiCallRecord) ListByTimeAndStatus(ctx *gin.Context, startTime int64, endTime int64, status []int64, offset int64) (response []AiCallRecord, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(offset)).
		Where("create_time >= ? ", startTime).
		Where("create_time <= ? ", endTime).
		Where("status in ? ", status).
		Where("deleted = ? ", 0).
		Order("create_time asc").
		Limit(1000).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTimeAndStatus,startTime:%v,endTime:%v,status=%v err:%+v", startTime, endTime, status, err)
	}
	return
}
