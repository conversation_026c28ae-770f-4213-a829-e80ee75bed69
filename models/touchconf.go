package models

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TouchConf struct {
	ID         int64  `gorm:"column:id" json:"id"`
	Action     int64  `gorm:"column:action" json:"action"` //  见conf_act_map
	ConfName   string `gorm:"column:conf_name" json:"conf_name"`
	Source     int64  `gorm:"column:source" json:"source"`           //  业务线
	GradeIds   string `gorm:"column:grade_ids" json:"grade_ids"`     //  年级
	SubjectIds string `gorm:"column:subject_ids" json:"subject_ids"` //  学科
	SceneId    int64  `gorm:"column:scene_id" json:"scene_id"`       //  场景id 见conf_scene_map
	PriceTags  string `gorm:"column:price_tags" json:"price_tags"`   //  课程性质
	Status     int64  `gorm:"column:status" json:"status"`           //  0未启用 1已启用 2已删除
	ConfValue  string `gorm:"column:conf_value" json:"conf_value"`   //  模板与变量配置
	CreateTime int64  `gorm:"column:create_time" json:"create_time"`
	CreateUid  int64  `gorm:"column:create_uid" json:"create_uid"` //  创建人
	CreateName string `gorm:"column:create_name" json:"create_name"`
	UpdateTime int64  `gorm:"column:update_time" json:"update_time"`
	UpdateUid  int64  `gorm:"column:update_uid" json:"update_uid"` //  更新人
	UpdateName string `gorm:"column:update_name" json:"update_name"`
}

func (t *TouchConf) TableName() string {
	return "tblTouchConf"
}

func (t *TouchConf) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (t *TouchConf) Tx(ctx *gin.Context) *gorm.DB {
	return t.SchemaClient().WithContext(ctx).Begin()
}

func (t *TouchConf) GetGradeIds() (ret []string, err error) {
	ret = make([]string, 0)
	if len(t.GradeIds) == 0 {
		return ret, nil
	}

	err = json.Unmarshal([]byte(t.GradeIds), &ret)
	return ret, err
}

func (t *TouchConf) GetSubjectIds() (ret []string, err error) {
	ret = make([]string, 0)
	if len(t.SubjectIds) == 0 {
		return ret, nil
	}

	err = json.Unmarshal([]byte(t.SubjectIds), &ret)
	return ret, err
}

func (t *TouchConf) GetPriceTags() (ret []string, err error) {
	ret = make([]string, 0)
	if len(t.PriceTags) == 0 {
		return ret, nil
	}

	err = json.Unmarshal([]byte(t.PriceTags), &ret)
	return ret, err
}

func (t *TouchConf) GetConfigValue() (ret map[string][]string, err error) {
	ret = make(map[string][]string, 0)
	if len(t.ConfValue) == 0 {
		return ret, nil
	}

	err = json.Unmarshal([]byte(t.ConfValue), &ret)
	return ret, err
}

func (t *TouchConf) GetTouchConfigBySourceAndAction(ctx *gin.Context, action, source int64) (touchConfList []TouchConf, err error) {
	conditions := map[string]interface{}{
		"action": action,
		"source": source,
		"status": defines.TouchConfStatusEnable,
	}

	if err = t.SchemaClient().WithContext(ctx).Table(t.TableName()).Where(conditions).Order("id desc").Find(&touchConfList).Error; err != nil {
		zlog.Warnf(ctx, "GetTouchConfigBySourceAndAction failed, conditions:%+v, err:%+v", conditions, err)
	}

	return touchConfList, err
}
