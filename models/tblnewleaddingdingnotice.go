package models

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

const (
	DingDingNoticeTaskInit = iota
	DingDingNoticeTaskSendSuccess
	DingDingNoticeTaskSendFail
)

/*
CREATE TABLE `tblNewLeadDingDingNotice` (
`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
`business_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '老师资产UID',
`course_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '课程ID',
`person_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '老师真人uid',
`student_uids` text  COMMENT '一条消息里的学生UID',
`status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送状态，0初始|1成功|2失败',
`fail_message` varchar(1000) NOT NULL DEFAULT '' COMMENT '失败原因',
`window_time` int(5) unsigned NOT NULL DEFAULT '0' COMMENT '配置窗口时间',
`show_student_name_nums` int(2) unsigned NOT NULL DEFAULT '0' COMMENT '展示学生的最大数量',
`send_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发送时间戳',
`send_time_arr` varchar(200) NOT NULL DEFAULT '' COMMENT '发送的时间范围',
`content` varchar(1000) NOT NULL DEFAULT '' COMMENT '发送内容',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `idx_c_b_p` (`course_id`,`business_uid`,`person_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='【班主任】新例子提醒消息表';

*/

type TblNewLeadDingDingNotice struct {
	Id                  int64  `gorm:"primary_key;column:id" json:"id"`
	CourseId            int64  `gorm:"column:course_id" json:"course_id"`       // 课程id
	BusinessUid         int64  `gorm:"column:business_uid" json:"business_uid"` // 资产id
	PersonUid           int64  `gorm:"column:person_uid" json:"person_uid"`
	Status              int    `gorm:"column:status" json:"status"` // 提醒消息状态
	FailMessage         string `gorm:"column:fail_message" json:"fail_message"`
	StudentUids         string `gorm:"column:student_uids" json:"student_uids"`
	WindowTime          int    `gorm:"column:window_time" json:"window_time"`
	ShowStudentNameNums int    `gorm:"column:show_student_name_nums" json:"show_student_name_nums"`
	SendTimeArr         string `gorm:"column:send_time_arr" json:"send_time_arr"`
	SendTime            int64  `gorm:"column:send_time" json:"send_time"`
	Content             string `gorm:"column:content" json:"content"`
	CreateTime          int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime          int64  `gorm:"column:update_time" json:"updateTime"`
}

func (a *TblNewLeadDingDingNotice) TableName() string {
	return "tblNewLeadDingDingNotice"
}

func (a *TblNewLeadDingDingNotice) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (a *TblNewLeadDingDingNotice) GetUnSendMessage(ctx *gin.Context) ([]TblNewLeadDingDingNotice, error) {
	var res = make([]TblNewLeadDingDingNotice, 0)
	err := helpers.MysqlClientFuDao.Table(a.TableName()).WithContext(ctx).Where("status = ? and send_time < ? and send_time > ?", DingDingNoticeTaskInit, time.Now().Unix(), time.Now().Unix()-3600*1).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (a *TblNewLeadDingDingNotice) GetNoticeByCourseIdBusinessUid(ctx *gin.Context, courseId, kpUid int64) (*TblNewLeadDingDingNotice, error) {
	conditions := map[string]interface{}{
		"course_id":    courseId,
		"business_uid": kpUid,
		"status":       DingDingNoticeTaskInit,
	}
	var res = &TblNewLeadDingDingNotice{}
	err := helpers.MysqlClientFuDao.Table(a.TableName()).WithContext(ctx).Where(conditions).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (a *TblNewLeadDingDingNotice) GetNoticeById(ctx *gin.Context, id int64) (*TblNewLeadDingDingNotice, error) {
	conditions := map[string]interface{}{
		"id": id,
	}
	var res = &TblNewLeadDingDingNotice{}
	err := helpers.MysqlClientFuDao.Table(a.TableName()).WithContext(ctx).Where(conditions).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (a *TblNewLeadDingDingNotice) AddNotice(ctx *gin.Context, notice *TblNewLeadDingDingNotice) error {
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Create(notice).Error
	return err
}

func (a *TblNewLeadDingDingNotice) UpdateById(ctx *gin.Context, id int64, updates map[string]interface{}) error {
	conditions := map[string]interface{}{
		"id": id,
	}
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Updates(updates).Error
	return err
}
