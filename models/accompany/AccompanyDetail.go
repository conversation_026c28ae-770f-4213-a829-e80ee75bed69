package accompany

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
)

var ObjAccompanyDetail LessonAccompanyDetail

type LessonAccompanyDetail struct {
	Id         int64 `gorm:"primary_key;column:id" json:"id"`
	CourseId   int64 `gorm:"column:course_id" json:"courseId"`
	LessonId   int64 `gorm:"column:lesson_id" json:"lessonId"`
	StartTime  int64 `gorm:"column:start_time" json:"startTime"`
	EndTime    int64 `gorm:"column:end_time" json:"endTime"`
	Deleted    int64 `gorm:"column:deleted" json:"deleted"`
	UpdateTime int64 `gorm:"column:update_time" json:"updateTime"`
	CreateTime int64 `gorm:"column:create_time" json:"createTime"`
	BanxueId   int64 `gorm:"column:banxue_id" json:"banxueId"`
}

const (
	DeleteYes = 1
	DeleteNo  = 0
)

func (ad *LessonAccompanyDetail) TableName() string {
	return "tblLessonAccompanyingDetail"
}

func (ad *LessonAccompanyDetail) GetBanxueListByDateRange(ctx *gin.Context, startTime int64, endTime int64) (list []LessonAccompanyDetail, err error) {
	err = helpers.MysqlClientFuDao.Table(ad.TableName()).WithContext(ctx).
		Where("start_time > ?", startTime).
		Where("end_time < ?", endTime).
		Where("deleted = ?", DeleteNo).Find(&list).Error
	return
}
