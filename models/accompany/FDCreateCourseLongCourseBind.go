package accompany

import (
	"assistantdeskgo/helpers"
	"github.com/gin-gonic/gin"
)

var LongCourseBind FDCreateCourseLongCourseBind

type FDCreateCourseLongCourseBind struct {
	Id                  int64 `gorm:"primary_key;column:id" json:"id"`
	AssistantUid        int64 `gorm:"column:assistant_uid" json:"assistantUid"`
	SmallCourseId       int64 `gorm:"column:small_course_id" json:"smallCourseId"`
	SmallCourseLessonId int64 `gorm:"column:small_course_lesson_id" json:"smallCourseLessonId"`
	LongCourseId        int64 `gorm:"column:long_course_id" json:"longCourseId"`
	LongCourseLessonId  int64 `gorm:"column:long_course_lesson_id" json:"longCourseLessonId"`
	IsValid             int64 `gorm:"column:is_valid" json:"isValid"`
	IsDelete            int64 `gorm:"column:is_delete" json:"isDelete"`
	TaskId              int64 `gorm:"column:task_id" json:"taskId"`
	OpUid               int64 `gorm:"column:op_uid" json:"opUid"`
	CreateTime          int64 `gorm:"column:create_time" json:"createTime"`
	UpdateTime          int64 `gorm:"column:update_time" json:"updateTime"`
	LongContractId      int64 `gorm:"column:long_contract_id" json:"longContractId"`
	BanxueStartTime     int64 `gorm:"column: banxue_start_time" json:"banxueStartTime"`
	BanxueEndTime       int64 `gorm:"column: banxue_end_time" json:"banxueEndTime"`
}

const (
	HasValid = 1
)

func (lcb *FDCreateCourseLongCourseBind) TableName() string {
	return "tblFDCreateCourseLongCourseBind"
}

func (lcb *FDCreateCourseLongCourseBind) GetLessonIdByDateRange(ctx *gin.Context, lessonIds []int64, startTime int64, endTime int64) (list []FDCreateCourseLongCourseBind, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(lcb.TableName()).
		Where("long_course_lesson_id in ?", lessonIds).
		Where("banxue_start_time > ?", startTime).
		Where("banxue_start_time < ?", endTime).
		Where("is_valid = ?", HasValid).Find(&list).Error
	return
}
