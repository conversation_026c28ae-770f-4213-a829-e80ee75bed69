package models

import (
	"assistantdeskgo/api/aiturbo"
	"assistantdeskgo/components"
	"assistantdeskgo/helpers"
	"encoding/json"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
	"time"
)

const (
	STATUS_DEFAULT           = 0
	STATUS_CALL_AUDIO        = 1
	STATUS_CALL_AUDIO_FINISH = 2
	STATUS_TEXT_ABSTRACT     = 3
	STATUS_FINISH            = 4

	STATUS_EXPIRE = -1
)

type TagStruct struct {
	Label       string `json:"label"`
	Value       string `json:"value"`
	TagKey      string `json:"tagkey"`
	TagInfo     string `json:"taginfo"`
	TagValue    string `json:"tagvalue"`
	Credibility string `json:"credibility"`
	HideFlag    string `json:"hideflag"`
	Remark      string `json:"remark"`
}

type AiCallRecordTask struct {
	CallId        string `gorm:"primary_key;column:call_id" json:"call_id"`   //  CallId
	SourceType    int64  `gorm:"column:source_type" json:"source_type"`       //  通话来源类型
	CallMode      int64  `gorm:"column:call_mode" json:"call_mode"`           //  外呼类型 9 帮帮盾
	FromUid       int64  `gorm:"column:from_uid" json:"from_uid"`             //  主叫方的uid
	ToUid         int64  `gorm:"column:to_uid" json:"to_uid"`                 //  被叫方的uid
	DeviceUid     int64  `gorm:"column:device_uid" json:"device_uid"`         //  呼叫班主任uid
	PersonUid     int64  `gorm:"column:person_uid" json:"person_uid"`         //  呼叫班主任uid
	StartTime     int64  `gorm:"column:start_time" json:"start_time"`         //  开始接听时间
	StopTime      int64  `gorm:"column:stop_time" json:"stop_time"`           //  结束接听时间
	Duration      int64  `gorm:"column:duration" json:"duration"`             //  通话时长
	CallType      int64  `gorm:"column:call_type" json:"call_type"`           //  '通话类型 1-呼出 2-呼入
	CourseId      int64  `gorm:"column:course_id" json:"course_id"`           //  课程id
	RemindId      int64  `gorm:"remind_id" json:"remind_id"`                  //  待办ID
	Status        int64  `gorm:"column:status" json:"status"`                 //  状态
	Content       string `gorm:"column:content" json:"content"`               //  状态
	RecordFile    string `gorm:"column:record_file" json:"record_file"`       //  文件
	ResourseType  string `gorm:"column:resourse_type" json:"resourse_type"`   //  状态
	TransformTime int64  `gorm:"column:transform_time" json:"transform_time"` //  语音转文本时间
	AiCallTime    int64  `gorm:"column:ai_call_time" json:"ai_call_time"`     //  发起调用AI接口的时间
	AiResultTime  int64  `gorm:"column:ai_result_time" json:"ai_result_time"` //  获取AI结果的时间
	Abstract      string `gorm:"column:abstract" json:"abstract"`             //  摘要
	Tags          string `gorm:"column:tags" json:"tags"`                     //  标签
	Accurate      string `gorm:"column:accurate" json:"accurate"`             //  准
	Inaccurate    string `gorm:"column:inaccurate" json:"inaccurate"`         //  不准
	CreateTime    int64  `gorm:"column:create_time" json:"create_time"`       //  创建时间
	UpdateTime    int64  `gorm:"column:update_time" json:"update_time"`       //  更新时间
	Deleted       int64  `gorm:"column:deleted" json:"deleted"`               //  是否删除
}

func (a *AiCallRecordTask) TableName() string {
	return "tblAiCallRecordTask"
}

func (a *AiCallRecordTask) Insert(ctx *gin.Context, task AiCallRecordTask) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Create(task).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", task, err)
	}
	return
}

func (a *AiCallRecordTask) BatchInsert(ctx *gin.Context, task []AiCallRecordTask) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).CreateInBatches(task, 500).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", task, err)
	}
	return
}

func (a *AiCallRecordTask) PageByByCond(ctx *gin.Context, toUid int64, fromUid int64, callType int64, courseId int64, page int, pageSize int) (response []AiCallRecordTask, count int64, err error) {
	if toUid <= 0 {
		return
	}
	conditions := map[string]interface{}{
		"to_uid":    toUid,
		"call_type": callType,
		"deleted":   0,
	}
	if fromUid > 0 {
		conditions["from_uid"] = fromUid
	}
	if courseId > 0 {
		conditions["course_id"] = courseId
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName()).
		Where(conditions).
		Order("create_time desc").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListAiCallRecordTaskByCond List failed, toUid:%d,fromUid=%d,courseId=%v err:%+v", toUid, fromUid, courseId, err)
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Count(&count).Error
	if err != nil {
		zlog.Warnf(ctx, "ListAiCallRecordTaskByCond Count failed, toUid:%d,fromUid=%d,courseId=%v err:%+v", toUid, fromUid, courseId, err)
	}
	return
}

func (a AiCallRecordTask) ListByParam(ctx *gin.Context, studentUid, courseId, lastTime int64, limit int) (list []AiCallRecordTask, err error) {
	conditions := map[string]interface{}{
		"to_uid":    studentUid,
		"call_type": 1,
		"deleted":   0,
	}
	if courseId > 0 {
		conditions["course_id"] = courseId
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName()).
		Where(conditions).
		Where("create_time < ?", lastTime).
		Order("create_time desc").
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListAiCallRecordTaskByCond List failed, toUid:%d,lastTime=%d,courseId=%v err:%+v", studentUid, lastTime, courseId, err)
	}
	return
}

func (a *AiCallRecordTask) ListByCallIdList(ctx *gin.Context, callIdList []string) (response []AiCallRecordTask, err error) {
	conditions := map[string]interface{}{
		"call_id": callIdList,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetAiTaskByCallId Get failed, callId:%s err:%+v", callIdList, err)
	}
	return
}

func (a *AiCallRecordTask) GetByCallId(ctx *gin.Context, callId string) (response AiCallRecordTask, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetAiTaskByCallId Get failed, callId:%s err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecordTask) ListByCallIdIn(ctx *gin.Context, callId []string) (response []AiCallRecordTask, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetAiTaskByCallId Get failed, callId:%s err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecordTask) UpdateFeedBack(ctx *gin.Context, callId string, accurate string, inaccurate string) (err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}

	updates := map[string]interface{}{
		"accurate":    accurate,
		"inaccurate":  inaccurate,
		"update_time": time.Now().Unix(),
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Updates(updates).Error
	if err != nil {
		zlog.Warnf(ctx, "UpdateFeedBack  failed, task:%v err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecordTask) UpdateStatus(ctx *gin.Context, callId []string, status int64) (err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}

	updates := map[string]interface{}{
		"status":      status,
		"update_time": time.Now().Unix(),
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Updates(updates).Error
	if err != nil {
		zlog.Warnf(ctx, "UpdateFeedBack  failed, task:%v err:%+v", callId, err)
	}
	return
}

func (a *AiCallRecordTask) ListByTimeAndStatus(ctx *gin.Context, startTime int64, endTime int64, status []int64) (response []AiCallRecordTask, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
		Where("create_time >= ? ", startTime).
		Where("create_time <= ? ", endTime).
		Where("status in ? ", status).
		Where("deleted = ? ", 0).
		Order("create_time asc").
		Limit(1000).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTimeAndStatus,startTime:%v,endTime:%v,status=%v err:%+v", startTime, endTime, status, err)
	}
	return
}

func (a *AiCallRecordTask) ListByTimeAndStatusAndScanTime(ctx *gin.Context, startTime int64, endTime int64, status int64) (response []AiCallRecordTask, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
		Where("create_time >= ? ", startTime).
		Where("create_time <= ? ", endTime).
		Where("status = ?", status).
		Where("deleted = ? ", 0).
		Order("create_time asc").
		Limit(1000).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTimeAndStatus,startTime:%v,endTime:%v,status=%v err:%+v", startTime, endTime, status, err)
	}
	return
}

func (a *AiCallRecordTask) Updates(ctx *gin.Context, taskList []AiCallRecordTask) (err error) {

	tx := helpers.MysqlClientFuDao.Begin()
	for _, task := range taskList {
		err = tx.WithContext(ctx).Table(a.TableName()).Updates(task).Error
		if err != nil {
			tx.Rollback()
			return components.ErrorDbInsert.WrapPrintf(err, "Update tblAiCallRecordTask, err=%v", err)
		}
	}
	tx.Commit()
	if tx.Error != nil {
		zlog.Warnf(ctx, "提交事务失败, 详情: %s", tx.Error)
		return err
	}
	return nil
}

func (a *AiCallRecordTask) ListByTimeAndStatusDuration(ctx *gin.Context, startTime int64, status []int64, duration int64, sizes int) (response []AiCallRecordTask, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
		Where("create_time > ? ", startTime).
		Where("status in ? ", status).
		Where("deleted = ? ", 0).
		Where("duration > ? ", duration).
		Order("create_time asc").
		Limit(sizes).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTimeAndStatusDuration,startTime:%v,duration:%v,status=%v err:%+v", startTime, status, err)
	}
	return
}

func (a *AiCallRecordTask) From(callRecord AssistantCallRecord) AiCallRecordTask {
	now := time.Now().Unix()
	task := AiCallRecordTask{}
	task.CallId = utils.ToString(callRecord.CallId)
	task.SourceType = callRecord.SourceType
	task.CallMode = callRecord.CallMode
	task.FromUid = callRecord.FromUid
	task.ToUid = callRecord.ToUid
	task.DeviceUid = callRecord.DeviceUid
	task.PersonUid = callRecord.PersonUid
	task.StartTime = callRecord.StartTime
	task.StopTime = callRecord.StopTime
	task.Duration = callRecord.Duration
	task.CallType = callRecord.CallType
	task.CourseId = callRecord.CourseId
	task.RecordFile = callRecord.RecordFile
	task.ResourseType = callRecord.ResourseType
	task.CreateTime = now
	task.UpdateTime = now
	return task
}

func (a *AiCallRecordTask) From2(callRecord *touchmisgo.CallRecordInfo) AiCallRecordTask {
	now := time.Now().Unix()
	task := AiCallRecordTask{}
	task.CallId = utils.ToString(callRecord.CallId)
	task.SourceType = callRecord.SourceType
	task.CallMode = callRecord.CallMode
	task.FromUid = callRecord.FromUid
	task.ToUid = callRecord.ToUid
	task.DeviceUid = callRecord.DeviceUid
	task.PersonUid = callRecord.PersonUid
	task.StartTime = callRecord.StartTime
	task.StopTime = callRecord.StopTime
	task.Duration = callRecord.Duration
	task.CallType = callRecord.CallType
	task.CourseId = callRecord.CourseId
	task.RecordFile = callRecord.RecordFile
	task.ResourseType = callRecord.ResourceType
	task.CreateTime = now
	task.UpdateTime = now
	return task
}

func (a *AiCallRecordTask) GetAbstractTags() []TagStruct {
	result := make([]TagStruct, 0)
	if len(a.Tags) <= 0 {
		return result
	}
	_ = json.Unmarshal([]byte(a.Tags), &result)
	return result
}

func (a *AiCallRecordTask) GetCommonAbstractTags() []aiturbo.AbstractTag {
	result := make([]aiturbo.AbstractTag, 0)
	if len(a.Tags) <= 0 {
		return result
	}
	oriTag := make([]TagStruct, 0)
	_ = json.Unmarshal([]byte(a.Tags), &oriTag)
	for _, tag := range oriTag {
		if tag.HideFlag == "1" {
			continue
		}
		tagKey := tag.Label
		tagInfo := tag.Value
		if len(tag.Label) <= 0 {
			tagKey = tag.TagKey
		}
		if len(tag.Value) <= 0 {
			tagInfo = tag.TagInfo
		}
		tmp := aiturbo.AbstractTag{
			TagKey:   tagKey,
			TagInfo:  tagInfo,
			HideFlag: tag.HideFlag,
		}
		result = append(result, tmp)
	}
	return result
}

func (a *AiCallRecordTask) ListOrderByCreateTime(ctx *gin.Context, lastTime int64, size int64) (response []AiCallRecordTask, err error) {

	limit := int(size)
	if limit <= 0 {
		limit = 1000
	}

	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
		Where("create_time <= ? ", lastTime).
		Where("deleted = ? ", 0).
		Order("create_time desc").
		Limit(limit).
		Find(&response).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListOrderByCreateTime last=%v,err=%v", lastTime, err)
	}
	return
}
