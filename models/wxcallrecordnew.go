package models

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type WxCallRecordNew struct {
	Id           int64  `gorm:"primary_key;column:id" json:"id"`             //  主键ID
	CallId       int64  `gorm:"column:call_id" json:"call_id"`               //  CallId来自鲲鹏
	MsgType      int64  `gorm:"column:msg_type" json:"msg_type"`             //  通话类型16语音通话 17视频通话
	StudentUid   int64  `gorm:"column:student_uid" json:"student_uid"`       //  学员uid
	FromUid      int64  `gorm:"column:from_uid" json:"from_uid"`             //  主叫方的uid
	ToUid        int64  `gorm:"column:to_uid" json:"to_uid"`                 //  被叫方的uid
	PersonUid    int64  `gorm:"column:person_uid" json:"person_uid"`         //  真人uid
	DeviceUid    int64  `gorm:"column:device_uid" json:"device_uid"`         //  资产ID
	CourseId     int64  `gorm:"column:course_id" json:"course_id"`           //  课程ID
	StartTime    int64  `gorm:"column:start_time" json:"start_time"`         //  开始接听时间
	StopTime     int64  `gorm:"column:stop_time" json:"stop_time"`           //  结束接听时间
	Duration     int64  `gorm:"column:duration" json:"duration"`             //  通话时长
	FromRemoteId string `gorm:"column:from_remote_id" json:"from_remote_id"` //  呼叫方微信ID
	ToRemoteId   string `gorm:"column:to_remote_id" json:"to_remote_id"`     //  被呼叫方微信ID
	RecordFile   string `gorm:"column:record_file" json:"record_file"`       //  通话语音文件
	CallType     int64  `gorm:"column:call_type" json:"call_type"`           //  通话类型 1-呼出 2-呼入
	Content      string `gorm:"column:content" json:"content"`               //  语音转文本结果
	CallResult   int64  `gorm:"column:call_result" json:"call_result"`       //  通话状态 0初始化(异常) 1正常结束 2未接通 3拒接 4取消
	Status       int    `gorm:"column:status" json:"status"`                 //  状态，0默认；1语音转化中；2AI生成中；4完成
	RemindId     int64  `gorm:"remind_id" json:"remind_id"`                  //  待办ID
	ExtData      string `gorm:"ext_data" json:"ext_data"`                    //  扩展字段
	Deleted      int64  `gorm:"column:deleted" json:"deleted"`               //  是否删除
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`       //  创建时间
	UpdateTime   int64  `gorm:"column:update_time" json:"update_time"`       //  更新时间
}

func (a *WxCallRecordNew) TableName(studentUid int64) string {
	tableNum := studentUid % 32
	return fmt.Sprintf("%s%d", "tblWxCallRecord", tableNum)
}

func (a *WxCallRecordNew) GetByCallId(ctx *gin.Context, callId int64, studentUid int64) (response WxCallRecordNew, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(studentUid)).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByCallId List failed, callId=%d, err=%s", callId, err.Error())
	}

	return
}

func (a *WxCallRecordNew) ListByTimeRange(ctx *gin.Context, studentUid, assistantUid, startTime int64, limit int) (response []WxCallRecordNew, err error) {
	db := helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName(studentUid)).
		Where("deleted = 0 and student_uid = ? and create_time < ?", studentUid, startTime)
	if assistantUid > 0 {
		db.Where("device_uid = ?", assistantUid)
	}
	err = db.Limit(limit).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "ListByTimeRange failed, studentUid=%v, startTime = %d, limit=%d, err=%s", studentUid, startTime, limit, err.Error())
	}

	return
}

func (a *WxCallRecordNew) Insert(ctx *gin.Context, studentUid int64, wxCallRecord WxCallRecordNew) error {
	err := helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(studentUid)).Create(&wxCallRecord).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", wxCallRecord, err)
	}
	return err
}

func (a *WxCallRecordNew) Update(ctx *gin.Context, studentUid int64, wxCallRecord WxCallRecordNew) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(studentUid)).Where(a).Updates(wxCallRecord).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", wxCallRecord, err)
	}
	return
}
