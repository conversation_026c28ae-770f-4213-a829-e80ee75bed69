package models

import (
	"assistantdeskgo/helpers"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const StuAiMessageType1 = 1 //话术推荐
const StuAiMessageType2 = 2 //AI摘要-电话
const StuAiMessageType3 = 3 //AI摘要-企微

type StuAiMessage struct {
	Id           int64  `gorm:"primary_key;column:id" json:"id"`          //主键ID
	MsgType      int64  `gorm:"column:msg_type" json:"msgType"`           //消息类型，1、AI摘要；2、AI话术推荐
	MsgId        int64  `gorm:"msg_id" json:"msgId"`                      //消息实体ID
	StudentUid   int64  `gorm:"column:student_uid" json:"studentUid"`     //学员uid
	AssistantUid int64  `gorm:"column:assistant_uid" json:"assistantUid"` //辅导uid
	PersonUid    int64  `gorm:"column:person_uid" json:"personUid"`       //真人uid
	Title        string `gorm:"title" json:"title"`                       //标题
	Content      string `gorm:"column:content" json:"content"`            //消息内容
	Status       int    `gorm:"column:status" json:"status"`              //状态 0:未读，1:已读
	Deleted      int64  `gorm:"column:deleted" json:"deleted"`            //是否删除
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`     //创建时间
	UpdateTime   int64  `gorm:"column:update_time" json:"updateTime"`     //更新时间
}

func (a *StuAiMessage) TableName(studentUid int64) string {
	tableNum := studentUid % 32
	return fmt.Sprintf("%s%d", "tblStuAIMessage", tableNum)
}

func (a *StuAiMessage) Insert(ctx *gin.Context, studentUid int64) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(studentUid)).Create(&a).Error
	if err != nil {
		zlog.Warnf(ctx, "Insert failed, data=%v,err:%+v", a, err)
	}
	return
}

func (a *StuAiMessage) Update(ctx *gin.Context, studentUid int64, StuAiMessage StuAiMessage) (err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName(studentUid)).Where(a).Updates(StuAiMessage).Error
	if err != nil {
		zlog.Warnf(ctx, "Update failed, data=%v,err:%+v", StuAiMessage, err)
	}
	return
}

func (a *StuAiMessage) Count(ctx *gin.Context, studentUid int64) (count int64, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Model(a).
		Where(a).
		Table(a.TableName(studentUid)).
		Where("status = ?", 0).
		Count(&count).
		Error
	if err != nil {
		zlog.Warnf(ctx, "countStuAiMessage failed, studentUid:%d, err:%+v", studentUid, err)
	}
	return
}

func (a *StuAiMessage) List(ctx *gin.Context, studentUid int64, pageNum, pageSize int) (list []StuAiMessage, err error) {
	err = helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName(studentUid)).
		Model(a).
		Where(a).
		Order("id desc").
		Offset((pageNum - 1) * pageSize).
		Limit(pageSize).
		Find(&list).
		Error
	if err != nil {
		zlog.Warnf(ctx, "ListStuAiMessageByCond List failed, studentUid:%d, err:%+v", studentUid, err)
	}
	return
}
