package models

import (
	"assistantdeskgo/defines"
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strings"
)

const (
	AuthorityForGroup    = 1 // 团队
	AuthorityForPersonal = 0 // 个人

	BatchQueryLimit = 1000
)

type MessageGroup struct {
	ID                int64  `json:"id" gorm:"id"`                                   // 自增id
	Name              string `json:"name" gorm:"name"`                               // 消息组名称
	Desc              string `json:"desc" gorm:"desc"`                               // 消息组描述
	FolderId          int64  `json:"folder_id" gorm:"folder_id"`                     // 文件夹ID
	Authority         int64  `json:"authority" gorm:"authority"`                     // 0-私人（消息组使用）、1-团队（消息组使用）
	PermissionGroupId int64  `json:"permission_group_id" gorm:"permission_group_id"` // 团队范围、归属组织
	AvailableRange    string `json:"available_range" gorm:"available_range"`         // 可用范围枚举，student 学生维度，general: 通用配置
	IsDeleted         int64  `json:"is_deleted" gorm:"is_deleted"`                   // 是否删除：0-未删除、1-删除
	CreateTime        int64  `json:"create_time" gorm:"create_time"`                 // 创建时间
	CreateUid         int64  `json:"create_uid" gorm:"create_uid"`                   // 创建人uid
	CreateName        string `json:"create_name" gorm:"create_name"`                 // 创建者
	UpdateTime        int64  `json:"update_time" gorm:"update_time"`                 // 更新时间
	UpdateUid         int64  `json:"update_uid" gorm:"update_uid"`                   // 更新人uid
	UpdateName        string `json:"update_name" gorm:"update_name"`                 // 更新者
}

func (m *MessageGroup) TableName() string {
	return "tblMessageGroup"
}

func (m *MessageGroup) SchemaClient() *gorm.DB {
	return helpers.MysqlClientFuDao
}

func (m *MessageGroup) Tx(ctx *gin.Context) *gorm.DB {
	return m.SchemaClient().WithContext(ctx).Begin()
}

func (m *MessageGroup) BatchInsertWithTx(ctx *gin.Context, data []*MessageGroup, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		db := tx.WithContext(ctx).Table(m.TableName())
		err = db.CreateInBatches(data, 100).Error
		if err != nil {
			zlog.Warnf(ctx, "MessageGroup BatchInsertWithTx err, err:%+v", err)
		}
		return
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName())
	err = db.CreateInBatches(data, 100).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup BatchInsert err, err:%+v", err)
	}
	return
}

func (m *MessageGroup) BatchUpdateWithTx(ctx *gin.Context, data []*MessageGroupFolder, tx *gorm.DB) (err error) {
	if len(data) == 0 {
		return nil
	}
	if tx != nil {
		for _, messageGroup := range data {
			_err := tx.WithContext(ctx).Table(m.TableName()).Updates(messageGroup).Error
			if _err != nil {
				err = _err
				if err != nil {
					zlog.Warnf(ctx, "MessageGroup BatchUpdateWithTx err, data:%+v, err:%+v", messageGroup, err)
				}
				return
			}
		}
		return
	}

	for _, messageGroup := range data {
		_err := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName()).Updates(messageGroup).Error
		if _err != nil {
			err = _err
			if err != nil {
				zlog.Warnf(ctx, "MessageGroup BatchUpdate err, data:%+v, err:%+v", messageGroup, err)
			}
			return
		}
	}
	return
}

// GetById 删除或者不删除都可以查出来
func (m *MessageGroup) ListByNameLike(ctx *gin.Context, name string, groupIdList []int64) (groupList []MessageGroup, err error) {

	db := m.SchemaClient().WithContext(ctx).Table(m.TableName())
	db.Where("name like ?", "%"+name+"%")
	db.Where("authority = ?", defines.AuthorityForGroup)
	if len(groupIdList) > 0 {
		db.Where("permission_group_id in ?", groupIdList)
	}
	err = db.Find(&groupList).Error
	if err != nil {
		zlog.Warnf(ctx, "ListByNameLike GetById err, name:%+v, err:%+v", name, err)
	}
	return
}

func (m *MessageGroup) GetById(ctx *gin.Context, id int64) (groupList []MessageGroup, err error) {
	conditions := map[string]interface{}{
		"id": id,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&groupList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup GetById err, id:%+v, err:%+v", id, err)
	}
	return
}

func (m *MessageGroup) GetByIdAndTeam(ctx *gin.Context, id int64) (groupList []MessageGroup, err error) {
	conditions := map[string]interface{}{
		"id":         id,
		"authority":  defines.AuthorityForGroup,
		"is_deleted": 0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&groupList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup GetById err, id:%+v, err:%+v", id, err)
	}
	return
}

func (m *MessageGroup) GetByFolderIds(ctx *gin.Context, folderIds []int64) (messageGroup []MessageGroup, err error) {
	if len(folderIds) == 0 {
		return
	}
	conditions := map[string]interface{}{
		"folder_id":  folderIds,
		"is_deleted": 0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&messageGroup).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByFolderIds fail, folderIds:%+v, err:%+v", folderIds, err)
	}
	return
}

func (m *MessageGroup) UpdateById(ctx *gin.Context, id int64, values map[string]interface{}, tx *gorm.DB) (err error) {
	conditions := map[string]interface{}{
		"id": id,
	}
	err = tx.WithContext(ctx).Table(m.TableName()).Where(conditions).Updates(values).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup UpdateById err, id:%+v, err:%+v", id, err)
	}
	return
}

func (m *MessageGroup) UpdateByFolderId(ctx *gin.Context, folderId int64, values map[string]interface{}, tx *gorm.DB) (err error) {
	conditions := map[string]interface{}{
		"folder_id": folderId,
	}
	err = tx.WithContext(ctx).Table(m.TableName()).Where(conditions).Updates(values).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup UpdateById err, folderId:%+v, err:%+v", folderId, err)
	}
	return
}

func (m *MessageGroup) UpdateAvailableRange(ctx *gin.Context, fromAvailableRange, toAvailableRange string) (err error) {
	conditions := map[string]interface{}{
		"available_range": fromAvailableRange,
	}
	values := map[string]interface{}{
		"available_range": toAvailableRange,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Updates(values).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup UpdateAvailableRange err, fromAvailableRange:%+v, toAvailableRange:%+v, err:%+v", fromAvailableRange, toAvailableRange, err)
	}
	return
}

func (m *MessageGroup) List(ctx *gin.Context, uid int) (folderList []MessageGroupFolder, err error) {
	conditions := map[string]interface{}{
		"create_uid": uid,
		"is_deleted": 0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&folderList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup List err, uid:%+v, err:%+v", uid, err)
	}
	return
}

func (m *MessageGroup) ListByCreateUid(ctx *gin.Context, createUid int) (folderList []MessageGroup, err error) {

	conditions := map[string]interface{}{
		"create_uid": createUid,
		"is_deleted": 0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&folderList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup List err, uid:%+v, err:%+v", createUid, err)
	}
	return
}

func (m *MessageGroup) ListByFolderId(ctx *gin.Context, folderId int64) (folderList []MessageGroupFolder, err error) {
	conditions := map[string]interface{}{
		"folder_id":  folderId,
		"is_deleted": 0,
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions).Find(&folderList).Error
	if err != nil {
		zlog.Warnf(ctx, "MessageGroup List err, folderId:%+v, err:%+v", folderId, err)
	}
	return
}

func (m *MessageGroup) DistinctByGroupIdList(ctx *gin.Context, groupIdList []int64) (folderIdList []int64, err error) {
	conditions := map[string]interface{}{
		"authority":  AuthorityForGroup,
		"is_deleted": 0,
	}
	if len(groupIdList) > 0 {
		conditions["permission_group_id"] = groupIdList
	}
	err = m.SchemaClient().WithContext(ctx).Table(m.TableName()).Select("distinct folder_id").
		Where(conditions).
		Find(&folderIdList).Error
	if err != nil {
		zlog.Warnf(ctx, "DistinctByGroupIdList List err, folderId:%+v, err:%+v", groupIdList, err)
	}
	return
}

func (m *MessageGroup) ListByFolderAndAuthority(ctx *gin.Context, folderIdList []int64, groupIdList []int64) (messageGroup []MessageGroup, err error) {
	conditions := map[string]interface{}{
		"authority":  1,
		"folder_id":  folderIdList,
		"is_deleted": 0,
	}
	if len(groupIdList) > 0 {
		conditions["permission_group_id"] = groupIdList
	}

	tmpList := make([]MessageGroup, 0)
	err = m.SchemaClient().
		WithContext(ctx).
		Table(m.TableName()).
		Where(conditions).
		FindInBatches(&tmpList, BatchQueryLimit, func(tx *gorm.DB, batch int) error {
			messageGroup = append(messageGroup, tmpList...)
			return nil
		}).Error
	if err != nil {
		zlog.Warnf(ctx, "DistinctByGroupIdList List err, createUid:%+v, err:%+v", messageGroup, err)
	}
	return
}

func (m *MessageGroup) GetListByCond(ctx *gin.Context, folderId int64, messageGroupName string, groupIds []int64, authority int, userId int64, permissionGroupId []int64, createName string, startTime int64, endTime int64, pageNum int, pageSize int) (messageGroup []MessageGroup, total int64, err error) {
	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(m.TableName())

	if folderId > 0 {
		db = db.Where("folder_id = ?", folderId)
	}
	if len(groupIds) > 0 {
		db = db.Where("id in (?)", groupIds)
	}

	if strings.Trim(messageGroupName, " ") != "" {
		messageGroupName = strings.Trim(messageGroupName, " ")
		db = db.Where("name like ?", "%"+messageGroupName+"%")
	}

	if authority == defines.AuthorityForGroup {
		if len(permissionGroupId) > 0 {
			db = db.Where("permission_group_id in ?", permissionGroupId)
		}
		db = db.Where("authority = ?", authority)
	} else {
		db.Where("authority = ?", defines.AuthorityForPersonal)
		db.Where("create_uid = ?", userId)
	}

	if createName != "" {
		db = db.Where("create_name like ?", "%"+createName+"%")
	}

	if startTime > 0 && endTime > 0 {
		db = db.Where("create_time >= ? and create_time <= ?", startTime, endTime)
	}

	db = db.Where("is_deleted = ?", 0)

	limit := pageSize
	offset := pageNum * limit
	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	db = db.Limit(int(limit)).Offset(int(offset))
	err = db.Order("create_time desc").Find(&messageGroup).Error

	return messageGroup, total, err
}

func (m *MessageGroup) GetListByPermissionGroupIds(ctx *gin.Context, permissionGroupIds []int64, fields []string) (messageGroup []MessageGroup, err error) {
	conditions := map[string]interface{}{
		"authority":  AuthorityForGroup,
		"is_deleted": 0,
	}
	if len(permissionGroupIds) > 0 {
		conditions["permission_group_id"] = permissionGroupIds
	}

	db := m.SchemaClient().WithContext(ctx).Table(m.TableName()).Where(conditions)
	if len(fields) > 0 {
		db.Select(fields)
	}

	err = db.Find(&messageGroup).Error
	if err != nil {
		zlog.Warnf(ctx, "GetListByPermissionGroupIds err, permissionGroupIds:%+v, err:%+v", permissionGroupIds, err)
	}

	return messageGroup, err
}

// SearchAllList 搜索消息组
func (m *MessageGroup) SearchAllList(ctx *gin.Context, availableRange []string, name string, permissionGroupIdList, folderIds []int64, userId int64, fields []string) (groupList []MessageGroup, err error) {
	db := m.SchemaClient().WithContext(ctx).Table(m.TableName())
	if len(name) > 0 {
		db = db.Where("name like ?", "%"+name+"%")
	}

	if len(folderIds) > 0 {
		db = db.Where("folder_id in ?", folderIds)
	}

	if len(availableRange) > 0 {
		db = db.Where("available_range in ? ", availableRange)
	}

	if len(permissionGroupIdList) > 0 {
		if userId > 0 {
			db.Where("( (authority = ? and permission_group_id in ?)  or create_uid = ?)", defines.AuthorityForGroup, permissionGroupIdList, userId)
		} else {
			db.Where("(authority = ? and permission_group_id in ?)", defines.AuthorityForGroup, permissionGroupIdList)
		}
	} else {
		if userId > 0 {
			db.Where("( authority = ? or create_uid = ?)", defines.AuthorityForGroup, userId)
		} else {
			db.Where("authority = ?", defines.AuthorityForGroup)
		}
	}

	db = db.Where("is_deleted = ?", 0)

	if len(fields) > 0 {
		db.Select(fields)
	}

	err = db.Find(&groupList).Error
	if err != nil {
		zlog.Warnf(ctx, "ListByNameLike GetById err, name:%+v, err:%+v", name, err)
	}
	return
}
