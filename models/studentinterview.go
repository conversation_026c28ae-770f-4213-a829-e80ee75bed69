package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

// 访谈类型-原值
const NoType = 0      //未分类
const Daily = 1       //日常
const NewUser = 2     //用户家访
const EnRoll = 3      //续报
const WithDraw = 4    //退课
const NewCallBack = 5 //新用户回访
const BcInterview = 6 //课中回访
const Other = 99      //其他
const TftcJSQ = 7     //金丝雀-用户投放透传

// 访谈记录二进制映射的十进制数 bitMap  二进制位数是第(n-1)位   1 代表第0位
const DailyBitValue = 1        // 二进制第0位 01  =>1 * 2^0
const NewUserBitValue = 2      // 二进制第1位 10  =>0 * 2^0 + 1* 2^1
const EnRollBitValue = 4       // 二进制第2位 100 =>0 * 2^0 + 0* 2^1 + 1*2^2
const WithDrawBitValue = 8     // 1000
const NewCallBackBitValue = 16 // 10000
const BcInterviewBitValue = 32 // 100000
const OtherBitValue = 64       // 1000000
const TftcJSQValue = 128       // 10000000

var InterViewTypeBitMap = map[int]int{
	Daily:       DailyBitValue,
	NewUser:     NewUserBitValue,
	EnRoll:      EnRollBitValue,
	WithDraw:    WithDrawBitValue,
	NewCallBack: NewCallBackBitValue,
	BcInterview: BcInterviewBitValue,
	Other:       OtherBitValue,
	TftcJSQ:     TftcJSQValue,
}
var InterViewTypeMap = map[int]string{
	NoType:      "未分类",
	Daily:       "日常",
	NewUser:     "用户家访",
	EnRoll:      "续报",
	WithDraw:    "退课",
	NewCallBack: "新用户回访",
	BcInterview: "课中回访",
	Other:       "其他",
	TftcJSQ:     "投放透传",
}

type StudentInterview struct {
	ID              int64  `gorm:"primaryKey;column:id;type:bigint(20) unsigned"`
	StudentUID      int64  `gorm:"column:student_uid;type:bigint(20) unsigned;default:0"`  // 学生uid
	InterviewTime   int64  `gorm:"column:interview_time;type:int(10) unsigned;default:0"`  // 访谈时间
	Type            int    `gorm:"column:type;type:int(10) unsigned;default:0"`            // 访谈类型
	Content         string `gorm:"column:content;type:varchar(1000);default:0"`            // 访谈记录
	Deleted         int    `gorm:"column:deleted;type:tinyint(3) unsigned;default:0"`      // 是否删除
	CreateTime      int64  `gorm:"column:create_time;type:int(10) unsigned;default:0"`     // 创建时间
	UpdateTime      int64  `gorm:"column:update_time;type:int(10) unsigned;default:0"`     // 更新时间
	OperatorUID     int64  `gorm:"column:operator_uid;type:bigint(20) unsigned;default:0"` // 操作人uid
	Operator        string `gorm:"column:operator;type:varchar(100);default:0"`            // 操作人
	ExtData         string `gorm:"column:ext_data;type:varchar(10000)"`                    // 扩展数据
	CourseID        int64  `gorm:"column:course_id;type:int(10);default:0"`                // 打包课程Id
	ChannelType     int    `gorm:"column:channel_type;type:tinyint(1);default:0"`          // 家访渠道
	Friendliness    uint   `gorm:"column:friendliness;type:tinyint(2) unsigned;default:0"` // 亲密度
	CheckStatus     uint   `gorm:"column:check_status;type:tinyint(3) unsigned;default:0"` // 质检结果状态
	RecheckUID      int64  `gorm:"column:recheck_uid;type:bigint(20) unsigned;default:0"`  // 复检人uid
	CheckStatusTime int64  `gorm:"column:check_status_time;type:int(10);default:0"`        // 质检结果处理时间
	SeasonYear      int    `gorm:"column:season_year;type:smallint(5);default:0"`          // 学年
	LearnSeasonID   int    `gorm:"column:learn_season_id;type:tinyint(3);default:0"`       // 学季数字
	RoleType        int    `gorm:"column:role_type;type:tinyint(4);default:0"`             // 沟通角色
}

func (a *StudentInterview) TableName() string {
	return "tblStudentInterview"
}

func (a *StudentInterview) ListByToUidAndFromUidAndTimeRange(ctx *gin.Context, StudentUID, assistantUid, courseId, time int64, limit int) (response []StudentInterview, err error) {
	conditions := map[string]interface{}{
		"student_uid": StudentUID,
		"deleted":     0,
	}
	db := helpers.MysqlClientFuDao.WithContext(ctx).
		Table(a.TableName()).
		Where(conditions).
		Where("create_time < ?", time)
	if assistantUid > 0 {
		db = db.Where("operator_uid = ?", assistantUid)
	}
	err = db.Order("id desc").Limit(limit).
		Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetInterviewList failed, student_uid=%d, courseId=%d", StudentUID, courseId)
	}

	return
}

func (a *StudentInterview) GetInterViewTypeStr() []string {
	result := make([]string, 0)
	for bitType, bitValue := range InterViewTypeBitMap {
		if 0 < a.Type&bitValue {
			result = append(result, InterViewTypeMap[bitType])
		}
	}
	return result
}

func (a *StudentInterview) GetByIds(ctx *gin.Context, ids []int64) (response []StudentInterview, err error) {
	conditions := map[string]interface{}{
		"id":      ids,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Model(&StudentInterview{}).Where(conditions).Find(&response).Error
	if err != nil {
		return nil, err
	}

	return
}

func (a *StudentInterview) BatchCreate(ctx *gin.Context, interviews []StudentInterview) ([]int64, []int64, error) {
	if len(interviews) == 0 {
		return nil, nil, nil
	}

	db := helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName())
	result := db.Create(&interviews)
	if result.Error != nil {
		zlog.Warnf(ctx, "BatchCreate StudentInterview failed: %v", result.Error)
		return nil, nil, result.Error
	}

	createdIDs := make([]int64, len(interviews))
	courseIDs := make([]int64, len(interviews))

	for i, interview := range interviews {
		createdIDs[i] = interview.ID
		courseIDs[i] = interview.CourseID
	}

	return createdIDs, courseIDs, nil
}

func (a *StudentInterview) UpdateByMap(ctx *gin.Context, ids []int64, content string) error {
	updates := map[string]interface{}{
		"update_time": time.Now().Unix(),
		"content":     content,
	}
	return helpers.MysqlClientFuDao.WithContext(ctx).Model(&StudentInterview{}).Where("id in (?) and deleted = ? ", ids, false).Limit(len(ids)).Updates(updates).Error
}
