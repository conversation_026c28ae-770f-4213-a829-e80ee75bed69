package models

import (
	"assistantdeskgo/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const ()

type AssistantCallRecord struct {
	CallId        int64  `gorm:"primary;column:call_id" json:"call_id"`         //  主键id
	SourceType    int64  `gorm:"column:source_type" json:"source_type"`         //
	FromUid       int64  `gorm:"column:from_uid" json:"from_uid"`               //
	FromPhone     string `gorm:"column:from_phone" json:"from_phone"`           //
	KmsFromPhone  string `gorm:"column:kms_from_phone" json:"kms_from_phone"`   //
	HashFromPhone string `gorm:"column:hash_from_phone" json:"hash_from_phone"` //

	ToUid       int64  `gorm:"column:to_uid" json:"to_uid"`               //
	ToPhone     string `gorm:"column:to_phone" json:"to_phone"`           //
	KmsToPhone  string `gorm:"column:kms_to_phone" json:"kms_to_phone"`   //
	HashToPhone string `gorm:"column:hash_to_phone" json:"hash_to_phone"` //

	ResourseType string `gorm:"column:resourse_type" json:"resourse_type"` //
	Line         int64  `gorm:"column:line" json:"line"`                   //
	CallMode     int64  `gorm:"column:call_mode" json:"call_mode"`         //
	CallType     int64  `gorm:"column:call_type" json:"call_type"`         //
	CallResult   int64  `gorm:"column:call_result" json:"call_result"`     //

	Duration        int64  `gorm:"column:duration" json:"duration"`                   //
	StartTime       int64  `gorm:"column:start_time" json:"start_time"`               //
	StopTime        int64  `gorm:"column:stop_time" json:"stop_time"`                 //
	TriggerType     int64  `gorm:"column:trigger_type" json:"trigger_type"`           //
	CourseId        int64  `gorm:"column:course_id" json:"course_id"`                 //
	LessonId        int64  `gorm:"column:lesson_id" json:"lesson_id"`                 //
	LearnSeason     string `gorm:"column:learn_season" json:"learn_season"`           //
	ClassId         int64  `gorm:"column:class_id" json:"class_id"`                   //
	ServerId        int64  `gorm:"column:server_id" json:"server_id"`                 //
	ExtData         string `gorm:"column:ext_data" json:"ext_data"`                   //
	IsCheck         int64  `gorm:"column:is_check" json:"is_check"`                   //
	IsConfirm       int64  `gorm:"column:is_confirm" json:"is_confirm"`               //
	SyncSssistant   int64  `gorm:"column:sync_assistant" json:"sync_assistant"`       //
	CheckStatus     int64  `gorm:"column:check_status" json:"check_status"`           //
	ViewAssistant   int64  `gorm:"column:view_assistant" json:"view_assistant"`       //
	CheckStatusTime int64  `gorm:"column:check_status_time" json:"check_status_time"` //
	GroupCheck      int64  `gorm:"column:group_check" json:"group_check"`             //
	GroupCheckTime  int64  `gorm:"column:group_check_time" json:"group_check_time"`   //
	RecheckUid      int64  `gorm:"column:recheck_uid" json:"recheck_uid"`             //
	UploadTime      int64  `gorm:"column:upload_time" json:"upload_time"`             //
	DeviceUid       int64  `gorm:"column:device_uid" json:"device_uid"`               //
	PersonUid       int64  `gorm:"column:person_uid" json:"person_uid"`               //

	RecordFile string `gorm:"column:record_file" json:"record_file"` //
	CreateTime int64  `gorm:"column:create_time" json:"create_time"` //
	UpdateTime int64  `gorm:"column:update_time" json:"update_time"` //
	Deleted    int64  `gorm:"column:deleted" json:"deleted"`         //
}

func (a *AssistantCallRecord) TableName() string {
	return "tblAssistantCallRecord"
}

func (a *AssistantCallRecord) GetByCallId(ctx *gin.Context, callId int64) (response AssistantCallRecord, err error) {
	conditions := map[string]interface{}{
		"call_id": callId,
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).Where(conditions).Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "GetByCallId List failed, callId=%v", callId, err)
	}

	return
}

func (a *AssistantCallRecord) PullCallRecord(ctx *gin.Context, callId int64, size int) (response []AssistantCallRecord, err error) {
	conditions := map[string]interface{}{
		"deleted": 0,
	}
	err = helpers.MysqlClientFuDao.WithContext(ctx).Table(a.TableName()).
		Where(conditions).
		Where("call_id > ?", callId).
		Order("call_id asc").
		Limit(size).
		Find(&response).Error
	if err != nil {
		zlog.Warnf(ctx, "PullCallRecord List failed, callId=%v", callId, err)
	}

	return
}
